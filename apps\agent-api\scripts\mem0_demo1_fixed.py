import asyncio
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from openai import AsyncOpenAI
from mem0 import AsyncMemoryClient
from dotenv import load_dotenv
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- 1. 配置 ---
load_dotenv(dotenv_path="../1.env")

# API 配置
MEM0_API_KEY = os.environ.get("MEM0_API_KEY")
ARK_API_KEY = os.environ.get("VOLCENGINE_API_KEY")
VOLCANO_LLM_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
VOLCANO_LLM_ENDPOINT_ID = os.environ.get("VOLCANO_LLM_ENDPOINT_ID")

# 用户和配置
USER_ID = "demo_user_volcano_mem0_chat_001"

# 系统提示
SYSTEM_PROMPT = """你是一个温暖、体贴的情感陪伴AI助手。
你拥有长期记忆，能够记住用户的情感状态、重要事件、偏好和我们的对话历史。
请以温和、共情的语调回应，关注用户的情感需求。
当用户感到难过时给予安慰，开心时分享喜悦，困惑时提供支持。
利用你的记忆为用户提供连续性和个性化的情感支持。"""

class SimpleEmotionalChatbot:
    """简化的情感聊天机器人"""

    def __init__(self):
        self.mem0_client = None
        self.llm_client = None

    async def initialize(self):
        """初始化客户端"""
        if not all([MEM0_API_KEY, ARK_API_KEY, VOLCANO_LLM_ENDPOINT_ID]):
            raise ValueError("缺少必要的API密钥或配置")

        self.mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)
        self.llm_client = AsyncOpenAI(
            base_url=VOLCANO_LLM_BASE_URL,
            api_key=ARK_API_KEY,
        )

        logger.info("情感聊天机器人初始化完成")

    async def search_memories(self, query: str, limit: int = 2, fast_mode: bool = False) -> List[Dict]:
        """
        优化的记忆搜索：提高速度
        """
        if fast_mode:
            # 快速模式：跳过搜索，直接返回空结果
            return []

        try:
            # 优化查询长度，减少处理时间
            optimized_query = query[:100]  # 限制查询长度

            # 使用简单搜索，参考原始程序的成功实现
            memories = await self.mem0_client.search(
                query=optimized_query,
                user_id=USER_ID,
                limit=limit  # 减少搜索数量提高速度
            )

            # 处理搜索结果
            relevant_memories = []
            if memories and hasattr(memories, 'results') and memories.results:
                for memory_obj in memories.results:
                    if isinstance(memory_obj, dict):
                        memory_text = memory_obj.get('memory', '')
                        score = memory_obj.get('score', 0)
                        if memory_text:
                            relevant_memories.append({
                                'memory': memory_text,
                                'score': score
                            })
            elif memories and isinstance(memories, list):
                for memory_obj in memories:
                    if isinstance(memory_obj, dict):
                        memory_text = memory_obj.get('memory', '')
                        score = memory_obj.get('score', 0)
                        if memory_text:
                            relevant_memories.append({
                                'memory': memory_text,
                                'score': score
                            })

            # 按分数排序
            relevant_memories.sort(key=lambda x: x.get('score', 0), reverse=True)

            return relevant_memories[:limit]

        except Exception as e:
            logger.error(f"记忆搜索失败: {e}")
            return []

    async def add_memory(self, user_input: str, ai_response: str) -> bool:
        """
        增强的记忆添加：参考原始程序的成功实现
        """
        try:
            # 构建对话消息格式
            conversation_messages = [
                {"role": "user", "content": user_input},
                {"role": "assistant", "content": ai_response}
            ]

            logger.info(f"准备存储的消息: {conversation_messages}")

            # 使用简单添加，参考原始程序的成功实现
            add_result = await self.mem0_client.add(
                messages=conversation_messages,
                user_id=USER_ID,
                metadata={
                    "timestamp": datetime.now().isoformat(),
                    "session": "emotional_chat"
                },
                output_format="v1.1"  # 使用原始程序的成功格式
            )

            logger.info(f"存储API返回结果: {add_result}")

            # 显示存储结果的详细信息
            if add_result:
                if hasattr(add_result, 'results') and add_result.results:
                    logger.info(f"✓ 记忆存储成功，新增 {len(add_result.results)} 条记忆:")
                    for i, memory in enumerate(add_result.results, 1):
                        if isinstance(memory, dict):
                            memory_text = memory.get('memory', '')
                            memory_id = memory.get('id', 'N/A')
                            logger.info(f"  {i}. [ID: {memory_id}] {memory_text[:100]}...")

                    # 异步验证存储（不阻塞用户响应）
                    asyncio.create_task(self._verify_memory_storage(user_input))
                    return True

                elif isinstance(add_result, dict):
                    logger.info(f"✓ 记忆存储成功: {add_result}")
                    return True
                else:
                    logger.info(f"✓ 记忆存储成功，返回类型: {type(add_result)}")
                    return True
            else:
                logger.warning("⚠️ 记忆存储返回结果为空")
                return False

        except Exception as e:
            logger.error(f"记忆添加失败: {e}")
            logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"完整错误栈: {traceback.format_exc()}")
            return False

    async def _verify_memory_storage(self, user_input: str):
        """异步验证记忆存储"""
        try:
            # 等待1秒让记忆索引完成（减少等待时间）
            await asyncio.sleep(1)

            verification_result = await self.mem0_client.search(
                query=user_input[:50],
                user_id=USER_ID,
                limit=1
            )

            if verification_result and (
                (hasattr(verification_result, 'results') and verification_result.results) or
                (isinstance(verification_result, list) and verification_result)
            ):
                logger.info("✓ 验证成功：记忆已可搜索")
            else:
                logger.warning("⚠️ 验证失败：刚存储的记忆无法立即搜索到")
                logger.warning(f"验证搜索结果: {verification_result}")

        except Exception as e:
            logger.error(f"验证记忆存储失败: {e}")

    async def chat(self, user_input: str, fast_mode: bool = False) -> str:
        """处理用户输入并生成回复"""
        try:
            # 1. 搜索相关记忆（可选快速模式）
            relevant_memories = await self.search_memories(user_input, limit=2, fast_mode=fast_mode)

            # 2. 构建LLM提示
            llm_messages = [{"role": "system", "content": SYSTEM_PROMPT}]

            # 添加记忆上下文
            if relevant_memories:
                memory_texts = [mem['memory'] for mem in relevant_memories]
                memory_context = "以下是相关的记忆信息：\n" + "\n".join([f"- {memory}" for memory in memory_texts])
                llm_messages.append({
                    "role": "system",
                    "content": f"记忆上下文:\n{memory_context}\n\n请根据这些记忆信息来回应用户。"
                })

            # 添加用户输入
            llm_messages.append({"role": "user", "content": user_input})

            # 3. 调用LLM生成回复
            response = await self.llm_client.chat.completions.create(
                model=VOLCANO_LLM_ENDPOINT_ID,
                messages=llm_messages,
                max_tokens=1024,
                temperature=0.7,
            )
            ai_response = response.choices[0].message.content

            # 4. 同步保存记忆（重要：不使用异步任务）
            if not fast_mode:  # 快速模式跳过记忆保存
                try:
                    logger.info("正在保存对话记忆...")
                    success = await self.add_memory(user_input, ai_response)
                    if success:
                        logger.info("记忆保存成功")
                    else:
                        logger.warning("记忆保存失败")
                except Exception as e:
                    logger.error(f"保存记忆时出错: {e}")

            return ai_response

        except Exception as e:
            logger.error(f"聊天处理失败: {e}")
            return "抱歉，我遇到了一些问题，请稍后再试。"

    async def get_all_memories(self) -> List[Dict]:
        """获取所有记忆"""
        try:
            memories = await self.mem0_client.get_all(user_id=USER_ID, limit=50)

            if memories and hasattr(memories, 'results') and memories.results:
                return memories.results
            elif memories and isinstance(memories, list):
                return memories
            else:
                return []

        except Exception as e:
            logger.error(f"获取记忆失败: {e}")
            return []

    async def clear_memories(self) -> bool:
        """清除所有记忆（安全确认版本）"""
        try:
            print(f"⚠️  警告：即将删除用户 {USER_ID} 的所有记忆！")
            confirm = await asyncio.to_thread(input, "请输入 'DELETE' 来确认删除所有记忆: ")

            if confirm.strip() != "DELETE":
                print("操作已取消。")
                return False

            await self.mem0_client.delete_all(user_id=USER_ID)
            logger.info("所有记忆已清除")
            return True
        except Exception as e:
            logger.error(f"清除记忆失败: {e}")
            return False

    async def show_detailed_memories(self):
        """显示详细的记忆诊断信息"""
        try:
            print(f"\n--- 📋 用户 {USER_ID} 的记忆诊断 ---")

            # 尝试不同的API格式获取记忆
            print(">>> 尝试使用 v1.1 格式获取记忆...")
            try:
                memories_v11 = await self.mem0_client.get_all(user_id=USER_ID, limit=50, output_format="v1.1")
                print(f">>> v1.1 API 返回结果: {type(memories_v11)}")
            except Exception as e:
                print(f">>> v1.1 格式获取失败: {e}")
                memories_v11 = None

            print(">>> 尝试使用默认格式获取记忆...")
            try:
                memories_default = await self.mem0_client.get_all(user_id=USER_ID, limit=50)
                print(f">>> 默认 API 返回结果: {type(memories_default)}")
            except Exception as e:
                print(f">>> 默认格式获取失败: {e}")
                memories_default = None

            # 处理结果
            memories = memories_v11 or memories_default

            if memories and hasattr(memories, 'results') and memories.results:
                print(f"\n✅ 总共找到 {len(memories.results)} 条记忆:")
                for i, memory in enumerate(memories.results, 1):
                    memory_text = memory.get('memory', 'N/A')
                    memory_id = memory.get('id', 'N/A')
                    created_at = memory.get('created_at', 'N/A')
                    updated_at = memory.get('updated_at', 'N/A')
                    categories = memory.get('categories', [])
                    metadata = memory.get('metadata', {})

                    print(f"\n{i}. 记忆 ID: {memory_id}")
                    print(f"   内容: {memory_text}")
                    print(f"   创建时间: {created_at}")
                    print(f"   更新时间: {updated_at}")
                    if categories:
                        print(f"   分类: {', '.join(categories)}")
                    if metadata:
                        print(f"   元数据: {metadata}")

            elif memories and isinstance(memories, list):
                print(f"\n✅ 总共找到 {len(memories)} 条记忆:")
                for i, memory in enumerate(memories, 1):
                    print(f"{i}. {memory}")

            else:
                print("\n❌ 该用户暂无记忆记录")
                print("🔍 可能的原因:")
                print("   1. 记忆确实没有存储成功")
                print("   2. 记忆正在处理中，需要等待几分钟")
                print("   3. API Key 权限不足")
                print("   4. 用户ID不匹配")

            # 尝试搜索测试
            print(f"\n--- 🔍 搜索功能测试 ---")
            test_queries = ["alex", "hello", "姓名", "name", "用户"]
            for query in test_queries:
                try:
                    search_result = await self.mem0_client.search(query=query, user_id=USER_ID, limit=5)
                    if search_result and (
                        (hasattr(search_result, 'results') and search_result.results) or
                        (isinstance(search_result, list) and search_result)
                    ):
                        print(f">>> 搜索 '{query}': 找到结果")
                        if hasattr(search_result, 'results'):
                            for res in search_result.results[:2]:
                                print(f"    - {res.get('memory', str(res))[:50]}...")
                        else:
                            for res in search_result[:2]:
                                print(f"    - {res.get('memory', str(res))[:50]}...")
                    else:
                        print(f">>> 搜索 '{query}': 无结果")
                except Exception as e:
                    print(f">>> 搜索 '{query}' 失败: {e}")

        except Exception as e:
            print(f"❌ 获取记忆时出现错误: {e}")
            import traceback
            print(f"完整错误栈: {traceback.format_exc()}")

    async def debug_config(self):
        """显示配置和环境信息诊断"""
        print("\n--- 🔍 配置和环境信息诊断 ---")

        print(f">>> 用户ID: {USER_ID}")
        print(f">>> MEM0_API_KEY 状态: {'✓ 已设置' if MEM0_API_KEY else '❌ 未设置'}")
        if MEM0_API_KEY:
            print(f">>> MEM0_API_KEY 长度: {len(MEM0_API_KEY)} 字符")
            print(f">>> MEM0_API_KEY 前缀: {MEM0_API_KEY[:10]}..." if len(MEM0_API_KEY) > 10 else f">>> MEM0_API_KEY: {MEM0_API_KEY}")

        print(f">>> ARK_API_KEY 状态: {'✓ 已设置' if ARK_API_KEY else '❌ 未设置'}")
        print(f">>> VOLCANO_LLM_ENDPOINT_ID 状态: {'✓ 已设置' if VOLCANO_LLM_ENDPOINT_ID else '❌ 未设置'}")

        if VOLCANO_LLM_ENDPOINT_ID:
            print(f">>> VOLCANO_LLM_ENDPOINT_ID: {VOLCANO_LLM_ENDPOINT_ID}")

        # 检查环境文件
        import os
        env_file_path = "../1.env"
        if os.path.exists(env_file_path):
            print(f">>> 环境文件 {env_file_path}: ✓ 存在")
            try:
                with open(env_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f">>> 环境文件内容长度: {len(content)} 字符")
                    lines = content.strip().split('\n')
                    for line in lines:
                        if line.strip() and not line.strip().startswith('#') and '=' in line:
                            key = line.split('=')[0].strip()
                            print(f"    - {key}: {'已设置' if '=' in line and line.split('=')[1].strip() else '未设置'}")
            except Exception as e:
                print(f">>> 读取环境文件失败: {e}")
        else:
            print(f">>> 环境文件 {env_file_path}: ❌ 不存在")

        # Python包版本检查
        try:
            import mem0
            print(f">>> mem0ai 版本: {getattr(mem0, '__version__', '未知')}")
        except ImportError:
            print(">>> mem0ai: ❌ 未安装")

        try:
            import openai
            print(f">>> openai 版本: {getattr(openai, '__version__', '未知')}")
        except ImportError:
            print(">>> openai: ❌ 未安装")

        try:
            import dotenv
            print(f">>> python-dotenv: ✓ 已安装")
        except ImportError:
            print(">>> python-dotenv: ❌ 未安装")


# 主要功能函数
async def main_chat():
    """主聊天功能"""
    chatbot = SimpleEmotionalChatbot()

    try:
        await chatbot.initialize()

        print("🤖 情感陪伴AI助手已启动 (修复版)")
        print("💡 我拥有持久记忆功能，会记住我们的对话")
        print("📝 输入 'quit' 或 'exit' 退出聊天")
        print("🔍 输入 'memories' 查看所有记忆")
        print("🗑️ 输入 'clear' 清除所有记忆")

        while True:
            user_input = await asyncio.to_thread(input, f"\n👤 你: ")

            if user_input.lower() in ['quit', 'exit']:
                print("👋 再见！我会记住我们的对话。")
                break

            if user_input.lower() == 'memories':
                memories = await chatbot.get_all_memories()
                if memories:
                    print(f"\n📚 找到 {len(memories)} 条记忆:")
                    for i, memory in enumerate(memories, 1):
                        memory_text = memory.get('memory', '')
                        created_at = memory.get('created_at', '')[:16]
                        print(f"{i}. [{created_at}] {memory_text}")
                else:
                    print("📝 暂无记忆记录")
                continue

            if user_input.lower() == 'clear':
                if await chatbot.clear_memories():
                    print("✅ 所有记忆已清除")
                else:
                    print("❌ 清除记忆失败")
                continue

            # 处理用户输入
            ai_response = await chatbot.chat(user_input)
            print(f"🤖 AI: {ai_response}")

    except Exception as e:
        logger.error(f"聊天程序错误: {e}")
        print(f"❌ 程序出现错误: {e}")


async def test_memory_features():
    """测试记忆功能"""
    print("🧪 测试记忆功能...")

    try:
        chatbot = SimpleEmotionalChatbot()
        await chatbot.initialize()

        # 测试添加记忆
        print(">>> 测试添加记忆...")
        success = await chatbot.add_memory("我叫张三", "很高兴认识你，张三！")
        print(f"添加记忆结果: {success}")

        # 等待索引完成
        await asyncio.sleep(2)

        # 测试搜索记忆
        print(">>> 测试搜索记忆...")
        memories = await chatbot.search_memories("我的名字", limit=3)
        print(f"搜索结果: {len(memories)} 条记忆")
        for mem in memories:
            print(f"  - {mem['memory']} (score: {mem['score']:.3f})")

        # 测试对话
        print(">>> 测试对话...")
        response = await chatbot.chat("你还记得我的名字吗？")
        print(f"AI回复: {response}")

    except Exception as e:
        logger.error(f"测试失败: {e}")


async def performance_test():
    """性能测试"""
    print("⚡ 性能测试...")

    try:
        chatbot = SimpleEmotionalChatbot()
        await chatbot.initialize()

        # 测试搜索速度
        import time

        start_time = time.time()
        memories = await chatbot.search_memories("测试查询", limit=5)
        search_time = time.time() - start_time

        print(f"搜索耗时: {search_time:.2f} 秒")
        print(f"搜索结果: {len(memories)} 条记忆")

        # 测试添加速度
        start_time = time.time()
        success = await chatbot.add_memory("性能测试", "测试回复")
        add_time = time.time() - start_time

        print(f"添加耗时: {add_time:.2f} 秒")
        print(f"添加结果: {success}")

    except Exception as e:
        logger.error(f"性能测试失败: {e}")


async def show_detailed_memories():
    """显示详细记忆诊断"""
    try:
        chatbot = SimpleEmotionalChatbot()
        await chatbot.initialize()
        await chatbot.show_detailed_memories()
    except Exception as e:
        logger.error(f"详细记忆诊断失败: {e}")


async def debug_configuration():
    """显示配置信息"""
    try:
        chatbot = SimpleEmotionalChatbot()
        await chatbot.initialize()
        await chatbot.debug_config()
    except Exception as e:
        logger.error(f"配置诊断失败: {e}")


if __name__ == "__main__":
    print("=== 修复版 Mem0 + 火山引擎 情感陪伴AI助手 ===")
    print("选择操作:")
    print("1. 开始聊天")
    print("2. 测试记忆功能")
    print("3. 查看用户记忆 (详细诊断)")
    print("4. 性能测试")
    print("5. 清除所有记忆 (⚠️ 危险操作)")
    print("6. 显示配置信息")
    print("7. 退出")

    try:
        choice = input("请输入选择 (1-7): ").strip()

        if choice == "1":
            asyncio.run(main_chat())
        elif choice == "2":
            asyncio.run(test_memory_features())
        elif choice == "3":
            asyncio.run(show_detailed_memories())
        elif choice == "4":
            asyncio.run(performance_test())
        elif choice == "5":
            asyncio.run(main_chat())  # 清除记忆通过聊天界面的clear命令
        elif choice == "6":
            asyncio.run(debug_configuration())
        elif choice == "7":
            print("👋 再见！")
        else:
            print("无效选择，启动聊天模式...")
            asyncio.run(main_chat())

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
