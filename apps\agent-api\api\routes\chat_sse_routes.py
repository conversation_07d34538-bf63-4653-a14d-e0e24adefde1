# api/routes/chat_sse_routes.py
import asyncio
import json
import logging
from typing import AsyncIterable, Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from api.models.chat_models import (
    ChatRequest, SSEData,
    TextChunkData, SuggestedQuestionsData, CrisisAlertData, StreamEndData, ErrorData
)
from api.dependencies.auth import get_current_user
from api.services.session_service import SessionService, get_session_service
from api.services.crisis_detection_service import CrisisDetectionResult
from api.settings import get_settings

router = APIRouter(tags=["Chat"])
logger = logging.getLogger(__name__)
settings = get_settings()

@router.post("/message")
async def chat_message_sse_endpoint(
    request_data: ChatRequest,
    agent_service,
    current_user: Dict[str, Any] = Depends(get_current_user),
    session_service: SessionService = Depends(get_session_service)
):
    # 从JWT Token中安全获取用户ID，而非从请求体
    userId = current_user.get("sub")

    sessionId = request_data.sessionId
    message_content = request_data.message.content
    characterId = request_data.characterId
    promptParams = request_data.promptParams

    if not message_content:
        async def error_event_gen():
            yield SSEData(event="error", data=ErrorData(type="Validation Error", message="Message content cannot be empty.")).to_sse_format()
        return StreamingResponse(error_event_gen(), media_type="text/event-stream")

    logger.info(f"Received chat message from user {userId} in session {sessionId}")

    async def event_generator() -> AsyncIterable[str]:
        stream_ended_properly = False
        has_non_critical_errors = False
        complete_ai_response = ""
        ai_message_id: Optional[str] = None
        stream_end_sent_by_generator = False

        try:
            def create_sse_event_str(event_name: str, data_model: BaseModel) -> str:
                event = SSEData(event=event_name, data=data_model)
                return event.to_sse_format()

            # try:
            #     user_crisis_result: CrisisDetectionResult = await agno_agent_service.detect_crisis(
            #         message_content,
            #         userId
            #     )
            #     if user_crisis_result.is_crisis:
            #         logger.warning(f"User message crisis detected for session {sessionId}, user {userId}. Type: {user_crisis_result.type}, Level: {user_crisis_result.level}")
            #         yield create_sse_event_str("crisis_alert", CrisisAlertData(
            #             is_crisis=True,
            #             type=user_crisis_result.type,
            #             level=user_crisis_result.level,
            #             hotline_info=user_crisis_result.hotline_info,
            #             message=f"用户消息中检测到危机情况: {user_crisis_result.message or user_crisis_result.type}"
            #         ))
            #         yield create_sse_event_str("stream_end", StreamEndData(status="error_user_crisis", messageId=None, finalContentLength=0))
            #         stream_end_sent_by_generator = True
            #         return
            # except Exception as e:
            #     logger.error(f"Error during user message crisis detection for session {sessionId}: {e}", exc_info=True)
            #     has_non_critical_errors = True
            #     yield create_sse_event_str(
            #         "error",
            #         ErrorData(type="UserCrisisDetectionError", message=f"Error during user message crisis detection: {str(e)}")
            #     )

            async def save_user_msg_task_internal():
                try:
                    # 创建用户消息载荷，确保不包含 conversation_id 字段
                    user_message_payload = {
                        "session_id": sessionId,
                        "role": "user",
                        "content": message_content,
                        "content_type": "text",
                        "message_type": "user",
                        "status": "sent"
                    }

                    # 如果请求中包含时间戳，则使用它
                    if hasattr(request_data.message, 'timestamp') and request_data.message.timestamp:
                        user_message_payload["created_at"] = request_data.message.timestamp

                    # 保存用户消息
                    saved_user_message = await session_service.save_chat_message(user_message_payload)
                    await session_service.update_session_metadata(sessionId)
                    logger.info(f"User message for session {sessionId} saved successfully. ID: {saved_user_message.get('id')}")
                    return saved_user_message
                except Exception as e:
                    logger.error(f"Error saving user message for session {sessionId}: {e}", exc_info=True)
                    return None

            # 先保存用户消息，确保消息已保存到数据库再继续
            try:
                save_user_msg_result = await save_user_msg_task_internal()
                if not save_user_msg_result:
                    logger.warning(f"User message for session {sessionId} failed to save, but continuing with response generation")
            except Exception as e:
                logger.error(f"Exception while saving user message for session {sessionId}: {e}", exc_info=True)
                yield create_sse_event_str(
                    "error",
                    ErrorData(type="UserMessageSaveError", message=f"Error saving user message: {str(e)}")
                )
                has_non_critical_errors = True

            # response_stream, agent = await agno_agent_service.generate_response_stream(
            #     user_id=userId,
            #     session_id=sessionId,
            #     message=message_content,
            #     session_service=session_service,
            #     character_id=characterId,
            #     prompt_params=promptParams
            # )

            # async for text_chunk in response_stream:
            #     yield create_sse_event_str("text_chunk", TextChunkData(delta=text_chunk))
            #     complete_ai_response += text_chunk

            # logger.info(f"AI response stream completed for session {sessionId}. Length: {len(complete_ai_response)}")

            async def save_ai_msg_task_internal_post():
                nonlocal ai_message_id
                try:
                    if complete_ai_response:
                        # 创建 AI 消息载荷，确保不包含 conversation_id 字段
                        ai_message_payload = {
                            "session_id": sessionId,
                            "role": "assistant",
                            "content": complete_ai_response,
                            "content_type": "text",
                            "message_type": "assistant",
                            "status": "sent"  # 修改为数据库约束允许的值
                        }

                        # 保存 AI 消息
                        saved_message = await session_service.save_chat_message(ai_message_payload)
                        ai_message_id = saved_message.get("id")
                        await session_service.update_session_metadata(sessionId)
                        logger.info(f"AI message for session {sessionId} (ID: {ai_message_id}) saved successfully.")
                except Exception as e:
                    logger.error(f"Error saving AI message for session {sessionId}: {e}", exc_info=True)

            async def process_memory_task_internal():
                try:
                    if complete_ai_response:
                        logger.debug(f"Starting async memory processing for user {userId}, session {sessionId}")

                        logger.debug(f"Completed async memory processing for user {userId}, session {sessionId}")
                except Exception as e:
                    logger.error(f"Error in async memory processing for user {userId}, session {sessionId}: {e}", exc_info=True)

            save_ai_msg_task = asyncio.create_task(save_ai_msg_task_internal_post())
            memory_task = asyncio.create_task(process_memory_task_internal())

            # try:
            #     crisis_result: CrisisDetectionResult = await agno_agent_service.detect_crisis(
            #         complete_ai_response,
            #         userId
            #     )
            #     if crisis_result.is_crisis:
            #         alert_data = CrisisAlertData(
            #             is_crisis=crisis_result.is_crisis,
            #             type=crisis_result.type,
            #             level=crisis_result.level,
            #             hotline_info=crisis_result.hotline_info,
            #             message=crisis_result.message
            #         )
            #         yield create_sse_event_str("crisis_alert", alert_data)
            # except Exception as e:
            #     logger.error(f"Error during crisis detection for session {sessionId}: {e}", exc_info=True)
            #     has_non_critical_errors = True
            #     yield create_sse_event_str(
            #         "error",
            #         ErrorData(type="CrisisDetectionError", message=f"Error during crisis detection: {str(e)}")
            #     )

            # try:
            #     if agent and complete_ai_response:
            #         recent_messages = await session_service.get_recent_messages(sessionId)
            #         formatted_messages = [
            #             {"role": msg["role"], "content": msg["content"]}
            #             for msg in recent_messages
            #         ]
            #         suggested_questions_list = await agno_agent_service.get_suggested_questions(
            #             agent=agent,
            #             complete_response=complete_ai_response,
            #             message_history=formatted_messages
            #         )
            #         if suggested_questions_list:
            #             yield create_sse_event_str(
            #                 "suggested_questions",
            #                 SuggestedQuestionsData(questions=suggested_questions_list)
            #             )
            # except Exception as e:
            #     logger.error(f"Error generating suggested questions for session {sessionId}: {e}", exc_info=True)
            #     has_non_critical_errors = True
            #     yield create_sse_event_str(
            #         "error",
            #         ErrorData(type="SuggestedQuestionsError", message=f"Error generating suggested questions: {str(e)}")
            #     )

            # await asyncio.gather(
            #     save_ai_msg_task,
            #     memory_task,
            #     return_exceptions=True
            # )
            # stream_ended_properly = True
            print("暂时注释掉了")
        except Exception as e:
            logger.error(f"Unhandled error in chat stream for session {sessionId}: {e}", exc_info=True)
            stream_ended_properly = False

        finally:
            if not stream_end_sent_by_generator:
                if stream_ended_properly:
                    final_status_to_send = "done"
                    if has_non_critical_errors:
                        final_status_to_send = "done_with_errors"

                    current_complete_ai_response = complete_ai_response if 'complete_ai_response' in locals() and isinstance(complete_ai_response, str) else ""
                    current_ai_message_id = ai_message_id if 'ai_message_id' in locals() and isinstance(ai_message_id, str) else None

                    logger.info(f"Stream ended by finally block with status: {final_status_to_send} for session {sessionId}.")
                    yield create_sse_event_str(
                        "stream_end",
                        StreamEndData(
                            messageId=current_ai_message_id,
                            status=final_status_to_send,
                            finalContentLength=len(current_complete_ai_response)
                        )
                    )
                else:
                    logger.error(f"Stream did not end properly (finally block). Session {sessionId}. Sending error event and stream_end.")
                    yield create_sse_event_str(
                        "error",
                        ErrorData(
                            type="server_error",
                            message="An unexpected error occurred and the stream was interrupted."
                        )
                    )
                    current_complete_ai_response_on_error = complete_ai_response if 'complete_ai_response' in locals() and isinstance(complete_ai_response, str) else ""
                    current_ai_message_id_on_error = ai_message_id if 'ai_message_id' in locals() and isinstance(ai_message_id, str) else None
                    yield create_sse_event_str(
                        "stream_end",
                        StreamEndData(
                            messageId=current_ai_message_id_on_error,
                            status="error_stream_unexpectedly_terminated",
                            finalContentLength=len(current_complete_ai_response_on_error)
                        )
                    )
            else:
                logger.info(f"Stream_end already sent by generator logic for session {sessionId}.")

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream"
    )
