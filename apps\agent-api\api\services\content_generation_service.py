from typing import List, Dict, Any, Optional
from api.services.llm_proxy_service import llm_proxy_service # Assuming llm_proxy_service is an instance
from api.settings import logger
import json

class ContentGenerationService:
    def __init__(self, llm_service_instance = None): # Allow passing llm_proxy_service instance for testing
        # This will be useful if you want to mock llm_service during tests
        self.llm_service = llm_service_instance or llm_proxy_service

    async def generate_session_summary_and_tags(
        self,
        messages_list: List[Dict[str, Any]],
        model_name: Optional[str] = None # Allow specifying model if needed
    ) -> Dict[str, Any]:
        """
        Generates a summary, a potential topic, and tags for a given list of chat messages.

        Args:
            messages_list: A list of message dictionaries (e.g., {"role": "user", "content": "..."}).
            model_name: Optional model name to override the default for summary generation.

        Returns:
            A dictionary containing "summary", "topic", and "tags" (list of strings).
            Returns default/empty values if generation fails or no messages are provided.
        """
        if not messages_list:
            logger.warning("ContentGenerationService: No messages provided for summary generation.")
            return {"summary": None, "topic": None, "tags": []}

        # Combine messages into a single text block for the prompt
        # Simple concatenation, might need more sophisticated formatting for better results
        full_conversation_text = "\n".join([
            f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"
            for msg in messages_list
        ])

        # TODO: Consider a more robust way to estimate token count if necessary
        # For now, assume the conversation text is within reasonable limits for summary models.
        # if len(full_conversation_text) > 3500: # Rough character limit, adjust based on typical token ratios
            # logger.warning(f"Conversation text for summary is very long ({len(full_conversation_text)} chars), might be truncated or fail.")
            # Consider truncating or summarizing in chunks if this becomes an issue

        # Define the prompt for the LLM
        # This prompt asks for JSON output, which is generally more reliable to parse.
        prompt = f"""你是一个对话分析助手。请根据以下对话内容，生成一个简洁的总结、一个核心主题（5-10个字）、以及3-5个相关的关键词标签。
请严格按照以下JSON格式返回，不要包含任何额外的解释或Markdown标记：
{{
  "summary": "对话的简洁总结...",
  "topic": "对话的核心主题...",
  "tags": ["标签1", "标签2", "标签3"]
}}

对话内容如下：

{full_conversation_text}"""

        try:
            logger.debug(f"ContentGenerationService: Sending request to LLM for summary. Conversation length: {len(full_conversation_text)} chars.")
            # 修复：使用正确的 generate_text 方法替代不存在的 generate_full_response
            response_str = await self.llm_service.generate_text(
                prompt=prompt,
                max_tokens=400,
                temperature=0.5 # Slightly creative for good summaries/topics
            )

            if not response_str:
                logger.error("ContentGenerationService: LLM returned no response for summary generation.")
                return {"summary": None, "topic": "无法生成主题", "tags": []}

            logger.debug(f"ContentGenerationService: Raw LLM response for summary: {response_str}")

            # Attempt to parse the JSON response
            # Handle potential JSON decoding errors gracefully
            try:
                parsed_response = json.loads(response_str)
                # Validate basic structure
                if not all(k in parsed_response for k in ["summary", "topic", "tags"]):
                    logger.error(f"ContentGenerationService: LLM response JSON missing required keys. Response: {response_str}")
                    # Fallback: try to use the whole response as summary if parsing fails structurally
                    return {"summary": response_str[:500], "topic": "解析主题失败", "tags": []} # Truncate if too long

                # Ensure tags are a list of strings
                if not isinstance(parsed_response.get("tags"), list) or \
                   not all(isinstance(tag, str) for tag in parsed_response.get("tags", [])):
                    logger.warning(f"ContentGenerationService: 'tags' field is not a list of strings or is missing. Response: {response_str}")
                    parsed_response["tags"] = [] # Default to empty list if tags are malformed

                return {
                    "summary": parsed_response.get("summary"),
                    "topic": parsed_response.get("topic"),
                    "tags": parsed_response.get("tags", [])
                }
            except json.JSONDecodeError as json_err:
                logger.error(f"ContentGenerationService: Failed to decode LLM JSON response for summary: {json_err}. Response: {response_str}")
                # Fallback: use the raw response (or part of it) as summary if JSON is malformed
                return {"summary": response_str[:500], "topic": "解析总结失败", "tags": []}

        except Exception as e:
            logger.error(f"ContentGenerationService: Error during summary generation LLM call: {e}", exc_info=True)
            return {"summary": f"生成总结时出错: {str(e)[:100]}", "topic": "生成主题时出错", "tags": []}

    async def generate_mood_feedback(
        self,
        score: int,
        note: Optional[str],
        tags: Optional[List[str]],
        model_name: Optional[str] = None # Allow specifying model if needed
    ) -> Optional[str]:
        """
        Generates AI feedback based on the user's mood entry.

        Args:
            score: The mood score (1-10).
            note: Optional user note about the mood.
            tags: Optional list of tags associated with the mood.
            model_name: Optional model name to override the default.

        Returns:
            A string containing AI-generated feedback, or None if generation fails.
        """
        if not (1 <= score <= 10):
            logger.warning(f"ContentGenerationService: Invalid mood score ({score}) for feedback generation. Must be 1-10.")
            return None

        prompt_parts = [
            f"用户当前的心情评分为 {score} (1分表示非常差，10分表示非常好)。"
        ]

        if note:
            prompt_parts.append(f"用户记录的笔记是：\"{note}\"")

        if tags:
            prompt_parts.append(f"用户关联的标签有：{', '.join(tags)}。")

        user_mood_description = " ".join(prompt_parts)

        system_prompt = """你是一位充满同理心和智慧的AI伙伴。用户刚刚记录了他们的心情。请根据用户提供的心情评分、笔记和标签，给出一段温馨、积极、有建设性或能引发思考的简短回应（建议1-3句话）。
- 如果分数较低，尝试给予理解和鼓励，可以提出一些简单的放松建议或积极想法。
- 如果分数中等，可以给予肯定并鼓励保持。
- 如果分数较高，可以分享喜悦并鼓励继续保持积极心态。
- 尽量自然、口语化，避免过于刻板说教。你的目标是让用户感到被理解和支持。"""

        prompt = f"{system_prompt}\n\n{user_mood_description}"

        try:
            logger.info(f"ContentGenerationService: Sending request to LLM for mood feedback. Score: {score}")
            # 修复：使用正确的 generate_text 方法替代不存在的 generate_full_response
            response_str = await self.llm_service.generate_text(
                prompt=prompt,
                max_tokens=150,
                temperature=0.7 # Moderately creative and empathetic
            )

            if not response_str:
                logger.error("ContentGenerationService: LLM returned no response for mood feedback.")
                return None

            logger.debug(f"ContentGenerationService: Raw LLM response for mood feedback: {response_str}")
            # Assuming the response is directly usable text, no JSON parsing needed here unless specified by prompt.
            return response_str.strip()

        except Exception as e:
            logger.error(f"ContentGenerationService: Error during mood feedback generation LLM call: {e}", exc_info=True)
            return None

# Instantiate the service for global use, similar to other services
content_generation_service = ContentGenerationService()

# Example usage (for testing purposes, typically not run here):
# async def main():
#     test_messages = [
#         {"role": "user", "content": "你好，今天天气怎么样？"},
#         {"role": "assistant", "content": "你好！今天天气晴朗，很适合出门。"},
#         {"role": "user", "content": "太好了，我正打算出去散步。"}
#     ]
#     result = await content_generation_service.generate_session_summary_and_tags(test_messages)
#     print(result)

# if __name__ == "__main__":
#     import asyncio
#     asyncio.run(main())
