# 故事 1.6: 对话式提醒功能

## 基本信息
- **故事编号**: 1.6
- **故事标题**: 对话式提醒功能
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: 老年用户
- **优先级**: 中（P2）
- **工作量估计**: 6-10 个工作日
- **依赖关系**: 故事 1.1-Frontend-Permissions (系统权限管理), 故事 1.4 (实时语音会话), 故事 1.6-B (后端提醒API), 故事 1.6-UI (提醒功能界面设计 - **本故事将先行实现逻辑，UI可使用临时占位组件**)
- **Status**: Approved

## 故事描述

作为老年用户，我希望能直接在聊天时告诉AI帮我记事，**以便** 我不用去学习复杂的日历或闹钟App。

## 验收标准

### AC1: 对话中的提醒设置体验
- [ ] 用户可以通过自然语言在对话中设置提醒（"明天下午三点提醒我吃药"）
- [ ] AI能够智能识别提醒意图并给出个性化的确认回复（**该过程由后端的Function Calling驱动**）
- [ ] 提醒设置过程完全融入自然对话流，无需额外的表单或菜单
- [ ] 支持各种时间表达方式（相对时间、绝对时间、模糊时间）

### AC2: 智能确认和编辑界面
- [ ] **当AI的回复包含提醒确认时**，前端能渲染一个结构化的确认信息卡片（时间、内容）
- [ ] 用户可以通过对话修改提醒的时间或内容
- [ ] 提供清晰的"确认设置"和"取消"交互选项
- [ ] 支持提醒的二次编辑和删除功能

### AC3: 本地推送通知集成
- [ ] 客户端成功接收提醒解析结果并设置本地推送通知
- [ ] 推送通知在预定时间准确触发，内容包含AI角色的个性化表达
- [ ] 通知点击后能够回到应用并显示相关的对话上下文
- [ ] 支持推送通知的权限管理和用户引导

### AC4: 提醒管理功能
- [ ] 在对话界面中能够查看已设置的提醒列表
- [ ] 支持通过对话方式取消或修改现有提醒
- [ ] 提醒触发后在对话中有适当的反馈和跟进
- [ ] 与系统日历集成（可选功能）

## Tasks / Subtasks

### 第一阶段：提醒意图识别和确认UI (2-3天)
- [ ] **对话流中的提醒处理** (AC1)
  - 扩展`TextChatService.ts`（或`RtcChatService.ts`）以识别和处理来自后端的、包含提醒确认的特殊消息结构。
  - **移除对/api/v1/reminders/parse接口的调用**。
  - 实现提醒上下文的会话状态管理。

- [ ] **AI确认消息组件** (AC1, AC2)
  - 创建`src/components/features/reminders/ReminderConfirmation.tsx`
  - 显示解析后的提醒信息（时间、内容、重要性）
  - 实现AI个性化的确认语言和表达
  - 支持时间格式的本地化显示

- [ ] **提醒编辑交互** (AC2)
  - 在对话界面中实现提醒的即时编辑功能
  - 支持时间和内容的自然语言修改
  - 实现修改后的重新确认流程
  - 添加提醒取消的对话交互

### 第二阶段：推送通知系统集成 (2天)
- [ ] **通知权限管理** (AC3)
  - 创建`src/services/NotificationService.ts`
  - 实现推送通知权限的请求和检查
  - 处理权限拒绝的优雅降级和用户引导
  - 支持权限状态的实时监控

- [ ] **本地推送调度** (AC3)
  - 集成Expo Notifications进行本地推送管理
  - 实现提醒的精确时间调度
  - 配置推送通知的内容格式（包含AI角色元素）
  - 处理应用在后台时的通知触发

- [ ] **通知点击处理** (AC3)
  - 实现通知点击后的深度链接处理
  - 恢复相关的对话上下文和提醒信息
  - 在对话界面中高亮显示已触发的提醒
  - 支持通知的标记已读和跟进操作

### 第三阶段：提醒管理界面 (1-2天)
- [ ] **提醒列表显示** (AC4)
  - 创建`src/components/features/reminders/ReminderList.tsx`
  - 在对话中通过自然语言查询显示提醒列表
  - 实现提醒的状态管理（待触发、已触发、已完成）
  - 支持提醒的分类和优先级显示

- [ ] **对话式提醒管理** (AC4)
  - 实现"查看我的提醒"等语音/文本命令
  - 支持"取消明天的提醒"等自然语言管理操作
  - 在对话中显示提醒操作的结果确认
  - 添加提醒相关的智能建议和提示

- [ ] **提醒跟进机制** (AC4)
  - 实现提醒触发后的对话跟进
  - AI主动询问任务完成情况
  - 支持重复提醒的智能设置
  - 提供提醒效果的反馈收集

### 第四阶段：系统集成和优化 (1-2天)
- [ ] **系统日历集成** (AC4 - 可选)
  - 请求系统日历权限
  - 将确认的提醒写入系统日历应用
  - 支持日历事件的双向同步
  - 处理日历冲突和时间占用提示

- [ ] **状态管理优化** (AC1, AC2, AC4)
  - 扩展reminderStore管理提醒相关状态
  - 实现提醒数据的本地缓存和同步
  - 优化提醒设置流程的性能
  - 支持离线状态下的提醒管理

- [ ] **用户体验优化** (AC1, AC2, AC3)
  - 添加提醒设置的触觉反馈
  - 实现提醒确认的庆祝动效
  - 优化时间选择的用户界面
  - 添加提醒功能的用户引导和帮助

## Dev Notes

CRITICAL: This is a **frontend functionality story**. 
**UI NOTE**: While Story 1.6-UI will provide the final polished components, this story can proceed by building the logic and using placeholder UI components as described below. This unblocks backend integration and logic development.

Load the following standards for implementation:
- `@docs/architecture/mobile-app-tech-stack.md`
- `@docs/architecture/mobile-app-source-tree.md`
- `@docs/architecture/mobile-app-coding-standards.md`

**Technical Guidance from Architecture:**

### Relevant API Endpoints to Consume:
从 `@docs/architecture/03-api-design.md` 和 `@docs/stories/backend/1.6-B.story.md` 中的关键接口：
- **提醒解析API**:
  - **(已移除)**不再需要前端调用独立的解析API。此逻辑已通过后端的`Function Calling`集成到核心对话流中。前端的职责是渲染后端返回的确认任务。
- **提醒管理API**:
  - `GET /api/v1/reminders` - 获取用户提醒列表
  - `POST /api/v1/reminders` - 创建提醒
  - `PUT /api/v1/reminders/{id}` - 更新提醒
  - `DELETE /api/v1/reminders/{id}` - 删除提醒

### UI Components to Build/Use:
从 `@docs/architecture/06-frontend-architecture.md` 中的组件架构：
- **提醒相关组件** (`src/components/features/reminders/`):
  - `ReminderConfirmation.tsx` - **提醒确认卡片组件**:
    - **功能**: 当AI解析用户提醒意图后，在聊天流中显示此卡片。
    - **结构**: 应包含解析出的**提醒内容**、**时间**（格式化为用户友好的字符串，如"明天下午3:00"），以及两个按钮："确认设置"和"修改"。
    - **交互**: 点击"确认设置"会调用服务创建提醒；点击"修改"则允许用户通过文本或语音进一步修正。
  - `ReminderList.tsx` - **提醒列表展示组件**:
    - **功能**: 当用户查询"我的提醒"时，在聊天流中显示此组件。
    - **结构**: 以列表形式展示多个 `ReminderCard.tsx`。
    - **交互**: 列表应可滚动。
  - `ReminderCard.tsx` - **单个提醒的卡片组件**:
    - **功能**: 在提醒列表中代表一个已设置的提醒。
    - **结构**: 显示**提醒内容**、**预定时间**和**状态**（如"待触发"）。包含一个"取消"或"编辑"按钮。
- **服务层扩展**:
  - `NotificationService.ts` - 推送通知管理
  - 扩展`TextChatService.ts` - 支持提醒相关的对话处理
- **状态管理**: 新增`reminderStore.ts`

### 核心服务实现 (`src/services/NotificationService.ts`)
根据 `expo-notifications` 最新文档，该服务应负责封装所有与通知相关的功能。
```typescript
// src/services/NotificationService.ts
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform, Alert } from 'react-native';
import { ReminderData } from '../store/reminderStore'; // 假设Store中会定义此类型

// 初始化通知处理器
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

class NotificationService {
  /**
   * 检查并请求推送通知权限
   * @returns {Promise<boolean>} 是否已获得权限
   */
  static async requestPermissions(): Promise<boolean> {
    if (!Device.isDevice) {
      Alert.alert('功能限制', '推送通知功能仅在真实设备上可用。');
      return false;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      Alert.alert(
        '权限被拒绝',
        '您拒绝了通知权限，将无法接收到提醒。请在系统设置中手动开启。',
        [{ text: '好的' }]
      );
      return false;
    }
    
    // Android 平台需要设置通知渠道
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    return true;
  }

  /**
   * 调度一个本地推送通知
   * @param {ReminderData} reminder - 提醒数据对象
   * @returns {Promise<string>} 返回调度后的通知ID
   */
  static async scheduleNotification(reminder: ReminderData): Promise<string> {
    const hasPermission = await this.requestPermissions();
    if (!hasPermission) {
      throw new Error('无法调度通知：缺少权限。');
    }

    const identifier = await Notifications.scheduleNotificationAsync({
      content: {
        title: "心桥提醒",
        body: reminder.content,
        data: { 
          type: 'reminder',
          reminderId: reminder.id,
        },
      },
      trigger: {
        date: reminder.reminderTime,
      },
    });

    return identifier;
  }

  /**
   * 根据ID取消一个已调度的通知
   * @param {string} notificationId - 通知的ID
   * @returns {Promise<void>}
   */
  static async cancelNotification(notificationId: string): Promise<void> {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  }

  /**
   * 添加一个监听器来处理用户与通知的交互
   * @param {(response: Notifications.NotificationResponse) => void} handler - 处理函数
   * @returns {Notifications.Subscription} 返回一个订阅对象，用于后续移除监听
   */
  static addResponseListener(handler: (response: Notifications.NotificationResponse) => void): Notifications.Subscription {
    const responseListener = Notifications.addNotificationResponseReceivedListener(handler);
    return responseListener;
  }
}

export default NotificationService;
```

### User Flow to Implement:
从 `@docs/prd/ux-design.md` 中的对话驱动原则：
1. **自然语言设置**: 用户在正常对话中提及提醒需求
2. **智能识别**: AI识别并解析提醒意图
3. **个性化确认**: AI用角色化语言确认提醒详情
4. **一键确认**: 用户确认后AI设置提醒并给出反馈
5. **按时提醒**: 推送通知准时触发，带有AI角色特色
6. **对话跟进**: 提醒后AI在对话中关心执行情况
- **深度链接**: 通知点击后的应用内导航和上下文恢复

## Testing

Dev Note: Story Requires the following tests:

- [x] Jest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Jest with React Native Testing Library Integration Test: location: `src/components/features/reminders/__tests__/`
- [x] Detox E2E: location: `e2e/reminders/reminder-flow.e2e.ts`

### 单元测试示例

#### 1. `reminderStore` 状态管理测试
```typescript
// src/store/__tests__/reminderStore.test.ts
import { useReminderStore } from '../reminderStore';
import { act } from '@testing-library/react-native';

const mockReminder = { 
  id: '1', 
  content: '吃药', 
  reminderTime: new Date(), 
  status: 'pending' 
};

describe('useReminderStore', () => {
  beforeEach(() => {
    // 每个测试前重置状态
    act(() => {
      useReminderStore.setState({ reminders: [], pendingReminder: null });
    });
  });

  it('should add a new reminder', () => {
    act(() => {
      useReminderStore.getState().addReminder(mockReminder);
    });

    const { reminders } = useReminderStore.getState();
    expect(reminders).toHaveLength(1);
    expect(reminders[0]).toEqual(mockReminder);
  });

  it('should update an existing reminder', () => {
    // 先添加一个
    act(() => {
      useReminderStore.getState().addReminder(mockReminder);
    });
    
    // 再更新它
    act(() => {
      useReminderStore.getState().updateReminder('1', { status: 'completed' });
    });

    const { reminders } = useReminderStore.getState();
    expect(reminders[0].status).toBe('completed');
  });
});
```

#### 2. `NotificationService` 模拟测试
```typescript
// src/services/__tests__/NotificationService.test.ts
import NotificationService from '../NotificationService';
import * as Notifications from 'expo-notifications';

// 完整模拟 expo-notifications 库
jest.mock('expo-notifications', () => ({
  setNotificationHandler: jest.fn(),
  getPermissionsAsync: jest.fn(),
  requestPermissionsAsync: jest.fn(),
  setNotificationChannelAsync: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
  cancelScheduledNotificationAsync: jest.fn(),
}));

const mockReminder = { 
  id: '1', 
  content: '该吃药了', 
  reminderTime: new Date(Date.now() + 10000) 
};

describe('NotificationService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should schedule a notification when permission is granted', async () => {
    // 模拟权限已授予
    (Notifications.getPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
    (Notifications.scheduleNotificationAsync as jest.Mock).mockResolvedValue('notification-id-123');
    
    const notificationId = await NotificationService.scheduleNotification(mockReminder);

    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalledTimes(1);
    expect(Notifications.scheduleNotificationAsync).toHaveBeenCalledWith({
      content: {
        title: "心桥提醒",
        body: mockReminder.content,
        data: { type: 'reminder', reminderId: mockReminder.id },
      },
      trigger: {
        date: mockReminder.reminderTime,
      },
    });
    expect(notificationId).toBe('notification-id-123');
  });

  it('should not schedule a notification when permission is denied', async () => {
    // 模拟权限被拒绝
    (Notifications.getPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'denied' });
    (Notifications.requestPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'denied' });

    // 断言它会抛出错误
    await expect(NotificationService.scheduleNotification(mockReminder)).rejects.toThrow('无法调度通知：缺少权限。');
    expect(Notifications.scheduleNotificationAsync).not.toHaveBeenCalled();
  });
});
```

### 手动测试步骤
- 测试不同时间表达方式的提醒设置（"明天"、"一小时后"、"每天早上8点"）
- 验证推送通知在应用关闭时的正常触发
- 测试提醒的修改和取消功能
- 确认通知权限被拒绝时的降级体验
- 测试与系统日历的集成（如果实现）

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.4（实时语音会话）已完成
- [ ] 故事1.6-B（后端提醒API）已完成并可正常调用
- [ ] 基础对话界面功能正常运行
- [ ] 推送通知相关权限已在项目中配置

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 端到端的提醒设置和触发流程正常运行
- [ ] 推送通知能够准时触发并具有正确内容
- [ ] 提醒管理功能完整可用
- [ ] 单元测试覆盖率≥80%
- [ ] E2E测试覆盖主要提醒场景

## 风险与缓解措施

### 主要风险
1. **时间解析准确性**: 自然语言时间表达的理解偏差
2. **推送通知权限**: 用户拒绝权限影响核心功能
3. **时区处理**: 跨时区用户的时间计算问题
4. **通知可靠性**: 系统通知在不同设备上的一致性

### 缓解措施
1. **多重确认机制、清晰的时间显示、用户验证环节**
2. **优雅的权限引导、权限拒绝的功能降级**
3. **统一的时区处理、用户时区设置、时间显示本地化**
4. **跨平台测试、备用提醒方案、用户反馈机制**

## 数据流设计

### 提醒数据与状态
```typescript
// src/store/reminderStore.ts (或类似文件)
export type ReminderData = {
  id: string;
  originalText: string;
  reminderTime: Date;
  content: string;
  // 此处应与后端API对齐
  notificationId?: string; // 保存调度后的通知ID，用于取消
  status: 'pending' | 'triggered' | 'completed' | 'cancelled';
  aiConfirmation?: string;
};

// 状态管理
export const useReminderStore = create<{
  reminders: ReminderData[];
  pendingReminder: Partial<ReminderData> | null;
  addReminder: (reminder: ReminderData) => void;
  updateReminder: (id: string, updates: Partial<ReminderData>) => void;
  deleteReminder: (id: string) => void;
}>();
```

### 推送通知调度流程
```typescript
// 调用示例 - 在确认提醒后
import NotificationService from '../services/NotificationService';
import { useReminderStore } from '../store/reminderStore';

// ...
const handleConfirmReminder = async (reminder: ReminderData) => {
  try {
    // 1. 调度本地通知
    const notificationId = await NotificationService.scheduleNotification(reminder);
    
    // 2. 将通知ID保存到Store，以便后续管理（如取消）
    useReminderStore.getState().updateReminder(reminder.id, { notificationId: notificationId, status: 'pending' });

    // 3. (可选) 调用后端API保存提醒
    // await api.reminders.create({ ...reminder, notificationId });
    
  } catch (error) {
    console.error("提醒设置失败:", error);
    // 向用户显示错误提示
  }
};
```

## 相关文档引用
- [用户故事原文](../../prd/user-stories.md#故事-16-对话式提醒功能)
- [UX设计规范](../../prd/ux-design.md)
- [前端架构设计](../../architecture/06-frontend-architecture.md)
- [API接口设计](../../architecture/03-api-design.md)
- [后端提醒API](../backend/1.6-B.story.md)
- [移动应用技术栈](../../architecture/mobile-app-tech-stack.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md) 