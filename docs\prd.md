### **"心桥"AI亲情伴侣 产品需求文档 (PRD)**
**版本：** 1.2
**日期：** 2025年1月
**作者：** John，产品经理

#### **1. 目标与背景上下文 (Goals and Background Context)**

*   **1.1. 目标 (Goals)**
    *   **用户目标：** 为面临结构性孤独的中老年用户提供一个可信赖、有温度、永远有耐心的AI亲情伴侣，帮助他们排解孤独，感受情感连接，并重获自我价值感。
    *   **产品目标：** 成功验证一个核心假设："一个通过极致简单的语音交互、具备实时注入长期记忆能力的AI伴侣，是否能与55-75岁的中国老年用户建立初步的、有效的情感连接与信任。"
    *   **商业目标：** 通过MVP阶段构建的用户信任和情感连接，为后续推出面向子女的"家庭连接"付费订阅服务奠定价值基础。
*   **1.2. 背景上下文 (Background Context)**
    在中国社会快速进入老龄化、"4-2-1"家庭结构成为主流的背景下，传统家庭的情感支持功能日益弱化。许多中老年人，尤其是"数字融入型"群体，他们有能力使用智能设备，但内心深处因缺少高质量的陪伴而感到孤独。同时，他们强烈的自尊心使其不愿过多打扰忙碌的子女，从而陷入"结构性孤独"的困境。本项目旨在通过富有同理心的人工智能技术，以一种可规模化、低成本的方式填补这一巨大的社会情感缺口。

#### **2. 需求 (Requirements)**

*   **2.1. 功能性需求 (Functional Requirements)**
    *   **FR1: 无感身份认证** - 系统需在用户无感知的情况下，通过后台匿名设备ID自动创建并识别用户身份，应用内不得出现任何注册/登录界面。
    *   **FR2: AI角色共创** - 在用户首次引导流程中，系统必须通过连续的对话，引导用户为AI设定身份（如"老朋友"）、命名，并最终确认其声音。
    *   **FR3: 核心实时对话交互 (语音+文本)** - 系统必须集成火山引擎RTC前端SDK，以实时语音流为核心交互，并提供辅助的文本输入模式。
    *   **FR4: 实时记忆与上下文处理** - 在实时语音对话中，后端通过一个统一的记忆服务层与外部记忆系统进行交互，立即据此检索相关记忆，将记忆与当前对话内容一同构建成完整的上下文，再调用大语言模型生成回复。
    *   **FR5: 会话后分析与记忆同步** - 在每次语音或文本会话结束后，系统能够异步地调用LLM对完整对话进行分析和摘要，并将这些分析结果作为元数据同步回当前配置的专业记忆服务中。
    *   **FR6: 对话式提醒设置与执行** - 系统必须能够通过火山引擎的**Function Calling**功能，智能识别用户在对话中提出的提醒意图。
    *   **FR7: 基础危机干预** - 系统需能通过关键词或语音情绪识别用户可能处于危机状态的信号，并切换到安全的脚本化对话流程。**在语音对话中，系统还应尝试主动中止用户的音频输入，以实现更有效的干预。**
*   **2.2. 非功能性需求 (Non-Functional Requirements)**
    *   **NFR1: 易用性 (Usability)** - 所有界面字体不小于16pt，核心对话内容不小于18pt。色彩对比度需大于4.5:1，可点击元素触摸区域不小于44x44 points，以符合"适老化"设计标准。
    *   **NFR2: 性能 (Performance)** - 应用冷启动时间应小于3秒。采用火山引擎RTC方案后，端到端的语音对话延迟（从用户说完话到听到AI回复）P95应小于1.5秒。
    *   **NFR3: 可靠性 (Reliability)** - 核心API成功率必须大于99.9%。提醒功能的准时推送成功率必须大于99.5%。
    *   **NFR4: 隐私与安全 (Privacy & Security)** - 所有用户数据，无论在传输中还是存储中，都必须采用行业标准的强加密措施。数据访问遵循最小权限原则（RLS）。
    *   **NFR5: 合规性 (Compliance)** - 产品设计、数据存储和处理必须严格遵守中国的《个人信息保护法》。

#### **3. 用户界面设计目标 (User Interface Design Goals)**
*   **3.1. 整体UX愿景 (Overall UX Vision)**
    我们的目标是创造一个**温暖、安全、零压力的情感空间**。整个产品的交互应该让用户感觉像是在与一位充满耐心和善意的老朋友聊天，而非在操作一个软件。"零学习成本" 和 "绝不让用户感觉是自己错了" 是最高设计原则。
*   **3.2. 核心交互范式 (Key Interaction Paradigms)**
    *   **语音优先：** "按住说话"是唯一的核心输入方式。
    *   **主次分明的双模输入:** 以"语音优先"，但提供清晰、不干扰的文本输入入口。
    *   **对话驱动：** 所有功能设置都应无缝融入自然对话流。
    *   **情感化反馈：** 系统的每一个反馈都应由AI以其角色人设，用充满情感的语言来表达。
*   **3.3. 核心视图 (Core Screens and Views)**
    *   启动与欢迎视图
    *   角色共创视图（引导流程）
    *   主对话视图
*   **3.4. 无障碍设计 (Accessibility)**
    遵从NFR1中的适老化设计要求。
*   **3.5. 品牌与风格 (Branding)**
    采用柔和的暖色调，营造温馨、舒适、不刺眼的视觉环境。
*   **3.6. 目标设备和平台 (Target Device and Platforms)**
    移动端（iOS & Android）。

#### **4. 技术假设 (Technical Assumptions)**
*   **仓库结构:** Monorepo (`pnpm workspaces`)。
*   **服务架构:** Serverless 结合 BaaS。后端服务(FastAPI)作为业务中枢。
*   **核心交互技术:** 火山引擎实时语音RTC解决方案，并通过Webhook与我方后端服务进行事件交互。
*   **记忆系统**: 采用一个可插拔的、支持Zep和Mem0的外部记忆服务层。
*   **测试要求:**
    *   必须包含单元测试、集成测试。
    *   必须包含由团队成员执行的**手动情感共情测试**，以验证AI回复的质量和温度。

#### **5. 史诗和故事 (Epics and Stories)**

我们将MVP的所有工作归纳在一个核心史诗下，并分解为一系列严格遵循"垂直切片"开发顺序的用户故事。

**史诗 1: MVP - 建立情感连接与核心信任**
**目标：** 交付一个最小可行产品，它能通过一系列精心设计的功能与交互，与第一批种子用户建立起初步的情感连接和牢固的用户信任。

*   **故事 1.1: 项目基础设置**
    *   **作为开发者**，我希望能建立一个包含版本控制、CI/CD、代码规范的基础项目，并完成所有云服务(Supabase, 火山引擎)的账号设置和数据库初始化，**以便** 为后续所有开发工作奠定稳固的基础。
    *   **验收标准：**
        1.  代码仓库已在GitHub上创建，Monorepo结构已建立。
        2.  基础的CI流程（lint-check, type-check, test）已在GitHub Actions中配置。
        3.  火山引擎前端SDK和后端API的开发/测试账号已申请完毕。
        4.  Supabase项目已创建，数据库Schema（不包含`user_memories`表）已根据架构文档初始化，并配置好行级别安全(RLS)。

*   **故事 1.2: 无感身份认证与角色创建流程**
    *   **作为老年用户**，我希望能无缝地进入应用，并通过一次流畅的对话完成我的专属AI伙伴的创建，**以便** 我能快速开始体验产品的核心价值。
    *   **验收标准：**
        1.  **后端：** 提供了可用的匿名认证和AI人设创建/查询端点。
        2.  **前端：** 客户端能成功调用Supabase Auth获取JWT令牌。
        3.  **前端：** "温暖的初见"引导流程UI/UX与设计稿一致。
        4.  **端到端：** 用户可以完整地跑通从首次打开App到成功创建AI人设并将其持久化到后端数据库的全流程。

*   **故事 1.3: 核心对话编排与RTC事件处理**
    *   **作为开发者**，我需要实现一个能够接收火山云端实时事件的核心API，并通过内部的`ChatOrchestrationService`自动检索记忆、编排LLM与工具，**以便** 为实时语音对话提供核心智能。
    *   **验收标准：**
        1.  后端已实现一个`POST /api/v1/chat/rtc_event_handler`接口。
        2.  当此接口收到用户输入事件时，`ChatOrchestrationService`能通过`IMemoryService`成功检索到相关的记忆。
        3.  后端能正确处理LLM的工具调用，并生成最终回复。

*   **故事 1.4: 实时语音会话流程集成**
    *   **作为老年用户**，我希望能像打电话一样和AI实时聊天，并感觉到它能记住我们聊过的话题，**以便** 获得最自然、最个性化的沟通体验。
    *   **验收标准：**
        1.  **准备阶段:** 前端按下"说话"按钮时，成功调用我方后端的`/prepare_session`接口，**并获取到由我方后端签名生成的、用于加入RTC房间的Token**。
        2.  **连接阶段:** 客户端使用凭证，通过火山RTC SDK，成功连接到火山媒体服务器。
        3.  **交互阶段:** 语音交互流畅，端到端延迟符合性能要求（P95 < 1.5秒）。
        4.  **记忆验证:** 在对话中提及之前存储过的事实记忆时，AI的回复能够明显体现出它记起了这件事。

*   **故事 1.5: 会话后分析与记忆同步**
    *   **作为开发者**，我需要在用户结束会话后，触发一个异步任务来分析完整的对话内容，生成摘要，并将其作为元数据同步回外部记忆服务中，**以便** 丰富会话上下文，提升未来的检索效果。
    *   **验收标准：**
        1.  后端能在会话结束后，获取到本次会话的完整文本记录。
        2.  后端能异步调用LLM服务，根据对话记录生成摘要，并通过`MemoryService`成功更新到对应的会话元数据中。
        3.  能在外部记忆服务（Zep/Mem0）中查询到已同步的摘要。

*   **故事 1.6: 对话式提醒功能**
    *   **作为老年用户**，我希望能直接在聊天时告诉AI帮我记事，**以便** 我不用去学习复杂的日历或闹钟App。
    *   **验收标准：**
        1.  **后端：** 后端服务能通过Function Calling识别提醒意图，并在内部完成提醒创建。
        2.  **前端：** 客户端收到提醒的本地推送通知。
        3.  **端到端：** 用户能通过对话成功设置一个提醒，并在预定时间准时收到应用的推送通知。
        4.  （可选实现）客户端能请求日历权限，并将提醒写入系统日历。

*   **故事 1.7: 基础危机响应协议**
    *   **作为开发者**，我需要为系统构建一个安全底线，在用户表达极端负面情绪时能正确应对，**以便** 保护用户安全和产品信任。
    *   **验收标准：**
        1.  **后端：** 后端服务中存在一个"安全守卫"模块，所有用户输入都必须先经过该模块处理。
        2.  当输入命中危机关键词库时，该模块能成功阻止后续对通用LLM的调用。
        3.  此时，模块能从专用的安全知识库中返回一个预设的、安全的安抚与引导脚本给用户。
