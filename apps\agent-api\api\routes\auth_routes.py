from fastapi import APIRouter, Depends, HTTPException, status
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel

from api.services.auth_service import AuthService
from api.services.user_service import UserService
from api.dependencies.auth import get_current_user
from api.settings import logger

router = APIRouter(
    prefix="/auth",
    tags=["Authentication and User Onboarding"],
)

# 实例化服务
auth_service = AuthService()
user_service = UserService()

# 认证相关的Pydantic模型
class DeviceInfo(BaseModel):
    device_id: str
    platform: str
    app_version: str

class AnonymousLoginRequest(BaseModel):
    device_info: DeviceInfo

class AnonymousLoginResponse(BaseModel):
    user: Dict[str, Any]
    access_token: str
    refresh_token: str

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class RefreshTokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    expires_in: int

class CharacterInfo(BaseModel):
    name: str
    role: str
    voice_id: str

class OnboardingRequest(BaseModel):
    # 移除userId字段，现在从JWT Token中获取
    nickname: str
    core_needs: List[str]
    interests: Optional[List[str]] = None
    communication_style_preference: Optional[str] = None
    allow_chat_analysis: Optional[bool] = True
    character: Optional[CharacterInfo] = None

class OnboardingResponse(BaseModel):
    success: bool
    message: str
    user_profile: Dict[str, Any]

@router.post(
    "/anonymous-login",
    response_model=AnonymousLoginResponse,
    summary="Anonymous User Login",
    description="Create or login anonymous user based on device fingerprint",
    status_code=status.HTTP_200_OK,
)
async def anonymous_login(request_data: AnonymousLoginRequest):
    """匿名用户登录 - AC1: 无感身份认证服务"""
    logger.info(f"Anonymous login attempt for device: {request_data.device_info.device_id}")

    try:
        # 转换为DeviceInfo数据类
        from api.services.auth_service import DeviceInfo
        device_info = DeviceInfo(
            device_id=request_data.device_info.device_id,
            platform=request_data.device_info.platform,
            app_version=request_data.device_info.app_version
        )

        # 创建匿名用户
        auth_result = await auth_service.create_anonymous_user(device_info)

        return AnonymousLoginResponse(
            user=auth_result.user,
            access_token=auth_result.access_token,
            refresh_token=auth_result.refresh_token
        )

    except Exception as e:
        logger.exception(f"Error during anonymous login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post(
    "/refresh-token",
    response_model=RefreshTokenResponse,
    summary="Refresh Access Token",
    description="Refresh access token using refresh token",
    status_code=status.HTTP_200_OK,
)
async def refresh_token(request_data: RefreshTokenRequest):
    """刷新访问Token - AC1: JWT Token管理"""
    logger.info("Token refresh attempt")

    try:
        # 刷新令牌
        auth_result = await auth_service.refresh_token(request_data.refresh_token)

        return RefreshTokenResponse(
            access_token=auth_result.access_token,
            refresh_token=auth_result.refresh_token,
            expires_in=auth_result.expires_in
        )

    except Exception as e:
        logger.exception(f"Error during token refresh: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@router.post(
    "/finalize_onboarding",
    response_model=OnboardingResponse,
    summary="Finalize User Onboarding Process",
    description="Receives user's collected onboarding data, saves it, and marks onboarding as complete.",
    status_code=status.HTTP_200_OK,
)
async def finalize_onboarding(
    request_data: OnboardingRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """完成引导流程 - AC4: 引导流程数据API"""
    # 修复：从JWT Token获取用户ID
    current_user_id = current_user.get("sub")
    if not current_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的用户令牌"
        )

    logger.info(f"Finalize onboarding attempt for user: {current_user_id}")

    try:
        # 更新用户画像信息
        update_data = {
            "nickname": request_data.nickname,
            "onboarding_completed": True
        }

        # 添加可选字段
        if request_data.interests:
            update_data["interests"] = request_data.interests
        if request_data.communication_style_preference:
            update_data["communication_style_preference"] = request_data.communication_style_preference
        if request_data.allow_chat_analysis is not None:
            update_data["allow_chat_analysis"] = request_data.allow_chat_analysis

        # 更新用户画像 - 使用从JWT Token获取的用户ID
        updated_profile = await user_service.update_user_profile(
            user_id=current_user_id,  # 使用从Token获取的用户ID
            update_data=update_data
        )

        if not updated_profile:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新用户画像失败"
            )

        # 如果提供了角色信息，进行角色绑定
        if request_data.character:
            from api.services.character_service import CharacterService
            character_service = CharacterService()

            # 根据角色名称查找角色ID
            characters_response = await character_service.get_characters_list(limit=100)
            character_id = None
            for char in characters_response.data:
                if char.name == request_data.character.name:
                    character_id = char.id
                    break

            if character_id:
                await character_service.bind_user_character(
                    user_id=current_user_id,  # 使用从Token获取的用户ID
                    character_id=character_id
                )

        return OnboardingResponse(
            success=True,
            message="引导流程完成成功",
            user_profile=updated_profile
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error during finalize onboarding: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="引导流程完成失败"
        )
