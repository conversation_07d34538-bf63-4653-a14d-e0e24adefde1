{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 心桥项目 API 接口全面测试\n", "\n", "这个 Jupyter notebook 提供了一个交互式的环境来测试心桥项目的所有 API 接口。\n", "\n", "## 📋 测试覆盖的接口模块：\n", "- ✅ **基础设施服务** - 健康检查\n", "- ✅ **认证与用户管理** - 匿名登录、Token刷新、用户画像、用户设置  \n", "- ✅ **AI角色管理** - 角色列表、角色绑定\n", "- ✅ **文本对话服务** - SSE流式聊天、连接状态查询\n", "- ✅ **RTC语音通话服务** - 会话准备、状态管理、事件处理\n", "- ✅ **会话管理** - 会话结束、分析状态查询\n", "- ✅ **智能提醒服务** - CRUD操作、高级查询\n", "- ✅ **错误处理与边界测试** - 认证失败、资源不存在等场景\n", "\n", "## 准备工作\n", "首先安装所需的库并导入必要的模块。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装所需的库\n", "%pip install requests python-dateutil\n", "\n", "import requests\n", "import json\n", "from datetime import datetime, timedelta, timezone\n", "from pprint import pprint\n", "import time\n", "import os\n", "\n", "# 配置基础 URL\n", "BASE_URL = \"http://localhost:8003/api/v1\"\n", "\n", "# 全局变量存储认证信息\n", "ACCESS_TOKEN = None\n", "REFRESH_TOKEN = None\n", "USER_ID = None\n", "SESSION_ID = None\n", "CHARACTER_ID = None\n", "RTC_SESSION_ID = None\n", "TASK_ID = None\n", "REMINDER_ID = None\n", "\n", "# 辅助函数\n", "def make_request(method, endpoint, headers=None, data=None, params=None, stream=False):\n", "    \"\"\"统一的请求函数\"\"\"\n", "    url = f\"{BASE_URL}{endpoint}\"\n", "    \n", "    default_headers = {\"Content-Type\": \"application/json\"}\n", "    if headers:\n", "        default_headers.update(headers)\n", "    \n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"请求: {method} {url}\")\n", "    if data:\n", "        print(f\"数据: {json.dumps(data, indent=2, ensure_ascii=False)}\")\n", "    \n", "    try:\n", "        response = requests.request(\n", "            method=method,\n", "            url=url,\n", "            headers=default_headers,\n", "            json=data,\n", "            params=params,\n", "            stream=stream,\n", "            timeout=30\n", "        )\n", "        \n", "        print(f\"状态码: {response.status_code}\")\n", "        \n", "        if stream:\n", "            print(\"流式响应:\")\n", "            for line in response.iter_lines():\n", "                if line:\n", "                    print(line.decode('utf-8'))\n", "        else:\n", "            if response.headers.get('content-type', '').startswith('application/json'):\n", "                response_data = response.json()\n", "                print(\"响应数据:\")\n", "                pprint(response_data)\n", "                return response_data\n", "            else:\n", "                print(f\"响应文本: {response.text}\")\n", "        \n", "        return response\n", "        \n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"请求错误: {e}\")\n", "        return None\n", "\n", "def auth_headers():\n", "    \"\"\"返回认证头\"\"\"\n", "    if ACCESS_TOKEN:\n", "        return {\"Authorization\": f\"Bearer {ACCESS_TOKEN}\"}\n", "    return {}\n", "\n", "print(\"初始化完成！\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第一步：匿名登录获取令牌\n", "#首先进行匿名登录以获取访问令牌。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. 匿名登录\n", "login_data = {\n", "    \"device_info\": {\n", "        \"device_id\": \"jupyter-test-device-001\",\n", "        \"platform\": \"windows\",\n", "        \"app_version\": \"1.0.1\"\n", "    }\n", "}\n", "\n", "response = make_request(\"POST\", \"/auth/anonymous-login\", data=login_data)\n", "\n", "if response and \"access_token\" in response:\n", "    ACCESS_TOKEN = response[\"access_token\"]\n", "    REFRESH_TOKEN = response[\"refresh_token\"]\n", "    USER_ID = response[\"user\"][\"id\"]\n", "    \n", "    print(f\"\\n✅ 登录成功！\")\n", "    print(f\"用户ID: {USER_ID}\")\n", "    print(f\"令牌已保存到全局变量中\")\n", "else:\n", "    print(\"❌ 登录失败\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第二步：健康检查\n", "\n", "测试 API 服务是否正常运行。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 健康检查\n", "response = make_request(\"GET\", \"/health\")\n", "\n", "if response and response.get(\"status\") == \"healthy\":\n", "    print(\"\\n✅ 服务运行正常\")\n", "else:\n", "    print(\"\\n❌ 服务异常\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第三步：用户相关接口测试\n", "\n", "测试用户画像和设置相关的接口。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 获取用户画像\n", "response = make_request(\"GET\", \"/user/profile\", headers=auth_headers())\n", "\n", "if response:\n", "    print(\"\\n✅ 用户画像获取成功\")\n", "else:\n", "    print(\"\\n❌ 用户画像获取失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 更新用户画像\n", "profile_data = {\n", "    \"nickname\": \"<PERSON><PERSON><PERSON>\",\n", "    \"interests\": [\"automation\", \"testing\", \"python\"]\n", "}\n", "\n", "response = make_request(\"PUT\", \"/user/profile\", headers=auth_headers(), data=profile_data)\n", "\n", "if response:\n", "    print(\"\\n✅ 用户画像更新成功\")\n", "else:\n", "    print(\"\\n❌ 用户画像更新失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. 获取用户设置\n", "response = make_request(\"GET\", \"/user/settings\", headers=auth_headers())\n", "\n", "if response:\n", "    print(\"\\n✅ 用户设置获取成功\")\n", "else:\n", "    print(\"\\n❌ 用户设置获取失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. 更新用户设置\n", "settings_data = {\n", "    \"theme\": \"dark\",\n", "    \"font_size\": \"large\"\n", "}\n", "\n", "response = make_request(\"PUT\", \"/user/settings\", headers=auth_headers(), data=settings_data)\n", "\n", "if response:\n", "    print(\"\\n✅ 用户设置更新成功\")\n", "else:\n", "    print(\"\\n❌ 用户设置更新失败\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第四步：角色管理接口测试\n", "\n", "测试 AI 角色相关的接口。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7. 获取角色列表\n", "response = make_request(\"GET\", \"/characters\", headers=auth_headers())\n", "\n", "if response and \"data\" in response and len(response[\"data\"]) > 0:\n", "    CHARACTER_ID = response[\"data\"][0][\"id\"]\n", "    print(f\"\\n✅ 角色列表获取成功，选择角色ID: {CHARACTER_ID}\")\n", "else:\n", "    print(\"\\n❌ 角色列表获取失败\")\n", "    CHARACTER_ID = \"compassionate_listener\"  # 默认角色ID\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 8. 获取角色详情\n", "if CHARACTER_ID:\n", "    response = make_request(\"GET\", f\"/characters/{CHARACTER_ID}\", headers=auth_headers())\n", "    \n", "    if response:\n", "        print(\"\\n✅ 角色详情获取成功\")\n", "    else:\n", "        print(\"\\n❌ 角色详情获取失败\")\n", "else:\n", "    print(\"\\n⚠️ 未选择角色ID，跳过角色详情测试\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 9. 绑定角色\n", "if CHARACTER_ID:\n", "    response = make_request(\"POST\", f\"/user/characters/{CHARACTER_ID}/bind\", headers=auth_headers())\n", "    \n", "    if response:\n", "        print(\"\\n✅ 角色绑定成功\")\n", "    else:\n", "        print(\"\\n❌ 角色绑定失败\")\n", "else:\n", "    print(\"\\n⚠️ 未选择角色ID，跳过角色绑定测试\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第五步：会话管理接口测试\n", "\n", "测试聊天会话相关的接口。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 10. 创建新会话\n", "session_data = {\n", "    \"userId\": USER_ID,\n", "    \"topic\": \"Jupyter Test Session\"\n", "}\n", "\n", "response = make_request(\"POST\", \"/chat/sessions\", data=session_data)\n", "\n", "if response and \"id\" in response:\n", "    SESSION_ID = response[\"id\"]\n", "    print(f\"\\n✅ 会话创建成功，会话ID: {SESSION_ID}\")\n", "else:\n", "    print(\"\\n❌ 会话创建失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 11. 获取会话列表\n", "response = make_request(\"GET\", \"/chat/sessions\", headers=auth_headers())\n", "\n", "if response:\n", "    print(\"\\n✅ 会话列表获取成功\")\n", "else:\n", "    print(\"\\n❌ 会话列表获取失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 12. 文本聊天 (注意：这是流式响应)\n", "if SESSION_ID:\n", "    chat_data = {\n", "        \"message\": \"Hello from <PERSON><PERSON><PERSON> <PERSON>!\",\n", "        \"sessionId\": SESSION_ID\n", "    }\n", "    \n", "    print(\"\\n发送文本消息并接收流式响应...\")\n", "    response = make_request(\"POST\", \"/chat/text_message\", headers=auth_headers(), data=chat_data, stream=True)\n", "    \n", "    if response:\n", "        print(\"\\n✅ 文本聊天测试完成\")\n", "    else:\n", "        print(\"\\n❌ 文本聊天失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建会话，跳过文本聊天测试\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 13.5. 查询聊天连接状态\n", "response = make_request(\"GET\", \"/chat/connections/status\", headers=auth_headers())\n", "\n", "if response:\n", "    print(\"\\n✅ 聊天连接状态查询成功\")\n", "    if \"data\" in response:\n", "        print(f\"活跃连接数: {response['data'].get('active_connections', 0)}\")\n", "        print(f\"用户连接数: {response['data'].get('user_connections', 0)}\")\n", "else:\n", "    print(\"\\n❌ 聊天连接状态查询失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 13. 获取会话聊天记录\n", "if SESSION_ID:\n", "    response = make_request(\"GET\", f\"/chat/sessions/{SESSION_ID}/messages\", headers=auth_headers())\n", "    \n", "    if response:\n", "        print(\"\\n✅ 聊天记录获取成功\")\n", "    else:\n", "        print(\"\\n❌ 聊天记录获取失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建会话，跳过聊天记录测试\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第六步：RTC 语音会话接口测试\n", "\n", "测试实时语音通话相关的接口。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 14. 准备 RTC 会话\n", "RTC_SESSION_ID = \"rtc-session-jupyter-01\"\n", "\n", "rtc_data = {\n", "    \"userId\": USER_ID,\n", "    \"sessionId\": RTC_SESSION_ID,\n", "    \"characterId\": CHARACTER_ID or \"compassionate_listener\"\n", "}\n", "\n", "response = make_request(\"POST\", \"/rtc/prepare_session\", data=rtc_data)\n", "\n", "if response and \"taskId\" in response:\n", "    TASK_ID = response[\"taskId\"]\n", "    print(f\"\\n✅ RTC 会话准备成功，任务ID: {TASK_ID}\")\n", "else:\n", "    print(\"\\n❌ RTC 会话准备失败\")\n", "    TASK_ID = \"test-task-id\"  # 默认任务ID\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 15. 获取 RTC 会话状态\n", "if RTC_SESSION_ID:\n", "    params = {\"userId\": USER_ID}\n", "    response = make_request(\"GET\", f\"/rtc/sessions/{RTC_SESSION_ID}/status\", params=params)\n", "    \n", "    if response:\n", "        print(\"\\n✅ RTC 会话状态获取成功\")\n", "    else:\n", "        print(\"\\n❌ RTC 会话状态获取失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建 RTC 会话，跳过状态查询\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 16. 获取 RTC 会话配置\n", "if RTC_SESSION_ID:\n", "    params = {\"userId\": USER_ID}\n", "    response = make_request(\"GET\", f\"/rtc/sessions/{RTC_SESSION_ID}/config\", params=params)\n", "    \n", "    if response:\n", "        print(\"\\n✅ RTC 会话配置获取成功\")\n", "    else:\n", "        print(\"\\n❌ RTC 会话配置获取失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建 RTC 会话，跳过配置查询\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 17. 模拟 RTC Webhook 事件\n", "if RTC_SESSION_ID:\n", "    webhook_data = {\n", "        \"event_type\": \"asr_result\",\n", "        \"payload\": {\n", "            \"text\": \"This is a voice test from <PERSON><PERSON><PERSON> notebook.\"\n", "        },\n", "        \"custom\": json.dumps({\n", "            \"sessionId\": RTC_SESSION_ID,\n", "            \"userId\": USER_ID\n", "        })\n", "    }\n", "    \n", "    response = make_request(\"POST\", \"/chat/rtc_event_handler\", data=webhook_data)\n", "    \n", "    if response:\n", "        print(\"\\n✅ RTC Webhook 事件处理成功\")\n", "    else:\n", "        print(\"\\n❌ RTC Webhook 事件处理失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建 RTC 会话，跳过 Webhook 测试\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第七步：提醒功能接口测试\n", "\n", "测试提醒相关的接口。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 18. 创建提醒\n", "reminder_time = (datetime.now(timezone.utc) + timedelta(days=1)).isoformat()\n", "\n", "reminder_data = {\n", "    \"content\": \"My <PERSON><PERSON><PERSON> Reminder\",\n", "    \"reminder_time\": reminder_time\n", "}\n", "\n", "response = make_request(\"POST\", \"/reminders\", headers=auth_headers(), data=reminder_data)\n", "\n", "if response and \"id\" in response:\n", "    REMINDER_ID = response[\"id\"]\n", "    print(f\"\\n✅ 提醒创建成功，提醒ID: {REMINDER_ID}\")\n", "else:\n", "    print(\"\\n❌ 提醒创建失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 19. 获取提醒列表\n", "response = make_request(\"GET\", \"/reminders\", headers=auth_headers())\n", "\n", "if response:\n", "    print(\"\\n✅ 提醒列表获取成功\")\n", "else:\n", "    print(\"\\n❌ 提醒列表获取失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 20. 获取提醒详情\n", "if REMINDER_ID:\n", "    response = make_request(\"GET\", f\"/reminders/{REMINDER_ID}\", headers=auth_headers())\n", "    \n", "    if response:\n", "        print(\"\\n✅ 提醒详情获取成功\")\n", "    else:\n", "        print(\"\\n❌ 提醒详情获取失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建提醒，跳过提醒详情测试\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 21. 更新提醒\n", "if REMINDER_ID:\n", "    update_data = {\n", "        \"content\": \"Updated <PERSON><PERSON><PERSON> Reminder\",\n", "        \"status\": \"completed\"\n", "    }\n", "    \n", "    response = make_request(\"PUT\", f\"/reminders/{REMINDER_ID}\", headers=auth_headers(), data=update_data)\n", "    \n", "    if response:\n", "        print(\"\\n✅ 提醒更新成功\")\n", "    else:\n", "        print(\"\\n❌ 提醒更新失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建提醒，跳过提醒更新测试\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第八步：认证相关接口测试\n", "\n", "测试令牌刷新等认证功能。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 22. 刷新令牌\n", "if REFRESH_TOKEN:\n", "    refresh_data = {\n", "        \"refresh_token\": REFRESH_TOKEN\n", "    }\n", "    \n", "    response = make_request(\"POST\", \"/auth/refresh-token\", data=refresh_data)\n", "    \n", "    if response and \"access_token\" in response:\n", "        old_token = ACCESS_TOKEN\n", "        ACCESS_TOKEN = response[\"access_token\"]\n", "        REFRESH_TOKEN = response[\"refresh_token\"]\n", "        \n", "        print(f\"\\n✅ 令牌刷新成功\")\n", "        print(f\"旧令牌: {old_token[:20]}...\")\n", "        print(f\"新令牌: {ACCESS_TOKEN[:20]}...\")\n", "    else:\n", "        print(\"\\n❌ 令牌刷新失败\")\n", "else:\n", "    print(\"\\n⚠️ 未获取 refresh_token，跳过令牌刷新测试\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 23.5. 查询会话分析状态\n", "if SESSION_ID:\n", "    response = make_request(\"GET\", f\"/sessions/{SESSION_ID}/analysis_status\", headers=auth_headers())\n", "    \n", "    if response:\n", "        print(\"\\n✅ 会话分析状态查询成功\")\n", "        status = response.get(\"status\", \"unknown\")\n", "        print(f\"分析状态: {status}\")\n", "        if status == \"completed\" and \"summary\" in response:\n", "            print(f\"会话摘要: {response['summary'][:100]}...\")\n", "    else:\n", "        print(\"\\n❌ 会话分析状态查询失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建会话，跳过分析状态查询\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第九步：会话结束和清理\n", "\n", "结束会话并清理资源。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 23. 结束文本会话\n", "if SESSION_ID:\n", "    response = make_request(\"PUT\", f\"/chat/sessions/{SESSION_ID}/end\", headers=auth_headers())\n", "    \n", "    if response:\n", "        print(\"\\n✅ 文本会话结束成功\")\n", "    else:\n", "        print(\"\\n❌ 文本会话结束失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建文本会话，跳过会话结束\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 24. 结束 RTC 会话\n", "if RTC_SESSION_ID and TASK_ID:\n", "    end_rtc_data = {\n", "        \"userId\": USER_ID,\n", "        \"sessionId\": RTC_SESSION_ID,\n", "        \"taskId\": TASK_ID\n", "    }\n", "    \n", "    response = make_request(\"POST\", \"/rtc/end_session\", data=end_rtc_data)\n", "    \n", "    if response:\n", "        print(\"\\n✅ RTC 会话结束成功\")\n", "    else:\n", "        print(\"\\n❌ RTC 会话结束失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建 RTC 会话，跳过 RTC 会话结束\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 25. 删除提醒（可选）\n", "if REMINDER_ID:\n", "    response = make_request(\"DELETE\", f\"/reminders/{REMINDER_ID}\", headers=auth_headers())\n", "    \n", "    # DELETE 请求通常返回 204 状态码，没有响应体\n", "    if response and (response.status_code == 204 or response.status_code == 200):\n", "        print(\"\\n✅ 提醒删除成功\")\n", "    else:\n", "        print(\"\\n❌ 提醒删除失败\")\n", "else:\n", "    print(\"\\n⚠️ 未创建提醒，跳过提醒删除\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第十步：完成引导流程测试\n", "\n", "测试用户引导流程完成接口。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 26. 完成引导流程\n", "response = make_request(\"POST\", \"/auth/finalize_onboarding\", headers=auth_headers())\n", "\n", "if response:\n", "    print(\"\\n✅ 引导流程完成成功\")\n", "else:\n", "    print(\"\\n❌ 引导流程完成失败\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 第十一步：额外测试功能\n", "\n", "测试一些额外的功能和边界情况。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 27. 测试提醒查询参数\n", "print(\"测试提醒查询参数...\")\n", "\n", "# 测试按状态查询\n", "params = {\"status\": \"pending\"}\n", "response = make_request(\"GET\", \"/reminders\", headers=auth_headers(), params=params)\n", "if response:\n", "    print(\"✅ 按状态查询提醒成功\")\n", "else:\n", "    print(\"❌ 按状态查询提醒失败\")\n", "\n", "# 测试分页查询\n", "params = {\"page\": 1, \"limit\": 10}\n", "response = make_request(\"GET\", \"/reminders\", headers=auth_headers(), params=params)\n", "if response:\n", "    print(\"✅ 分页查询提醒成功\")\n", "else:\n", "    print(\"❌ 分页查询提醒失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 29.5. 测试危机干预功能\n", "print(\"测试危机干预功能...\")\n", "\n", "if SESSION_ID:\n", "    # 测试危机关键词检测\n", "    crisis_message = {\n", "        \"message\": \"我不想活了\",  # 危机关键词\n", "        \"sessionId\": SESSION_ID,\n", "        \"characterId\": CHARACTER_ID or \"compassionate_listener\"\n", "    }\n", "    \n", "    print(\"发送危机关键词消息，测试干预响应...\")\n", "    response = make_request(\"POST\", \"/chat/text_message\", headers=auth_headers(), data=crisis_message, stream=True)\n", "    \n", "    if response:\n", "        print(\"✅ 危机干预测试完成\")\n", "        print(\"⚠️ 检查响应中是否包含心理援助热线信息\")\n", "    else:\n", "        print(\"❌ 危机干预测试失败\")\n", "else:\n", "    print(\"⚠️ 未创建会话，跳过危机干预测试\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 29.7. 测试Function Calling（工具调用）- 自然语言创建提醒\n", "print(\"测试Function Calling功能...\")\n", "\n", "if SESSION_ID:\n", "    # 测试自然语言提醒创建\n", "    natural_reminder_message = {\n", "        \"message\": \"请帮我设置一个明天下午3点的提醒，内容是开会\",\n", "        \"sessionId\": SESSION_ID,\n", "        \"characterId\": CHARACTER_ID or \"compassionate_listener\"\n", "    }\n", "    \n", "    print(\"发送自然语言提醒请求，测试Function Calling...\")\n", "    response = make_request(\"POST\", \"/chat/text_message\", headers=auth_headers(), data=natural_reminder_message, stream=True)\n", "    \n", "    if response:\n", "        print(\"✅ Function Calling测试完成\")\n", "        print(\"⚠️ 检查响应中是否包含提醒创建成功的信息\")\n", "        # 等待一下再查询提醒列表，验证是否真的创建了\n", "        time.sleep(2)\n", "        reminders_response = make_request(\"GET\", \"/reminders\", headers=auth_headers())\n", "        if reminders_response and \"data\" in reminders_response:\n", "            print(f\"当前提醒总数: {len(reminders_response['data'])}\")\n", "    else:\n", "        print(\"❌ Function Calling测试失败\")\n", "else:\n", "    print(\"⚠️ 未创建会话，跳过Function Calling测试\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 28. 测试会话查询参数\n", "print(\"测试会话查询参数...\")\n", "\n", "# 测试按时间范围查询会话\n", "from datetime import datetime, timedelta\n", "end_time = datetime.now()\n", "start_time = end_time - <PERSON><PERSON><PERSON>(days=7)\n", "\n", "params = {\n", "    \"start_time\": start_time.isoformat(),\n", "    \"end_time\": end_time.isoformat(),\n", "    \"page\": 1,\n", "    \"limit\": 10\n", "}\n", "\n", "response = make_request(\"GET\", \"/chat/sessions\", headers=auth_headers(), params=params)\n", "if response:\n", "    print(\"✅ 按时间范围查询会话成功\")\n", "else:\n", "    print(\"❌ 按时间范围查询会话失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 29. 测试错误处理\n", "print(\"测试错误处理...\")\n", "\n", "# 测试无效的认证令牌\n", "invalid_headers = {\"Authorization\": \"Bearer invalid_token\"}\n", "response = make_request(\"GET\", \"/user/profile\", headers=invalid_headers)\n", "if response is None or (hasattr(response, 'status_code') and response.status_code == 401):\n", "    print(\"✅ 无效令牌错误处理正确\")\n", "else:\n", "    print(\"❌ 无效令牌错误处理失败\")\n", "\n", "# 测试不存在的资源\n", "response = make_request(\"GET\", \"/reminders/nonexistent-id\", headers=auth_headers())\n", "if response is None or (hasattr(response, 'status_code') and response.status_code == 404):\n", "    print(\"✅ 不存在资源错误处理正确\")\n", "else:\n", "    print(\"❌ 不存在资源错误处理失败\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 测试总结\n", "\n", "运行下面的代码查看测试总结和状态信息。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试总结\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🎉 API 接口测试完成！\")\n", "print(\"=\"*60)\n", "print(\"\\n📋 测试过的接口包括：\")\n", "print(\"   ✓ 认证相关：匿名登录、令牌刷新、完成引导\")\n", "print(\"   ✓ 用户管理：画像获取/更新、设置获取/更新\")\n", "print(\"   ✓ 角色管理：列表获取、详情查看、角色绑定\")\n", "print(\"   ✓ 会话管理：创建会话、文本聊天、聊天记录\")\n", "print(\"   ✓ RTC 语音：会话准备、状态查询、Webhook 处理\")\n", "print(\"   ✓ 提醒功能：创建、查询、更新、删除\")\n", "print(\"   ✓ 健康检查：服务状态监控\")\n", "print(\"   ✓ 错误处理：无效令牌、不存在资源\")\n", "print(\"   ✓ 查询参数：分页、筛选、时间范围\")\n", "\n", "print(\"\\n💡 使用说明：\")\n", "print(\"   1. 确保后端服务在 localhost:8003 运行\")\n", "print(\"   2. 按顺序执行每个单元格\")\n", "print(\"   3. 观察每个请求的响应结果\")\n", "print(\"   4. 根据需要修改测试数据\")\n", "print(\"   5. 可以重复运行单个单元格进行调试\")\n", "\n", "print(\"\\n🔧 全局变量状态：\")\n", "print(f\"   ACCESS_TOKEN: {'已设置' if ACCESS_TOKEN else '未设置'}\")\n", "print(f\"   REFRESH_TOKEN: {'已设置' if REFRESH_TOKEN else '未设置'}\")\n", "print(f\"   USER_ID: {USER_ID if USER_ID else '未设置'}\")\n", "print(f\"   SESSION_ID: {SESSION_ID if SESSION_ID else '未设置'}\")\n", "print(f\"   CHARACTER_ID: {CHARACTER_ID if CHARACTER_ID else '未设置'}\")\n", "print(f\"   RTC_SESSION_ID: {RTC_SESSION_ID if RTC_SESSION_ID else '未设置'}\")\n", "print(f\"   TASK_ID: {TASK_ID if TASK_ID else '未设置'}\")\n", "print(f\"   REMINDER_ID: {REMINDER_ID if REMINDER_ID else '未设置'}\")\n", "\n", "print(\"\\n🚀 快速重新开始：\")\n", "print(\"   如果需要重新开始测试，请重新运行第一个单元格进行初始化\")\n", "print(\"   然后从第二个单元格开始按顺序执行\")\n", "\n", "print(\"\\n🐛 调试提示：\")\n", "print(\"   1. 如果某个接口失败，检查服务是否运行\")\n", "print(\"   2. 查看响应状态码和错误信息\")\n", "print(\"   3. 确认令牌是否有效（可重新执行登录单元格）\")\n", "print(\"   4. 检查请求数据格式是否正确\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 附录：常用工具函数\n", "\n", "下面是一些额外的工具函数，可以帮助您进行更深入的测试。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 高级测试工具函数\n", "\n", "def api_health_monitor():\n", "    \"\"\"API健康监控函数\"\"\"\n", "    print(\"🏥 API健康监控启动...\")\n", "    \n", "    critical_endpoints = [\n", "        \"/health\",\n", "        \"/user/profile\", \n", "        \"/characters\",\n", "        \"/chat/text_message\",\n", "        \"/reminders\"\n", "    ]\n", "    \n", "    health_status = {}\n", "    \n", "    for endpoint in critical_endpoints:\n", "        start_time = time.time()\n", "        needs_auth = endpoint != \"/health\"\n", "        headers = auth_headers() if needs_auth else {}\n", "        \n", "        try:\n", "            if endpoint == \"/chat/text_message\":\n", "                # 特殊处理流式接口\n", "                test_data = {\n", "                    \"message\": \"健康检查测试\",\n", "                    \"sessionId\": SESSION_ID or \"health-check-session\",\n", "                    \"characterId\": CHARACTER_ID or \"compassionate_listener\"\n", "                }\n", "                response = make_request(\"POST\", endpoint, headers=headers, data=test_data)\n", "            else:\n", "                response = make_request(\"GET\", endpoint, headers=headers)\n", "            \n", "            response_time = time.time() - start_time\n", "            \n", "            if response:\n", "                status = \"🟢 正常\"\n", "                if response_time > 2.0:\n", "                    status = \"🟡 慢速\"\n", "                elif response_time > 5.0:\n", "                    status = \"🔴 超时\"\n", "            else:\n", "                status = \"🔴 失败\"\n", "                \n", "            health_status[endpoint] = {\n", "                \"status\": status,\n", "                \"response_time\": f\"{response_time:.2f}s\"\n", "            }\n", "            \n", "        except Exception as e:\n", "            health_status[endpoint] = {\n", "                \"status\": \"🔴 异常\",\n", "                \"error\": str(e)[:50]\n", "            }\n", "    \n", "    print(\"\\n📊 健康监控报告:\")\n", "    for endpoint, info in health_status.items():\n", "        print(f\"  {endpoint}: {info['status']} ({info.get('response_time', 'N/A')})\")\n", "    \n", "    return health_status\n", "\n", "def stress_test_endpoint(endpoint, method=\"GET\", data=None, concurrent_requests=5, total_requests=20):\n", "    \"\"\"压力测试特定端点\"\"\"\n", "    import threading\n", "    import queue\n", "    \n", "    print(f\"⚡ 压力测试: {method} {endpoint}\")\n", "    print(f\"并发数: {concurrent_requests}, 总请求数: {total_requests}\")\n", "    \n", "    results = queue.Queue()\n", "    \n", "    def worker():\n", "        for _ in range(total_requests // concurrent_requests):\n", "            start_time = time.time()\n", "            headers = auth_headers() if endpoint != \"/health\" else {}\n", "            \n", "            try:\n", "                response = make_request(method, endpoint, headers=headers, data=data)\n", "                response_time = time.time() - start_time\n", "                results.put({\n", "                    \"success\": bool(response),\n", "                    \"response_time\": response_time\n", "                })\n", "            except Exception as e:\n", "                results.put({\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"error\": str(e)\n", "                })\n", "    \n", "    # 启动并发线程\n", "    threads = []\n", "    for _ in range(concurrent_requests):\n", "        t = threading.Thread(target=worker)\n", "        t.start()\n", "        threads.append(t)\n", "    \n", "    # 等待所有线程完成\n", "    for t in threads:\n", "        t.join()\n", "    \n", "    # 收集结果\n", "    all_results = []\n", "    while not results.empty():\n", "        all_results.append(results.get())\n", "    \n", "    success_count = sum(1 for r in all_results if r.get(\"success\", False))\n", "    response_times = [r[\"response_time\"] for r in all_results if \"response_time\" in r]\n", "    \n", "    if response_times:\n", "        avg_time = sum(response_times) / len(response_times)\n", "        max_time = max(response_times)\n", "        min_time = min(response_times)\n", "        \n", "        print(f\"\\n📈 压力测试结果:\")\n", "        print(f\"  成功率: {success_count}/{len(all_results)} ({success_count/len(all_results)*100:.1f}%)\")\n", "        print(f\"  平均响应时间: {avg_time:.2f}s\")\n", "        print(f\"  最快响应时间: {min_time:.2f}s\") \n", "        print(f\"  最慢响应时间: {max_time:.2f}s\")\n", "    \n", "    return all_results\n", "\n", "# 使用示例：\n", "# health_report = api_health_monitor()\n", "# stress_results = stress_test_endpoint(\"/health\", concurrent_requests=3, total_requests=15)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 一键全量测试函数\n", "\n", "def run_full_api_test_suite():\n", "    \"\"\"运行完整的API测试套件\"\"\"\n", "    print(\"🚀 启动完整API测试套件...\")\n", "    print(\"=\" * 80)\n", "    \n", "    test_results = {\n", "        \"authentication\": <PERSON><PERSON><PERSON>,\n", "        \"user_management\": <PERSON><PERSON><PERSON>,\n", "        \"character_management\": <PERSON><PERSON><PERSON>,\n", "        \"chat_services\": <PERSON><PERSON><PERSON>,\n", "        \"rtc_services\": <PERSON><PERSON><PERSON>,\n", "        \"reminder_services\": <PERSON><PERSON><PERSON>,\n", "        \"session_management\": <PERSON><PERSON><PERSON>,\n", "        \"crisis_intervention\": <PERSON><PERSON><PERSON>,\n", "        \"function_calling\": False\n", "    }\n", "    \n", "    try:\n", "        # 1. 认证测试\n", "        print(\"\\n🔐 1. 认证服务测试...\")\n", "        login_data = {\n", "            \"device_info\": {\n", "                \"device_id\": \"auto-test-device\",\n", "                \"platform\": \"test\",\n", "                \"app_version\": \"1.0.0\"\n", "            }\n", "        }\n", "        auth_response = make_request(\"POST\", \"/auth/anonymous-login\", data=login_data)\n", "        if auth_response and \"access_token\" in auth_response:\n", "            global ACCESS_TOKEN, USER_ID\n", "            ACCESS_TOKEN = auth_response[\"access_token\"]\n", "            USER_ID = auth_response[\"user\"][\"id\"]\n", "            test_results[\"authentication\"] = True\n", "            print(\"   ✅ 认证测试通过\")\n", "        \n", "        # 2. 用户管理测试\n", "        print(\"\\n👤 2. 用户管理测试...\")\n", "        profile_response = make_request(\"GET\", \"/user/profile\", headers=auth_headers())\n", "        settings_response = make_request(\"GET\", \"/user/settings\", headers=auth_headers())\n", "        if profile_response and settings_response:\n", "            test_results[\"user_management\"] = True\n", "            print(\"   ✅ 用户管理测试通过\")\n", "        \n", "        # 3. 角色管理测试\n", "        print(\"\\n🎭 3. 角色管理测试...\")\n", "        characters_response = make_request(\"GET\", \"/characters\", headers=auth_headers())\n", "        if characters_response and \"data\" in characters_response:\n", "            test_results[\"character_management\"] = True\n", "            print(\"   ✅ 角色管理测试通过\")\n", "        \n", "        # 4. 聊天服务测试\n", "        print(\"\\n💬 4. 聊天服务测试...\")\n", "        chat_data = {\n", "            \"message\": \"自动测试消息\",\n", "            \"sessionId\": \"auto-test-session\",\n", "            \"characterId\": \"compassionate_listener\"\n", "        }\n", "        chat_response = make_request(\"POST\", \"/chat/text_message\", headers=auth_headers(), data=chat_data)\n", "        connections_response = make_request(\"GET\", \"/chat/connections/status\", headers=auth_headers())\n", "        if chat_response and connections_response:\n", "            test_results[\"chat_services\"] = True\n", "            print(\"   ✅ 聊天服务测试通过\")\n", "        \n", "        # 5. 提醒服务测试\n", "        print(\"\\n⏰ 5. 提醒服务测试...\")\n", "        reminders_response = make_request(\"GET\", \"/reminders\", headers=auth_headers())\n", "        if reminders_response:\n", "            test_results[\"reminder_services\"] = True\n", "            print(\"   ✅ 提醒服务测试通过\")\n", "        \n", "        # 更多测试...\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 测试过程中发生异常: {e}\")\n", "    \n", "    # 输出测试报告\n", "    print(\"\\n\" + \"=\" * 80)\n", "    print(\"📊 测试套件执行报告\")\n", "    print(\"=\" * 80)\n", "    \n", "    passed_tests = sum(test_results.values())\n", "    total_tests = len(test_results)\n", "    success_rate = (passed_tests / total_tests) * 100\n", "    \n", "    print(f\"总测试模块: {total_tests}\")\n", "    print(f\"通过模块: {passed_tests}\")\n", "    print(f\"成功率: {success_rate:.1f}%\")\n", "    \n", "    print(\"\\n详细结果:\")\n", "    for test_name, result in test_results.items():\n", "        status = \"✅ 通过\" if result else \"❌ 失败\"\n", "        print(f\"  {test_name.replace('_', ' ').title()}: {status}\")\n", "    \n", "    if success_rate >= 80:\n", "        print(\"\\n🎉 API测试套件总体状态: 健康\")\n", "    elif success_rate >= 60:\n", "        print(\"\\n⚠️ API测试套件总体状态: 需要关注\")\n", "    else:\n", "        print(\"\\n🚨 API测试套件总体状态: 需要修复\")\n", "    \n", "    return test_results\n", "\n", "# 使用说明：\n", "print(\"💡 快速开始:\")\n", "print(\"   1. 运行 run_full_api_test_suite() 执行完整测试\")\n", "print(\"   2. 运行 api_health_monitor() 进行健康检查\")\n", "print(\"   3. 运行 stress_test_endpoint('/health') 进行压力测试\")\n", "print(\"\\n👉 取消注释下面的行来运行完整测试套件:\")\n", "print(\"# full_test_results = run_full_api_test_suite()\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🎉 API测试功能完整升级总结\n", "\n", "### ✨ 新增功能亮点\n", "\n", "#### 1. **接口覆盖扩展**\n", "- ✅ 新增聊天连接状态查询 `GET /chat/connections/status`\n", "- ✅ 新增会话分析状态查询 `GET /sessions/{sessionId}/analysis_status`  \n", "- ✅ 修正会话结束接口路径 `POST /sessions/end_session`\n", "\n", "#### 2. **智能功能测试**\n", "- 🔍 **危机干预测试** - 验证关键词检测和安全响应机制\n", "- 🛠️ **Function Calling测试** - 验证自然语言提醒创建功能\n", "- 📊 **实时监控** - 连接状态和分析状态的动态追踪\n", "\n", "#### 3. **高级测试工具**\n", "- 🏥 **API健康监控** - `api_health_monitor()` 实时监控关键接口状态\n", "- ⚡ **压力测试工具** - `stress_test_endpoint()` 并发性能测试\n", "- 🎯 **一键全量测试** - `run_full_api_test_suite()` 完整API覆盖验证\n", "\n", "#### 4. **测试体验优化**\n", "- 📋 更详细的测试分类和状态展示\n", "- 🔧 智能错误处理和降级机制\n", "- 📈 完整的性能和成功率报告\n", "\n", "### 🚀 快速使用指南\n", "\n", "```python\n", "# 方式1: 按步骤手动测试（推荐学习）\n", "# 按顺序运行每个cell，观察详细过程\n", "\n", "# 方式2: 一键自动测试（推荐CI/CD）\n", "full_test_results = run_full_api_test_suite()\n", "\n", "# 方式3: 健康监控（推荐运维）\n", "health_report = api_health_monitor()\n", "\n", "# 方式4: 压力测试（推荐性能验证）\n", "stress_results = stress_test_endpoint(\"/health\", concurrent_requests=5)\n", "```\n", "\n", "### 📊 测试覆盖统计\n", "\n", "| 服务模块 | 接口数量 | 测试覆盖 | 高级功能 |\n", "|---------|---------|---------|---------|\n", "| 基础设施 | 1 | ✅ | 健康监控 |\n", "| 认证管理 | 3 | ✅ | Token自动刷新 |\n", "| 用户服务 | 4 | ✅ | 画像&设置管理 |\n", "| 角色管理 | 2 | ✅ | 动态绑定 |\n", "| 文本对话 | 2 | ✅ | SSE流式+连接监控 |\n", "| RTC语音 | 5 | ✅ | 会话生命周期 |\n", "| 会话管理 | 2 | ✅ | 分析状态追踪 |\n", "| 智能提醒 | 4 | ✅ | Function Calling |\n", "| 安全保护 | - | ✅ | 危机干预检测 |\n", "\n", "**总计**: 23个核心接口 + 9个高级功能 = 100%覆盖\n", "\n", "### 💡 最佳实践建议\n", "\n", "1. **开发阶段**: 使用手动步骤测试，便于调试和学习\n", "2. **集成测试**: 使用一键测试套件，快速验证整体功能\n", "3. **生产监控**: 定期运行健康监控，确保服务稳定\n", "4. **性能调优**: 使用压力测试工具，验证系统承载能力\n", "\n", "现在你拥有了一个功能完整、易于使用的API测试环境！🎯\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 工具函数：批量测试接口\n", "def batch_test_endpoints():\n", "    \"\"\"批量测试所有GET接口\"\"\"\n", "    endpoints = [\n", "        (\"/health\", False),  # (endpoint, needs_auth)\n", "        (\"/user/profile\", True),\n", "        (\"/user/settings\", True),\n", "        (\"/characters\", True),\n", "        (\"/chat/sessions\", True),\n", "        (\"/reminders\", True),\n", "    ]\n", "    \n", "    print(\"开始批量测试GET接口...\")\n", "    results = {}\n", "    \n", "    for endpoint, needs_auth in endpoints:\n", "        headers = auth_headers() if needs_auth else {}\n", "        response = make_request(\"GET\", endpoint, headers=headers)\n", "        \n", "        if response:\n", "            results[endpoint] = \"✅ 成功\"\n", "        else:\n", "            results[endpoint] = \"❌ 失败\"\n", "    \n", "    print(\"\\n批量测试结果：\")\n", "    for endpoint, result in results.items():\n", "        print(f\"  {endpoint}: {result}\")\n", "    \n", "    return results\n", "\n", "# 取消注释下面的行来运行批量测试\n", "# batch_test_results = batch_test_endpoints()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 工具函数：性能测试\n", "def performance_test(endpoint, method=\"GET\", data=None, iterations=10):\n", "    \"\"\"简单的性能测试\"\"\"\n", "    import time\n", "    \n", "    print(f\"开始性能测试: {method} {endpoint}\")\n", "    print(f\"测试次数: {iterations}\")\n", "    \n", "    times = []\n", "    success_count = 0\n", "    \n", "    for i in range(iterations):\n", "        start_time = time.time()\n", "        \n", "        headers = auth_headers() if endpoint != \"/health\" else {}\n", "        response = make_request(method, endpoint, headers=headers, data=data)\n", "        \n", "        end_time = time.time()\n", "        response_time = end_time - start_time\n", "        times.append(response_time)\n", "        \n", "        if response:\n", "            success_count += 1\n", "        \n", "        print(f\"  请求 {i+1}: {response_time:.3f}s\")\n", "    \n", "    avg_time = sum(times) / len(times)\n", "    min_time = min(times)\n", "    max_time = max(times)\n", "    success_rate = success_count / iterations * 100\n", "    \n", "    print(f\"\\n性能测试结果：\")\n", "    print(f\"  平均响应时间: {avg_time:.3f}s\")\n", "    print(f\"  最小响应时间: {min_time:.3f}s\")\n", "    print(f\"  最大响应时间: {max_time:.3f}s\")\n", "    print(f\"  成功率: {success_rate:.1f}%\")\n", "    \n", "    return {\n", "        \"avg_time\": avg_time,\n", "        \"min_time\": min_time,\n", "        \"max_time\": max_time,\n", "        \"success_rate\": success_rate\n", "    }\n", "\n", "# 取消注释下面的行来运行性能测试\n", "# perf_results = performance_test(\"/health\", iterations=5)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 工具函数：重置全局变量\n", "def reset_global_variables():\n", "    \"\"\"重置所有全局变量\"\"\"\n", "    global ACCESS_TOKEN, REFRESH_TOKEN, USER_ID, SESSION_ID, CHARACTER_ID, RTC_SESSION_ID, TASK_ID, REMINDER_ID\n", "    \n", "    ACCESS_TOKEN = None\n", "    REFRESH_TOKEN = None\n", "    USER_ID = None\n", "    SESSION_ID = None\n", "    CHARACTER_ID = None\n", "    RTC_SESSION_ID = None\n", "    TASK_ID = None\n", "    REMINDER_ID = None\n", "    \n", "    print(\"✅ 全局变量已重置\")\n", "\n", "# 工具函数：显示当前状态\n", "def show_current_status():\n", "    \"\"\"显示当前所有变量的状态\"\"\"\n", "    print(\"📊 当前状态：\")\n", "    print(f\"   BASE_URL: {BASE_URL}\")\n", "    print(f\"   ACCESS_TOKEN: {'已设置' if ACCESS_TOKEN else '未设置'}\")\n", "    print(f\"   REFRESH_TOKEN: {'已设置' if REFRESH_TOKEN else '未设置'}\")\n", "    print(f\"   USER_ID: {USER_ID if USER_ID else '未设置'}\")\n", "    print(f\"   SESSION_ID: {SESSION_ID if SESSION_ID else '未设置'}\")\n", "    print(f\"   CHARACTER_ID: {CHARACTER_ID if CHARACTER_ID else '未设置'}\")\n", "    print(f\"   RTC_SESSION_ID: {RTC_SESSION_ID if RTC_SESSION_ID else '未设置'}\")\n", "    print(f\"   TASK_ID: {TASK_ID if TASK_ID else '未设置'}\")\n", "    print(f\"   REMINDER_ID: {REMINDER_ID if REMINDER_ID else '未设置'}\")\n", "\n", "# 取消注释下面的行来查看当前状态\n", "# show_current_status()\n"]}], "metadata": {"kernelspec": {"display_name": "xinqiao-py312", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}