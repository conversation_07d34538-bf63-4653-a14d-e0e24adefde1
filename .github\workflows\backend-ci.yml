name: Backend CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/agent-api/**'
      - '.github/workflows/backend-ci.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/agent-api/**'

env:
  PYTHON_VERSION: '3.12'

jobs:
  test:
    name: Test and Quality Checks
    runs-on: ubuntu-latest
    
    # 设置PostgreSQL服务用于测试
    services:
      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    env:
      # 测试数据库配置
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      SUPABASE_URL: http://localhost:54321
      SUPABASE_ANON_KEY: test_anon_key
      SUPABASE_SERVICE_ROLE_KEY: test_service_key
      
      # 模拟API密钥（用于测试）
      OPENAI_API_KEY: sk-test-key-not-real
      OPENAI_MODEL: gpt-4o-mini
      VOLCANO_RTC_API_KEY: test_rtc_key
      VOLCANO_RTC_APP_ID: test_app_id
      VOLCANO_RTC_SECRET: test_secret
      
      # 应用配置
      APP_ENV: testing
      DEBUG: false
      SECRET_KEY: test-secret-key-for-ci
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/pip
        key: ${{ runner.os }}-python-${{ env.PYTHON_VERSION }}-${{ hashFiles('apps/agent-api/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-python-${{ env.PYTHON_VERSION }}-
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y postgresql-client
    
    - name: Install Python dependencies
      working-directory: apps/agent-api
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Setup database and pgvector
      run: |
        # 等待PostgreSQL启动
        until pg_isready -h localhost -p 5432 -U postgres; do
          echo "Waiting for PostgreSQL..."
          sleep 1
        done
        
        # 创建pgvector扩展
        PGPASSWORD=postgres psql -h localhost -U postgres -d test_db -c "CREATE EXTENSION IF NOT EXISTS vector;"
        
        # 验证扩展安装
        PGPASSWORD=postgres psql -h localhost -U postgres -d test_db -c "SELECT extname, extversion FROM pg_extension WHERE extname = 'vector';"
    
    - name: Run code formatting check (Black)
      working-directory: apps/agent-api
      run: |
        black --check --diff .
    
    - name: Run import sorting check (isort)
      working-directory: apps/agent-api
      run: |
        isort --check-only --diff .
    
    - name: Run linting (Ruff)
      working-directory: apps/agent-api
      run: |
        ruff check .
    
    - name: Run type checking (MyPy)
      working-directory: apps/agent-api
      run: |
        mypy . --ignore-missing-imports
    
    - name: Run database connection tests
      working-directory: apps/agent-api
      run: |
        pytest tests/test_database.py -v --tb=short
    
    - name: Run vector operations tests
      working-directory: apps/agent-api
      run: |
        pytest tests/test_vector_operations.py -v --tb=short
    
    - name: Run external API tests (mock mode)
      working-directory: apps/agent-api
      run: |
        pytest tests/test_external_apis.py -v --tb=short -k "mock or credentials"
    
    - name: Run full test suite with coverage
      working-directory: apps/agent-api
      run: |
        pytest tests/ -v --cov=api --cov=db --cov=agents \
          --cov-report=html --cov-report=xml --cov-report=term-missing \
          --cov-fail-under=70
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        file: apps/agent-api/coverage.xml
        directory: apps/agent-api
        flags: backend
        name: backend-coverage
    
    - name: Test FastAPI application startup
      working-directory: apps/agent-api
      run: |
        # 启动应用并测试健康检查
        timeout 30s uvicorn api.main:app --host 0.0.0.0 --port 8000 &
        APP_PID=$!
        
        # 等待应用启动
        sleep 10
        
        # 测试健康检查接口
        curl -f http://localhost:8000/api/v1/health || exit 1
        
        # 测试API文档
        curl -f http://localhost:8000/docs || exit 1
        
        # 停止应用
        kill $APP_PID || true

  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install security scanning tools
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit
    
    - name: Run safety check (dependency vulnerabilities)
      working-directory: apps/agent-api
      run: |
        safety check -r requirements.txt
    
    - name: Run bandit security linter
      working-directory: apps/agent-api
      run: |
        bandit -r . -f json -o bandit-report.json || true
        bandit -r . -f txt

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      working-directory: apps/agent-api
      run: |
        docker build -t agent-api:${{ github.sha }} .
    
    - name: Test Docker image
      run: |
        # 创建测试环境变量文件
        cat > test.env << EOF
        DATABASE_URL=postgresql://test:test@localhost:5432/test
        OPENAI_API_KEY=sk-test-key
        OPENAI_MODEL=gpt-4o-mini
        VOLCANO_RTC_API_KEY=test_key
        VOLCANO_RTC_APP_ID=test_app
        VOLCANO_RTC_SECRET=test_secret
        SUPABASE_URL=http://localhost:54321
        SUPABASE_ANON_KEY=test_anon_key
        SUPABASE_SERVICE_ROLE_KEY=test_service_key
        APP_ENV=testing
        DEBUG=false
        SECRET_KEY=test-secret-key
        EOF
        
        # 测试容器启动
        docker run --env-file test.env -d --name test-container -p 8080:8000 agent-api:${{ github.sha }}
        
        # 等待容器启动
        sleep 15
        
        # 测试健康检查（可能失败，因为没有数据库连接）
        curl -f http://localhost:8080/api/v1/health || echo "Health check failed (expected without DB)"
        
        # 清理
        docker stop test-container
        docker rm test-container
        rm test.env 