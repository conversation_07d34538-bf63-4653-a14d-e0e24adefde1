#!/usr/bin/env python3
"""
使用火山引擎官方SDK对比测试
用于验证我们的签名实现与官方SDK的差异
"""

import os
import sys
import asyncio
import json
import datetime
import hashlib
import hmac
import base64
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from volcengine.ApiInfo import ApiInfo
    from volcengine.Credentials import Credentials
    from volcengine.base.Service import Service
    from volcengine.ServiceInfo import ServiceInfo
    print("✅ 火山引擎官方SDK导入成功")
except ImportError as e:
    print(f"❌ 火山引擎官方SDK导入失败: {e}")
    print("请确保已安装: pip install volcengine")
    sys.exit(1)

from api.services.volcano_client_service import get_volcano_client_service


class CustomRtcService(Service):
    """基于官方SDK的自定义RTC服务"""

    def __init__(self, access_key: str, secret_key: str):
        # 创建服务信息
        service_info = ServiceInfo(
            "rtc.volcengineapi.com",
            {'Accept': 'application/json'},
            Credentials(access_key, secret_key, 'rtc', 'cn-north-1'),
            30, 30
        )

        # 创建API信息
        api_info = {
            "StartVoiceChat": ApiInfo("POST", "/", {"Action": "StartVoiceChat", "Version": "2024-12-01"}, {}, {}),
        }

        super(CustomRtcService, self).__init__(service_info, api_info)

    def start_voice_chat(self, body):
        """调用StartVoiceChat API"""
        res = self.json("StartVoiceChat", {}, body)
        if res == '':
            raise Exception("StartVoiceChat: empty response")
        res_json = json.loads(res)
        return res_json


def test_official_sdk():
    """测试官方SDK"""
    print("\n" + "="*60)
    print(" 官方SDK测试")
    print("="*60)

    try:
        # 从环境变量获取配置
        access_key = os.getenv('VOLCANO_ACCESS_KEY_ID')
        secret_key_raw = os.getenv('VOLCANO_SECRET_ACCESS_KEY')
        app_id = os.getenv('VOLCANO_RTC_APP_ID')

        if not all([access_key, secret_key_raw, app_id]):
            print("❌ 缺少必需的环境变量")
            return False

        # 处理SECRET_ACCESS_KEY - 如果是Base64编码的，需要解码
        try:
            secret_key = base64.b64decode(secret_key_raw).decode('utf-8')
            print("✅ SECRET_ACCESS_KEY已从Base64解码")
        except Exception:
            secret_key = secret_key_raw
            print("✅ SECRET_ACCESS_KEY使用原始值")

        print(f"Access Key: {access_key[:8]}...")
        print(f"Secret Key长度: {len(secret_key)}")
        print(f"App ID: {app_id}")

        # 创建官方SDK服务实例
        rtc_service = CustomRtcService(access_key, secret_key)

        # 准备测试数据
        test_data = {
            'AppId': app_id,
            'RoomId': 'signature-test-room',
            'TaskId': 'signature-test-task',
            'Config': {
                'ASRConfig': {
                    'Provider': 'volcano',
                    'ProviderParams': {
                        'Mode': 'bigmodel',
                        'AppId': '**********',
                        'AccessToken': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox',
                        'ApiResourceId': 'volc.bigasr.sauc.duration',
                        'StreamMode': 0
                    }
                },
                'TTSConfig': {
                    'Provider': 'volcano_bidirection',
                    'ProviderParams': {
                        'app': {
                            'appid': '**********',
                            'token': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox'
                        },
                        'audio': {
                            'voice_type': 'zh_male_qingshuangnanda_mars_bigtts',
                            'speech_rate': 0,
                            'pitch_rate': 0
                        },
                        'ResourceId': 'volc.service_type.10029'
                    }
                },
                'LLMConfig': {
                    'Mode': 'ArkV3',
                    'EndPointId': 'ep-20250704092428-tl9sc',
                    'MaxTokens': 1024,
                    'Temperature': 0.7,
                    'SystemMessages': ['你是一个AI助手。']
                },
                'AgentConfig': {
                    'UserId': 'signature-test-bot',
                    'TargetUserId': ['signature-test-user']
                }
            }
        }

        print("\n🔄 使用官方SDK调用StartVoiceChat...")

        try:
            result = rtc_service.start_voice_chat(test_data)
            print("✅ 官方SDK调用成功!")
            print(f"结果: {result}")
            return True

        except Exception as e:
            error_str = str(e)
            print(f"❌ 官方SDK调用失败: {error_str}")

            # 分析错误类型
            if 'SignatureDoesNotMatch' in error_str:
                print("🔍 官方SDK也遇到签名验证失败，可能是配置问题")
            elif 'invalid UserID in AgentConfig' in error_str:
                print("✅ 官方SDK签名验证通过！问题确实是跨服务授权或服务开通问题")
            elif 'InvalidParameter' in error_str:
                print("✅ 官方SDK签名验证通过！问题是参数配置问题")
            else:
                print(f"🔍 其他错误: {error_str}")

            return False

        # 构建测试数据
        test_data = {
            'AppId': app_id,
            'RoomId': 'official-sdk-test-room',
            'TaskId': 'official-sdk-test-task',
            'Config': {
                'ASRConfig': {
                    'Provider': 'volcano',
                    'ProviderParams': {
                        'Mode': 'bigmodel',
                        'AppId': '**********',
                        'AccessToken': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox',
                        'ApiResourceId': 'volc.bigasr.sauc.duration',
                        'StreamMode': 0
                    }
                },
                'TTSConfig': {
                    'Provider': 'volcano_bidirection',
                    'ProviderParams': {
                        'app': {
                            'appid': '**********',
                            'token': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox'
                        },
                        'audio': {
                            'voice_type': 'zh_male_qingshuangnanda_mars_bigtts',
                            'speech_rate': 0,
                            'pitch_rate': 0
                        },
                        'ResourceId': 'volc.service_type.10029'
                    }
                },
                'LLMConfig': {
                    'Mode': 'ArkV3',
                    'EndPointId': 'ep-20250704092428-tl9sc',
                    'MaxTokens': 1024,
                    'Temperature': 0.7,
                    'SystemMessages': ['你是一个AI助手。']
                },
                'AgentConfig': {
                    'UserId': 'official-sdk-bot',
                    'TargetUserId': ['official-sdk-user']
                }
            }
        }

        # 2. 使用我们的实现生成签名
        print("\n🔄 使用我们的实现生成签名...")
        try:
            service = get_volcano_client_service()

            # 使用相同的参数生成签名
            our_headers = service.get_signed_headers(
                service="rtc",
                host=host,
                region="cn-north-1",
                method=method,
                path=path,
                query_params=query_params,
                body=body
            )

            # 提取我们的签名
            auth_header = our_headers.get('Authorization', '')
            if 'Signature=' in auth_header:
                our_signature = auth_header.split('Signature=')[1]
                print(f"✅ 我们的签名: {our_signature}")
            else:
                print(f"❌ 无法提取我们的签名: {auth_header}")
                our_signature = None

        except Exception as e:
            print(f"❌ 我们的签名失败: {e}")
            our_signature = None

        # 3. 对比签名
        print("\n📊 签名对比结果:")
        if official_signature and our_signature:
            if official_signature == our_signature:
                print("✅ 签名完全一致！")
                return True
            else:
                print("❌ 签名不一致")
                print(f"官方SDK: {official_signature}")
                print(f"我们的:   {our_signature}")
                return False
        else:
            print("❌ 无法进行对比，某个签名生成失败")
            return False

    except Exception as e:
        error_str = str(e)
        print(f"❌ 官方SDK调用失败: {error_str}")

        # 分析错误类型
        if 'SignatureDoesNotMatch' in error_str:
            print("🔍 官方SDK也遇到签名验证失败，可能是配置问题")
        elif 'invalid UserID in AgentConfig' in error_str:
            print("✅ 官方SDK签名验证通过！问题确实是跨服务授权或服务开通问题")
        elif 'InvalidParameter' in error_str:
            print("✅ 官方SDK签名验证通过！问题是参数配置问题")
        else:
            print(f"🔍 其他错误: {error_str}")

        return False


async def test_our_implementation():
    """测试我们的实现"""
    print("\n" + "="*60)
    print(" 我们的实现测试")
    print("="*60)

    try:
        service = get_volcano_client_service()

        test_data = {
            'AppId': service.app_id,
            'RoomId': 'our-impl-test-room',
            'TaskId': 'our-impl-test-task',
            'Config': {
                'ASRConfig': {
                    'Provider': 'volcano',
                    'ProviderParams': {
                        'Mode': 'bigmodel',
                        'AppId': '**********',
                        'AccessToken': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox',
                        'ApiResourceId': 'volc.bigasr.sauc.duration',
                        'StreamMode': 0
                    }
                },
                'TTSConfig': {
                    'Provider': 'volcano_bidirection',
                    'ProviderParams': {
                        'app': {
                            'appid': '**********',
                            'token': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox'
                        },
                        'audio': {
                            'voice_type': 'zh_male_qingshuangnanda_mars_bigtts',
                            'speech_rate': 0,
                            'pitch_rate': 0
                        },
                        'ResourceId': 'volc.service_type.10029'
                    }
                },
                'LLMConfig': {
                    'Mode': 'ArkV3',
                    'EndPointId': 'ep-20250704092428-tl9sc',
                    'MaxTokens': 1024,
                    'Temperature': 0.7,
                    'SystemMessages': ['你是一个AI助手。']
                },
                'AgentConfig': {
                    'UserId': 'our-impl-bot',
                    'TargetUserId': ['our-impl-user']
                }
            }
        }

        print("🔄 使用我们的实现调用StartVoiceChat...")

        result = await service._call_volcano_api('StartVoiceChat', test_data)
        print("✅ 我们的实现调用成功!")
        print(f"结果: {result}")
        return True

    except Exception as e:
        error_str = str(e)
        print(f"❌ 我们的实现调用失败: {error_str}")

        # 分析错误类型
        if 'SignatureDoesNotMatch' in error_str:
            print("🔍 我们的实现遇到签名验证失败")
        elif 'invalid UserID in AgentConfig' in error_str:
            print("✅ 我们的实现签名验证通过！问题确实是跨服务授权或服务开通问题")
        elif 'InvalidParameter' in error_str:
            print("✅ 我们的实现签名验证通过！问题是参数配置问题")
        else:
            print(f"🔍 其他错误: {error_str}")

        return False


def compare_configurations():
    """对比配置信息"""
    print("\n" + "="*60)
    print(" 配置对比")
    print("="*60)

    # 环境变量
    access_key = os.getenv('VOLCANO_ACCESS_KEY_ID')
    secret_key = os.getenv('VOLCANO_SECRET_ACCESS_KEY')
    app_id = os.getenv('VOLCANO_RTC_APP_ID')

    print(f"环境变量 Access Key: {access_key}")
    print(f"环境变量 Secret Key: {'*' * len(secret_key) if secret_key else 'None'}")
    print(f"环境变量 App ID: {app_id}")

    # 我们的服务配置
    try:
        service = get_volcano_client_service()
        print(f"\n我们的服务 Access Key: {service.access_key}")
        print(f"我们的服务 Secret Key: {'*' * len(service.secret_key)}")
        print(f"我们的服务 App ID: {service.app_id}")

        # 检查是否一致
        if service.access_key == access_key:
            print("✅ Access Key 一致")
        else:
            print("❌ Access Key 不一致")

        if service.app_id == app_id:
            print("✅ App ID 一致")
        else:
            print("❌ App ID 不一致")

    except Exception as e:
        print(f"❌ 获取服务配置失败: {e}")


async def main():
    """主函数"""
    print("🔍 火山引擎SDK对比测试")
    print("这个脚本将对比官方SDK和我们的实现，帮助找出问题所在")

    # 1. 对比配置
    compare_configurations()

    # 2. 对比签名算法
    signature_success = compare_signatures()

    # 3. 测试我们的实现
    our_success = await test_our_implementation()

    # 4. 总结
    print("\n" + "="*60)
    print(" 测试结果总结")
    print("="*60)

    if signature_success and our_success:
        print("🎉 签名一致且API调用成功！问题已解决。")
    elif signature_success and not our_success:
        print("🔍 签名一致但API调用失败。可能是服务配置问题。")
    elif not signature_success and not our_success:
        print("🔍 签名不一致且API调用失败。需要修复签名算法。")
    elif not signature_success and our_success:
        print("🤔 意外情况：签名不一致但API调用成功。")
    else:
        print("🔍 需要进一步分析。")

    print("\n📋 建议:")
    if not signature_success:
        print("1. 对比官方SDK的签名实现细节")
        print("2. 检查我们的V4签名算法实现")
        print("3. 验证签名参数的处理方式")
    elif not our_success:
        print("1. 检查火山引擎账号配置和服务开通状态")
        print("2. 确认跨服务授权已开通")
        print("3. 验证所有服务的免费额度是否充足")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生异常: {e}")
