# **"心桥"MVP阶段开发执行补充指南**

---

## 🎯 **MVP成功的关键成功要素补充**

### **1. 开发前的"技术可行性验证"阶段**

**你的文档中提到但需要立即执行的：**

**1.1 ASR准确率基准测试 (关键！)**
```
执行方案：
- 立即购买火山引擎ASR服务测试版
- 录制20-30段不同方言的老年人语音样本
- 设定基准：识别准确率必须>85%才能继续
- 如果不达标，立即评估讯飞、百度等替代方案
- 预算：1-2万元，时间：1周

具体测试场景：
1. 标准普通话老年人（对照组）
2. 轻微方言口音
3. 有电视背景音环境
4. 语速较慢、停顿较多的表达
5. 包含"嗯、啊"等语气词的自然对话
```

**1.2 情感共情能力验证**
```
执行方案：
- 准备20个包含不同情绪的测试用例
- 通过火山引擎LLM测试现有的情感识别和回应能力
- 建立"情感回应评分标准"(1-5分)
- 目标：平均得分>3.5分才能继续

测试用例示例：
- "我今天心情不太好，老伴走了三年了，特别想他"
- "孩子们都忙，很久没来看我了，我也不敢打扰他们"
- "刚才去医院检查，医生说我血压有点高，有点担心"
```

---

## 📅 **3个月MVP详细排期建议**

### **第1-2周：技术栈验证与环境搭建**
```
关键里程碑：
□ ASR/TTS/LLM API调通并基准测试完成
□ Supabase项目创建，基础数据表设计完成
□ React Native项目初始化，基础组件库搭建
□ CI/CD基础流水线搭建完成
□ 法律顾问对接，隐私政策初稿完成

团队配置：
- 1名全栈工程师负责后端架构
- 1名前端工程师负责RN开发
- 1名产品经理跟进测试和文档
```

### **第3-6周：核心功能开发**
```
关键里程碑：
□ 无感身份系统开发完成
□ 核心对话交互（录音-识别-回复-播放）闭环完成
□ 角色共创流程完整实现
□ 基础记忆系统（短期+身份记忆）完成
□ 情感化异常处理机制集成

风险控制：
- 每周五下午进行内部Demo，及时发现问题
- 建立"每日构建+自动化测试"机制
- 如果ASR准确率不达标，立即启动备选方案
```

### **第7-10周：提醒功能与危机响应**
```
关键里程碑：
□ 对话式提醒的NLU识别完成
□ Supabase Cron Jobs提醒调度系统完成
□ 危机响应关键词检测与脚本化干预完成
□ 推送通知在各种手机系统下的可靠性测试
□ 完整的"温暖初见"用户引导流程

重点测试：
- 提醒功能在锁屏/后台状态下的可靠性
- 不同Android厂商（小米、华为、OPPO）的推送兼容性
```

### **第11-12周：完善与上线准备**
```
关键里程碑：
□ 适老化UI/UX最终优化
□ 完整的异常场景处理测试
□ 真实老年用户的可用性测试（5-8人）
□ TestFlight/Google Play内测渠道准备
□ 应用商店资料准备（截图、描述、隐私政策）
□ 监控和日志系统部署完成
```

---

## 🔧 **技术实现关键补充**

### **1. 核心技术栈具体选型建议**

**前端补充：**
```javascript
// 关键依赖包推荐
{
  "expo": "~49.0.0",
  "expo-av": "~13.4.1",        // 音频录制播放
  "expo-application": "~5.2.0", // 设备ID获取
  "expo-secure-store": "~12.1.1", // 敏感数据存储
  "@react-navigation/native": "^6.0.0",
  "@supabase/supabase-js": "^2.0.0"
}

// 必须集成的错误监控
import * as Sentry from '@sentry/react-native';
```

**后端补充：**
```typescript
// Supabase Edge Function结构建议
supabase/functions/
├── chat/
│   ├── index.ts              // 核心对话中间件
│   ├── persona-manager.ts    // 角色管理
│   ├── memory-manager.ts     // 记忆管理
│   └── crisis-detector.ts    // 危机检测
├── reminders/
│   └── trigger.ts            // 定时提醒触发
└── shared/
    ├── types.ts              // 类型定义
    └── utils.ts              // 工具函数
```

### **2. 数据库设计关键补充**

```sql
-- 核心表结构（补充字段）
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE,
  -- 关键：用于成本控制的使用量统计
  daily_api_calls INTEGER DEFAULT 0,
  monthly_api_calls INTEGER DEFAULT 0,
  api_quota_reset_date DATE DEFAULT CURRENT_DATE
);

CREATE TABLE ai_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  ai_name TEXT NOT NULL,
  user_preferred_name TEXT NOT NULL,
  role_type TEXT NOT NULL, -- 'friend', 'junior', etc.
  voice_id TEXT NOT NULL,
  -- 关键：个性化设置
  personality_traits JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 关键：用于危机响应的事件表
CREATE TABLE crisis_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  trigger_type TEXT NOT NULL, -- 'keyword', 'emotion', 'manual'
  trigger_content TEXT,
  confidence_score FLOAT,
  handled BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 🎮 **用户测试执行方案补充**

### **原型测试具体执行步骤**

**1. 测试用户招募**
```
目标：8-10名测试用户
条件：
- 年龄60-75岁
- 熟练使用微信
- 子女不在同城
- 愿意尝试新App

招募渠道：
- 通过团队成员的父母/亲戚介绍
- 社区老年活动中心合作
- 提供小额测试酬谢（200元购物卡）
```

**2. 测试环境设计**
```
测试场景：
1. 安静环境下的基础功能测试
2. 有电视背景音的真实家居环境测试
3. 不同光线条件下的UI可读性测试

测试任务：
1. 独立完成App下载和首次启动
2. 完成"温暖初见"角色创建流程
3. 进行5分钟自由对话
4. 设置一个吃药提醒
5. 第二天查看提醒是否收到

观察要点：
- 是否有操作卡顿或困惑
- 是否需要多次重复操作
- 情绪反应（微笑、皱眉、叹气）
- 主动提出的问题或抱怨
```

---

## 💰 **成本控制与监控补充**

### **API使用量控制机制**

**1. 具体的"隐形熔断"策略**
```typescript
// 用量控制中间件示例
interface UsageLimit {
  dailyCallLimit: 50;      // 每日API调用上限
  monthlyCallLimit: 1000;  // 每月上限
  emergencyThreshold: 80;  // 80%时温柔提醒
}

// 超量处理话术
const quotaExceededResponses = [
  "哎呀，今天跟您聊得太开心了，说的我都有点累啦。我得去休息一下充充电，我们明天再接着聊，好吗？",
  "今天陪您聊了这么久，我也需要好好休息一下。您也早点休息，明天我们继续聊天哦！"
];
```

**2. 成本预估**
```
MVP阶段3个月预估成本：
- 火山引擎API费用：2-5万元（100用户规模）
- Supabase服务费：500-1000元/月
- 服务器/CDN：1000-2000元/月
- 其他工具（Sentry、分析工具）：1000元/月
- 应用商店费用：iOS 688元，Android免费
总计：3-7万元（不含人工成本）
```

---

## 🔍 **监控与数据采集补充**

### **关键埋点事件定义**

```typescript
// 核心转化漏斗埋点
enum TrackingEvents {
  // 获客转化
  APP_INSTALLED = 'app_installed',
  FIRST_LAUNCH = 'first_launch',
  PERMISSION_GRANTED = 'permission_granted',
  
  // 核心体验
  ONBOARDING_STARTED = 'onboarding_started',
  ROLE_CREATED = 'role_created',
  FIRST_MESSAGE_SENT = 'first_message_sent',
  FIRST_AI_RESPONSE_PLAYED = 'first_ai_response_played',
  
  // 功能使用
  REMINDER_SET = 'reminder_set',
  REMINDER_RECEIVED = 'reminder_received',
  CRISIS_DETECTED = 'crisis_detected',
  
  // 留存指标
  DAILY_SESSION_START = 'daily_session_start',
  WEEKLY_RETURN = 'weekly_return'
}
```

---

## ⚠️ **风险控制执行清单**

### **技术风险缓解措施**

**1. ASR失败应急预案**
```
Plan A: 火山引擎ASR
Plan B: 讯飞语音（备选集成）
Plan C: 百度语音（最后备选）

切换标准：
- 准确率<85%
- 响应时间>3秒
- 可用性<99%
```

**2. 数据安全应急响应**
```
即时响应流程：
1. 发现安全事件后1小时内启动应急响应
2. 技术团队立即评估影响范围
3. 法务团队准备合规报告
4. 公关团队准备对外声明
5. 24小时内向监管部门报告

技术措施：
- 所有敏感数据二次加密
- 定期安全扫描和渗透测试
- 访问日志完整记录和定期审计
```

---

## 📋 **MVP上线前最终检查清单**

### **产品功能验收**
```
□ 新用户可以在5分钟内完成完整的首次体验流程
□ AI能够记住用户的称呼并在对话中正确使用
□ 语音识别在安静环境下准确率>90%
□ AI回复时间<3秒
□ 提醒功能在3种不同手机品牌上测试成功
□ 危机响应在测试关键词下能正确触发
□ 网络中断时有合适的情感化提示
□ App在低电量模式下仍能正常使用
```

### **合规与安全验收**
```
□ 《隐私政策》已经法律顾问审核
□ 用户协议符合《个保法》要求
□ 所有API调用已加密
□ 数据库RLS策略已启用并测试
□ 危机响应的法律免责条款已确认
□ ICP备案已提交
□ 应用商店审核资料已准备完毕
```

### **运营准备验收**
```
□ "首席体验官"微信群已建立
□ 客服话术和FAQ已准备
□ 监控大盘已配置并测试
□ 灰度发布流程已演练
□ 用户反馈收集渠道已建立
□ 舆情监控工具已部署
```

---

## 🎯 **MVP成功标准的量化补充**

### **技术指标基准线**
- **ASR准确率**：>85%（基础要求），>90%（优秀）
- **API响应时间**：P95 < 2秒
- **App崩溃率**：< 0.5%
- **提醒送达率**：> 95%

### **用户体验指标**
- **完成首次引导流程的用户比例**：> 80%
- **首次对话成功率**：> 90%
- **次日留存率**：> 60%
- **用户满意度**：NPS > 50

---

## 💡 **关键提醒**

1. **技术验证要在开发前，不是开发中** - ASR测试是第0步
2. **真实用户测试不能省** - 至少8-10名真实老年用户的深度测试
3. **监控系统和日志要在第一天就部署** - 没有数据就无法优化
4. **成本控制机制要内置到架构中** - 后期再加会很麻烦
5. **法律合规要同步进行** - 不能等产品做完再考虑合规

这份补充指南重点关注MVP阶段的实际执行，配合你原有的战略规划文档，应该能确保MVP的成功交付。有任何具体问题欢迎继续讨论！