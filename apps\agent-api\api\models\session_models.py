from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator
from datetime import datetime
from pydantic import ConfigDict

class CreateSessionRequest(BaseModel):
    # 移除userId字段，现在从JWT Token中获取
    characterId: Optional[str] = Field("default", description="选择的AI角色ID (UUID or 'default')")
    topic: Optional[str] = Field(None, max_length=255, description="会话的初始主题 (可选)")
    topicType: Optional[str] = Field(
        "custom",
        description="主题类型 ('custom', 'reflection', 'meditation', 'breathing', 'quick_start', 'preset')"
    )
    initialSystemPromptOverride: Optional[str] = Field(None, min_length=1, description="特定于此会话的初始系统提示覆盖 (高级功能，可选)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="其他元数据 (可选)")

    @field_validator('topicType')
    def validate_topic_type(cls, v):
        """验证主题类型是否符合数据库约束"""
        valid_types = ['custom', 'reflection', 'meditation', 'breathing', 'quick_start', 'preset']
        if v not in valid_types:
            raise ValueError(f"主题类型必须是以下值之一: {', '.join(valid_types)}")
        return v

class ChatSessionResponse(BaseModel):
    id: str = Field(description="会话ID (UUID)")
    user_id: str = Field(description="用户ID (UUID)")
    topic: Optional[str] = Field(None, description="会话主题")
    topic_type: Optional[str] = Field(None, description="主题类型")
    status: str = Field(description="会话状态 (active, completed, archived, deleted)")
    created_at: datetime = Field(description="创建时间 (UTC)")
    updated_at: datetime = Field(description="最后更新时间 (UTC)")
    last_message_at: Optional[datetime] = Field(None, description="最后消息时间 (UTC)")
    summary: Optional[str] = Field(None, description="会话总结")
    tags: Optional[List[str]] = Field(None, description="会话标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据，包含character_id")

    model_config = ConfigDict(populate_by_name=True) # Allows using field names like user_id directly if DB returns snake_case

class PaginationInfo(BaseModel):
    page: int = Field(description="当前页码")
    limit: int = Field(description="每页条目数")
    total: int = Field(description="总条目数")
    pages: int = Field(description="总页数")

class SessionListResponse(BaseModel):
    sessions: List[ChatSessionResponse] = Field(description="会话列表")
    pagination: PaginationInfo = Field(description="分页信息")

# Based on existing ChatMessageInput in chat_models.py but for response
class ChatMessageResponse(BaseModel):
    id: str = Field(description="消息ID")
    session_id: str = Field(description="会话ID")
    user_id: Optional[str] = Field(None, description="发送者用户ID (如果是用户消息)")
    role: str = Field(description="角色 (user, assistant, system)")
    content: str = Field(description="消息内容")
    created_at: datetime = Field(description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    # tokens: Optional[int] = Field(None, description="消息使用的token数") # Consider adding if tracked

    model_config = ConfigDict(populate_by_name=True)

class MessageHistoryResponse(BaseModel):
    messages: List[ChatMessageResponse] = Field(description="消息列表")
    pagination: PaginationInfo = Field(description="分页信息")

class EndSessionResponseData(BaseModel):
    sessionId: str = Field(description="会话ID")
    summary: Optional[str] = Field(None, description="AI生成的会话总结")
    endedAt: str = Field(description="会话结束时间 (ISO格式)")

class EndSessionResponse(BaseModel):
    success: bool = Field(description="操作是否成功")
    data: Optional[EndSessionResponseData] = Field(None, description="包含总结等数据的对象")
    message: Optional[str] = Field(None, description="操作结果的消息提示")
