# 心桥项目后端详细设计

## 5.1 内部模块设计

`agent-api`内部将由以下几个核心Python模块组成：

*   **`main.py`:** FastAPI应用的入口
*   **`api/routes/`:** 存放所有API路由文件
    *   `rtc_routes.py`: RTC会话控制路由
    *   `chat_routes.py`: 包含核心的事件处理接口，如`/rtc_event_handler`和`/text_message`
    *   其他业务路由...

*   **`api/services/`:** 存放所有业务逻辑服务
    *   **`VolcanoClientService.py`:**
        *   **职责**: 封装所有对火山引擎RTC OpenAPI的直接调用，包括V4签名、重试和错误处理。提供`start_voice_chat`, `stop_voice_chat`, `ban_user_stream`等底层API方法。
        *   **[架构要求]** 构建`StartVoiceChat`请求体时，**必须**包含`EnableConversationStateCallback`等参数，以支持前端状态同步。
    *   **`RtcSessionService.py`:** 
        *   **职责**: 管理RTC会话的生命周期，负责数据库操作和并发控制。使用`VolcanoClientService`来与火山引擎API交互。
    *   **`ChatOrchestrationService.py` (新增核心):**
        *   **职责**: 编排完整的对话流程。在检测到危机时，除了返回脚本化回复，还应**异步触发**对`VolcanoClientService`中`ban_user_stream`方法的调用。
        *   集成`MemoryService`, `LLMProxyService`, `ToolExecutorService`来处理单次用户请求。
    *   **`ToolExecutorService.py` (新增):**
        *   **职责**: 定义应用可用的工具（Functions），并执行LLM发出的工具调用请求。
    *   **`MemoryService.py`:**
        *   定义`IMemoryService`抽象接口及`Zep/Mem0`具体实现。
    *   **`PromptBuilderService.py`:**
        *   构建最终发送给LLM的提示，包含用户画像、记忆和工具定义。
    *   **`LLMProxyService.py`:** 
        *   底层LLM调用客户端，直连火山引擎LLM，支持工具调用。
        *   **`stream_chat(messages, tools, **kwargs) -> AsyncIterator[LLMResponseChunk]`**
    *   **`CrisisDetectionService.py`:** 危机关键词检测。

## 5.2 数据与持久化

* **业务数据:** 存储在Supabase PostgreSQL中
* **对话记忆:** 通过`IMemoryService`接口，持久化到外部专业的记忆服务中（Zep AI / Mem0 AI）。
* **缓存层:** 使用Redis缓存用户画像、角色信息等热点数据

## 5.3 性能与健壮性

### 核心接口性能要求 (`/api/v1/chat/rtc_event_handler`)

* **端到端延迟:** < 1.5秒 (P95)，分解如下：
  - 记忆检索延迟：< 200ms (P95)
  - LLM调用延迟：< 800ms (P95)  
  - 响应处理延迟：< 100ms (P95)
  - 其他处理时间：< 400ms (P95，包含网络传输)
* **并发处理:** 支持至少100个并发会话
* **API响应:** 单次API调用响应时间 < 200ms (P95)
* **内存使用:** 单个请求处理内存占用 < 50MB

### 实现策略

以下伪代码展示了新的、不依赖Agno的原生LLM编排流程。

```python
# In chat_routes.py
async def handle_rtc_event(
    request: RtcWebhookRequest, 
    orchestrator: ChatOrchestrationService = Depends(get_orchestrator)
):
    # 1. 解析上下文 (与旧版类似)
    custom_data = json.loads(request.custom) if request.custom else {}
    user_message = request.payload.text # 假设
    
    # 2. 核心逻辑全部委托给编排器
    final_ai_response = await orchestrator.handle_message(
        user_message=user_message,
        context=custom_data
    )

    # 3. 将最终的AI回复文本包装成火山期望的格式返回
    return build_volcano_tts_response(final_ai_response)

# In ChatOrchestrationService.py (Conceptual)
class ChatOrchestrationService:
    # ... (services injected via __init__)

    async def handle_message(self, user_message, context):
        # 0. 危机检测
        is_crisis = await self.crisis_detection_service.detect(user_message)
        if is_crisis:
            # [架构要求] 异步触发流封禁，不阻塞主流程
            room_id = context.get("roomId")
            user_id = context.get("userId")
            if room_id and user_id:
                asyncio.create_task(
                    self.volcano_client.ban_user_stream(room_id, user_id, ban_audio=True, ban_video=False)
                )
            return self._get_scripted_crisis_response()

        # 1. 检索记忆
        memory = await self.memory_service.get_memory_context(...)
        
        # 2. 构建初始消息列表和工具
        messages = self.prompt_builder.build(memory, user_message)
        tools = self.tool_executor.get_tool_definitions()

        # 3. 进入LLM-工具调用循环
        while True:
            llm_response_chunk = await self.llm_proxy.chat(messages=messages, tools=tools)
            
            # [架构要求] 对LLM返回的工具调用进行健壮性检查
            tool_calls = llm_response_chunk.get('tool_calls')
            if not isinstance(tool_calls, list):
                # 如果不是列表（如None或字典），视作没有工具调用
                tool_calls = []

            if tool_calls:
                # 如果LLM请求调用工具
                messages.append(llm_response_chunk.to_message())
                tool_results = await self.tool_executor.execute(tool_calls)
                messages.extend(tool_results.to_messages())
                # 继续循环，让LLM基于工具结果继续思考
            
            elif llm_response_chunk.is_text_content:
                # 如果是最终的文本回复，则流式返回
                self.final_response_for_memory = llm_response_chunk.full_text
                async for text_chunk in llm_response_chunk.stream_text():
                    yield text_chunk
                break # 结束循环

    async def run_post_response_tasks(self):
        # 异步保存AI回复及更新外部记忆
        await save_message_to_db(...)
        await self.memory_service.add_conversation_turn(...)
```

### 错误处理与监控

```python
@app.middleware("http")
async def error_handling_middleware(request: Request, call_next):
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"Request failed: {e}", exc_info=True)
        # 返回符合火山标准的错误响应
        return StreamingResponse(
            error_sse_stream(str(e)),
            media_type="text/event-stream"
        )
```

### 安全性增强

```python
async def verify_volcano_request(request: Request):
    # 方法1: IP白名单验证
    client_ip = request.client.host
    if client_ip not in VOLCANO_IP_WHITELIST:
        raise HTTPException(401, "Unauthorized")
        
    # 方法2: 共享密钥验证
    auth_header = request.headers.get("Authorization")
    if not auth_header or not verify_shared_secret(auth_header):
        raise HTTPException(401, "Invalid authorization")
```

## 5.4 测试策略

### 单元测试
* 对所有Service模块进行100%覆盖
* Mock掉外部依赖（DB、LLM、火山API）

### 集成测试 (关键)

```python
async def test_native_tool_calling_flow():
    # 1. 模拟一个会触发工具调用的用户输入
    mock_request = {
        "messages": [{"role": "user", "content": "帮我查一下今天北京的天气"}],
        "model": "xinqiao_v2_native",
        "custom": json.dumps({"sessionId": "test_session_tool_call"})
    }
    
    # 2. Mock掉LLM的响应，让它返回一个工具调用请求
    with mock.patch("LLMProxyService.chat") as mock_llm:
        mock_llm.side_effect = [
            LLMResponse(tool_calls=[ToolCall(name="get_weather", args={"city": "beijing"})]),
            LLMResponse(text_content="北京今天晴天，15到25度。")
        ]

        # 3. 验证ToolExecutorService是否被正确调用
        with mock.patch("ToolExecutorService.execute") as mock_executor:
            mock_executor.return_value = ToolResult(tool_name="get_weather", content="北京晴，20度")
            response = await client.post("/api/v1/chat/rtc_event_handler", json=mock_request)
            
            # 4. 验证最终返回的是基于工具结果的回复
            full_response = await response.text()
            assert "北京今天晴天" in full_response

async def test_volcano_rtc_integration():
    # 1. 模拟火山引擎的标准请求
    mock_request = {
        "messages": [{"role": "user", "content": "你好"}],
        "stream": True,
        "model": "agno_companion_v1",
        "custom": json.dumps({"sessionId": "test_session"})
    }
    
    # 2. 验证响应格式符合火山标准
    response = await client.post("/api/v1/chat/rtc_event_handler", json=mock_request)
    assert response.status_code == 200
    
    # 3. 验证返回的是JSON
    assert "application/json" in response.headers["content-type"]
    response_data = response.json()
    assert response_data["decision"] == "speak"

```

### 性能测试

```python
async def test_performance_requirements():
    # 测试端到端延迟
    start_time = time.time()
    await handle_rtc_event(test_request)
    assert time.time() - start_time < 1.5
    
    # 测试并发处理能力
    tasks = [handle_rtc_event(test_request) for _ in range(100)]
    results = await asyncio.gather(*tasks)
    assert all(result.status_code == 200 for result in results)
```

## 5.5 数据模型设计

核心数据表将包括：
- `users`: 用户基本信息
- `user_profiles`: 用户画像详情
- `user_settings`: 用户应用设置
- `characters`: AI角色配置
- `chat_sessions`: 聊天会话
- `chat_messages`: 聊天消息
- `reminders`: 提醒设置

所有API的请求和响应体都应定义为严格的Pydantic模型。

### 从现有代码迁移的最佳实践
本次架构升级的核心是将记忆层抽象并外化。在具体的开发实现过程中，强烈建议开发者团队遵循以下迁移策略，以最大化地复用现有代码资产：
- **复用服务层结构**: `apps/agent-api/api/services/`下的服务（如`session_service.py`, `user_service.py`）已经定义了清晰的业务边界。新功能的开发应尽量复用或扩展这些服务，而不是从零开始。
- **借鉴路由与依赖注入**: `apps/agent-api/api/routes/`下的路由文件展示了FastAPI依赖注入和请求验证的最佳实践。新API的实现应遵循这些模式。
- **迁移数据模型**: `apps/agent-api/api/models/`下的Pydantic模型是前后端契约的基础。在开发新功能时，应优先复用和扩展这些模型，而不是创建重复的定义。
- **保留核心业务流**: 尽管底层的`agent.arun()`调用被替换，但围绕它构建的业务流程（如错误处理、日志记录、异步任务分发）仍然具有很高的参考价值。开发者应将这些流程平移到新的`ChatOrchestrationService`中。

简而言之，我们的目标是"替换引擎，保留车身"。通过充分参考和借鉴现有代码，我们可以显著加快开发速度，并保证新旧功能在风格和质量上的一致性。

## 5.6 外部服务集成实现规范

### 5.6.1 Memory Service 集成标准

**Zep Cloud 集成要求**：
- 必须使用 `zep-cloud` SDK 的 `AsyncZep` 客户端
- 实现用户和会话的自动创建管理
- 支持语义搜索和上下文检索
- 错误处理：连接失败时优雅降级，记录详细日志

**Mem0 AI 集成要求**：
- 必须使用 `mem0` SDK 的 `AsyncMemoryClient`
- 实现多策略记忆搜索（关键词+语义）
- 支持记忆去重和相关性排序
- 异步记忆存储验证机制

**通用实现原则**：
- 所有记忆服务必须实现 `IMemoryService` 接口
- 必须支持会话级别的记忆隔离
- 必须实现连接健康检查机制
- API Key 未配置时必须优雅降级

### 5.6.2 LLM Service 集成标准

**火山引擎 LLM 集成要求**：
- 必须使用 OpenAI 兼容的 `AsyncOpenAI` 客户端
- 支持流式和非流式响应模式
- 实现工具调用（Function Calling）功能
- 支持连接测试和健康检查

**核心实现特性**：
- 请求超时设置：30秒
- 自动重试机制：3次，指数退避
- 错误分类处理：认证、限流、业务错误
- 流式响应的异步生成器模式

### 5.6.3 火山引擎 API 签名规范

**签名算法实现**：
- 算法：HMAC-SHA256
- 服务标识：rtc
- 区域：cn-beijing
- 版本：2024-12-01

**签名流程**：
1. 创建规范化请求（方法、路径、查询参数、头部、载荷）
2. 构建待签名字符串（算法、时间戳、凭证范围、请求哈希）
3. 计算签名密钥（日期、区域、服务、请求标识）
4. 生成最终签名和授权头

**安全要求**：
- 密钥必须通过环境变量传递
- 签名过程中的敏感信息不得记录日志
- 必须实现时间戳验证防重放攻击

### 5.6.4 集成测试策略

**单元测试覆盖**：
- Memory Service：85%+ 覆盖率
- LLM Service：80%+ 覆盖率
- 签名算法：100% 覆盖率

**集成测试场景**：
```python
# 真实服务连接测试
async def test_real_service_integration():
    # 1. 测试 Memory Service 真实连接
    memory_service = await get_memory_service("zep")
    assert await memory_service.test_connection()
    
    # 2. 测试 LLM Service 真实连接
    llm_service = await get_llm_proxy_service()
    assert await llm_service.test_connection()
    
    # 3. 测试端到端对话流程
    response = await llm_service.call_llm(
        messages=[{"role": "user", "content": "测试消息"}]
    )
    assert response and len(response) > 0
```

**性能基准测试**：
- Memory 检索：< 200ms (P95)
- LLM 调用：< 800ms (P95)
- 签名生成：< 10ms (P95)
- 端到端延迟：< 1.5s (P95) 