# api/middleware/rate_limit.py
"""
API频率限制中间件
实现基于IP地址和用户的请求频率控制
"""
import time
from collections import defaultdict, deque
from typing import Dict, Deque
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import asyncio
import os

from api.settings import logger

# 配置 - P1性能调优：放宽频率限制提升多用户体验
RATE_LIMIT_PER_MINUTE = int(os.getenv("API_RATE_LIMIT_PER_MINUTE", "500"))  # 提升到500/分钟
RATE_LIMIT_WINDOW = 60  # 60秒窗口

class RateLimiter:
    """简单的滑动窗口频率限制器"""

    def __init__(self):
        # 存储每个客户端的请求时间戳
        self.requests: Dict[str, Deque[float]] = defaultdict(lambda: deque())
        self.lock = asyncio.Lock()

    async def is_allowed(self, client_id: str, limit: int = RATE_LIMIT_PER_MINUTE, window: int = RATE_LIMIT_WINDOW) -> bool:
        """检查客户端是否允许发送请求"""
        async with self.lock:
            now = time.time()
            client_requests = self.requests[client_id]

            # 清理过期的请求记录
            while client_requests and client_requests[0] <= now - window:
                client_requests.popleft()

            # 检查是否超过限制
            if len(client_requests) >= limit:
                return False

            # 记录当前请求
            client_requests.append(now)
            return True

    async def get_remaining_requests(self, client_id: str, limit: int = RATE_LIMIT_PER_MINUTE, window: int = RATE_LIMIT_WINDOW) -> int:
        """获取剩余请求次数"""
        async with self.lock:
            now = time.time()
            client_requests = self.requests[client_id]

            # 清理过期的请求记录
            while client_requests and client_requests[0] <= now - window:
                client_requests.popleft()

            return max(0, limit - len(client_requests))

# 全局频率限制器实例
rate_limiter = RateLimiter()

async def rate_limit_middleware(request: Request, call_next):
    """频率限制中间件"""
    try:
        # 获取客户端标识（IP地址）
        client_ip = request.client.host if request.client else "unknown"

        # 检查是否允许请求
        if not await rate_limiter.is_allowed(client_ip):
            logger.warning(f"Rate limit exceeded for client: {client_ip}")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {RATE_LIMIT_PER_MINUTE} requests per minute"
                },
                headers={
                    "X-RateLimit-Limit": str(RATE_LIMIT_PER_MINUTE),
                    "X-RateLimit-Window": str(RATE_LIMIT_WINDOW),
                    "X-RateLimit-Remaining": "0",
                    "Retry-After": str(RATE_LIMIT_WINDOW)
                }
            )

        # 执行请求
        response = await call_next(request)

        # 添加频率限制头信息
        remaining = await rate_limiter.get_remaining_requests(client_ip)
        response.headers["X-RateLimit-Limit"] = str(RATE_LIMIT_PER_MINUTE)
        response.headers["X-RateLimit-Window"] = str(RATE_LIMIT_WINDOW)
        response.headers["X-RateLimit-Remaining"] = str(remaining)

        return response

    except Exception as e:
        logger.exception(f"Error in rate limit middleware: {e}")
        # 如果中间件出错，不阻止请求继续
        return await call_next(request)
