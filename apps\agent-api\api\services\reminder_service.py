"""
提醒服务 - 故事1.6-B核心实现

处理提醒的CRUD操作，集成Function Calling工具调用，
实现架构师要求的时间解析健壮性和记忆服务集成。

关键特性：
1. 时间解析健壮性 - 使用arrow库，统一UTC存储
2. 工具调用集成 - 支持create_reminder_from_tool方法
3. 记忆服务集成 - 记录用户提醒行为和偏好
4. 数据一致性保障 - 完整CRUD操作和事务处理
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
import json
import uuid

import arrow
from arrow.parser import ParserError

from api.models.schema_models import (
    Reminder, ReminderPattern, UserReminderSettings,
    CreateReminderRequest, UpdateReminderRequest, ToolResult
)
from api.services.memory_service import IMemoryService, get_memory_service
from api.settings import logger
from db.supabase_init import get_supabase_client
from fastapi import HTTPException, status


class ReminderService:
    """提醒服务 - 处理提醒相关的所有业务逻辑"""

    def __init__(
        self,
        memory_service: Optional[IMemoryService] = None
    ):
        self.memory_service = memory_service  # 延迟初始化
        self.logger = logging.getLogger(__name__)

    async def _get_memory_service(self) -> IMemoryService:
        """获取记忆服务实例（延迟初始化）"""
        if self.memory_service is None:
            self.memory_service = await get_memory_service()
        return self.memory_service

    async def create_reminder_from_tool(
        self,
        user_id: str,
        arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        从Function Calling工具调用创建提醒

        实现架构师建议：
        1. 时间解析健壮性 - 使用arrow库，支持自然语言fallback
        2. 数据一致性保障 - 完整的异常处理和事务回滚
        3. 记忆服务集成 - 记录用户提醒行为

        Args:
            user_id: 用户ID
            arguments: 工具调用参数，包含content和time

        Returns:
            工具执行结果字典
        """
        try:
            # 提取参数
            content = arguments.get('content', '').strip()
            time_str = arguments.get('time', '').strip()

            if not content:
                return {
                    "success": False,
                    "message": "提醒内容不能为空",
                    "error": "missing_content"
                }

            if not time_str:
                return {
                    "success": False,
                    "message": "提醒时间不能为空",
                    "error": "missing_time"
                }

            # 架构师要求：时间解析健壮性 - 使用arrow库
            parsed_time = await self.parse_time_with_fallback(time_str)
            if not parsed_time:
                return {
                    "success": False,
                    "message": f"无法解析时间格式: {time_str}。请提供更清晰的时间表达，如'2024-12-20 15:00'或'明天下午3点'",
                    "error": "invalid_time_format"
                }

            # 创建提醒数据
            request_data = CreateReminderRequest(
                content=content,
                reminder_time=parsed_time
            )

            # 创建提醒
            reminder = await self.create_reminder(user_id, request_data)

            # 架构师要求：记忆服务集成 - 记录用户提醒行为
            await self._record_reminder_memory(user_id, reminder, "created")

            self.logger.info(
                f"Function Calling成功创建提醒 - 用户: {user_id}, "
                f"提醒ID: {reminder.id}, 内容: {content}, 时间: {parsed_time}"
            )

            return {
                "success": True,
                "message": f"提醒已成功创建！我会在{self._format_time_for_user(parsed_time)}提醒您：{content}",
                "reminder_id": reminder.id,
                "reminder_time": parsed_time.isoformat(),
                "content": content
            }

        except Exception as e:
            self.logger.error(f"Function Calling创建提醒失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": "创建提醒时遇到了技术问题，请稍后再试",
                "error": str(e)
            }

    async def parse_time_with_fallback(self, time_str: str) -> Optional[datetime]:
        """
        时间解析健壮性 - 架构师重点关注

        支持多种时间格式：
        1. ISO 8601格式：2024-12-20T15:00:00Z
        2. 自然语言：明天下午3点、后天上午9点
        3. 相对时间：1小时后、30分钟后

        统一转换为UTC时间存储

        Args:
            time_str: 时间字符串

        Returns:
            解析后的UTC时间，失败返回None
        """
        if not time_str:
            return None

        try:
            # 1. 尝试Arrow库中文locale解析 - 架构师建议
            try:
                # 使用中文locale和Asia/Shanghai时区
                parsed = arrow.get(time_str, locale='zh_CN', tzinfo='Asia/Shanghai')
                # 确保转换为UTC时间
                return parsed.to('UTC').datetime.replace(tzinfo=timezone.utc)
            except ParserError:
                pass

            # 2. 尝试ISO 8601格式解析
            try:
                parsed = arrow.get(time_str)
                utc_time = parsed.to('UTC').datetime
                return utc_time.replace(tzinfo=timezone.utc)
            except ParserError:
                pass

            # 3. 尝试常见格式解析
            common_formats = [
                'YYYY-MM-DD HH:mm:ss',
                'YYYY-MM-DD HH:mm',
                'YYYY/MM/DD HH:mm',
                'MM-DD HH:mm',
                'HH:mm'
            ]

            for fmt in common_formats:
                try:
                    parsed = arrow.get(time_str, fmt)
                    # 如果只是时间没有日期，默认为今天
                    if fmt == 'HH:mm':
                        now = arrow.now()
                        parsed = parsed.replace(year=now.year, month=now.month, day=now.day)
                        # 如果时间已过，设为明天
                        if parsed < now:
                            parsed = parsed.shift(days=1)
                    utc_time = parsed.to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)
                except (ParserError, ValueError):
                    continue

            # 4. 自然语言解析fallback
            natural_time = await self._parse_natural_language_time(time_str)
            if natural_time:
                return natural_time

            return None

        except Exception as e:
            self.logger.error(f"时间解析异常: {time_str}, 错误: {str(e)}")
            return None

    async def _parse_natural_language_time(self, time_str: str) -> Optional[datetime]:
        """
        自然语言时间解析 - fallback机制

        支持中文自然语言表达：
        - 上午/下午 + 时间（如：上午10点、下午3点）
        - 明天/后天 + 时间
        - 下周/下个月 + 时间
        - 相对时间表达

        Args:
            time_str: 自然语言时间字符串

        Returns:
            解析后的UTC时间，失败返回None
        """
        try:
            time_str = time_str.strip().lower()
            now = arrow.now('Asia/Shanghai')  # 使用本地时区

            # 首先处理纯时间表达（如"上午10点"、"下午3点"）
            # 架构师重点关注：边界情况 - 今天已过的时间应推导到未来
            if ('上午' in time_str or '下午' in time_str or '点' in time_str) and \
               '明天' not in time_str and '后天' not in time_str and '下周' not in time_str:

                parsed_time = self._parse_time_of_day(time_str)
                if parsed_time:
                    # 构建今天的目标时间
                    target_time = now.replace(
                        hour=parsed_time['hour'],
                        minute=parsed_time['minute'],
                        second=0,
                        microsecond=0
                    )

                    # 如果时间已过，设为明天（架构师风险点）
                    if target_time < now:
                        self.logger.debug(f"时间已过，调整到明天: {time_str}")
                        target_time = target_time.shift(days=1)

                    # 时区安全转换：Asia/Shanghai -> UTC
                    utc_time = target_time.to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)

            # 明天的情况
            if '明天' in time_str:
                target_date = now.shift(days=1)
                time_part = time_str.replace('明天', '').strip()
                parsed_time = self._parse_time_of_day(time_part)
                if parsed_time:
                    utc_time = target_date.replace(
                        hour=parsed_time['hour'],
                        minute=parsed_time['minute']
                    ).to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)
                else:
                    # 如果没有指定具体时间，使用当前时间
                    utc_time = target_date.replace(
                        hour=now.hour,
                        minute=now.minute,
                        second=0,
                        microsecond=0
                    ).to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)

            # 后天的情况
            if '后天' in time_str:
                target_date = now.shift(days=2)
                time_part = time_str.replace('后天', '').strip()
                parsed_time = self._parse_time_of_day(time_part)
                if parsed_time:
                    utc_time = target_date.replace(
                        hour=parsed_time['hour'],
                        minute=parsed_time['minute']
                    ).to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)

            # 今晚的情况
            if '今晚' in time_str or '晚上' in time_str:
                time_part = time_str.replace('今晚', '').replace('晚上', '').strip()
                parsed_time = self._parse_time_of_day(time_part)
                if parsed_time:
                    hour = parsed_time['hour']
                    # 如果小于12，认为是PM
                    if hour < 12:
                        hour += 12
                    utc_time = now.replace(
                        hour=hour,
                        minute=parsed_time['minute']
                    ).to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)

            # 后天晚上的情况
            if '后天晚上' in time_str:
                target_date = now.shift(days=2)
                time_part = time_str.replace('后天晚上', '').strip()
                parsed_time = self._parse_time_of_day(time_part) or {'hour': 20, 'minute': 0}  # 默认晚上8点
                hour = parsed_time['hour']
                if hour < 12:  # 确保是晚上
                    hour += 12
                utc_time = target_date.replace(
                    hour=hour,
                    minute=parsed_time['minute']
                ).to('UTC').datetime
                return utc_time.replace(tzinfo=timezone.utc)

            # 下个月的情况（如"下个月15号"）
            if '下个月' in time_str:
                import re
                day_match = re.search(r'(\d+)号', time_str)
                if day_match:
                    day = int(day_match.group(1))
                    target_date = now.shift(months=1).replace(day=day)
                    time_part = time_str.replace('下个月', '').replace(f'{day}号', '').strip()
                    parsed_time = self._parse_time_of_day(time_part) or {'hour': 9, 'minute': 0}  # 默认上午9点
                    utc_time = target_date.replace(
                        hour=parsed_time['hour'],
                        minute=parsed_time['minute']
                    ).to('UTC').datetime
                    return utc_time.replace(tzinfo=timezone.utc)

            # 星期解析（周一、周二等）
            weekday_map = {
                '一': 0, '二': 1, '三': 2, '四': 3, '五': 4, '六': 5, '日': 6, '天': 6
            }

            if '下周' in time_str:
                for day_name, day_num in weekday_map.items():
                    if f'下周{day_name}' in time_str:
                        # 计算下周目标日期
                        days_ahead = day_num - now.weekday()
                        if days_ahead <= 0:  # 目标是下周
                            days_ahead += 7
                        days_ahead += 7  # 下周，再加7天

                        target_date = now.shift(days=days_ahead)
                        time_part = time_str.replace(f'下周{day_name}', '').strip()
                        parsed_time = self._parse_time_of_day(time_part)
                        if parsed_time:
                            utc_time = target_date.replace(
                                hour=parsed_time['hour'],
                                minute=parsed_time['minute']
                            ).to('UTC').datetime
                            return utc_time.replace(tzinfo=timezone.utc)

            # 本周的情况（周一、周二等）- 架构师重点关注边界处理
            for day_name, day_num in weekday_map.items():
                if f'周{day_name}' in time_str and '下周' not in time_str:
                    # 计算目标星期几与当前星期几的差值
                    current_weekday = now.weekday()  # 0=周一, 4=周五
                    days_ahead = day_num - current_weekday

                    # 如果是本周已过的日期或当天，设为下周
                    if days_ahead <= 0:
                        days_ahead += 7
                        self.logger.debug(f"本周已过的星期，调整到下周: {time_str}, days_ahead={days_ahead}")

                    target_date = now.shift(days=days_ahead)
                    time_part = time_str.replace(f'周{day_name}', '').strip()
                    parsed_time = self._parse_time_of_day(time_part)
                    if parsed_time:
                        # 时区安全转换：Asia/Shanghai -> UTC
                        utc_time = target_date.replace(
                            hour=parsed_time['hour'],
                            minute=parsed_time['minute']
                        ).to('UTC').datetime
                        return utc_time.replace(tzinfo=timezone.utc)

            # 相对时间解析
            relative_time = self._parse_relative_time(time_str)
            if relative_time:
                utc_time = relative_time.to('UTC').datetime
                return utc_time.replace(tzinfo=timezone.utc)

            return None

        except Exception as e:
            self.logger.debug(f"自然语言时间解析失败: {time_str}, 错误: {str(e)}")
            return None

    def _parse_time_of_day(self, time_str: str) -> Optional[Dict[str, int]]:
        """解析一天中的时间部分"""
        if not time_str:
            return {'hour': 9, 'minute': 0}  # 默认上午9点

        time_str = time_str.strip()

        # 处理上午/下午
        is_pm = False
        if '下午' in time_str or 'pm' in time_str.lower():
            is_pm = True
            time_str = time_str.replace('下午', '').replace('pm', '').strip()
        elif '上午' in time_str or 'am' in time_str.lower():
            time_str = time_str.replace('上午', '').replace('am', '').strip()

        # 提取数字
        import re
        time_match = re.search(r'(\d+)(?:[:：](\d+))?', time_str)
        if time_match:
            hour = int(time_match.group(1))
            minute = int(time_match.group(2)) if time_match.group(2) else 0

            # 处理12小时制
            if is_pm and hour < 12:
                hour += 12
            elif not is_pm and hour == 12:
                hour = 0

            # 验证有效性
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                return {'hour': hour, 'minute': minute}

        return None

    def _parse_relative_time(self, time_str: str) -> Optional[arrow.Arrow]:
        """解析相对时间表达"""
        import re

        # 匹配数字+时间单位
        patterns = [
            (r'(\d+)\s*小时后', 'hours'),
            (r'(\d+)\s*分钟后', 'minutes'),
            (r'(\d+)\s*天后', 'days'),
            (r'(\d+)\s*周后', 'weeks'),
        ]

        for pattern, unit in patterns:
            match = re.search(pattern, time_str)
            if match:
                amount = int(match.group(1))
                # 使用与其他方法一致的时区
                return arrow.now('Asia/Shanghai').shift(**{unit: amount})

        return None

    async def parse_natural_time(self, time_str: str) -> Optional[datetime]:
        """
        公开的自然语言时间解析接口 - 供测试使用

        Args:
            time_str: 自然语言时间字符串

        Returns:
            解析后的UTC时间（使用标准timezone.utc）
        """
        parsed_time = await self.parse_time_with_fallback(time_str)
        if parsed_time:
            # 确保使用标准的timezone.utc，而不是arrow的tzutc()
            if parsed_time.tzinfo != timezone.utc:
                # 转换为UTC时间，并使用标准的timezone.utc
                utc_time = parsed_time.astimezone(timezone.utc)
                return utc_time.replace(tzinfo=timezone.utc)
            return parsed_time
        return None

    def _format_time_for_user(self, dt: datetime) -> str:
        """格式化时间为用户友好的显示"""
        try:
            arr = arrow.get(dt)
            local_time = arr.to('Asia/Shanghai')  # 默认使用中国时区

            now = arrow.now('Asia/Shanghai')

            # 如果是今天
            if local_time.date() == now.date():
                return f"今天{local_time.format('HH:mm')}"
            # 如果是明天
            elif local_time.date() == now.shift(days=1).date():
                return f"明天{local_time.format('HH:mm')}"
            # 其他日期
            else:
                return local_time.format('MM月DD日 HH:mm')
        except Exception:
            return dt.strftime('%Y-%m-%d %H:%M')

    async def create_reminder(
        self,
        user_id: str,
        request: CreateReminderRequest
    ) -> Reminder:
        """
        创建提醒

        Args:
            user_id: 用户ID
            request: 创建提醒请求

        Returns:
            创建的提醒对象
        """
        try:
            client = await get_supabase_client()
            reminder_id = str(uuid.uuid4())
            now = datetime.now(timezone.utc)

            # 构建数据库插入数据
            insert_data = {
                "id": reminder_id,
                "user_id": user_id,
                "content": request.content,
                "reminder_time": request.reminder_time.isoformat(),
                "status": "pending",
                "pattern_id": None,
                "function_call_payload": None,
                "created_at": now.isoformat(),
                "updated_at": now.isoformat()
            }

            # 插入数据库
            response = await client.table("reminders").insert(insert_data).execute()

            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create reminder"
                )

            created_reminder = response.data[0]

            # 返回Reminder对象
            reminder = Reminder(
                id=created_reminder["id"],
                user_id=created_reminder["user_id"],
                content=created_reminder["content"],
                reminder_time=datetime.fromisoformat(created_reminder["reminder_time"].replace('Z', '+00:00')),
                status=created_reminder["status"],
                pattern_id=created_reminder.get("pattern_id"),
                created_at=datetime.fromisoformat(created_reminder["created_at"].replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(created_reminder["updated_at"].replace('Z', '+00:00'))
            )

            self.logger.info(f"创建提醒成功 - ID: {reminder_id}, 用户: {user_id}")
            return reminder

        except Exception as e:
            self.logger.error(f"创建提醒失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建提醒失败: {str(e)}"
            )

    async def get_user_reminders(
        self,
        user_id: str,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 10,
        offset: int = 0
    ) -> List[Reminder]:
        """
        获取用户的提醒列表

        Args:
            user_id: 用户ID
            status: 提醒状态过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤
            limit: 返回结果数量限制
            offset: 分页偏移量

        Returns:
            提醒列表
        """
        try:
            client = await get_supabase_client()

            # 构建查询
            query = client.table("reminders").select("*").eq("user_id", user_id)

            # 添加状态过滤
            if status:
                query = query.eq("status", status)

            # 添加日期过滤
            if start_date:
                query = query.gte("reminder_time", start_date.isoformat())
            if end_date:
                query = query.lte("reminder_time", end_date.isoformat())

            # 按时间排序并应用分页
            query = query.order("reminder_time", desc=False).range(offset, offset + limit - 1)

            response = await query.execute()

            # 转换为Reminder对象
            reminders = []
            for item in response.data:
                reminder = Reminder(
                    id=item["id"],
                    user_id=item["user_id"],
                    content=item["content"],
                    reminder_time=datetime.fromisoformat(item["reminder_time"].replace('Z', '+00:00')),
                    status=item["status"],
                    pattern_id=item.get("pattern_id"),
                    created_at=datetime.fromisoformat(item["created_at"].replace('Z', '+00:00')),
                    updated_at=datetime.fromisoformat(item["updated_at"].replace('Z', '+00:00'))
                )
                reminders.append(reminder)

            self.logger.info(f"查询用户提醒 - 用户: {user_id}, 返回{len(reminders)}条记录")
            return reminders

        except Exception as e:
            self.logger.error(f"查询提醒失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"查询提醒失败: {str(e)}"
            )

    async def update_reminder(
        self,
        reminder_id: str,
        user_id: str,
        request: UpdateReminderRequest
    ) -> Optional[Reminder]:
        """
        更新提醒

        Args:
            reminder_id: 提醒ID
            user_id: 用户ID
            request: 更新请求

        Returns:
            更新后的提醒对象，失败返回None
        """
        try:
            client = await get_supabase_client()

            # 构建更新数据
            update_data = {
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            if request.content is not None:
                update_data["content"] = request.content
            if request.reminder_time is not None:
                update_data["reminder_time"] = request.reminder_time.isoformat()
            if request.status is not None:
                update_data["status"] = request.status

            # 执行更新
            response = await client.table("reminders").update(update_data).eq("id", reminder_id).eq("user_id", user_id).execute()

            if not response.data:
                self.logger.warning(f"更新提醒失败 - ID: {reminder_id}, 用户: {user_id}")
                return None

            updated_item = response.data[0]

            # 返回更新后的对象
            reminder = Reminder(
                id=updated_item["id"],
                user_id=updated_item["user_id"],
                content=updated_item["content"],
                reminder_time=datetime.fromisoformat(updated_item["reminder_time"].replace('Z', '+00:00')),
                status=updated_item["status"],
                pattern_id=updated_item.get("pattern_id"),
                created_at=datetime.fromisoformat(updated_item["created_at"].replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(updated_item["updated_at"].replace('Z', '+00:00'))
            )

            self.logger.info(f"更新提醒成功 - ID: {reminder_id}, 用户: {user_id}")
            return reminder

        except Exception as e:
            self.logger.error(f"更新提醒失败: {str(e)}", exc_info=True)
            return None

    async def count_user_reminders(
        self,
        user_id: str,
        status: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> int:
        """
        计算用户提醒数量

        Args:
            user_id: 用户ID
            status: 提醒状态过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            提醒总数
        """
        try:
            client = await get_supabase_client()

            # 构建查询
            query = client.table("reminders").select("id", count="exact").eq("user_id", user_id)

            # 添加状态过滤
            if status:
                query = query.eq("status", status)

            # 添加日期过滤
            if start_date:
                query = query.gte("reminder_time", start_date)
            if end_date:
                query = query.lte("reminder_time", end_date)

            response = await query.execute()
            return response.count

        except Exception as e:
            self.logger.error(f"计算提醒数量失败: {str(e)}", exc_info=True)
            return 0

    async def get_reminder_by_id(
        self,
        reminder_id: str,
        user_id: str
    ) -> Optional[Reminder]:
        """
        根据ID获取提醒

        Args:
            reminder_id: 提醒ID
            user_id: 用户ID

        Returns:
            提醒对象，失败返回None
        """
        try:
            client = await get_supabase_client()

            # 查询提醒
            response = await client.table("reminders").select("*").eq("id", reminder_id).eq("user_id", user_id).execute()

            if not response.data:
                return None

            item = response.data[0]

            # 转换为Reminder对象
            reminder = Reminder(
                id=item["id"],
                user_id=item["user_id"],
                content=item["content"],
                reminder_time=datetime.fromisoformat(item["reminder_time"].replace('Z', '+00:00')),
                status=item["status"],
                pattern_id=item.get("pattern_id"),
                created_at=datetime.fromisoformat(item["created_at"].replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(item["updated_at"].replace('Z', '+00:00'))
            )

            return reminder

        except Exception as e:
            self.logger.error(f"获取提醒失败: {str(e)}", exc_info=True)
            return None

    async def delete_reminder(
        self,
        reminder_id: str,
        user_id: str
    ) -> bool:
        """
        删除提醒

        Args:
            reminder_id: 提醒ID
            user_id: 用户ID

        Returns:
            删除是否成功
        """
        try:
            client = await get_supabase_client()

            # 执行删除
            response = await client.table("reminders").delete().eq("id", reminder_id).eq("user_id", user_id).execute()

            if response.data:
                self.logger.info(f"删除提醒成功 - ID: {reminder_id}, 用户: {user_id}")
                return True
            else:
                self.logger.warning(f"删除提醒失败，可能不存在 - ID: {reminder_id}, 用户: {user_id}")
                return False

        except Exception as e:
            self.logger.error(f"删除提醒失败: {str(e)}", exc_info=True)
            return False

    async def _record_reminder_memory(
        self,
        user_id: str,
        reminder: Reminder,
        action: str
    ) -> None:
        """
        记录提醒行为到记忆系统 - 架构师要求的记忆服务集成

        Args:
            user_id: 用户ID
            reminder: 提醒对象
            action: 操作类型（created, updated, deleted）
        """
        try:
            memory_service = await self._get_memory_service()
            if memory_service is None:
                return

            memory_content = f"用户{action}了一个提醒：{reminder.content}，" \
                           f"时间：{self._format_time_for_user(reminder.reminder_time)}"

            metadata = {
                "type": "reminder_action",
                "action": action,
                "reminder_id": reminder.id,
                "reminder_content": reminder.content,
                "reminder_time": reminder.reminder_time.isoformat(),
                "user_preference_data": {
                    "preferred_time_format": "natural_language",
                    "reminder_pattern": "single_event"
                }
            }

            # 生成一个临时session_id用于记忆记录
            temp_session_id = f"reminder_{reminder.id}"

            await memory_service.add_memory(
                user_id=user_id,
                session_id=temp_session_id,
                human_message=f"创建提醒：{reminder.content}",
                assistant_message=memory_content,
                metadata=metadata
            )

            self.logger.info(f"提醒行为已记录到记忆系统 - 用户: {user_id}, 操作: {action}")

        except Exception as e:
            # 架构师要求：记忆服务失败不应影响提醒创建
            self.logger.warning(f"记录提醒记忆失败，但不影响提醒操作: {str(e)}")


# 全局实例应该在需要时创建，并注入依赖
_reminder_service_instance: Optional[ReminderService] = None
_reminder_service_lock = asyncio.Lock()

async def get_reminder_service() -> ReminderService:
    """
    获取提醒服务实例，并确保依赖已注入

    架构师建议：实现优雅降级机制，记忆服务失败时使用无记忆模式
    """
    global _reminder_service_instance
    if _reminder_service_instance is None:
        async with _reminder_service_lock:
            if _reminder_service_instance is None:
                try:
                    memory_service = await get_memory_service()
                    _reminder_service_instance = ReminderService(memory_service=memory_service)
                    logger.info("ReminderService初始化成功")
                except Exception as e:
                    logger.warning(f"记忆服务初始化失败，使用无记忆模式: {e}")
                    _reminder_service_instance = ReminderService(memory_service=None)
    return _reminder_service_instance
