#!/usr/bin/env python3
"""
顺序运行API测试脚本

此脚本演示了新的测试数据共享功能：
1. 每个测试会自动从共享配置中读取前面测试的结果
2. 每个测试完成后会将重要数据保存到共享配置
3. 运行01_health_test会清理所有历史数据，开始新的测试轮次

使用示例：
1. 运行完整测试流程：
   python run_sequential_tests.py

2. 从指定测试开始：
   python run_sequential_tests.py --start-from auth

3. 只运行特定测试组：
   python run_sequential_tests.py --tests health auth user

4. 查看共享配置状态：
   python run_sequential_tests.py --show-config
"""

import asyncio
import argparse
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester

# 测试执行顺序（重要：顺序不能随意调整）
SEQUENTIAL_TESTS = [
    ("health", "01_health_test", "🏥 健康检查 - 清理历史数据，开始新测试"),
    ("auth", "02_auth_test", "🔐 认证服务 - 用户登录和引导流程"),
    ("user", "03_user_test", "👤 用户管理 - 使用认证信息更新用户画像"),
    ("character", "04_character_test", "🎭 角色管理 - 获取和验证角色信息"),
    ("session", "05_session_test", "💬 会话管理 - 使用绑定的角色创建会话"),
    ("chat", "06_chat_test", "🗨️ 聊天服务 - 使用已创建的会话进行聊天"),
    ("reminder", "07_reminder_test", "⏰ 提醒管理 - 创建和管理用户提醒"),
    ("rtc", "08_rtc_test", "📞 RTC会话 - 实时通信会话管理"),
    ("rtc_webhook", "09_rtc_webhook_test", "🔗 RTC Webhook - 事件回调处理"),
]

class SequentialTestRunner:
    """顺序测试运行器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        self.base_url = base_url
        self.config_file = Path("e2e_tests/shared_config.json")

    def show_shared_config(self):
        """显示当前共享配置状态"""
        if not self.config_file.exists():
            print("📄 共享配置文件不存在")
            return

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            print("📄 当前共享配置状态:")
            print("=" * 60)

            # 基本信息
            print(f"设备ID: {config.get('device_id', '未设置')}")
            print(f"平台: {config.get('platform', '未设置')}")
            print(f"昵称: {config.get('nickname', '未设置')}")

            # 认证信息
            if "auth" in config:
                auth = config["auth"]
                print(f"用户ID: {auth.get('user_id', '未设置')}")
                print(f"认证状态: {'已认证' if auth.get('access_token') else '未认证'}")
                print(f"认证时间: {auth.get('updated_at', '未知')}")

            # 角色信息
            if "character_id" in config:
                print(f"角色ID: {config['character_id']}")

            # 测试结果
            if "test_results" in config:
                print(f"\n📊 已完成的测试:")
                for test_name, result in config["test_results"].items():
                    completed_time = result.get("completed_at", "未知时间")
                    print(f"  ✅ {test_name}: {completed_time}")

            print("=" * 60)

        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")

    async def run_single_test(self, test_name: str, module_name: str, description: str):
        """运行单个测试"""
        print(f"\n{'='*80}")
        print(f"{description}")
        print(f"{'='*80}")

        try:
            # 动态导入测试模块
            module = __import__(module_name)

            # 获取测试类名 - 处理特殊命名情况
            class_name_mapping = {
                "rtc": "RTCTester",
                "rtc_webhook": "RTCWebhookTester"
            }
            test_class_name = class_name_mapping.get(test_name, f"{test_name.title()}Tester")

            if hasattr(module, test_class_name):
                test_class = getattr(module, test_class_name)

                # 运行测试
                async with test_class(self.base_url) as tester:
                    await tester.run_tests()

                    success_rate = (tester.test_results["passed"] / tester.test_results["total"] * 100) if tester.test_results["total"] > 0 else 0

                    if success_rate >= 80:  # 成功率阈值
                        print(f"✅ {test_name.upper()} 测试通过 (成功率: {success_rate:.1f}%)")
                        return True
                    else:
                        print(f"❌ {test_name.upper()} 测试失败 (成功率: {success_rate:.1f}%)")
                        return False
            else:
                print(f"❌ 找不到测试类: {test_class_name}")
                return False

        except Exception as e:
            print(f"❌ {test_name.upper()} 测试异常: {e}")
            return False

    async def run_sequential_tests(self, test_names: list = None, start_from: str = None):
        """顺序运行测试"""
        print(f"🚀 开始顺序API测试")
        print(f"📍 目标服务器: {self.base_url}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 确定要运行的测试
        if test_names:
            tests_to_run = [(name, module, desc) for name, module, desc in SEQUENTIAL_TESTS if name in test_names]
        else:
            tests_to_run = SEQUENTIAL_TESTS

        # 如果指定了起始测试，跳过之前的测试
        if start_from:
            start_index = next((i for i, (name, _, _) in enumerate(tests_to_run) if name == start_from), 0)
            tests_to_run = tests_to_run[start_index:]
            print(f"📍 从 {start_from} 测试开始")

        print(f"📋 将运行 {len(tests_to_run)} 个测试")

        success_count = 0
        for i, (test_name, module_name, description) in enumerate(tests_to_run, 1):
            print(f"\n🎯 [{i}/{len(tests_to_run)}] 准备运行: {test_name}")

            success = await self.run_single_test(test_name, module_name, description)
            if success:
                success_count += 1
            else:
                print(f"⚠️ {test_name} 测试失败，但继续执行后续测试")

            # 测试间隔
            if i < len(tests_to_run):
                await asyncio.sleep(2.0)

        # 最终汇总
        print(f"\n{'='*80}")
        print(f"📊 顺序测试完成汇总")
        print(f"{'='*80}")
        print(f"总测试数: {len(tests_to_run)}")
        print(f"成功: {success_count}")
        print(f"失败: {len(tests_to_run) - success_count}")
        print(f"成功率: {success_count / len(tests_to_run) * 100:.1f}%")

        if success_count == len(tests_to_run):
            print("🎉 所有测试都成功完成！")
        else:
            print("⚠️ 部分测试失败，请检查日志")

        print(f"📄 共享配置文件: {self.config_file}")
        print(f"📁 详细日志目录: e2e_tests/logs/")
        print(f"{'='*80}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='顺序API测试运行器')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')
    parser.add_argument('--tests', '-t', nargs='+',
                        choices=[name for name, _, _ in SEQUENTIAL_TESTS],
                        help='指定要运行的测试（保持顺序）')
    parser.add_argument('--start-from', '-s',
                        choices=[name for name, _, _ in SEQUENTIAL_TESTS],
                        help='从指定测试开始运行')
    parser.add_argument('--show-config', '-c', action='store_true',
                        help='显示当前共享配置状态')

    args = parser.parse_args()

    runner = SequentialTestRunner(args.url)

    # 显示配置状态
    if args.show_config:
        runner.show_shared_config()
        return

    # 运行测试
    await runner.run_sequential_tests(args.tests, args.start_from)

if __name__ == "__main__":
    asyncio.run(main())
