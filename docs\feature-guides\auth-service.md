# 认证服务功能说明文档

## 功能概述

认证服务提供完整的无感身份认证和用户管理能力，支持匿名用户自动创建、JWT Token管理、用户画像CRUD操作、AI角色配置管理等核心功能。该服务基于Supabase Auth构建，确保认证流程的安全性和可靠性。

### 主要特性

- **无感认证**：基于设备指纹自动创建匿名用户，无需用户主动注册
- **JWT Token管理**：支持访问令牌和刷新令牌的生成、验证和刷新
- **用户画像管理**：提供完整的用户信息CRUD操作
- **AI角色管理**：支持AI角色配置的查询和用户绑定
- **引导流程支持**：处理用户引导过程中收集的所有信息
- **安全防护**：API频率限制、JWT验证、数据隔离等安全机制

## 核心API端点

### 认证相关接口

#### 1. 匿名用户登录
```http
POST /api/v1/auth/anonymous-login
Content-Type: application/json

{
  "device_info": {
    "device_id": "unique-device-identifier",
    "platform": "ios|android", 
    "app_version": "1.0.0"
  }
}
```

**响应示例**:
```json
{
  "user": {
    "id": "uuid-user-id",
    "device_fingerprint": "hashed-fingerprint",
    "created_at": "2025-01-21T10:00:00Z"
  },
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 1800
}
```

#### 2. Token刷新
```http
POST /api/v1/auth/refresh-token
Content-Type: application/json

{
  "refresh_token": "your-refresh-token"
}
```

**响应示例**:
```json
{
  "access_token": "new-access-token",
  "refresh_token": "new-refresh-token", 
  "expires_in": 1800
}
```

#### 3. 完成引导流程
```http
POST /api/v1/auth/finalize_onboarding
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "userId": "user-uuid",
  "nickname": "用户昵称",
  "core_needs": ["emotional_support", "daily_chat"],
  "interests": ["music", "reading"],
  "communication_style_preference": "friendly",
  "character": {
    "name": "艾莉",
    "role": "陪伴者", 
    "voice_id": "voice_001"
  },
  "allow_chat_analysis": true
}
```

### 用户管理接口

#### 4. 获取用户画像
```http
GET /api/v1/user/profile
Authorization: Bearer your-access-token
```

**响应示例**:
```json
{
  "id": "user-uuid",
  "user_id": "user-uuid",
  "nickname": "用户昵称",
  "age_range": "25-35",
  "core_needs": ["emotional_support"],
  "interests": ["music"],
  "communication_style_preference": "friendly",
  "preferences": {
    "language": "zh-CN",
    "notification": true
  },
  "onboarding_completed": true,
  "created_at": "2025-01-21T10:00:00Z",
  "updated_at": "2025-01-21T10:30:00Z"
}
```

#### 5. 更新用户画像
```http
PUT /api/v1/user/profile
Authorization: Bearer your-access-token
Content-Type: application/json

{
  "nickname": "新昵称",
  "age_range": "25-35",
  "preferences": {
    "language": "en-US"
  }
}
```

### AI角色管理接口

#### 6. 获取角色列表
```http
GET /api/v1/characters
Authorization: Bearer your-access-token
```

**响应示例**:
```json
{
  "data": [
    {
      "id": "character-uuid",
      "name": "艾莉",
      "role": "陪伴者",
      "description": "温暖贴心的AI伙伴",
      "personality_traits": ["empathetic", "patient"],
      "voice_id": "voice_001",
      "avatar_url": "https://example.com/avatar.png"
    }
  ],
  "pagination": {
    "total": 10,
    "page": 1,
    "page_size": 20
  }
}
```

#### 7. 获取角色详情
```http
GET /api/v1/characters/{character_id}
Authorization: Bearer your-access-token
```

#### 8. 绑定用户角色
```http
POST /api/v1/user/characters/{character_id}/bind
Authorization: Bearer your-access-token
```

**响应示例**:
```json
{
  "success": true,
  "message": "角色绑定成功"
}
```

## 数据契约

### DeviceInfo 设备信息
```typescript
interface DeviceInfo {
  device_id: string;        // 设备唯一标识
  platform: "ios" | "android"; // 平台类型
  app_version: string;      // 应用版本号
}
```

### AuthResult 认证结果
```typescript
interface AuthResult {
  user: {
    id: string;
    device_fingerprint: string;
    created_at: string;
  };
  access_token: string;     // 访问令牌（30分钟有效）
  refresh_token: string;    // 刷新令牌（7天有效）
  expires_in: number;       // 过期时间（秒）
}
```

### UserProfile 用户画像
```typescript
interface UserProfile {
  id: string;
  user_id: string;
  nickname?: string;
  age_range?: string;
  core_needs?: string[];
  interests?: string[];
  communication_style_preference?: string;
  preferences?: Record<string, any>;
  onboarding_completed: boolean;
  created_at: string;
  updated_at: string;
}
```

### Character AI角色
```typescript
interface Character {
  id: string;
  name: string;
  role: string;
  description: string;
  personality_traits: string[];
  voice_id: string;
  avatar_url?: string;
}
```

### OnboardingData 引导数据
```typescript
interface OnboardingData {
  userId: string;
  nickname: string;
  core_needs: string[];
  interests?: string[];
  communication_style_preference?: string;
  character: {
    name: string;
    role: string;
    voice_id: string;
  };
  allow_chat_analysis?: boolean;
}
```

## 调用示例与注意事项

### 认证流程集成示例

```typescript
// 1. 应用启动时进行匿名登录
async function initializeAuth() {
  const deviceInfo = {
    device_id: await getDeviceId(), // 获取设备唯一标识
    platform: Platform.OS,         // "ios" 或 "android"
    app_version: "1.0.0"
  };

  try {
    const response = await fetch('/api/v1/auth/anonymous-login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ device_info: deviceInfo })
    });
    
    const authResult = await response.json();
    
    // 存储tokens到安全存储
    await SecureStore.setItemAsync('access_token', authResult.access_token);
    await SecureStore.setItemAsync('refresh_token', authResult.refresh_token);
    
    return authResult;
  } catch (error) {
    console.error('认证失败:', error);
    throw error;
  }
}

// 2. 自动Token刷新机制
async function refreshTokenIfNeeded() {
  const accessToken = await SecureStore.getItemAsync('access_token');
  
  // 检查token是否即将过期（提前5分钟刷新）
  if (isTokenExpiringSoon(accessToken)) {
    const refreshToken = await SecureStore.getItemAsync('refresh_token');
    
    const response = await fetch('/api/v1/auth/refresh-token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh_token: refreshToken })
    });
    
    const newTokens = await response.json();
    
    // 更新存储的tokens
    await SecureStore.setItemAsync('access_token', newTokens.access_token);
    await SecureStore.setItemAsync('refresh_token', newTokens.refresh_token);
  }
}

// 3. API请求拦截器
const apiClient = axios.create({
  baseURL: 'http://localhost:8003'
});

apiClient.interceptors.request.use(async (config) => {
  await refreshTokenIfNeeded();
  const token = await SecureStore.getItemAsync('access_token');
  
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  return config;
});

// 4. 处理401未授权响应
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token可能已过期，尝试刷新
      try {
        await refreshTokenIfNeeded();
        // 重试原请求
        return apiClient.request(error.config);
      } catch (refreshError) {
        // 刷新失败，重新登录
        await initializeAuth();
        return apiClient.request(error.config);
      }
    }
    return Promise.reject(error);
  }
);
```

### 用户画像管理示例

```typescript
// 获取用户画像
async function getUserProfile() {
  try {
    const response = await apiClient.get('/api/v1/user/profile');
    return response.data;
  } catch (error) {
    console.error('获取用户画像失败:', error);
    throw error;
  }
}

// 更新用户画像
async function updateUserProfile(updates: Partial<UserProfile>) {
  try {
    const response = await apiClient.put('/api/v1/user/profile', updates);
    return response.data;
  } catch (error) {
    console.error('更新用户画像失败:', error);
    throw error;
  }
}
```

### 引导流程完成示例

```typescript
async function finalizeOnboarding(onboardingData: OnboardingData) {
  try {
    const response = await apiClient.post('/api/v1/auth/finalize_onboarding', onboardingData);
    
    if (response.data.success) {
      // 引导完成，更新应用状态
      console.log('引导流程完成');
      return response.data.user_profile;
    }
  } catch (error) {
    console.error('完成引导流程失败:', error);
    throw error;
  }
}
```

### 重要注意事项

#### 1. 设备指纹生成
- **device_id必须保持稳定**：同一设备应始终返回相同的device_id
- **平台标识准确**：确保platform字段正确传递"ios"或"android"
- **版本号格式**：app_version应使用语义化版本号格式

#### 2. Token管理最佳实践
- **安全存储**：使用SecureStore或Keychain存储tokens，不要使用AsyncStorage
- **自动刷新**：在token过期前5分钟自动刷新
- **错误处理**：妥善处理401错误，实现自动重新认证
- **Token有效期**：access_token有效期30分钟，refresh_token有效期7天

#### 3. API调用注意事项
- **统一错误处理**：所有API调用都应包含错误处理逻辑
- **网络重试**：实现网络请求的重试机制
- **频率限制**：注意API频率限制（100次/分钟），避免过于频繁的请求
- **响应时间**：API响应时间通常<200ms，超时设置建议5-10秒

#### 4. 用户体验优化
- **后台刷新**：在应用后台时也要维护token的有效性
- **离线处理**：考虑网络不可用时的降级方案
- **加载状态**：为所有API调用提供适当的加载状态提示
- **错误提示**：为用户提供友好的错误提示信息

#### 5. 安全考虑
- **HTTPS通信**：生产环境必须使用HTTPS
- **Token泄露防护**：不要在日志中输出完整的token
- **设备绑定**：注意处理用户更换设备的场景
- **数据隔离**：确保用户只能访问自己的数据

#### 6. 调试和监控
- **日志记录**：记录关键的认证和API调用事件
- **性能监控**：监控API响应时间和成功率
- **错误上报**：实现认证失败和API错误的上报机制 