# "心桥"项目故事开发流程

## 📋 **开发流程概述**

本文档定义了"心桥"AI亲情伴侣项目的详细开发顺序，采用**"后端优先 + 前端UI功能一体化"**的开发策略，旨在最大化开发效率、降低技术风险、优化AI辅助开发体验。

### **核心开发理念**
- **后端优先**：构建稳定可靠的API基础，为前端开发扫清障碍。
- **记忆系统核心**：优先完善产品的核心竞争力（记忆），确保核心价值尽早验证。
- **UI功能一体化**：将每个功能的前端UI设计和逻辑实现作为一个整体故事，确保上下文清晰，便于AI辅助开发。
- **风险前置控制**：在项目初期和关键节点进行技术验证，规避重大风险。

---

## 📈 **开发流程分析：效率与稳定性**

本流程在设计时，充分考虑了开发效率、稳定性和"最小修改原则"，旨在实现平滑、递进式的开发。

### **开发效率保障**
1.  **后端先行，契约明确**: 第一阶段专注后端，确保在前端主要功能开发前，所有API接口都已稳定。这为前端提供了明确的开发契约，减少了等待和联调的阻塞。
2.  **基础设施先行**: 前端开发的第一个Sprint（Sprint 5）专注于搭建框架、UI库和通用服务。这为后续所有功能开发提供了统一、稳固的脚手架。
3.  **垂直功能切片**: 在前端功能开发阶段，每个故事都包含UI和逻辑，形成一个完整的垂直功能切片。开发者可以聚焦于一个闭环的功能，上下文清晰，效率高。

### **稳定性与最小修改原则**
本流程的核心是**"增量构建，而非颠覆式修改"**。
1.  **后端稳定性**: 后端故事按逻辑依赖顺序排列，每个新故事都是对现有稳定系统的功能"添加"，而不是"修改"。例如，提醒服务(`1.6-B`)和危机干预(`1.7`)都是在已稳定的对话核心(`1.3`)之上扩展，不影响其原有逻辑。
2.  **核心组件的前瞻性设计 (关键)**: 为了避免后续故事修改之前故事的核心代码，前端在实现核心组件时必须具备**前瞻性**。例如：
    - 在`故事1.4`中开发对话界面时，`ChatMessage`的数据结构和`ChatBubble`组件就应该设计成可扩展的，能支持不同类型的消息（如文本、语音、提醒卡片等）。
    - 这样，在后续`故事1.6`中实现提醒功能时，只需**新增**一个"提醒卡片"的渲染分支，而**无需修改**`ChatBubble`组件的核心逻辑，从而保证了系统的稳定性和最小化的修改。

---

## 🚀 **第一阶段：后端核心服务开发** (约8周)

### **Phase 0: 轻量级技术验证** (3-5天)

### **Sprint 1: 基础设施建设** (1周)

#### **故事 1.1-B: 项目基础设置（后端部分）**
```
📋 核心任务:
- Monorepo结构搭建，agent-api项目初始化
- Supabase数据库Schema创建，RLS策略配置
- CI/CD流程建立，环境配置管理
- API框架搭建，基础中间件配置

🎯 完成标准:
- 后端项目可正常启动和访问。
- 数据库表结构创建完成。
- CI/CD管道正常运行。
- 基础API健康检查接口可用。
```
**开发顺序原因**：所有后续开发的基础设施依赖，为团队协作提供标准化环境。

---

### **Sprint 2: 核心对话与认证 (调整后)** (2周)
> **关键阶段**：并行构建两大核心基础：用户身份和对话能力。

#### **故事 1.2-B: 后端认证服务与用户管理API**
```
📋 核心任务:
- 实现JWT认证机制，集成Supabase Auth
- 实现匿名用户无感创建流程
- 提供用户画像和AI角色管理的CRUD API

🎯 完成标准:
- 认证接口完整可用，用户管理功能正常。
```

#### **故事 1.3: 核心对话编排与RTC事件处理服务**
```
📋 核心任务:
- 实现`ChatOrchestrationService`，作为编排中心
- 开发`/api/v1/chat/rtc_event_handler`接口
- 实时保存对话消息，为后续记忆生成做准备

🎯 完成标准:
- 核心对话逻辑可用，能处理来自火山的RTC事件回调。
```
**开发顺序原因**：认证 (`1.2-B`) 和核心对话 (`1.3`) 是所有上层功能的两大基础支柱。它们都只依赖于 `1.1-B`，可以并行开发，应尽早完成。

---

### **Sprint 3: 会话管理与安全 (调整后)** (2-3周)
> **关键阶段**：在核心对话能力之上，构建完整的会话生命周期管理和安全保障。

#### **故事 1.3-Text: 后端文本对话服务 (SSE) (新增)**
```
📋 核心任务:
- 实现 `POST /api/v1/chat/text_message` 接口
- 复用`故事 1.3`的Agent核心服务，处理文本输入
- 以SSE流形式返回AI回复

🎯 完成标准:
- 纯文本聊天功能可用，可作为RTC的补充或降级方案。
```

#### **故事 1.4-B: 会话管理与火山RTC后端集成**
```
📋 核心任务:
- /api/v1/rtc/prepare_session接口实现
- 调用火山引擎StartVoiceChat，正确配置标准事件Webhook
- 会话生命周期管理

🎯 完成标准:
- RTC会话可成功启动，事件回调正常工作。
```

#### **故事 1.7-B: 基础危机干预服务**
```
📋 核心任务:
- 创建`CrisisDetectionService`，通过关键词检测危机信号
- 在`ChatOrchestrationService`中集成检测逻辑
- 在检测到危机时，中断LLM调用并返回脚本化回复

🎯 完成标准:
- 危机检测功能正常工作，可作为安全中间件集成到对话流程。
```
**开发顺序原因**：RTC会话管理 (`1.4-B`) 依赖于Sprint 2完成的认证和对话服务。危机响应协议 (`1.7-B`) 作为安全中间件，同样依赖于核心对话服务。

---

### **Sprint 4: 功能扩展 (调整后)** (2周)

#### **故事 1.5: 会话后分析与外部记忆服务同步**
```
📋 核心任务:
- /end_session接口触发异步会话后分析
- 调用LLM生成会话摘要
- 通过MemoryService将摘要同步到外部记忆服务

🎯 完成标准:
- 形成"对话 -> 会话后分析 -> 优化未来记忆检索"的完整闭环。
```

#### **故事 1.6-B: 基于Function Calling的提醒服务API**
```
📋 核心任务:
- 在ToolExecutorService中实现set_reminder工具
- 实现提醒CRUD API

🎯 完成标准:
- 提醒功能通过内部工具调用正常工作。
```

#### **故事 1.8-B: 应用设置服务 API (新增)**
```
📋 核心任务:
- 实现 `GET /api/v1/user/settings` 接口，获取用户个性化配置
- 实现 `PUT /api/v1/user/settings` 接口，持久化用户的设置更改
- 为新用户提供对老年人友好的默认设置

🎯 完成标准:
- 前端设置页面所需的数据接口完整可用。
```
**开发顺序原因**：记忆生成 (`1.5`)、提醒服务 (`1.6-B`) 和设置服务 (`1.8-B`) 都依赖于一个稳定且具备会话管理能力的系统 (`1.4-B`)，适合在此阶段作为功能扩展进行开发。

---

### **Sprint 5: 系统完善 (调整后)** (1周)

#### **故事 1.8-B: 应用设置服务 API**
```
📋 核心任务:
- 实现/api/v1/settings接口，支持用户偏好设置的读写
- 创建user_settings表并配置RLS

🎯 完成标准:
- 前端可以调用API来管理用户的主题、字体大小等偏好。
```
**开发顺序原因**：作为上层功能，它依赖于核心认证服务 (`1.2-B`)，在所有核心业务功能完成后开发，为前端提供支持。

---

## 📱 **第二阶段：前端应用开发** (约7周)

### **Sprint 5: 前端基础设施** (1-2周)

> **目标**: 为所有后续前端功能开发提供稳定、高效的框架和基础组件。

#### **故事 1.1-Frontend: 项目基础设置（前端部分）**
```
📋 核心任务:
- React Native/Expo项目初始化
- 状态管理(Zustand)、数据请求(React Query)和路由(Expo Router)架构搭建
```

#### **故事 1.1-UI: 基础设计系统与组件库实现**
```
📋 核心任务:
- 建立适老化设计系统（颜色、字体、间距）
- 开发基础UI组件库
```

#### **基础服务故事 (并行开发)**
- **故事 1.1-Frontend-ErrorHandling**: 全局错误处理与异常管理
- **故事 1.1-Frontend-Permissions**: 系统权限管理

---

### **Sprint 6-9: UI功能一体化开发** (4-5周)
> **核心策略**：每个功能都采用UI+逻辑一体化开发，确保上下文清晰。

#### **Sprint 6: 认证与角色创建** (1周)
**故事 1.2: 无感身份认证与角色创建流程**
```
📋 一体化开发内容:
- UI: 启动页、欢迎页、角色创建流程界面 (依赖1.2-UI)
- 功能: 集成后端认证API (依赖1.2-B)，实现状态管理和持久化
```

#### **Sprint 7: 实时对话功能** (2周)
**故事 1.4: 实时语音会话流程集成**
```
📋 一体化开发内容:
- UI: 主对话界面、双模输入控件、消息气泡 (依赖1.4-UI)
- 功能: 集成火山RTC SDK，处理实时消息，管理双模切换逻辑 (依赖1.4-B)
```

#### **Sprint 8: 扩展功能** (1周)
**故事 1.6: 对话式提醒功能**
```
📋 一体化开发内容:
- UI: 提醒确认卡片、列表、编辑界面 (依赖1.6-UI)
- 功能: 集成本地通知系统，调用后端提醒API (依赖1.6-B)
```

**故事 1.7-Frontend: 危机干预功能实现**
```
📋 一体化开发内容:
- UI: 危机响应、安全提示、紧急联系界面 (依赖1.7-UI)
- 功能: 处理危机检测结果，切换到安全模式 (依赖1.7-B)
```

---

### **Sprint 10: 系统完善** (1周)

#### **故事 1.8-Frontend: 应用设置模块**
```
📋 一体化开发内容:
- UI: 设置页面、主题/字体选择器、权限管理入口
- 功能: 集成后端设置API (依赖1.8-B)，实现用户偏好设置的读取和持久化
```
---

## 📊 **故事依赖关系图**

```mermaid
graph TD
    subgraph "后端核心服务 (Backend Core)"
        B0("1.0 技术预研") --> B1_B("1.1-B 基础设置")
        B1_B --> B2_B("1.2-B 认证服务")
        B2_B -- "依赖" --> B8_B("1.8-B 设置API(新增)")
        B1_B --> B3("1.3 对话处理服务")
        B3 --> B7_B("1.7-B 基础危机干预服务")
        B3 --> B3_Text("1.3-Text 文本对话(新增)")
        B2_B & B3 --> B4_B("1.4-B RTC会话管理")
        B4_B --> B5("1.5 记忆生成")
        B4_B --> B6_B("1.6-B 提醒服务(FC)")
        B2_B --> B8_B("1.8-B 设置服务API")
    end

    subgraph "前端UI设计 (UI Design)"
        F1_UI("1.1-UI 核心界面")
        F1_UI --> F2_UI("1.2-UI 引导流程")
        F1_UI --> F4_UI("1.4-UI 对话界面")
        F1_UI --> F6_UI("1.6-UI 提醒界面")
        F1_UI --> F7_UI("1.7-UI 危机干预界面")
    end

    subgraph "前端功能实现 (Frontend Feature)"
        F1_F("1.1-F 基础设置")
        F1_F --> F1_UI
        F1_F --> F_Error("1.1-ErrorHandling")
        F1_F --> F_Perm("1.1-Permissions")
        
        B2_B & F2_UI & F_Error --> F2_F("1.2 认证流程")
        
        B3_Text & F2_F & B4_B & F4_UI & F_Perm --> F4_F("1.4 实时语音会话")
        
        F4_F & B6_B & F6_UI --> F6_F("1.6 提醒功能")
        
        F4_F & B7_B & F7_UI --> F7_F("1.7 危机干预")

        F1_F & F_Perm --> F8_F("1.8 设置模块")
        B8_B --> F8_F
    end

    style B0 fill:#e6e6fa,stroke:#333,stroke-width:2px
    style F1_F fill:#d4fcd7,stroke:#333,stroke-width:2px
    style B3_Text fill:#e6e6fa,stroke:#333,stroke-width:2px
    style B8_B fill:#e6e6fa,stroke:#333,stroke-width:2px
```
