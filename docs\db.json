[{"table_name": "characters", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "characters", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "characters", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "characters", "column_name": "voice_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "characters", "column_name": "personality", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "characters", "column_name": "is_default", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "characters", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "chat_messages", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "chat_messages", "column_name": "session_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "role", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "chat_messages", "column_name": "content", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "chat_messages", "column_name": "message_type", "data_type": "character varying", "is_nullable": "YES", "column_default": "'text'::character varying"}, {"table_name": "chat_messages", "column_name": "metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "chat_messages", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "content_type", "data_type": "text", "is_nullable": "YES", "column_default": "'text'::text"}, {"table_name": "chat_messages", "column_name": "emotion_category", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "emotion_intensity", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "tokens_used", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": "'sent'::character varying"}, {"table_name": "chat_messages", "column_name": "quoted_message_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "reactions", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "is_edited", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "chat_messages", "column_name": "edit_count", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "chat_messages", "column_name": "deleted_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "is_deleted", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "chat_messages", "column_name": "structured_data", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_messages", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "chat_sessions", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "chat_sessions", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_sessions", "column_name": "character_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_sessions", "column_name": "topic", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_sessions", "column_name": "summary", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_sessions", "column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": "'active'::character varying"}, {"table_name": "chat_sessions", "column_name": "rtc_task_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "chat_sessions", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "chat_sessions", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "chat_sessions", "column_name": "analysis_status", "data_type": "character varying", "is_nullable": "YES", "column_default": "'pending'::character varying"}, {"table_name": "chat_sessions", "column_name": "last_message_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "crisis_events", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "crisis_events", "column_name": "session_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "crisis_events", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "crisis_events", "column_name": "risk_level", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "crisis_events", "column_name": "triggered_keywords", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_name": "crisis_events", "column_name": "confidence_score", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_name": "crisis_events", "column_name": "response_template", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "crisis_events", "column_name": "event_timestamp", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "reminder_patterns", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": "(gen_random_uuid())::text"}, {"table_name": "reminder_patterns", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "reminder_patterns", "column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_name": "reminder_patterns", "column_name": "pattern_type", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_name": "reminder_patterns", "column_name": "pattern_config", "data_type": "jsonb", "is_nullable": "NO", "column_default": null}, {"table_name": "reminder_patterns", "column_name": "is_active", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "reminder_patterns", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "reminder_patterns", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "reminders", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "reminders", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "reminders", "column_name": "content", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "reminders", "column_name": "reminder_time", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "reminders", "column_name": "status", "data_type": "character varying", "is_nullable": "NO", "column_default": "'pending'::character varying"}, {"table_name": "reminders", "column_name": "pattern_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "reminders", "column_name": "function_call_payload", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "reminders", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "reminders", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "rtc_sessions", "column_name": "session_id", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "user_id", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "character_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "task_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "room_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": "'preparing'::character varying"}, {"table_name": "rtc_sessions", "column_name": "voice_config", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "rtc_sessions", "column_name": "started_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "ended_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "error_message", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "rtc_sessions", "column_name": "volcano_response", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "user_character_bindings", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "user_character_bindings", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "user_character_bindings", "column_name": "character_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "user_character_bindings", "column_name": "is_active", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "user_character_bindings", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "user_character_bindings", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "user_profiles", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "user_profiles", "column_name": "nickname", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "age_range", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "core_needs", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "preferences", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "onboarding_completed", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "user_profiles", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "user_profiles", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "timezone('utc'::text, now())"}, {"table_name": "user_profiles", "column_name": "interests", "data_type": "ARRAY", "is_nullable": "YES", "column_default": "'{}'::text[]"}, {"table_name": "user_profiles", "column_name": "communication_style_preference", "data_type": "character varying", "is_nullable": "YES", "column_default": "NULL::character varying"}, {"table_name": "user_profiles", "column_name": "allow_chat_analysis", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "user_profiles", "column_name": "personality_summary_ai", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "user_settings", "column_name": "user_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "user_settings", "column_name": "theme", "data_type": "character varying", "is_nullable": "NO", "column_default": "'auto'::character varying"}, {"table_name": "user_settings", "column_name": "font_size", "data_type": "character varying", "is_nullable": "NO", "column_default": "'large'::character varying"}, {"table_name": "user_settings", "column_name": "high_contrast", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "user_settings", "column_name": "language", "data_type": "character varying", "is_nullable": "NO", "column_default": "'zh-CN'::character varying"}, {"table_name": "user_settings", "column_name": "notifications_enabled", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}]