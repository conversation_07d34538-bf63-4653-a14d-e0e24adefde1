Title: 如何提升语音识别准确性？--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1563620

Markdown Content:
如何提升语音识别准确性？--实时音视频-火山引擎

===============

实时对话式 AI 场景中，准确的语音识别结果能够让智能体更好地理解用户意图，从而提供更精准的服务。本文将介绍如何提高 RTC 实时对话式 AI 场景中语音识别的准确性。

选择合适的 ASR 模型
------------

实时对话式 AI 场景，支持使用火山引擎以下模型用于语音识别，你可以按需选择合适的 ASR 模型：

*   流式语音识别大模型：识别准确性更高。
*   流式语音识别：识别速度更快。

调整音量增益
------

在嘈杂环境下，可尝试将 `VolumeGain` 调低，以减少背景噪音对 ASR 识别的干扰。

 增益值越低，采集音量越低，有助于滤除部分环境噪音。但是，过低的增益也可能导致有效语音信号过弱。

调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，传入 `ASRConfig. VolumeGain`。

```JSON
"ASRConfig": {
    // ... ProviderParams ...
    "VolumeGain": 0.3  // 默认 1.0
}
```

JSON

调整 VAD 配置
---------

VAD 用于判断用户是否开始说话以及何时结束说话。不合理的 VAD 配置可能导致句子被错误地截断或合并，从而影响识别准确率。

 调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，传入 `ASRConfig.VADConfig.SilenceTime`。

```JSON
"VADConfig": {
    "SilenceTime": 800 // 如果用户语速较慢或停顿较多，可适当调大
}
```

JSON

使用热词
----

对于业务中常见的专有名词、人名、产品名、特定术语等，ASR 可能难以准确识别。通过配置热词，可以显著提高这些词汇的识别准确率。

> 仅火山引擎流式语音识别大模型支持热词功能。

具体配置如下：

方式 1：热词直传

方式 2：传入热词词表

直接在 API 请求中通过 JSON 字符串传入一个或多个热词。 例如：`"{"hotwords": [{"word": "火山引擎"},{"word": "实时音视频"},{"word": "豆包"}]}"`

*   **限制**：最大 200 tokens，超出会自动截断。
*   **配置**：调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，在 `ProviderParams` 中传入 `context`。
*   **配置示例**：
```JSON
"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "context": "{"hotwords": [{"word": "智能客服"},{"word": "数字人"},{"word": "火山大陆"}]}"
}
```
JSON    

在 API 请求中通过词表 ID 或名称。适合热词较多或需要动态管理的场

> 热词直传 (`context`) 的执行优先级高于热词词表。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。

1.   在火山[火山豆包语音控制台热词管理](https://www.google.com/url?sa=E&q=https%3A%2F%2Fconsole.volcengine.com%2Fspeech%2Fhotword)预先创建和维护热词词表。
2.   调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，在 `ProviderParams` 中传入 `boosting_table_id` 或 `boosting_table_name`。

> `boosting_table_id` 和 `boosting_table_name` 不可同时设置。

```JSON
"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "boosting_table_id": "YOUR_HOTWORD_TABLE_ID"
    // 或 "boosting_table_name": "YourHotwordTableName"
}
```

JSON

使用替换词
-----

替换词功能可以将 ASR 识别出的特定词汇替换为预期的标准词汇，常用于纠错、脱敏或别名替换。比如“二零二三年”替换为“2023年”。

> *   仅火山引擎流式语音识别大模型支持替换词功能。
> *   替换词的执行优先级低于热词。即如果一个词同时是热词和替换词的源词，优先执行热词，再执行替换词。例如，原词为“智立”：
> 
>  - 若热词有“致力”，替换词要求“智立→治理”，最后结果为 “致力”。
> 
>  - 若热词有“致力”，替换词要求“致力→治理”，最后结果为 “治理”。

具体配置如下：

1.   在 [火山豆包语音控制台替换词管理](https://console.volcengine.com/speech/correctword)创建替换词表，并获取替换词 ID 或文件名称。

2.   调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，在 `ProviderParams` 中传入 `correct_table_id` 或 `correct_table_name`。

> `correct_table_id` 和 `correct_table_name` 不可同时设置。

```JSON
"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "correct_table_id": "YOUR_CORRECT_TABLE_ID"
    // 或 "correct_table_name": "YourCorrectTableName"
}
```
JSON    

使用上下文轮次
-------

将最近指定轮次的对话内容作为上下文信息送入流式语音识别大模型，有助于模型理解当前对话的背景，从而可能提升对后续输入的识别准确性。

> *   仅火山引擎流式语音识别大模型支持上下文轮次功能。
> *   增加上下文轮数可能会略微增加处理开销，需根据实际效果进行调整。

调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，在 `ProviderParams` 中传入 `context_history_length`。

```JSON
"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "context_history_length": 3 // 示例：将最近3轮对话作为上下文传入流式语音识别大模型。取值：0(不开启)，或 [1, 21)之间的整数
}
```

JSON

