### **"心桥"AI亲情伴侣 产品需求文档 - 技术架构**
**版本：** 1.2 
**日期：** 2025年1月
**作者：** John，产品经理

> 本文档是完整PRD的第四部分，详细描述技术架构假设和实现要求。
> 相关文档：[项目概述](./overview.md) | [需求规格](./requirements.md) | [用户体验设计](./ux-design.md) | [用户故事](./user-stories.md)

#### **4. 技术假设 (Technical Assumptions)**

##### **4.1. 架构选型 (Architecture Choices)**

**仓库结构**
- 采用 Monorepo 架构 (`pnpm workspaces`)
- 统一管理前端、后端和共享代码
- 支持跨项目的类型共享和代码复用

**服务架构**
- Serverless 结合 BaaS (Backend as a Service)
- 后端API (FastAPI) 作为核心业务中枢，内置原生LLM集成服务
- 微服务化的功能模块设计

**核心交互技术**
- 火山引擎实时语音RTC解决方案
- 通过后端服务与火山引擎LLM原生集成，实现工具调用和上下文管理
- 实时语音流处理和上下文注入架构

##### **4.2. 前端技术栈 (Frontend Technology Stack)**

**核心框架**
- React Native + Expo 作为跨平台移动开发框架
- TypeScript 提供静态类型检查
- 基于 Obytes 脚手架快速启动

**UI与样式**
- Nativewind v4 (Tailwind CSS for React Native)
- 原子设计模式组织组件
- 适老化UI组件库

**状态管理**
- Zustand 作为轻量级状态管理方案
- React Query 处理服务器状态和缓存
- 本地存储使用 Expo SecureStore

**性能优化**
- @shopify/flash-list 优化长列表性能
- react-native-reanimated 提供流畅动画
- 代码分割和懒加载策略

##### **4.3. 后端技术栈 (Backend Technology Stack)**

**核心框架**
- Python 3.11+ 作为主要开发语言
- FastAPI 作为高性能异步Web框架
- Pydantic 进行数据验证和序列化

**核心AI能力**
- **原生LLM集成**: 通过自定义的`LLMProxyService`直连火山引擎LLM服务，实现对模型能力的完全掌控。
- **原生工具调用**: 设计`ToolExecutorService`，用于解析并执行LLM发出的工具调用请求，实现与外部API或内部服务的无缝集成。
- **可插拔记忆系统**: 通过抽象接口接入外部专业记忆服务（如Zep/Mem0），与核心对话逻辑解耦。

**数据存储**
- Supabase PostgreSQL 作为主数据库
- 支持实时订阅和行级别安全 (RLS)
- 通过外部服务（Zep/Mem0）进行向量化记忆检索

**外部服务集成**
- 火山引擎RTC API集成
- 大语言模型API调用
- 推送通知服务

##### **4.4. 数据架构 (Data Architecture)**

**核心数据表**
- `users` - 用户基础信息
- `user_profiles` - 用户画像数据
- `characters` - AI角色配置
- `chat_sessions` - 对话会话
- `chat_messages` - 消息历史
- `reminders` - 提醒设置

**数据安全**
- 行级别安全策略 (RLS)
- 数据加密传输和存储
- 最小权限访问原则

**数据同步**
- 实时数据同步机制
- 离线数据缓存策略
- 数据一致性保证

##### **4.5. 集成方案 (Integration Architecture)**

**火山引擎RTC集成**
- 前端SDK集成用于实时音视频
- 后端API集成用于会话管理
- 通过后端服务直连LLM实现智能对话

**实时通信流程**
```mermaid
sequenceDiagram
    participant Client as 移动端App
    participant BackendService as 心桥后端服务
    participant VolcanoRTC as 火山引擎RTC
    participant VolcanoLLM as 火山引擎LLM

    Client->>BackendService: 准备会话
    BackendService->>VolcanoRTC: 启动RTC会话
    VolcanoRTC-->>Client: 返回连接凭证
    
    loop 实时对话
        Client->>VolcanoRTC: 语音输入 (Audio)
        VolcanoRTC->>BackendService: ASR文本
        BackendService->>VolcanoLLM: 构造提示(含历史与工具)
        alt LLM需要调用工具
            VolcanoLLM-->>BackendService: 请求工具调用 (Tool Call Request)
            BackendService->>BackendService: 执行工具 (ToolExecutorService)
            BackendService-->>VolcanoLLM: 返回工具结果 (Tool Result)
        end
        VolcanoLLM-->>BackendService: AI回复文本 (Final Text)
        BackendService-->>VolcanoRTC: 注入TTS文本
        VolcanoRTC-->>Client: TTS语音输出 (Audio)
    end
```

##### **4.6. 测试策略 (Testing Strategy)**

**单元测试**
- 前端组件测试 (Jest + React Native Testing Library)
- 后端功能测试 (pytest)
- 代码覆盖率要求 > 80%

**集成测试**
- API接口测试
- 前后端联调测试
- 第三方服务集成测试

**性能测试**
- 语音延迟测试 (目标 < 1.5秒)
- 并发用户测试
- 内存和CPU性能测试

**手动情感共情测试**
- **关键要求：** 必须包含由团队成员执行的手动情感共情测试
- 验证AI回复的质量、温度和共情能力
- 确保AI人格一致性和情感表达自然度
- 测试危机干预功能的有效性

**用户验收测试**
- 真实用户场景测试
- 易用性和无障碍测试
- 错误处理和容错测试

##### **4.7. 部署和运维 (Deployment & Operations)**

**部署环境**
- 开发环境：本地开发服务器
- 测试环境：云端测试实例
- 生产环境：高可用云端部署

**CI/CD流程**
- GitHub Actions 自动化流水线
- 代码质量检查和自动测试
- 分环境自动部署

**监控和日志**
- 应用性能监控 (APM)
- 错误追踪和报警
- 用户行为分析

**扩容策略**
- 自动扩缩容配置
- 负载均衡和流量分发
- 数据库读写分离

##### **4.8. 安全架构 (Security Architecture)**

**认证授权**
- JWT Token 身份验证
- OAuth 2.0 第三方登录支持
- 基于角色的访问控制 (RBAC)

**数据保护**
- 传输层加密 (TLS 1.3)
- 数据库字段级加密
- 敏感信息脱敏处理

**API安全**
- 请求频率限制
- 输入验证和SQL注入防护
- CORS和CSRF保护

**合规要求**
- 符合《个人信息保护法》
- 数据本地化存储要求
- 用户数据删除和导出权利

##### **4.9. 技术风险和缓解措施**

**主要技术风险**
- 火山引擎RTC集成复杂度
- 实时记忆检索性能
- 大语言模型响应质量
- 网络连接稳定性

**缓解措施**
- 技术预研和原型验证
- 性能基准测试
- 降级和容错机制
- 多供应商备选方案 