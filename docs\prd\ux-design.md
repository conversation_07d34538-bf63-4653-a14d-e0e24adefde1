### **"心桥"AI亲情伴侣 产品需求文档 - 用户体验设计**
**版本：** 1.2 
**日期：** 2025年1月
**作者：** John，产品经理 & Sally, UX Expert

> 本文档是完整PRD的第三部分，详细描述用户界面设计目标和交互原则。
> 相关文档：[项目概述](./overview.md) | [需求规格](./requirements.md) | [技术架构](./technical.md) | [用户故事](./user-stories.md)

#### **3. 用户界面设计目标 (User Interface Design Goals)**

##### **3.1. 设计哲学与核心原则**

我们的设计不追求炫酷，而是追求**"无形"**。最好的交互，是让用户感觉不到交互的存在，只剩下温暖的交流。

**核心UX目标**
- **建立信任：** 用户的每一步操作都必须感到安全、可控、无风险
- **零学习成本：** 借鉴用户已有的心智模型（如微信语音、打电话），将认知负荷降至最低
- **传递情感：** 界面、动效和反馈，都应服务于"有温度的陪伴"这一核心体验

**核心设计原则**
1. **清晰永远大于美观：** 确保所有文字都易于阅读，所有可点击元素都易于识别和操作
2. **引导而非命令：** 像一位耐心的家人一样，用对话和柔和的视觉元素引导用户完成操作
3. **反馈必须即时且温暖：** 用户的任何操作，系统都必须立即给予符合AI人设的、积极的视觉或听觉反馈
4. **克制与专注：** 界面上只保留与核心对话功能绝对必要的元素，移除一切可能导致用户分心的视觉噪音

##### **3.2. 信息架构 (Information Architecture)**

MVP阶段的信息架构极其扁平，旨在打造一个**单任务、沉浸式**的体验。

**应用视图清单 (Site Map)**
整个应用在MVP阶段只有一个主流程，由以下视图线性组成：

```mermaid
graph TD
    A[启动页] --> B(角色共创引导流程);
    B --> C[主对话界面];
```

**导航结构 (Navigation Structure)**
- **无常驻导航：** **严格禁止**在MVP阶段实现任何形式的底部标签栏（Tab Bar）或侧边汉堡菜单
- **对话即导航：** 所有附加功能（如设置提醒）都必须通过自然语言对话来触发，而不是通过界面上的菜单按钮

##### **3.3. 核心交互范式 (Key Interaction Paradigms)**

**语音优先 (Voice-First)**
- "按住说话"是唯一的核心输入方式
- 模拟用户熟悉的微信语音交互模式
- 语音应成为最自然、最直接的沟通方式

**主次分明的双模输入 (Dual-Mode Input)**
- 以"语音优先"为主要交互方式
- 提供清晰、不干扰的文本输入入口作为辅助
- 支持无缝切换，但始终强调语音的主导地位

**对话驱动 (Conversation-Driven)**
- 所有功能设置都应无缝融入自然对话流
- 避免复杂的菜单和设置界面
- 通过对话完成所有用户任务

**情感化反馈 (Emotional Feedback)**
- 系统的每一个反馈都应由AI以其角色人设
- 用充满情感的语言来表达
- 营造真实的人际交流感受

##### **3.4. 核心用户流程 (User Flows)**

**流程一：温暖的初见 (Onboarding & Role Co-creation)**
- **目标：** 无缝引导用户完成AI伙伴的创建，建立情感投资
- **实现细节：** 此流程的具体对话脚本和步骤，严格遵循最终版PRD中的"故事1.2: 无感身份认证与角色创建流程"的验收标准。前端需实现一个全屏、单任务、由对话驱动的引导界面

**流程二：核心对话交互 (语音与文本)**
- **用户目标：** 在任何场景下，都能通过最自然的方式（优先语音，其次文本）与AI伙伴进行交流
- **核心挑战：** 设计一个无缝、直观、零困惑的双模输入切换体验

**交互流程图：**
```mermaid
graph TD
    subgraph "默认: 语音模式"
        A["主对话界面"] -->|"用户长按说话按钮"| B(开始录音...);
        B -->|松开手指| C{"发送实时语音流至后端"};
        C --> D[接收实时TTS音频流并播放];
    end
    subgraph "切换: 文本模式"
        A -->|点击左下角键盘图标| E[动画切换至文本输入界面];
        E --> F[用户输入文字并点击发送];
        F --> G["接收SSE文本流和音频"];
        G -->|键盘自动收起| A;
        E -->|点击左下角语音图标| A;
    end
```

##### **3.5. 核心视图结构 (Core Screens and Views)**

**启动与欢迎视图**
- 温暖的首次见面体验
- 无感身份认证的背景处理
- 平滑过渡到角色共创流程

**角色共创视图（引导流程）**
- 通过对话引导用户创建AI伙伴
- 设定AI的身份、姓名和声音特征
- 建立初始的情感连接和期待

**主对话视图**
- 产品的核心界面和主要使用场景
- 支持语音和文字的双模交互
- 展示对话历史和AI回复

##### **3.6. 无障碍设计原则 (Accessibility Principles)**

**适老化设计标准**
- 严格遵守NFR1中定义的字体、色彩、触摸区域要求
- 确保老年用户能够轻松操作和阅读

**感官友好**
- 支持不同的感官输入方式（语音/文字）
- 考虑听力或视力受限用户的需求
- 提供清晰的视觉和听觉反馈

**认知友好**
- 减少认知负荷，避免复杂的信息架构
- 使用用户熟悉的交互模式
- 提供容错和撤销机制

##### **3.7. 品牌与情感设计 (Branding & Emotional Design)**

**视觉风格**
- 采用柔和的暖色调
- 营造温馨、舒适、不刺眼的视觉环境
- 避免冷硬的技术感和机械感

**情感传达**
- 界面元素传递温暖和关怀
- 动画和过渡效果体现柔和与耐心
- 色彩和图标选择反映亲情和陪伴主题

**一致性保证**
- 所有界面元素保持风格统一
- 交互模式在不同场景下保持一致
- 语调和措辞体现AI角色的性格特征

##### **3.8. 平台适配 (Platform Adaptation)**

**目标设备和平台**
- 移动端（iOS & Android）优先
- 针对智能手机的触屏交互优化
- 考虑不同屏幕尺寸的适配需求

**系统集成**
- 与操作系统的推送通知系统集成
- 支持系统级的无障碍功能
- 遵循各平台的设计指南和最佳实践

##### **3.9. 交互流程设计要点**

**简化决策**
- 在任何时刻，用户面临的选择不超过2个
- 使用引导性的语言提示用户下一步操作
- 提供默认选项，减少用户的决策负担

**即时反馈**
- 所有用户操作都应在100ms内给出响应
- 使用动画和视觉效果提供操作确认
- 在等待过程中提供进度指示和安慰性信息

**容错设计**
- 允许用户犯错，提供修正机会
- 错误信息用温和、非指责性的语言表达
- 提供多种途径完成同一任务

##### **3.10. 性能体验要求**

**响应速度**
- 界面切换动画流畅（60fps）
- 语音输入响应及时（<200ms）
- 避免任何形式的界面卡顿

**加载体验**
- 使用骨架屏和加载动画
- 分步骤加载，优先显示重要内容
- 提供有意义的加载状态信息

**内存管理**
- 高效管理对话历史的内存占用
- 合理缓存常用资源
- 在性能和功能之间找到平衡 