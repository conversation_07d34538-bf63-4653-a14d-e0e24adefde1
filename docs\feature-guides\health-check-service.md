# 健康检查服务功能说明

## 功能概述

健康检查服务是心桥项目后端基础设施的核心组件，为前端应用提供实时的后端服务状态监控能力。该服务建立在FastAPI框架之上，运行在8003端口，提供标准化的健康状态检查接口。

### 主要功能特性
- **实时状态监控**：提供后端服务的实时运行状态
- **标准化响应格式**：遵循行业标准的健康检查响应规范
- **UTC时间戳**：提供精确的服务响应时间信息
- **高可用性**：支持高频率调用，适合前端心跳检测

### 技术规格
- **服务端口**：8003
- **协议**：HTTP/HTTPS
- **响应格式**：JSON
- **认证要求**：无（公开接口）

## 核心API端点

### GET /api/v1/health

**功能描述**：获取后端服务的健康状态

**请求方式**：`GET`

**请求URL**：`http://localhost:8003/api/v1/health`

**请求参数**：无

**响应格式**：
```json
{
  "status": "healthy",
  "timestamp": "2025-07-09T14:00:00.123456Z"
}
```

**状态码**：
- `200 OK`：服务正常运行
- `503 Service Unavailable`：服务不可用（极少情况）

## 数据契约

### 响应数据结构

```typescript
interface HealthCheckResponse {
  /** 服务状态，固定返回 "healthy" */
  status: "healthy";
  /** UTC时间戳，ISO 8601格式 */
  timestamp: string;
}
```

### 字段说明

| 字段名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `status` | string | ✅ | 服务状态标识，当前固定为 "healthy" |
| `timestamp` | string | ✅ | 服务响应时的UTC时间戳，格式：ISO 8601 |

### 时间戳格式示例
```
2025-07-09T14:00:00.123456Z
```

## 调用示例与注意事项

### JavaScript/TypeScript调用示例

#### 使用fetch API
```javascript
async function checkBackendHealth() {
  try {
    const response = await fetch('http://localhost:8003/api/v1/health');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const healthData = await response.json();
    console.log('Backend status:', healthData.status);
    console.log('Response time:', healthData.timestamp);
    
    return healthData.status === 'healthy';
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
}
```

#### 使用axios
```javascript
import axios from 'axios';

async function checkBackendHealth() {
  try {
    const response = await axios.get('http://localhost:8003/api/v1/health');
    return response.data.status === 'healthy';
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
}
```

#### React Hook示例
```typescript
import { useState, useEffect } from 'react';

interface HealthStatus {
  isHealthy: boolean;
  lastChecked: string | null;
  loading: boolean;
}

export const useBackendHealth = (intervalMs: number = 30000) => {
  const [health, setHealth] = useState<HealthStatus>({
    isHealthy: false,
    lastChecked: null,
    loading: true
  });

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const response = await fetch('http://localhost:8003/api/v1/health');
        const data = await response.json();
        
        setHealth({
          isHealthy: data.status === 'healthy',
          lastChecked: data.timestamp,
          loading: false
        });
      } catch (error) {
        setHealth({
          isHealthy: false,
          lastChecked: null,
          loading: false
        });
      }
    };

    // 立即检查一次
    checkHealth();

    // 设置定时检查
    const interval = setInterval(checkHealth, intervalMs);

    return () => clearInterval(interval);
  }, [intervalMs]);

  return health;
};
```

### 重要注意事项

#### 1. 🌐 **环境配置**
- **开发环境**：`http://localhost:8003`
- **生产环境**：需要替换为实际的生产服务器地址
- **端口确认**：确保后端服务运行在8003端口

#### 2. ⏱️ **调用频率建议**
- **心跳检测**：建议间隔30秒-1分钟
- **页面初始化**：可立即调用一次
- **避免过频**：不建议低于10秒间隔，避免给后端造成压力

#### 3. 🛡️ **错误处理**
- **网络错误**：需要妥善处理网络连接失败的情况
- **超时设置**：建议设置5-10秒的请求超时
- **重试机制**：可实现简单的重试逻辑（最多3次）

#### 4. 📱 **前端集成建议**
- **状态指示器**：可在UI中添加连接状态指示器
- **离线处理**：健康检查失败时的降级处理
- **用户体验**：避免频繁的错误提示打扰用户

#### 5. 🔍 **调试提示**
- **CORS问题**：本地开发时注意跨域配置
- **开发工具**：可在浏览器网络面板中观察请求状态
- **日志记录**：建议记录健康检查的关键节点

### 典型使用场景

1. **应用启动检查**：在前端应用初始化时验证后端可用性
2. **定期心跳监控**：定时检查后端服务状态，更新UI状态指示
3. **错误恢复检测**：在后端连接失败后，定期检查服务是否恢复
4. **系统监控面板**：在管理界面中显示各服务的健康状态

---

**文档版本**：v1.0  
**最后更新**：2025-07-09  
**维护团队**：后端开发团队  
**联系方式**：如有疑问请联系后端技术负责人 