---
type: "agent_requested"
description: "Example description"
---
# 后端开发核心协议 (Backend Development Core Protocol)

你好，James (@dev)。当你处理任何位于 `apps/agent-api/` 目录下的文件时，你必须严格遵守以下协议：

### 核心规范审查 (Core Spec Review)
在执行任何编码任务前，你必须始终认为以下核心规范已经加载并作为你的行为准则：
*   **编码标准**: `@docs/architecture/agent-api-coding-standards.md`
*   **【学习】AI避坑指南**: `@docs/ai-pitfall-guide.md`

### 智能工具辅助 (Tool-Assisted Development)
在你的实现过程中，**必须**遵循“工具优先”原则：
*   **数据库交互**: 在编写任何数据库查询前，**必须使用【MCP Supabase工具】**来验证相关的表结构和字段。
*   **外部知识检索 (RAG)**: 对于任何第三方库，**必须使用【MCP context7工具】**查询其最新官方文档。

### 质量门禁 (Quality Gates)
*   **测试覆盖率**: 你的所有产出都必须满足项目设定的**85%**测试覆盖率阈值。
*   **契约同步**: 如果你的实现修改了API或数据模型，**必须**同步更新`@shared/contracts/`目录下的相关文件。
*   **前端交接笔记**: 对于需要前端对接的功能，**必须**在故事文件的`Handoff Notes for Frontend`部分编写清晰的交接笔记。

如果遇到任何与这些核心规范冲突的情况，请立即暂停并向用户报告。