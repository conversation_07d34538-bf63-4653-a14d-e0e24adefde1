
import asyncio
import concurrent.futures
import httpx
import json
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Literal, TypedDict
from api.settings import settings, logger
from api.services.shared_thread_pool import run_in_shared_io_pool


class Message(TypedDict):
    """A dictionary representing a message object."""
    role: str
    content: str


class IMemoryService(ABC):
    """Abstract base class for memory services."""

    @abstractmethod
    async def add_memory(
        self, user_id: str, session_id: str, human_message: str, assistant_message: str, metadata: Dict[str, Any] | None = None
    ) -> None:
        """Adds a memory to the store."""
        raise NotImplementedError

    @abstractmethod
    async def get_memories(self, user_id: str, session_id: str, limit: int = 10) -> List[Message]:
        """Retrieves memories for a user, optionally filtered by session."""
        raise NotImplementedError

    @abstractmethod
    async def search_memory(self, user_id: str, query: str, limit: int = 5) -> List[Message]:
        """Searches memories for a user based on query."""
        raise NotImplementedError

    @abstractmethod
    async def delete_memory(self, user_id: str, session_id: str) -> None:
        """Deletes memories for a user session."""
        raise NotImplementedError

    @abstractmethod
    async def get_memory_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Retrieves memory context for a user based on a query."""
        raise NotImplementedError

    @abstractmethod
    async def update_session_metadata(self, user_id: str, session_id: str, summary: str, metadata: Dict[str, Any] | None = None) -> bool:
        """Updates session metadata with summary and other information."""
        raise NotImplementedError


class ZepMemoryServiceImpl(IMemoryService):
    """Real implementation for Zep Memory Service with official Zep Cloud SDK integration."""

    def __init__(self):
        """初始化Zep Memory Service - 使用官方Zep Cloud SDK"""
        self.api_key = getattr(settings, 'ZEP_API_KEY', None)
        self.timeout = 5.0  # 根据架构师建议设置5秒超时
        self.max_retries = 3
        self._client = None
        # P1性能调优：移除独立线程池，使用共享线程池

        if not self.api_key:
            logger.warning("ZEP_API_KEY not configured, ZepMemoryService will not function properly")

    def _get_client(self):
        """获取Zep客户端实例（懒加载）"""
        if self._client is None:
            try:
                from zep_cloud.client import Zep
                self._client = Zep(api_key=self.api_key)
            except ImportError:
                logger.error("zep-cloud package not installed. Please install with: pip install zep-cloud")
                raise
        return self._client

    async def add_memory(
        self, user_id: str, session_id: str, human_message: str, assistant_message: str, metadata: Dict[str, Any] | None = None
    ) -> None:
        """添加记忆到Zep Cloud - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Zep API key not configured, skipping memory addition")
                return

            # P1性能调优：使用共享IO线程池避免阻塞事件循环
            await run_in_shared_io_pool(
                self._add_memory_sync,
                user_id, session_id, human_message, assistant_message, metadata
            )

        except Exception as e:
            logger.error(f"Zep记忆添加失败: {e}")
            # 降级处理：记忆服务失败不应影响对话继续
            pass

    def _add_memory_sync(self, user_id: str, session_id: str, human_message: str, assistant_message: str, metadata: Dict[str, Any] | None = None):
        """同步添加记忆的实现 - 使用官方Zep SDK"""
        try:
            from zep_cloud import Message as ZepMessage

            client = self._get_client()

            # 确保会话存在
            try:
                client.memory.add_session(session_id=session_id, user_id=user_id)
            except Exception as e:
                # 如果会话已存在，忽略错误
                if "already exists" not in str(e).lower():
                    logger.debug(f"Session creation response: {e}")

            # 构建Zep消息格式
            messages = [
                ZepMessage(
                    role_type="user",
                    content=human_message,
                    metadata=metadata or {}
                ),
                ZepMessage(
                    role_type="assistant",
                    content=assistant_message,
                    metadata=metadata or {}
                )
            ]

            # 使用官方SDK添加消息
            client.memory.add(
                session_id=session_id,
                messages=messages
            )

            logger.debug(f"Successfully added memory to Zep for session {session_id}")

        except ImportError:
            logger.error("zep-cloud package not installed. Please install with: pip install zep-cloud")
            raise
        except Exception as e:
            logger.error(f"Zep SDK添加记忆失败: {e}")
            raise

    async def get_memories(self, user_id: str, session_id: str, limit: int = 10) -> List[Message]:
        """获取用户记忆 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Zep API key not configured, returning empty memories")
                return []

            # P1性能调优：使用共享IO线程池
            return await run_in_shared_io_pool(
                self._get_memories_sync,
                user_id, session_id, limit
            )

        except Exception as e:
            logger.error(f"Zep记忆获取失败: {e}")
            return []  # 降级处理

    def _get_memories_sync(self, user_id: str, session_id: str, limit: int = 10) -> List[Message]:
        """同步获取记忆的实现 - 使用官方SDK"""
        try:
            client = self._get_client()

            # 使用官方SDK获取会话记忆
            memory = client.memory.get(session_id=session_id)

            messages = []
            if hasattr(memory, 'messages') and memory.messages:
                for msg in memory.messages[-limit:]:  # 获取最近的N条消息
                    messages.append({
                        "role": msg.role_type if hasattr(msg, 'role_type') else msg.role,
                        "content": msg.content
                    })

            return messages

        except ImportError:
            logger.error("zep-cloud package not installed. Please install with: pip install zep-cloud")
            return []
        except Exception as e:
            logger.error(f"Zep SDK获取记忆失败: {e}")
            return []

    async def search_memory(self, user_id: str, query: str, limit: int = 5) -> List[Message]:
        """搜索用户记忆 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Zep API key not configured, returning empty search results")
                return []

            # P1性能调优：使用共享IO线程池
            return await run_in_shared_io_pool(
                self._search_memory_sync,
                user_id, query, limit
            )

        except Exception as e:
            logger.error(f"Zep记忆搜索失败: {e}")
            return []  # 降级处理

    def _search_memory_sync(self, user_id: str, query: str, limit: int = 5) -> List[Message]:
        """同步搜索记忆的实现 - 使用官方SDK"""
        try:
            client = self._get_client()

            # 由于我们需要按用户搜索，但新的API需要session_id
            # 我们可以通过查找用户的会话，然后搜索每个会话
            # 或者使用graph搜索功能
            try:
                # 首先尝试使用graph搜索，这是更好的方法
                response = client.graph.search(
                    user_id=user_id,
                    query=query,
                    limit=limit
                )

                messages = []
                if hasattr(response, 'edges') and response.edges:
                    for edge in response.edges:
                        if hasattr(edge, 'fact'):
                            content = edge.fact
                        else:
                            content = str(edge)

                        messages.append({
                            "role": "system",
                            "content": content
                        })

                return messages

            except Exception as graph_error:
                logger.debug(f"Graph search failed, trying alternative approach: {graph_error}")

                # 如果graph搜索失败，尝试列出用户会话并搜索
                try:
                    sessions = client.user.get_sessions(user_id=user_id)
                    messages = []

                    if hasattr(sessions, 'results') and sessions.results:
                        # 搜索最近的几个会话
                        for session in sessions.results[:3]:  # 限制搜索最近3个会话
                            session_id = session.session_id if hasattr(session, 'session_id') else session.id
                            try:
                                search_result = client.memory.search(
                                    session_id=session_id,
                                    text=query,
                                    limit=limit//3 + 1  # 分配搜索配额
                                )

                                if hasattr(search_result, 'results'):
                                    for result in search_result.results:
                                        if hasattr(result, 'message'):
                                            content = result.message.content
                                        elif hasattr(result, 'content'):
                                            content = result.content
                                        else:
                                            content = str(result)

                                        messages.append({
                                            "role": "system",
                                            "content": content
                                        })

                                        if len(messages) >= limit:
                                            break

                            except Exception as session_search_error:
                                logger.debug(f"Session search failed for {session_id}: {session_search_error}")
                                continue

                            if len(messages) >= limit:
                                break

                    return messages[:limit]  # 确保不超过限制

                except Exception as session_error:
                    logger.debug(f"Session listing failed: {session_error}")
                    return []

        except ImportError:
            logger.error("zep-cloud package not installed. Please install with: pip install zep-cloud")
            return []
        except Exception as e:
            logger.error(f"Zep SDK搜索记忆失败: {e}")
            return []

    async def delete_memory(self, user_id: str, session_id: str) -> None:
        """删除用户会话记忆 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Zep API key not configured, skipping memory deletion")
                return

            # P1性能调优：使用共享IO线程池
            await run_in_shared_io_pool(
                self._delete_memory_sync,
                user_id, session_id
            )

        except Exception as e:
            logger.error(f"Zep记忆删除失败: {e}")
            # 降级处理：记忆删除失败不应影响其他操作
            pass

    def _delete_memory_sync(self, user_id: str, session_id: str):
        """同步删除记忆的实现 - 使用官方SDK"""
        try:
            client = self._get_client()

            # 使用官方SDK删除会话记忆
            client.memory.delete(session_id=session_id)

            logger.debug(f"Successfully deleted memory for session {session_id}")

        except ImportError:
            logger.error("zep-cloud package not installed. Please install with: pip install zep-cloud")
        except Exception as e:
            logger.error(f"Zep SDK删除记忆失败: {e}")

    async def get_memory_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """获取用户记忆上下文 - 使用官方SDK"""
        try:
            memories = await self.search_memory(user_id, query, limit=10)

            # 如果有搜索结果，直接使用搜索的内容作为上下文
            if memories:
                context = "\n".join([msg["content"] for msg in memories])
            else:
                context = ""

            return {
                "memories": memories,
                "context": context
            }
        except Exception as e:
            logger.error(f"Zep记忆上下文获取失败: {e}")
            return {"memories": [], "context": ""}

    async def update_session_metadata(self, user_id: str, session_id: str, summary: str, metadata: Dict[str, Any] | None = None) -> bool:
        """更新会话元数据 - 通过添加摘要消息实现"""
        try:
            if not self.api_key:
                logger.warning("Zep API key not configured, skipping metadata update")
                return False

            # 通过添加系统消息来记录会话摘要
            await self.add_memory(
                user_id=user_id,
                session_id=session_id,
                human_message=f"会话摘要: {summary}",
                assistant_message="已记录会话摘要",
                metadata=metadata
            )
            return True

        except Exception as e:
            logger.error(f"Zep会话元数据更新失败: {e}")
            return False


class Mem0MemoryServiceImpl(IMemoryService):
    """Real implementation for Mem0 Memory Service with official SDK integration."""

    def __init__(self):
        """初始化Mem0 Memory Service - 使用官方SDK"""
        self.api_key = getattr(settings, 'MEM0_API_KEY', None)
        self.timeout = 5.0  # 根据架构师建议设置5秒超时
        self.max_retries = 3

        if not self.api_key:
            logger.warning("MEM0_API_KEY not configured, Mem0MemoryService will not function properly")

    async def add_memory(
        self, user_id: str, session_id: str, human_message: str, assistant_message: str, metadata: Dict[str, Any] | None = None
    ) -> None:
        """添加记忆到Mem0 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Mem0 API key not configured, skipping memory addition")
                return

            # 使用共享IO线程池避免阻塞事件循环
            await run_in_shared_io_pool(
                self._add_memory_sync,
                user_id, session_id, human_message, assistant_message, metadata
            )

        except Exception as e:
            logger.error(f"Mem0记忆添加失败: {e}")
            # 降级处理：记忆服务失败不应影响对话继续
            pass

    def _add_memory_sync(self, user_id: str, session_id: str, human_message: str, assistant_message: str, metadata: Dict[str, Any] | None = None):
        """同步添加记忆的实现 - 使用官方SDK"""
        try:
            # 导入并初始化 Mem0 客户端
            from mem0 import MemoryClient
            import os

            # 设置 API key 环境变量
            os.environ["MEM0_API_KEY"] = self.api_key
            client = MemoryClient()

            # 构建消息格式
            messages = [
                {
                    "role": "user",
                    "content": human_message
                },
                {
                    "role": "assistant",
                    "content": assistant_message
                }
            ]

            # 构建元数据
            final_metadata = {
                "session_id": session_id,
                **(metadata or {})
            }

            # 使用官方SDK添加记忆
            result = client.add(
                messages=messages,
                user_id=user_id,
                metadata=final_metadata
            )

            logger.debug(f"Mem0 add result: {result}")

        except ImportError:
            logger.error("mem0 package not installed. Please install with: pip install mem0ai")
            raise
        except Exception as e:
            logger.error(f"Mem0 SDK添加记忆失败: {e}")
            raise

    async def get_memories(self, user_id: str, session_id: str, limit: int = 10) -> List[Message]:
        """获取用户记忆 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Mem0 API key not configured, returning empty memories")
                return []

            # 使用共享IO线程池
            return await run_in_shared_io_pool(
                self._get_memories_sync,
                user_id, session_id, limit
            )

        except Exception as e:
            logger.error(f"Mem0记忆获取失败: {e}")
            return []  # 降级处理

    def _get_memories_sync(self, user_id: str, session_id: str, limit: int = 10) -> List[Message]:
        """同步获取记忆的实现 - 使用官方SDK"""
        try:
            # 导入并初始化 Mem0 客户端
            from mem0 import MemoryClient
            import os

            # 设置 API key 环境变量
            os.environ["MEM0_API_KEY"] = self.api_key
            client = MemoryClient()

            # 使用官方SDK获取所有记忆
            result = client.get_all(user_id=user_id)

            messages = []
            if "results" in result:
                # 过滤出与当前会话相关的记忆，如果需要的话
                for memory_item in result["results"]:
                    memory_text = memory_item.get("memory", "")
                    if memory_text:
                        messages.append({
                            "role": "system",
                            "content": memory_text
                        })

                        # 限制返回数量
                        if len(messages) >= limit:
                            break

            return messages

        except ImportError:
            logger.error("mem0 package not installed. Please install with: pip install mem0ai")
            return []
        except Exception as e:
            logger.error(f"Mem0 SDK获取记忆失败: {e}")
            return []

    async def search_memory(self, user_id: str, query: str, limit: int = 5) -> List[Message]:
        """搜索用户记忆 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Mem0 API key not configured, returning empty search results")
                return []

            # 使用共享IO线程池
            return await run_in_shared_io_pool(
                self._search_memory_sync,
                user_id, query, limit
            )

        except Exception as e:
            logger.error(f"Mem0记忆搜索失败: {e}")
            return []  # 降级处理

    def _search_memory_sync(self, user_id: str, query: str, limit: int = 5) -> List[Message]:
        """同步搜索记忆的实现 - 使用官方SDK"""
        try:
            # 导入并初始化 Mem0 客户端
            from mem0 import MemoryClient
            import os

            # 设置 API key 环境变量
            os.environ["MEM0_API_KEY"] = self.api_key
            client = MemoryClient()

            # 使用官方SDK搜索记忆
            result = client.search(
                query=query,
                user_id=user_id,
                limit=limit
            )

            messages = []
            if "results" in result:
                for memory_item in result["results"]:
                    memory_text = memory_item.get("memory", "")
                    if memory_text:
                        messages.append({
                            "role": "system",
                            "content": memory_text
                        })

            return messages

        except ImportError:
            logger.error("mem0 package not installed. Please install with: pip install mem0ai")
            return []
        except Exception as e:
            logger.error(f"Mem0 SDK搜索记忆失败: {e}")
            return []

    async def delete_memory(self, user_id: str, session_id: str) -> None:
        """删除用户会话记忆 - 使用官方SDK"""
        try:
            if not self.api_key:
                logger.warning("Mem0 API key not configured, skipping memory deletion")
                return

            # 使用共享IO线程池
            await run_in_shared_io_pool(
                self._delete_memory_sync,
                user_id, session_id
            )

        except Exception as e:
            logger.error(f"Mem0记忆删除失败: {e}")
            # 降级处理：记忆删除失败不应影响其他操作
            pass

    def _delete_memory_sync(self, user_id: str, session_id: str):
        """同步删除记忆的实现 - 使用官方SDK"""
        try:
            # 导入并初始化 Mem0 客户端
            from mem0 import MemoryClient
            import os

            # 设置 API key 环境变量
            os.environ["MEM0_API_KEY"] = self.api_key
            client = MemoryClient()

            # 尝试使用 v2 API 的过滤功能来删除会话相关的记忆
            try:
                # 首先使用过滤器获取特定会话的记忆
                filters = {
                    "AND": [
                        {"user_id": user_id},
                        {"metadata": {"session_id": session_id}}
                    ]
                }

                result = client.get_all(
                    filters=filters,
                    version="v2"
                )

                deleted_count = 0
                if "results" in result:
                    for memory_item in result["results"]:
                        memory_id = memory_item.get("id")
                        if memory_id:
                            try:
                                client.delete(memory_id)
                                deleted_count += 1
                                logger.debug(f"Deleted memory {memory_id} for session {session_id}")
                            except Exception as e:
                                logger.warning(f"Failed to delete memory {memory_id}: {e}")

                logger.info(f"Deleted {deleted_count} memories for session {session_id}")

            except Exception as filter_error:
                # 如果过滤器方法失败，回退到原始方法
                logger.warning(f"Filter-based deletion failed, using fallback method: {filter_error}")

                # 获取所有记忆，然后删除与指定会话相关的记忆
                result = client.get_all(user_id=user_id)

                deleted_count = 0
                if "results" in result:
                    for memory_item in result["results"]:
                        memory_id = memory_item.get("id")
                        metadata = memory_item.get("metadata", {})

                        # 检查是否与指定会话相关
                        if metadata.get("session_id") == session_id and memory_id:
                            try:
                                client.delete(memory_id)
                                deleted_count += 1
                                logger.debug(f"Deleted memory {memory_id} for session {session_id}")
                            except Exception as e:
                                logger.warning(f"Failed to delete memory {memory_id}: {e}")

                logger.info(f"Deleted {deleted_count} memories for session {session_id} (fallback method)")

        except ImportError:
            logger.error("mem0 package not installed. Please install with: pip install mem0ai")
        except Exception as e:
            logger.error(f"Mem0 SDK删除记忆失败: {e}")

    async def get_memory_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """获取用户记忆上下文 - 使用官方SDK"""
        try:
            memories = await self.search_memory(user_id, query, limit=10)
            return {
                "memories": memories,
                "context": "\n".join([msg["content"] for msg in memories])
            }
        except Exception as e:
            logger.error(f"Mem0记忆上下文获取失败: {e}")
            return {"memories": [], "context": ""}

    async def update_session_metadata(self, user_id: str, session_id: str, summary: str, metadata: Dict[str, Any] | None = None) -> bool:
        """更新会话元数据 - 通过添加摘要记忆实现"""
        try:
            if not self.api_key:
                logger.warning("Mem0 API key not configured, skipping metadata update")
                return False

            # 通过添加摘要记忆来实现元数据更新
            await self.add_memory(
                user_id=user_id,
                session_id=session_id,
                human_message=f"会话摘要: {summary}",
                assistant_message="已记录会话摘要",
                metadata=metadata
            )
            return True

        except Exception as e:
            logger.error(f"Mem0会话元数据更新失败: {e}")
            return False


async def get_memory_service(provider: Literal["zep", "mem0"] = "mem0") -> IMemoryService:
    """获取记忆服务实例"""
    if provider == "zep":
        return ZepMemoryServiceImpl()
    elif provider == "mem0":
        return Mem0MemoryServiceImpl()
    else:
        raise ValueError(f"Unsupported memory provider: {provider}")

# Alias for test compatibility
MemoryService = IMemoryService
