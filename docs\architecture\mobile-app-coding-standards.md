# 心桥移动应用编码规范

## 概述

本文档为心桥移动应用定义了核心编码标准，基于 React Native + Expo + TypeScript + NativeWind + Zustand 技术栈，确保代码质量、可维护性和团队协作效率。

## 1. 技术栈与架构

### 1.1 核心技术栈
- **React Native** + **Expo** - 跨平台移动应用开发
- **TypeScript** - 类型安全的 JavaScript 超集
- **NativeWind** - Tailwind CSS for React Native
- **Zustand** - 轻量级状态管理
- **Supabase** - 后端即服务（认证、数据库、实时功能）
- **火山引擎 RTC** - 实时音视频通信

### 1.2 项目结构规范
```
src/
├── app/           # Expo Router 页面
├── components/    # 可复用组件
├── lib/           # 工具库和配置
├── api/          # API 客户端
├── types/        # TypeScript 类型定义
└── translations/ # 国际化文件
```

## 2. TypeScript 规范

### 2.1 类型定义
```typescript
// ✅ 优先使用 type 而非 interface
type ButtonProps = {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  onPress: () => void;
  children: React.ReactNode;
};

// ✅ 使用联合类型而非 enum
type UserRole = 'admin' | 'user' | 'guest';

// ✅ 严格的函数类型
type AsyncHandler<T> = (data: T) => Promise<void>;
```

### 2.2 组件规范
```typescript
// ✅ 标准组件结构
import * as React from 'react';
import { Pressable, Text } from 'react-native';

type Props = {
  title: string;
  onPress: () => void;
  disabled?: boolean;
};

export const Button = React.memo(({ title, onPress, disabled }: Props) => {
  const handlePress = React.useCallback(() => {
    if (!disabled) onPress();
  }, [onPress, disabled]);

  return (
    <Pressable
      className={`px-4 py-3 rounded-lg ${
        disabled ? 'bg-gray-300' : 'bg-blue-600 active:bg-blue-700'
      }`}
      onPress={handlePress}
      disabled={disabled}
    >
      <Text className="text-white font-medium text-center">{title}</Text>
    </Pressable>
  );
});
```

## 3. 状态管理 (Zustand)

### 3.1 Store 定义
```typescript
// ✅ 标准 Store 结构
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  login: (user: User, token: string) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      token: null,
      login: (user, token) => set({ user, token, isAuthenticated: true }),
      logout: () => set({ user: null, token: null, isAuthenticated: false }),
    }),
    {
      name: 'auth-storage',
    }
  )
);
```

### 3.2 Store 使用规范
```typescript
// ✅ 选择性订阅，避免不必要的重渲染
const user = useAuthStore((state) => state.user);
const login = useAuthStore((state) => state.login);

// ✅ 在组件外使用 Store
const handleLogout = () => {
  useAuthStore.getState().logout();
};
```

## 4. 环境变量与配置

### 4.1 环境变量规范
```typescript
// ✅ env.js - 环境变量配置
export const env = {
  EXPO_PUBLIC_SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL!,
  EXPO_PUBLIC_SUPABASE_ANON_KEY: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
  EXPO_PUBLIC_VOLCANO_RTC_APP_ID: process.env.EXPO_PUBLIC_VOLCANO_RTC_APP_ID!,
  EXPO_PUBLIC_VOLCANO_RTC_APP_KEY: process.env.EXPO_PUBLIC_VOLCANO_RTC_APP_KEY!,
} as const;

// ✅ 类型安全的环境变量访问
import { env } from './env';
const supabaseUrl = env.EXPO_PUBLIC_SUPABASE_URL;
```

### 4.2 配置管理
- **前端变量**: 必须使用 `EXPO_PUBLIC_` 前缀
- **敏感信息**: 禁止在前端代码中硬编码（如 SUPABASE_SERVICE_ROLE_KEY、VOLCANO_LLM_APP_KEY 等）
- **类型安全**: 所有环境变量都需要类型定义
- **后端专用**: SERVICE_ROLE_KEY、JWT_SECRET、DATABASE_URL 等仅限后端使用

## 5. API 集成规范

### 5.1 Supabase 集成
```typescript
// ✅ Supabase 客户端配置
import { createClient } from '@supabase/supabase-js';
import { env } from '@/lib/env';

export const supabase = createClient(
  env.EXPO_PUBLIC_SUPABASE_URL,
  env.EXPO_PUBLIC_SUPABASE_ANON_KEY
);

// ✅ 认证状态管理
export const useAuth = () => {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return { user };
};
```

### 5.2 API 客户端
```typescript
// ✅ 统一的 API 客户端
import { supabase } from './supabase';

export const apiClient = {
  async request<T>(
    endpoint: string,
    options: { method?: string; data?: any } = {}
  ): Promise<T> {
    const { data: { session } } = await supabase.auth.getSession();
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(session?.access_token && {
          Authorization: `Bearer ${session.access_token}`,
        }),
      },
      body: options.data ? JSON.stringify(options.data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  },
};
```

## 6. 样式与 UI 规范

### 6.1 NativeWind 使用
```typescript
// ✅ 条件样式
const buttonStyles = `
  px-4 py-3 rounded-lg font-medium
  ${variant === 'primary' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}
  ${size === 'large' ? 'px-6 py-4 text-lg' : ''}
  ${disabled ? 'opacity-50' : 'active:opacity-80'}
`;

// ✅ 样式常量
const STYLES = {
  card: 'bg-white rounded-lg shadow-sm p-4',
  button: 'px-4 py-3 rounded-lg font-medium',
  text: {
    title: 'text-xl font-bold text-gray-900',
    body: 'text-base text-gray-700',
  },
} as const;
```

### 6.2 适老化设计
```javascript
// ✅ tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      fontSize: {
        'xl-plus': '1.375rem',     // 22px
        '2xl-plus': '1.75rem',     // 28px  
        '3xl-plus': '2.25rem',     // 36px
      },
      colors: {
        // 高对比度颜色
        primary: {
          600: '#1d4ed8',
          700: '#1e40af',
        },
      },
    },
  },
  plugins: [],
};
```

## 7. 性能优化规范

### 7.1 组件优化
```typescript
// ✅ 使用 React.memo 和 useCallback
const ListItem = React.memo(({ item, onPress }: Props) => {
  const handlePress = React.useCallback(() => {
    onPress(item.id);
  }, [item.id, onPress]);

  return (
    <Pressable onPress={handlePress}>
      <Text>{item.title}</Text>
    </Pressable>
  );
});

// ✅ 长列表必须使用 FlashList
import { FlashList } from '@shopify/flash-list';

const renderItem = ({ item }: { item: Item }) => (
  <ListItem item={item} onPress={handleItemPress} />
);

<FlashList
  data={items}
  renderItem={renderItem}
  estimatedItemSize={80}
  getItemType={(item) => item.type}
/>
```

### 7.2 资源优化
```typescript
// ✅ 使用 expo-image
import { Image } from 'expo-image';

<Image
  source={{ uri: imageUrl }}
  style={{ width: 100, height: 100 }}
  placeholder={require('./placeholder.png')}
  contentFit="cover"
  transition={1000}
/>
```

## 8. 错误处理与安全

### 8.1 错误边界
```typescript
// ✅ 应用级错误处理
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }: Props) {
  return (
    <View className="flex-1 justify-center items-center p-4">
      <Text className="text-red-600 text-lg font-medium mb-4">
        应用出现错误
      </Text>
      <Text className="text-gray-600 text-center mb-4">
        {error.message}
      </Text>
      <Button title="重试" onPress={resetErrorBoundary} />
    </View>
  );
}

// 在 app/_layout.tsx 中使用
<ErrorBoundary FallbackComponent={ErrorFallback}>
  <Slot />
</ErrorBoundary>
```

### 8.2 输入验证
```typescript
// ✅ 使用 Zod 进行数据验证
import { z } from 'zod';

const UserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(8, '密码至少8位'),
  age: z.number().min(18, '用户年龄必须大于18岁'),
});

type User = z.infer<typeof UserSchema>;

// 表单验证
const validateUser = (data: unknown): User => {
  return UserSchema.parse(data);
};
```

## 9. 测试规范

### 9.1 单元测试
```typescript
// ✅ 组件测试
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '../Button';

describe('Button', () => {
  it('should call onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={onPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(onPress).toHaveBeenCalledTimes(1);
  });
});
```

### 9.2 Hook 测试
```typescript
// ✅ 自定义 Hook 测试
import { renderHook, act } from '@testing-library/react-hooks';
import { useAuthStore } from '../stores/auth';

describe('useAuthStore', () => {
  it('should login user', () => {
    const { result } = renderHook(() => useAuthStore());
    
    act(() => {
      result.current.login(mockUser, mockToken);
    });
    
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toEqual(mockUser);
  });
});
```

## 10. 开发工具配置

### 10.1 必需工具
```json
// package.json scripts
{
  "scripts": {
    "start": "expo start",
    "android": "expo run:android",
    "ios": "expo run:ios",
    "web": "expo start --web",
    "lint": "eslint . --ext .ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch"
  }
}
```

### 10.2 代码质量
- **ESLint + Prettier** - 代码格式化和规范检查
- **TypeScript** - 严格类型检查
- **Husky** - Git hooks 自动化
- **Jest + React Native Testing Library** - 单元测试

## 11. 最佳实践总结

### ✅ 必须遵循
1. **类型安全** - 所有函数和变量都需要明确的类型定义
2. **环境变量** - 使用 `EXPO_PUBLIC_` 前缀，禁止硬编码敏感信息
3. **状态管理** - 使用 Zustand 管理全局状态，避免 prop drilling
4. **性能优化** - 长列表使用 FlashList，合理使用 memo 和 callback
5. **错误处理** - 使用 ErrorBoundary，统一的错误处理机制
6. **测试覆盖** - 关键组件和 Hook 必须有单元测试

### ⚠️ 禁止实践
1. **硬编码** - 任何敏感信息或配置都不能硬编码
2. **全局状态滥用** - 不要将所有状态都放入全局 Store
3. **内联函数** - 避免在 render 中直接定义函数
4. **未验证的用户输入** - 所有用户输入都必须经过验证
5. **资源泄漏** - useEffect 必须正确清理副作用

---

*本规范确保移动应用的代码质量、安全性和可维护性，所有开发者必须严格遵循。* 