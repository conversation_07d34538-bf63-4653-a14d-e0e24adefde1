<ReadMe.LLM>

<rules>
  <rule>1. **后端是总控中心 (Backend as Control Center)**: 您的Python后端服务负责整个AI对话生命周期的管理。这包括为客户端安全地生成Token，并通过调用OpenAPI来启动 (`StartVoiceChat`)、控制 (`UpdateVoiceChat`) 和停止 (`StopVoiceChat`) AI智能体。</rule>
  <rule>2. **Token是安全通行证 (Token is the Secure Pass)**: 客户端的所有RTC操作（如加入房间）都依赖您后端服务生成的Token。Token的生成与管理是后端的核心职责，必须确保安全和时效性。**严禁在客户端代码中硬编码或生成Token**。</rule>
  <rule>3. **异步与回调驱动 (Asynchronous and Callback-Driven)**: 许多服务端操作（如启动任务）是异步的。您必须部署一个能够接收HTTP(S) POST请求的Webhook服务。火山引擎RTC服务会通过这个回调地址，向您实时推送任务状态（如AI正在思考、正在说话）和Function Calling请求。请务必实现回调签名的验证逻辑。</rule>
  <rule>4. **ID的唯一性与关联性 (Uniqueness and Correlation of IDs)**: `RoomId`标识了用户与AI所在的同一个虚拟房间。`TaskId`是您为每一次AI对话会话定义的唯一ID，后续的更新和停止操作都依赖此ID。您的服务需要维护这些ID的映射关系。</rule>
  <rule>5. **主动交互是关键 (Proactive Interaction is Key)**: AI的主动提醒功能（如吃药提醒）是通过后端调用`UpdateVoiceChat`接口实现的。您的后端业务逻辑（如定时任务、数据库触发器）是这类功能的触发源，而不是客户端。</rule>
</rules>

<library_description>
本文档为火山引擎实时音视频（veRTC）的LLM专用说明，旨在指导Python后端开发者实现一个与前端App用户进行实时语音聊天的AI智能体。核心是通过调用服务端OpenAPI，编排ASR（语音识别）、LLM（大模型）、TTS（语音合成）服务，创建一个能进入RTC房间、具备记忆、并能主动与用户交互的AI语音机器人。
</library_description>

<!-- ================================================================== -->
<!-- 以下为后端开发者实现AI语音伴侣所需的核心功能上下文 -->
<!-- ================================================================== -->

<context_1>
  <context_1_description>
    **基础：为客户端生成身份凭证 (Token)**
    这是所有功能的前提。您的后端必须提供一个API端点，供客户端App在加入房间前调用，以获取合法的、有时效性的Token。Token中包含了AppID、UserID和RoomID等关键信息，用于RTC服务的鉴权。
  </context_1_description>

  <context_1_code_snippet>
    <!-- 推荐后端暴露的接口形式 -->
    POST /api/v1/generate_token
    Request Body: {"room_id": "string", "user_id": "string"}
    Response Body: {"token": "string"}

    <!-- Python 后端 (使用Flask框架的示例) -->
    <!-- 需要安装: pip install volcengine -->
    from flask import Flask, request, jsonify
    from volcengine.rtc.AccessToken import AccessToken
    import time

    app = Flask(__name__)

    APP_ID = "YOUR_APP_ID"
    APP_KEY = "YOUR_APP_KEY" # 严禁暴露在客户端

    @app.route('/api/v1/generate_token', methods=['POST'])
    def get_rtc_token():
        data = request.json
        room_id = data.get('room_id')
        user_id = data.get('user_id')
        
        if not room_id or not user_id:
            return jsonify({"error": "room_id and user_id are required"}), 400

        # Token有效期，例如2小时
        expire_timestamp = int(time.time()) + 7200
        
        # 创建AccessToken对象
        token_builder = AccessToken(APP_ID, APP_KEY, room_id, user_id)
        token_builder.expire_time(expire_timestamp)
        
        # 授予发布和订阅流的权限
        token_builder.add_privilege(AccessToken.priv_publish_stream, 0)
        token_builder.add_privilege(AccessToken.priv_subscribe_stream, 0)
        
        token_str, err = token_builder.serialize()
        if err:
            return jsonify({"error": "Failed to generate token"}), 500
        
        return jsonify({"token": token_str})
  </context_1_code_snippet>

  <context_1_examples>
    # 客户端在启动聊天前，向后端的`/api/v1/generate_token`接口发起请求。
    # 请求中携带自己的UserID和要进入的RoomID。
    # 后端验证通过后，调用上述函数生成Token，并将其返回给客户端。
    # 客户端使用此Token初始化RTC SDK并成功加入房间后，后端才可以调用StartVoiceChat。
  </context_1_examples>
</context_1>

<context_2>
  <context_2_description>
    **核心：启动和停止AI智能体**
    当用户进入App的聊天界面时，后端需要调用 `StartVoiceChat` 接口，创建一个AI智能体并让它“加入”到用户所在的RTC房间。对话结束时（如用户退出界面或通话超时），务必调用 `StopVoiceChat` 释放云端资源，避免产生不必要的计费。
  </context_2_description>

  <context_2_code_snippet>
    <!-- 启动AI智能体 -->
    POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
    Body:
    {
      "AppId": "string",
      "RoomId": "string",
      "TaskId": "string", // 任务唯一ID，由您定义
      "AgentConfig": {
        "UserId": "string", // AI智能体的ID
        "TargetUserId": ["string"], // AI需要对话的用户ID
        "WelcomeMessage": "string" // 开场白
      },
      "LLMConfig": { ... }, // 大模型配置，见Context 3
      "ASRConfig": { ... }, // 语音识别配置
      "TTSConfig": { ... }  // 语音合成配置
    }

    <!-- 停止AI智能体 -->
    POST https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01
    Body:
    {
      "AppId": "string",
      "RoomId": "string",
      "TaskId": "string"
    }
  </context_2_code_snippet>

  <context_2_examples>
    # Python 后端服务函数示例 (示意代码，需补充火山引擎API签名逻辑)
    import requests
    
    def start_ai_chat(app_id, room_id, user_id, ai_user_id, task_id):
        # ... 构造请求体和头部签名 ...
        # response = requests.post(...)
        # 成功后，AI会播报WelcomeMessage，并开始监听用户的语音
        pass

    def stop_ai_chat(app_id, room_id, task_id):
        # ... 构造请求体和头部签名 ...
        # response = requests.post(...)
        # 成功后，AI会离开房间，相关资源被释放
        pass
  </context_2_examples>
</context_2>

<context_3>
  <context_3_description>
    **大脑：赋予AI记忆和人格 (LLM配置)**
    为了让AI有记忆并保持特定的人设，您需要在调用`StartVoiceChat`时，精心构造`LLMConfig`。`SystemMessages`用于设定角色和规则（如“你是一个健康助手”），`HistoryLength`用于控制上下文记忆轮数，这对于实现连续、有逻辑的对话至关重要。
  </context_3_description>

  <context_3_code_snippet>
    <!-- StartVoiceChat Body中的LLMConfig部分 -->
    "LLMConfig": {
      "Mode": "ArkV3", // 或 "CozeBot", "CustomLLM"
      "EndPointId": "YOUR_LLM_ENDPOINT_ID", // 使用火山方舟时
      "SystemMessages": [
        "string" // AI的角色设定和行为准则
      ],
      "UserPrompts": [ // 用于提供对话示例，引导AI回复
        {"Role": "user", "Content": "你好"},
        {"Role": "assistant", "Content": "你好呀，我是您的健康助手小宁。"}
      ],
      "HistoryLength": 10 // 记住最近10轮对话
    }
  </context_3_code_snippet>

  <context_3_examples>
    <!-- 针对"有记忆的吃药提醒助手"的LLMConfig配置示例 -->
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "ep-xxxxxxxxxxxx",
      "SystemMessages": [
        "你是一个名叫“小康”的贴心健康助手，你的任务是与用户愉快地聊天，并根据用户的健康计划提醒他们按时吃药。",
        "你的语气总是友好、关怀备至。",
        "当被问及不了解的专业医疗问题时，要回答“这个问题我需要查一下，建议您咨询专业医生哦”。"
      ],
      "HistoryLength": 20 // 增加记忆长度以更好地追踪健康话题
    }
  </context_3_examples>
</context_3>

<context_4>
  <context_4_description>
    **主动交互：让AI发起提醒或动态干预**
    为了实现“提醒用户吃药”这类由后端逻辑触发的需求，需要调用`UpdateVoiceChat`接口。`Command`参数是核心，`ExternalTextToSpeech`让AI直接播报文本，`ExternalTextToLLM`则将文本作为新一轮用户输入送给LLM处理，AI会根据此信息生成回复。
  </context_4_description>

  <context_4_code_snippet>
    POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
    Body:
    {
      "AppId": "string",
      "RoomId": "string",
      "TaskId": "string",
      "Command": "string", // 可选 "interrupt", "ExternalTextToSpeech", "ExternalTextToLLM", "FinishSpeechRecognition", "function"
      "Message": "string", // Command为...ToSpeech, ...ToLLM, function时必填
      "InterruptMode": 1 // 播报优先级，1为高优，会打断当前对话
    }
  </context_4_code_snippet>

  <context_4_examples>
    # Python 后端服务函数示例，可由定时任务(如Celery, APScheduler)触发
    def send_ai_reminder(app_id, room_id, task_id, reminder_text):
        """让AI主动向用户发送语音提醒。"""
        # ... 构造请求体和头部签名 ...
        body = {
            "AppId": app_id, "RoomId": room_id, "TaskId": task_id,
            "Command": "ExternalTextToSpeech",
            "Message": reminder_text,
            "InterruptMode": 1 # 高优先级，立即打断并播报
        }
        # requests.post(...)

    # 示例调用
    # send_ai_reminder("YOUR_APP_ID", "user_xxx_room", "task_for_user_xxx", "主人，到时间吃降压药啦！")
  </context_4_examples>
</context_4>

<context_5>
  <context_5_description>
    **扩展能力：调用外部工具 (Function Calling)**
    为了让AI能获取实时信息（如天气）或操作外部系统（如查询用户的用药时间表），可以使用Function Calling功能。后端在启动AI时定义好工具，当AI识别用户意图后会通过Webhook请求后端执行该工具，后端执行完毕再将结果通过`UpdateVoiceChat`接口返回给AI。
  </context_5_description>

  <context_5_code_snippet>
    <!-- 1. 在StartVoiceChat中定义工具和回调地址 -->
    "LLMConfig": {
        "Tools": [{
            "Type": "function",
            "function": { "name": "get_medication_schedule", "description": "查询用户今日的用药计划", "parameters": { ... } }
        }]
    },
    "FunctionCallingConfig": {
      "ServerMessageUrl": "https://your-backend.com/api/v1/function_callback",
      "ServerMessageSignature": "your_secret_signature"
    }

    <!-- 2. 后端接收RTC的回调POST请求 -->
    // POST /api/v1/function_callback Body: {"message": [...], "signature": "..."}

    <!-- 3. 后端执行工具后，调用UpdateVoiceChat返回结果 -->
    POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
    Body:
    {
      "Command": "function",
      "Message":"{\"ToolCallID\":\"call_id_123\",\"Content\":\"北京今日晴，25摄氏度\"}"
    }
  </context_5_code_snippet>

  <context_5_examples>
    # Python后端(Flask)处理Function Calling回调的示例
    @app.route('/api/v1/function_callback', methods=['POST'])
    def handle_function_call():
        data = request.json
        # 1. 校验Signature
        
        # 2. 解析请求
        tool_call = data['message'][0]
        tool_call_id = tool_call['id']
        function_name = tool_call['function']['name']
        
        # 3. 执行本地函数
        result_content = ""
        if function_name == 'get_medication_schedule':
            result_content = get_user_schedule_from_db(data.get('UserId'))
        
        # 4. 将执行结果返回给RTC
        update_voice_chat_with_function_result(tool_call_id, result_content)
        
        return jsonify({"status": "ok"}), 200
  </context_5_examples>
</context_5>

</ReadMe.LLM>