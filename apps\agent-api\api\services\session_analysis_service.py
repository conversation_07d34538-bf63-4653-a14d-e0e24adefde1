"""
故事1.5：会话后分析与外部记忆服务同步

实现会话结束后的异步分析任务：
1. 对话摘要生成 - 使用LLM生成会话摘要和关键信息
2. 记忆服务同步 - 将摘要同步到外部记忆服务(Mem0/Zep)
3. 大型会话处理 - 智能分块处理超长对话
4. 异步任务可靠性保障 - 状态跟踪、重试机制、幂等性

关键架构师建议:
- 大型会话分块处理：最大4000 tokens，智能分段
- 异步任务可靠性：状态跟踪(pending/processing/completed/failed)，重试机制，5分钟超时
- 记忆服务容错：同步失败时降级策略，不影响会话结束
- 幂等性设计：避免重复分析同一会话
"""

import asyncio
import json
import time
import gc  # 添加垃圾回收模块
from datetime import datetime
from typing import Dict, List, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import AsyncSessionLocal
from api.services.llm_proxy_service import LLMProxyService
from api.services.memory_service import IMemoryService, get_memory_service
from api.settings import get_settings, logger

# 模块级别的配置实例
settings = get_settings()

# === 核心服务类 ===

class SessionAnalysisService:
    """
    会话后分析服务

    实现故事1.5的核心功能：
    1. 会话结束后的异步分析
    2. LLM摘要生成
    3. 外部记忆服务同步
    4. 大型会话分块处理
    5. 异步任务可靠性保障
    """

    def __init__(
        self,
        memory_service: Optional[IMemoryService] = None,
        llm_service: Optional[LLMProxyService] = None
    ):
        self._memory_service = memory_service
        self.llm_service = llm_service or LLMProxyService()

    async def _get_memory_service(self) -> IMemoryService:
        """懒加载获取记忆服务"""
        if self._memory_service is None:
            self._memory_service = await get_memory_service()
        return self._memory_service

    async def analyze_session_and_sync_memory(self, session_id: str) -> Dict[str, Any]:
        """
        会话后分析主入口函数 - 修复版本

        Args:
            session_id: 会话ID

        Returns:
            分析结果摘要

        实现架构师建议的核心功能：
        - 300秒超时保护机制
        - 内存管理和结果清理
        - 状态跟踪和幂等性检查
        - 大型会话分块处理
        - 记忆服务同步和容错
        """
        start_time = time.time()
        logger.info(f"开始会话后分析 - SessionId: {session_id}")

        try:
            # 架构师建议：添加300秒超时保护机制
            return await asyncio.wait_for(
                self._execute_analysis_workflow(session_id),
                timeout=300  # 5分钟超时限制
            )
        except asyncio.TimeoutError:
            logger.error(f"会话分析超时: {session_id}")
            await self._update_analysis_status(session_id, "timeout")
            return {"status": "timeout", "session_id": session_id}
        except Exception as e:
            logger.error(f"会话分析失败: {session_id}, 错误: {e}", exc_info=True)
            await self._update_analysis_status(session_id, "failed")
            return {"status": "failed", "session_id": session_id, "error": str(e)}
        finally:
            # 架构师建议：显式调用垃圾回收防止内存积累
            gc.collect()
            logger.debug(f"会话分析清理完成: {session_id}")

    async def _execute_analysis_workflow(self, session_id: str) -> Dict[str, Any]:
        """
        执行分析工作流的核心逻辑

        架构师建议：将核心逻辑分离，便于超时保护
        """
        start_time = time.time()

        try:
            # 1. 幂等性检查 - 避免重复分析
            if await self._check_analysis_already_done(session_id):
                logger.info(f"会话 {session_id} 已分析完成，跳过重复处理")
                return {"status": "already_completed", "session_id": session_id}

            # 2. 更新状态为处理中
            await self._update_analysis_status(session_id, "processing")

            # 3. 获取会话消息
            messages = await self._get_session_messages(session_id)
            if not messages:
                logger.warning(f"会话 {session_id} 没有消息，跳过分析")
                await self._update_analysis_status(session_id, "completed")
                return {"status": "no_messages", "session_id": session_id}

            # 4. 生成摘要（支持大型会话分块处理）
            summary_result = await self._generate_session_summary_with_timeout(messages)

                        # 5. 同步到记忆服务（容错机制）
            # 获取user_id（从session表获取）
            user_id = await self._get_session_user_id(session_id)

            sync_success = await self._sync_to_memory_service_with_retry(
                session_id, summary_result["summary"], summary_result["metadata"], user_id
            )

            # 6. 更新最终状态
            final_status = "completed" if sync_success else "sync_failed"
            await self._update_analysis_status(session_id, final_status)

            # 7. 性能监控
            elapsed_time = time.time() - start_time
            if settings.SESSION_ANALYSIS_LOG_PERFORMANCE:
                logger.info(f"会话后分析完成 - SessionId: {session_id}, 耗时: {elapsed_time:.2f}s, 状态: {final_status}")

            return {
                "status": final_status,
                "session_id": session_id,
                "summary": summary_result["summary"],
                "sync_success": sync_success,
                "elapsed_time": elapsed_time
            }

        except Exception as e:
            logger.error(f"分析工作流执行失败 - SessionId: {session_id}, 错误: {str(e)}", exc_info=True)
            await self._update_analysis_status(session_id, "failed")
            raise

    async def _generate_session_summary_with_timeout(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        带超时的会话摘要生成

        实现架构师建议的大型会话处理策略
        """
        try:
            # 使用配置的超时时间
            return await asyncio.wait_for(
                self._generate_session_summary(messages),
                timeout=settings.SESSION_ANALYSIS_TIMEOUT_SECONDS
            )
        except asyncio.TimeoutError:
            logger.error(f"会话摘要生成超时 (>{settings.SESSION_ANALYSIS_TIMEOUT_SECONDS}s)")
            raise

    async def _generate_session_summary(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成会话摘要

        实现架构师建议的大型会话分块处理机制
        """
        logger.info(f"开始生成会话摘要，消息数量: {len(messages)}")

        # 检查是否需要分块处理
        total_text = self._combine_messages_to_text(messages)
        estimated_tokens = len(total_text) // 4  # 粗略估计token数（1 token ≈ 4 chars）

        if estimated_tokens <= settings.SESSION_ANALYSIS_MAX_TOKENS:
            # 小型会话，直接处理
            return await self._generate_single_summary(total_text)
        else:
            # 大型会话，分块处理
            logger.info(f"大型会话检测 (估计 {estimated_tokens} tokens)，启用分块处理")
            return await self._generate_chunked_summary(messages)

    async def _sync_to_memory_service_with_retry(
        self, session_id: str, summary: str, metadata: Dict[str, Any], user_id: Optional[str] = None
    ) -> bool:
        """
        带重试机制的记忆服务同步

        实现架构师建议的容错机制
        """
        if not settings.MEMORY_SYNC_ENABLE_FALLBACK:
            logger.info("记忆服务同步已禁用，跳过同步")
            return True

        for attempt in range(settings.MEMORY_SYNC_MAX_RETRIES + 1):
            try:
                # 使用配置的超时时间
                await asyncio.wait_for(
                    self._sync_to_memory_service(session_id, summary, metadata, user_id),
                    timeout=settings.MEMORY_SYNC_TIMEOUT_SECONDS
                )
                logger.info(f"记忆服务同步成功 - SessionId: {session_id}")
                return True

            except asyncio.TimeoutError:
                logger.warning(f"记忆服务同步超时 - SessionId: {session_id}, 尝试 {attempt + 1}")
            except Exception as e:
                logger.warning(f"记忆服务同步失败 - SessionId: {session_id}, 尝试 {attempt + 1}, 错误: {str(e)}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < settings.MEMORY_SYNC_MAX_RETRIES:
                wait_time = settings.SESSION_ANALYSIS_RETRY_BASE_DELAY ** attempt
                logger.info(f"等待 {wait_time:.1f}s 后重试")
                await asyncio.sleep(wait_time)

        # 所有重试都失败了，但根据架构师建议不应影响会话结束
        logger.error(f"记忆服务同步最终失败 - SessionId: {session_id}，启用降级策略")
        return False

    async def _generate_single_summary(self, conversation_text: str) -> Dict[str, Any]:
        """生成单个摘要"""
        prompt = f"""请为以下对话生成一个简洁、准确的摘要（100-300字）：

对话内容：
{conversation_text}

要求：
1. 捕捉对话的核心主题和关键信息
2. 简洁明了，避免冗余
3. 保持客观中性的语调"""

        try:
            response = await self.llm_service.generate_text(
                prompt=prompt,
                max_tokens=300,
                temperature=0.3
            )
            return {"summary": response.strip() if response else "", "metadata": {}}
        except Exception as e:
            logger.error(f"LLM摘要生成失败: {e}")
            raise

    async def _generate_chunked_summary(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        大型会话的分块处理（架构师关注点）

        将对话分成多个块，分别生成摘要，然后整合
        """
        try:
            # 智能分块：按对话轮次分块，每块不超过4000 tokens
            chunks = self._split_messages_into_chunks(messages, settings.SESSION_ANALYSIS_MAX_TOKENS)

            chunk_summaries = []
            for i, chunk in enumerate(chunks):
                chunk_text = self._combine_messages_to_text(chunk)
                chunk_summary = await self._generate_single_summary(chunk_text)
                if chunk_summary["summary"]:
                    chunk_summaries.append(chunk_summary["summary"])

            if not chunk_summaries:
                raise Exception("所有分块的摘要生成都失败了")

            # 使用专门的合并方法整合各分块摘要
            final_summary = await self._merge_chunk_summaries(chunk_summaries)

            return {"summary": final_summary, "metadata": {}}

        except Exception as e:
            logger.error(f"分块摘要生成失败: {e}")
            raise

    def _split_messages_into_chunks(self, messages: List[Dict[str, Any]], max_tokens: int) -> List[List[Dict[str, Any]]]:
        """
        智能分块：将消息列表分成多个块，每块不超过max_tokens
        """
        chunks = []
        current_chunk = []
        current_tokens = 0

        for message in messages:
            # 估算消息的token数
            message_tokens = len(message.get("content", "")) // 4

            if current_tokens + message_tokens > max_tokens and current_chunk:
                # 当前块已满，开始新块
                chunks.append(current_chunk)
                current_chunk = [message]
                current_tokens = message_tokens
            else:
                current_chunk.append(message)
                current_tokens += message_tokens

        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk)

        logger.info(f"将 {len(messages)} 条消息分成 {len(chunks)} 个块进行处理")
        return chunks

    def _combine_messages_to_text(self, messages: List[Dict[str, Any]]) -> str:
        """将消息列表构建成对话文本"""
        conversation_lines = []
        for message in messages:
            role = message.get("role", "unknown")
            content = message.get("content", "")
            timestamp = message.get("timestamp", "")

            role_label = "用户" if role == "user" else "AI助手" if role == "assistant" else role
            conversation_lines.append(f"[{timestamp}] {role_label}: {content}")

        return "\n".join(conversation_lines)

    async def _sync_to_memory_service(self, session_id: str, summary: str, metadata: Dict[str, Any], user_id: Optional[str] = None):
        """尝试同步到外部记忆服务"""
        memory_service = await self._get_memory_service()

        if user_id:
            await memory_service.update_session_metadata(user_id, session_id, summary, metadata)
            logger.info(f"尝试同步摘要到记忆服务 - SessionId: {session_id}, UserId: {user_id}")
        else:
            logger.warning(f"无法获取user_id，跳过记忆服务同步 - SessionId: {session_id}")
            # 可以选择使用一个默认的user_id或者跳过同步
            # await memory_service.update_session_metadata("unknown_user", session_id, summary, metadata)

    async def _get_session_user_id(self, session_id: str) -> Optional[str]:
        """从chat_sessions表获取session对应的user_id"""
        try:
            async with AsyncSessionLocal() as db:
                query = text("SELECT user_id FROM chat_sessions WHERE id = :session_id")
                result = await db.execute(query, {"session_id": session_id})
                row = result.fetchone()

                if row and row.user_id:
                    return row.user_id
                else:
                    logger.warning(f"Session {session_id} 没有关联的user_id")
                    return None

        except Exception as e:
            logger.error(f"获取Session user_id失败: {e}")
            return None

    async def _get_session_messages(self, session_id: str) -> List[Dict[str, Any]]:
        """
        从数据库获取会话的完整对话记录

        数据来源说明：消息来自`/api/v1/chat/rtc_event_handler`和`/text_message`接口的实时保存
        """
        try:
            async with AsyncSessionLocal() as db:
                # 查询chat_messages表获取完整对话记录
                query = text("""
                    SELECT id, role, content, created_at, metadata
                    FROM chat_messages
                    WHERE session_id = :session_id
                    ORDER BY created_at ASC
                """)
                result = await db.execute(query, {"session_id": session_id})
                rows = result.fetchall()

                messages = []
                for row in rows:
                    # 标准化消息格式，确保角色字段正确
                    role = "user" if row.role == "user" else "assistant" if row.role == "assistant" else row.role
                    messages.append({
                        "id": row.id,
                        "role": role,
                        "content": row.content,
                        "timestamp": row.created_at.isoformat() if row.created_at else None,
                        "metadata": row.metadata
                    })

                logger.debug(f"获取到会话 {session_id} 的 {len(messages)} 条消息")
                return messages

        except Exception as e:
            logger.error(f"获取会话消息失败 - SessionId: {session_id}, Error: {e}", exc_info=True)
            raise

    async def _merge_chunk_summaries(self, chunk_summaries: List[str]) -> str:
        """
        合并分块摘要为最终摘要

        使用LLM将多个分块摘要合并为一个连贯的完整摘要
        """
        if not chunk_summaries:
            return "无法生成摘要（无有效分块）"

        if len(chunk_summaries) == 1:
            return chunk_summaries[0]

        try:
            combined_text = "\n\n".join(chunk_summaries)

            prompt = f"""以下是一个对话的多个阶段摘要，请将它们合并为一个连贯、完整的摘要（200-400字）：

{combined_text}

请生成一个综合性摘要，整合所有阶段的关键信息："""

            response = await self.llm_service.generate_text(
                prompt=prompt,
                max_tokens=400,
                temperature=0.3
            )

            return response.strip() if response else "合并摘要失败"

        except Exception as e:
            logger.error(f"合并分块摘要失败: {e}")
            # 降级策略：简单连接
            return f"对话摘要（分{len(chunk_summaries)}个阶段）：" + " | ".join(chunk_summaries)

    def _generate_fallback_summary(self, messages: List[Dict[str, Any]]) -> str:
        """生成降级摘要（当LLM不可用时）"""
        if not messages:
            return "空会话"

        user_messages = [m for m in messages if m.get("role") == "user"]
        ai_messages = [m for m in messages if m.get("role") == "assistant"]

        return f"会话包含{len(user_messages)}条用户消息和{len(ai_messages)}条AI回复，总计{len(messages)}条消息。（基础摘要，LLM服务不可用）"

    async def _check_analysis_already_done(self, session_id: str) -> bool:
        """检查会话分析状态（幂等性检查）"""
        try:
            async with AsyncSessionLocal() as db:
                query = text("SELECT analysis_status FROM chat_sessions WHERE id = :session_id")
                result = await db.execute(query, {"session_id": session_id})
                row = result.fetchone()
                return row.analysis_status == "completed" if row else False
        except Exception as e:
            logger.error(f"检查分析状态失败 - SessionId: {session_id}, Error: {e}")
            return False

    async def _update_analysis_status(self, session_id: str, status: str):
        """更新分析状态到数据库"""
        try:
            async with AsyncSessionLocal() as db:
                await db.execute(
                    text("""
                        UPDATE chat_sessions
                        SET analysis_status = :status, updated_at = :updated_at
                        WHERE id = :session_id
                    """),
                    {
                        "session_id": session_id,
                        "status": status,
                        "updated_at": datetime.now()
                    }
                )
                await db.commit()
                logger.debug(f"分析状态已更新 - SessionId: {session_id}, Status: {status}")
        except Exception as e:
            logger.error(f"更新分析状态失败 - SessionId: {session_id}, Status: {status}, Error: {e}")

    async def generate_session_summary(self, conversation_text: str) -> str:
        """
        统一的会话摘要生成方法

        Args:
            conversation_text: 对话文本内容

        Returns:
            生成的会话摘要

        这是P2架构一致性修复的核心方法，统一了所有会话摘要生成逻辑
        """
        try:
            # 使用LLM生成摘要
            summary_prompt = f"""
请为以下对话生成一个简洁的摘要，重点突出：
1. 对话的主要话题
2. 用户的核心需求或问题
3. 提供的建议或解决方案
4. 重要的情感或关键信息

对话内容：
{conversation_text}

请生成一个100字以内的摘要：
"""

            summary = await self.llm_service.generate_text(
                prompt=summary_prompt,
                max_tokens=200,
                temperature=0.3
            )

            if not summary or len(summary.strip()) < 10:
                # 如果LLM生成失败，使用fallback逻辑
                return self._generate_fallback_summary_from_text(conversation_text)

            return summary.strip()

        except Exception as e:
            logger.error(f"会话摘要生成失败: {e}")
            return self._generate_fallback_summary_from_text(conversation_text)

    def _generate_fallback_summary_from_text(self, conversation_text: str) -> str:
        """
        从对话文本生成fallback摘要

        Args:
            conversation_text: 对话文本内容

        Returns:
            fallback摘要
        """
        try:
            lines = conversation_text.split('\n')
            user_messages = []
            ai_messages = []

            for line in lines:
                line = line.strip()
                if line.startswith('user:') or line.startswith('用户:'):
                    user_messages.append(line)
                elif line.startswith('assistant:') or line.startswith('助手:'):
                    ai_messages.append(line)

            # 生成基础摘要
            summary_parts = []

            if user_messages:
                first_user_msg = user_messages[0].split(':', 1)[-1].strip()
                if len(first_user_msg) > 50:
                    first_user_msg = first_user_msg[:50] + "..."
                summary_parts.append(f"用户询问: {first_user_msg}")

            if len(user_messages) > 1:
                summary_parts.append(f"共进行了{len(user_messages)}轮对话")

            if ai_messages:
                summary_parts.append("AI提供了相关建议和回复")

            if summary_parts:
                return "；".join(summary_parts) + "。"
            else:
                return "用户与AI进行了对话交流。"

        except Exception as e:
            logger.error(f"Fallback摘要生成失败: {e}")
            return "对话摘要生成失败。"

    async def analyze_session_async(self, session_id: str, messages: List[Dict[str, Any]]) -> None:
        """
        异步分析会话方法，用于后台任务

        Args:
            session_id: 会话ID
            messages: 会话消息列表
        """
        try:
            logger.info(f"开始异步分析会话 - SessionId: {session_id}")

            # 生成对话文本
            conversation_text = self._combine_messages_to_text(messages)

            # 生成摘要
            summary = await self.generate_session_summary(conversation_text)

            # 同步到记忆服务
            memory_service = await self._get_memory_service()

            # 提取用户ID（假设第一条消息包含用户信息）
            user_id = None
            for msg in messages:
                if msg.get('user_id'):
                    user_id = msg['user_id']
                    break

            if user_id:
                await memory_service.update_session_metadata(
                    user_id=user_id,
                    session_id=session_id,
                    summary=summary,
                    metadata={"analysis_type": "async", "timestamp": datetime.now().isoformat()}
                )

            logger.info(f"异步分析完成 - SessionId: {session_id}")

        except Exception as e:
            logger.error(f"异步分析失败 - SessionId: {session_id}, Error: {e}")


def get_session_analysis_service() -> SessionAnalysisService:
    """获取会话分析服务实例（依赖注入）"""
    return SessionAnalysisService()


# 用于测试的mock函数（供测试文件patch使用）
async def get_session_messages(session_id: str) -> List[Dict[str, Any]]:
    """获取会话消息（独立函数，便于测试mock）"""
    service = get_session_analysis_service()
    return await service._get_session_messages(session_id)


async def update_analysis_status(session_id: str, status: str):
    """更新分析状态（独立函数，便于测试mock）"""
    service = get_session_analysis_service()
    await service._update_analysis_status(session_id, status)


async def check_analysis_status(session_id: str) -> str:
    """检查分析状态（独立函数，便于测试mock）"""
    service = get_session_analysis_service()
    return await service._check_analysis_already_done(session_id)


async def get_memory_service_for_test() -> IMemoryService:
    """获取记忆服务（独立函数，便于测试mock）"""
    return await get_memory_service()
