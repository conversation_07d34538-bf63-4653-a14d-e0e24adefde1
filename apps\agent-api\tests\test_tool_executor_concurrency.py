"""
并发安全测试 - 验证ToolExecutorService的用户上下文管理

测试确保在并发环境中，不同用户的上下文不会相互干扰，
避免用户数据混淆的安全漏洞。
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, patch

from api.services.tool_executor_service import ToolExecutorService, current_user_id
from api.models.schema_models import ToolCall


class TestToolExecutorConcurrency:
    """测试ToolExecutorService的并发安全性"""

    @pytest.fixture
    def tool_executor(self):
        """创建ToolExecutorService实例"""
        return ToolExecutorService()

    @pytest.mark.asyncio
    async def test_concurrent_user_context_isolation(self, tool_executor):
        """
        测试并发用户上下文隔离

        验证在并发环境中，不同用户的上下文不会相互干扰
        """
        # 模拟不同用户的请求
        user_ids = ["user_1", "user_2", "user_3", "user_4", "user_5"]
        results = []

        async def simulate_user_request(user_id: str, delay: float = 0.1):
            """模拟用户请求处理"""
            # 设置用户上下文
            tool_executor.set_user_context(user_id)

            # 模拟一些异步操作延迟
            await asyncio.sleep(delay)

            # 验证用户上下文是否正确
            current_id = current_user_id.get()
            results.append((user_id, current_id))

            return current_id == user_id

        # 并发执行多个用户请求
        tasks = [
            simulate_user_request(user_id, 0.05 + i * 0.01)
            for i, user_id in enumerate(user_ids)
        ]

        success_results = await asyncio.gather(*tasks)

        # 验证所有请求都成功维护了正确的用户上下文
        assert all(success_results), f"并发测试失败，上下文混乱: {results}"

        # 验证每个用户都获得了正确的上下文
        for user_id, current_id in results:
            assert user_id == current_id, f"用户 {user_id} 的上下文被错误设置为 {current_id}"

    @pytest.mark.asyncio
    async def test_tool_execution_user_isolation(self, tool_executor):
        """
        测试工具执行时的用户隔离

        确保在执行工具时，用户上下文正确传递且不会混乱
        """
        with patch('api.services.user_profile_service.user_profile_service.get_user_profile') as mock_get_profile:
            # 配置mock返回不同用户的数据
            mock_get_profile.side_effect = lambda uid: {
                "user_1": {"nickname": "用户1", "age": 25},
                "user_2": {"nickname": "用户2", "age": 30},
                "user_3": {"nickname": "用户3", "age": 35}
            }.get(uid, None)

            async def execute_for_user(user_id: str):
                """为特定用户执行工具调用"""
                # 设置用户上下文
                tool_executor.set_user_context(user_id)

                # 创建工具调用
                tool_call = ToolCall(
                    id=f"test_{user_id}",
                    name="get_user_profile",
                    arguments={}
                )

                # 执行工具
                result = await tool_executor._execute_get_user_profile(tool_call)

                return user_id, result

            # 并发执行多个用户的工具调用
            user_ids = ["user_1", "user_2", "user_3"]
            tasks = [execute_for_user(user_id) for user_id in user_ids]

            results = await asyncio.gather(*tasks)

            # 验证每个用户都获得了正确的数据
            for user_id, result in results:
                assert result.success, f"用户 {user_id} 的工具执行失败: {result.error}"

                expected_nickname = f"用户{user_id.split('_')[1]}"
                assert expected_nickname in result.content, \
                    f"用户 {user_id} 获得了错误的数据: {result.content}"

    @pytest.mark.asyncio
    async def test_context_inheritance_behavior(self, tool_executor):
        """
        测试上下文继承行为

        验证 contextvars 在 asyncio 中的正确行为：
        在同一事件循环中创建的任务会继承当前上下文，这是预期行为
        """
        # 第一个用户的请求
        tool_executor.set_user_context("user_1")
        user_1_context = current_user_id.get()
        assert user_1_context == "user_1"

        # 在同一事件循环中创建的任务会继承上下文
        async def inherited_context_task():
            # 在继承的上下文中，应该保持父上下文的值
            return current_user_id.get()

        # 创建新的任务，会继承当前上下文
        inherited_context = await asyncio.create_task(inherited_context_task())

        # 子任务应该继承父上下文的值（这是 contextvars 的正确行为）
        assert inherited_context == "user_1", f"子任务应该继承父上下文，但得到: {inherited_context}"

        # 测试上下文修改不会影响并发任务
        async def modify_context_task(new_user_id):
            tool_executor.set_user_context(new_user_id)
            await asyncio.sleep(0.01)  # 短暂延迟
            return current_user_id.get()

        # 并发执行多个上下文修改任务
        tasks = [modify_context_task(f"user_{i}") for i in range(2, 5)]
        results = await asyncio.gather(*tasks)

        # 验证每个任务都保持了自己的上下文
        for i, result in enumerate(results):
            expected = f"user_{i + 2}"
            assert result == expected, f"任务 {i} 期望 {expected}，实际得到 {result}"

    @pytest.mark.asyncio
    async def test_missing_user_context_handling(self, tool_executor):
        """
        测试缺少用户上下文时的处理

        确保在没有设置用户上下文时，工具执行会正确处理错误
        """
        # 不设置用户上下文，直接执行工具
        tool_call = ToolCall(
            id="test_no_context",
            name="get_user_profile",
            arguments={}
        )

        result = await tool_executor._execute_get_user_profile(tool_call)

        # 应该返回错误结果
        assert not result.success
        assert result.error == "missing_user_context"
        assert "用户身份验证失败" in result.content

    @pytest.mark.asyncio
    async def test_high_concurrency_stress(self, tool_executor):
        """
        高并发压力测试

        测试在高并发情况下用户上下文管理的稳定性
        """
        num_users = 50
        requests_per_user = 10

        async def user_stress_test(user_id: str, request_num: int):
            """单个用户的压力测试"""
            tool_executor.set_user_context(f"user_{user_id}")

            # 随机延迟模拟真实场景
            await asyncio.sleep(0.001 * (request_num % 10))

            current_id = current_user_id.get()
            return current_id == f"user_{user_id}"

        # 创建大量并发任务
        tasks = []
        for user_id in range(num_users):
            for request_num in range(requests_per_user):
                task = user_stress_test(str(user_id), request_num)
                tasks.append(task)

        # 执行所有任务
        results = await asyncio.gather(*tasks)

        # 验证所有请求都成功
        success_rate = sum(results) / len(results)
        assert success_rate == 1.0, f"高并发测试失败，成功率: {success_rate:.2%}"

    def test_context_variable_isolation(self):
        """
        测试上下文变量的隔离性

        验证 contextvars 确实提供了线程/任务级别的隔离
        """
        # 在主上下文中设置值
        current_user_id.set("main_user")
        assert current_user_id.get() == "main_user"

        import threading

        def thread_function():
            # 在新线程中，上下文变量应该是默认值
            thread_context = current_user_id.get()
            return thread_context

        # 创建新线程
        thread = threading.Thread(target=thread_function)
        thread.start()
        thread.join()

        # 主线程中的值应该保持不变
        assert current_user_id.get() == "main_user"
