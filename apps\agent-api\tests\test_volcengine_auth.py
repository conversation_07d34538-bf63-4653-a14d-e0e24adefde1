"""
火山引擎签名验证测试
"""
import time
import pytest
from unittest.mock import Mock, MagicMock
from fastapi import HTTPException, Request

from api.utils.volcengine_auth import (
    VolcengineSignatureValidator,
    create_volcengine_validator,
    generate_test_signature
)


class TestVolcengineSignatureValidator:
    """火山引擎签名验证器测试"""

    def setup_method(self):
        """测试前的设置"""
        self.webhook_secret = "test-webhook-secret-key"
        self.validator = VolcengineSignatureValidator(self.webhook_secret, signature_tolerance=300)

    def test_create_validator(self):
        """测试创建验证器"""
        validator = create_volcengine_validator(self.webhook_secret)
        assert validator.webhook_secret == self.webhook_secret
        assert validator.signature_tolerance == 300

    def test_create_validator_with_empty_secret(self):
        """测试使用空密钥创建验证器应该抛出异常"""
        with pytest.raises(ValueError, match="webhook_secret不能为空"):
            create_volcengine_validator("")

    def test_calculate_signature(self):
        """测试签名计算"""
        signature_string = "1234567890test message"
        signature = self.validator._calculate_signature(signature_string)

        # 验证签名是64位十六进制字符串（SHA256）
        assert len(signature) == 64
        assert all(c in '0123456789abcdef' for c in signature)

    def test_generate_test_signature(self):
        """测试生成测试签名"""
        timestamp = "1234567890"
        body = '{"test": "message"}'

        signature = generate_test_signature(self.webhook_secret, timestamp, body)

        # 验证签名格式
        assert len(signature) == 64
        assert all(c in '0123456789abcdef' for c in signature)

    def test_verify_signature_success(self):
        """测试签名验证成功"""
        # 准备测试数据
        timestamp = str(int(time.time()))
        body = '{"event_type": "message", "payload": {"text": "hello"}}'
        body_bytes = body.encode('utf-8')

        # 生成签名
        signature = generate_test_signature(self.webhook_secret, timestamp, body)

        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": signature,
            "X-Volcengine-Timestamp": timestamp
        }

        # 验证签名
        result = self.validator.verify_signature(mock_request, body_bytes)
        assert result is True

    def test_verify_signature_missing_signature_header(self):
        """测试缺少签名头"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Timestamp": str(int(time.time()))
        }

        body_bytes = b'{"test": "message"}'

        with pytest.raises(HTTPException) as exc_info:
            self.validator.verify_signature(mock_request, body_bytes)

        assert exc_info.value.status_code == 401
        assert "缺少签名头" in exc_info.value.detail

    def test_verify_signature_missing_timestamp_header(self):
        """测试缺少时间戳头"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": "dummy_signature"
        }

        body_bytes = b'{"test": "message"}'

        with pytest.raises(HTTPException) as exc_info:
            self.validator.verify_signature(mock_request, body_bytes)

        assert exc_info.value.status_code == 401
        assert "缺少时间戳头" in exc_info.value.detail

    def test_verify_signature_expired_timestamp(self):
        """测试过期的时间戳"""
        # 使用一个过期的时间戳（10分钟前）
        expired_timestamp = str(int(time.time()) - 600)
        body = '{"test": "message"}'
        signature = generate_test_signature(self.webhook_secret, expired_timestamp, body)

        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": signature,
            "X-Volcengine-Timestamp": expired_timestamp
        }

        body_bytes = body.encode('utf-8')

        with pytest.raises(HTTPException) as exc_info:
            self.validator.verify_signature(mock_request, body_bytes)

        assert exc_info.value.status_code == 401
        assert "请求时间戳过期" in exc_info.value.detail

    def test_verify_signature_invalid_timestamp_format(self):
        """测试无效的时间戳格式"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": "dummy_signature",
            "X-Volcengine-Timestamp": "invalid_timestamp"
        }

        body_bytes = b'{"test": "message"}'

        with pytest.raises(HTTPException) as exc_info:
            self.validator.verify_signature(mock_request, body_bytes)

        assert exc_info.value.status_code == 401
        assert "无效的时间戳格式" in exc_info.value.detail

    def test_verify_signature_invalid_signature(self):
        """测试无效的签名"""
        timestamp = str(int(time.time()))
        body = '{"test": "message"}'

        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": "invalid_signature",
            "X-Volcengine-Timestamp": timestamp
        }

        body_bytes = body.encode('utf-8')

        with pytest.raises(HTTPException) as exc_info:
            self.validator.verify_signature(mock_request, body_bytes)

        assert exc_info.value.status_code == 401
        assert "签名验证失败" in exc_info.value.detail

    def test_verify_ip_whitelist_no_whitelist(self):
        """测试没有IP白名单时应该通过"""
        mock_request = Mock(spec=Request)
        mock_request.client = Mock()
        mock_request.client.host = "***********"
        mock_request.headers = {}

        result = self.validator.verify_ip_whitelist(mock_request, None)
        assert result is True

    def test_verify_ip_whitelist_allowed_ip(self):
        """测试允许的IP地址"""
        mock_request = Mock(spec=Request)
        mock_request.client = Mock()
        mock_request.client.host = "***********"
        mock_request.headers = {}

        allowed_ips = ["***********", "********"]
        result = self.validator.verify_ip_whitelist(mock_request, allowed_ips)
        assert result is True

    def test_verify_ip_whitelist_forbidden_ip(self):
        """测试被禁止的IP地址"""
        mock_request = Mock(spec=Request)
        mock_request.client = Mock()
        mock_request.client.host = "***********00"
        mock_request.headers = {}

        allowed_ips = ["***********", "********"]

        with pytest.raises(HTTPException) as exc_info:
            self.validator.verify_ip_whitelist(mock_request, allowed_ips)

        assert exc_info.value.status_code == 403
        assert "IP地址不被允许" in exc_info.value.detail

    def test_get_real_ip_from_x_forwarded_for(self):
        """测试从X-Forwarded-For头获取真实IP"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Forwarded-For": "***********, ***********"
        }
        mock_request.client = Mock()
        mock_request.client.host = "***********"

        real_ip = self.validator._get_real_ip(mock_request)
        assert real_ip == "***********"

    def test_get_real_ip_from_x_real_ip(self):
        """测试从X-Real-IP头获取真实IP"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Real-IP": "***********"
        }
        mock_request.client = Mock()
        mock_request.client.host = "***********"

        real_ip = self.validator._get_real_ip(mock_request)
        assert real_ip == "***********"

    def test_get_real_ip_fallback_to_client(self):
        """测试回退到客户端IP"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {}
        mock_request.client = Mock()
        mock_request.client.host = "***********"

        real_ip = self.validator._get_real_ip(mock_request)
        assert real_ip == "***********"

    def test_get_real_ip_no_client(self):
        """测试没有客户端信息时"""
        mock_request = Mock(spec=Request)
        mock_request.headers = {}
        mock_request.client = None

        real_ip = self.validator._get_real_ip(mock_request)
        assert real_ip == "unknown"


class TestSignatureIntegration:
    """签名验证集成测试"""

    def test_end_to_end_signature_verification(self):
        """端到端签名验证测试"""
        webhook_secret = "integration-test-secret"
        validator = create_volcengine_validator(webhook_secret)

        # 准备测试数据
        timestamp = str(int(time.time()))
        body = '{"event_type": "rtc_message", "payload": {"text": "Hello World"}}'
        body_bytes = body.encode('utf-8')

        # 生成签名
        signature = generate_test_signature(webhook_secret, timestamp, body)

        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": signature,
            "X-Volcengine-Timestamp": timestamp
        }

        # 验证签名
        result = validator.verify_signature(mock_request, body_bytes)
        assert result is True

    def test_signature_verification_with_different_body(self):
        """测试不同请求体的签名验证应该失败"""
        webhook_secret = "integration-test-secret"
        validator = create_volcengine_validator(webhook_secret)

        # 准备测试数据
        timestamp = str(int(time.time()))
        original_body = '{"event_type": "rtc_message", "payload": {"text": "Hello World"}}'
        different_body = '{"event_type": "rtc_message", "payload": {"text": "Different Message"}}'

        # 使用原始body生成签名
        signature = generate_test_signature(webhook_secret, timestamp, original_body)

        # 但是验证时使用不同的body
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "X-Volcengine-Signature": signature,
            "X-Volcengine-Timestamp": timestamp
        }

        different_body_bytes = different_body.encode('utf-8')

        # 验证应该失败
        with pytest.raises(HTTPException) as exc_info:
            validator.verify_signature(mock_request, different_body_bytes)

        assert exc_info.value.status_code == 401
        assert "签名验证失败" in exc_info.value.detail
