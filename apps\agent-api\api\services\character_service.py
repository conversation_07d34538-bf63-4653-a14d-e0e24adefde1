# api/services/character_service.py
"""
角色管理服务实现 - 对应故事1.2-B的AC3
实现AI角色配置管理、角色列表查询、用户角色绑定等功能
"""
import os
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from supabase import create_client, Client

from api.settings import logger, get_settings

# 使用settings模块加载配置
settings = get_settings()

@dataclass
class Character:
    """AI角色数据类"""
    id: str
    name: str
    description: Optional[str] = None
    voice_id: Optional[str] = None
    personality: Optional[Dict[str, Any]] = None
    is_default: bool = False
    created_at: Optional[str] = None

@dataclass
class CharacterListResponse:
    """角色列表响应数据类"""
    data: List[Character]
    pagination: Dict[str, Any]

class CharacterService:
    """角色管理服务"""

    def __init__(self):
        self.supabase: Client = create_client(settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY)

    async def get_characters_list(self, page: int = 1, limit: int = 10) -> CharacterListResponse:
        """获取角色列表"""
        try:
            logger.info(f"Getting characters list - page: {page}, limit: {limit}")

            # 计算偏移量
            offset = (page - 1) * limit

            # 获取角色总数
            count_response = self.supabase.table("characters").select("id", count="exact").execute()
            total_count = count_response.count

            # 获取分页数据
            response = self.supabase.table("characters").select("*").range(offset, offset + limit - 1).order("created_at", desc=False).execute()

            characters = []
            if response.data:
                for char_data in response.data:
                    character = Character(
                        id=char_data["id"],
                        name=char_data["name"],
                        description=char_data.get("description"),
                        voice_id=char_data.get("voice_id"),
                        personality=char_data.get("personality"),
                        is_default=char_data.get("is_default", False),
                        created_at=char_data.get("created_at")
                    )
                    characters.append(character)

            # 构建分页信息
            total_pages = (total_count + limit - 1) // limit
            pagination = {
                "current_page": page,
                "total_pages": total_pages,
                "total_count": total_count,
                "page_size": limit,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }

            logger.info(f"Successfully retrieved {len(characters)} characters")

            return CharacterListResponse(
                data=characters,
                pagination=pagination
            )

        except Exception as e:
            logger.exception(f"Error getting characters list: {e}")
            raise

    async def get_character_by_id(self, character_id: str) -> Optional[Character]:
        """根据ID获取角色详情"""
        try:
            logger.info(f"Getting character details for: {character_id}")

            response = self.supabase.table("characters").select("*").eq("id", character_id).execute()

            if not response.data:
                logger.warning(f"Character not found: {character_id}")
                return None

            char_data = response.data[0]
            character = Character(
                id=char_data["id"],
                name=char_data["name"],
                description=char_data.get("description"),
                voice_id=char_data.get("voice_id"),
                personality=char_data.get("personality"),
                is_default=char_data.get("is_default", False),
                created_at=char_data.get("created_at")
            )

            logger.info(f"Successfully retrieved character: {character.name}")
            return character

        except Exception as e:
            logger.exception(f"Error getting character by id: {e}")
            # 对于数据库查询错误，返回None让路由层处理404
            return None

    async def bind_user_character(self, user_id: str, character_id: str) -> bool:
        """绑定用户与AI角色"""
        try:
            logger.info(f"Binding user {user_id} with character {character_id}")

            # 首先检查角色是否存在
            character = await self.get_character_by_id(character_id)
            if not character:
                logger.warning(f"Character not found for binding: {character_id}")
                return False

            # 检查是否已经绑定
            existing_binding = self.supabase.table("user_character_bindings").select("*").eq("user_id", user_id).eq("character_id", character_id).execute()

            if existing_binding.data:
                logger.info(f"User {user_id} already bound to character {character_id}")
                return True

            # 创建绑定关系
            binding_data = {
                "user_id": user_id,
                "character_id": character_id,
                "is_active": True,
                "created_at": datetime.now(timezone.utc).isoformat()
            }

            response = self.supabase.table("user_character_bindings").insert(binding_data).execute()

            if response.data:
                logger.info(f"Successfully bound user {user_id} to character {character_id}")
                return True
            else:
                logger.error(f"Failed to bind user {user_id} to character {character_id}")
                return False

        except Exception as e:
            logger.exception(f"Error binding user character: {e}")
            raise

    async def get_user_characters(self, user_id: str) -> List[Character]:
        """获取用户绑定的角色列表"""
        try:
            logger.info(f"Getting bound characters for user: {user_id}")

            # 通过联表查询获取用户绑定的角色
            response = self.supabase.table("user_character_bindings").select(
                "character_id, characters(*)"
            ).eq("user_id", user_id).eq("is_active", True).execute()

            characters = []
            if response.data:
                for binding in response.data:
                    char_data = binding["characters"]
                    character = Character(
                        id=char_data["id"],
                        name=char_data["name"],
                        description=char_data.get("description"),
                        voice_id=char_data.get("voice_id"),
                        personality=char_data.get("personality"),
                        is_default=char_data.get("is_default", False),
                        created_at=char_data.get("created_at")
                    )
                    characters.append(character)

            logger.info(f"Found {len(characters)} bound characters for user {user_id}")
            return characters

        except Exception as e:
            logger.exception(f"Error getting user characters: {e}")
            raise

# 全局角色服务实例
character_service = CharacterService()
