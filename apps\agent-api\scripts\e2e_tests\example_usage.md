# 测试数据共享功能使用示例

这个文档演示了新的测试数据共享功能的使用方法。

## 🎯 完整测试流程示例

### 1. 开始新的测试轮次

```bash
# 运行健康检查测试 - 会自动清理所有历史数据
python 01_health_test.py

# 输出示例：
# 🧹 开始清理历史测试数据...
# ✅ 清理日志目录完成
# ✅ 清理共享配置文件完成
# 🎯 测试数据清理完成，准备开始新的测试轮次
```

### 2. 执行认证流程

```bash
# 运行认证测试 - 会保存用户信息到共享配置
python 02_auth_test.py

# 生成的共享配置示例：
# {
#   "device_id": "test-device-uuid",
#   "auth": {
#     "access_token": "jwt-token",
#     "user_id": "user-uuid"
#   },
#   "test_results": {
#     "onboarding": {
#       "character_id": "character-uuid",
#       "nickname": "测试用户"
#     }
#   }
# }
```

### 3. 后续测试自动使用共享数据

```bash
# 运行用户测试 - 会自动读取认证信息和引导数据
python 03_user_test.py

# 日志输出示例：
# ✅ 用户已认证（从共享配置加载）
# ✅ 使用共享配置中的引导数据
# ✅ 更新共享配置: user_profile
```

## 🔄 推荐：使用顺序测试脚本

### 完整流程测试

```bash
# 一键运行完整的用户流程测试
python run_sequential_tests.py

# 输出示例：
# 🚀 开始顺序API测试
# 📍 目标服务器: http://localhost:8003
# 📋 将运行 9 个测试
#
# 🎯 [1/9] 准备运行: health
# ================================================================================
# 🏥 健康检查 - 清理历史数据，开始新测试
# ================================================================================
# ...
# ✅ HEALTH 测试通过 (成功率: 100.0%)
#
# 🎯 [2/9] 准备运行: auth
# ================================================================================
# 🔐 认证服务 - 用户登录和引导流程
# ================================================================================
# ...
```

### 从指定测试开始

```bash
# 从用户管理测试开始运行（会自动加载之前的认证数据）
python run_sequential_tests.py --start-from user

# 输出示例：
# 📍 从 user 测试开始
# 📋 将运行 7 个测试
# ✅ 用户已认证（从共享配置加载）
```

### 查看当前配置状态

```bash
# 查看当前共享配置状态
python run_sequential_tests.py --show-config

# 输出示例：
# 📄 当前共享配置状态:
# ============================================================
# 设备ID: test-device-12345
# 平台: android
# 昵称: 测试用户
# 用户ID: user-uuid-67890
# 认证状态: 已认证
# 认证时间: 2024-01-20T10:30:00
# 角色ID: character-uuid-abc
#
# 📊 已完成的测试:
#   ✅ onboarding: 2024-01-20T10:31:00
#   ✅ user_profile: 2024-01-20T10:32:00
#   ✅ character_binding: 2024-01-20T10:33:00
# ============================================================
```

## 📝 数据共享的具体应用

### 认证数据传递

```python
# 02_auth_test.py 保存认证信息
self.save_auth_info()  # 保存 access_token, user_id 等

# 03_user_test.py 自动加载认证信息
# 初始化时自动从共享配置加载：
if "auth" in self.shared_config:
    auth_data = self.shared_config["auth"]
    self.access_token = auth_data.get("access_token")
    self.user_id = auth_data.get("user_id")
```

### 引导数据传递

```python
# 02_auth_test.py 保存引导完成数据
onboarding_result = {
    "character_id": character_id,
    "nickname": onboarding_data["nickname"],
    "core_needs": onboarding_data["core_needs"]
}
self.save_test_result("onboarding", onboarding_result)

# 03_user_test.py 使用引导数据
onboarding_data = self.get_shared_config("test_results", {}).get("onboarding", {})
update_data = {
    "nickname": f"{onboarding_data.get('nickname', self.test_data['nickname'])}_updated",
    "core_needs": onboarding_data.get("core_needs", ["情感陪伴", "健康咨询"])
}
```

### 角色ID传递

```python
# 引导流程保存角色ID
self.update_shared_config("character_id", character_id)

# 后续测试自动使用缓存的角色ID
async def get_character_id(self):
    cached_character_id = self.get_shared_config("character_id")
    if cached_character_id:
        self.logger.info(f"✅ 使用缓存的角色ID: {cached_character_id}")
        return cached_character_id
    # 只有在没有缓存时才从API获取
```

## 🛠️ 高级用法

### 选择性测试组合

```bash
# 只测试核心认证和用户管理流程
python run_sequential_tests.py --tests health auth user

# 只测试聊天相关功能（需要先有认证数据）
python run_sequential_tests.py --tests session chat

# 只测试RTC相关功能
python run_sequential_tests.py --tests rtc rtc_webhook
```

### 调试和故障排除

```bash
# 1. 检查共享配置状态
python run_sequential_tests.py --show-config

# 2. 重新开始完整测试
python 01_health_test.py  # 清理历史数据
python run_sequential_tests.py

# 3. 从失败的测试点继续
python run_sequential_tests.py --start-from session
```

## 💡 最佳实践

1. **开始新测试轮次**：总是从 `01_health_test.py` 开始，确保环境干净
2. **顺序执行**：使用 `run_sequential_tests.py` 确保测试按正确顺序执行
3. **配置检查**：使用 `--show-config` 查看当前测试状态
4. **增量测试**：可以从任意测试点开始，利用已有的共享数据
5. **故障调试**：查看详细日志文件了解具体错误信息

这个新的测试系统使得API测试更加智能和高效，减少了重复的认证和数据获取操作，同时确保了测试之间的数据一致性。
