"""
故事1.6-B: 提醒管理API路由

实现提醒的CRUD操作API：
- GET /api/v1/reminders - 获取提醒列表
- POST /api/v1/reminders - 创建提醒
- PUT /api/v1/reminders/{reminder_id} - 更新提醒
- DELETE /api/v1/reminders/{reminder_id} - 删除提醒
"""
import logging
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from api.dependencies.auth import get_current_user
from api.services.reminder_service import ReminderService, get_reminder_service
from api.models.schema_models import (
    Reminder,
    CreateReminderRequest,
    UpdateReminderRequest,
    ReminderListResponse
)
from typing import Literal

# 定义提醒状态类型
ReminderStatus = Literal['pending', 'triggered', 'completed', 'cancelled']

# 定义分页提醒列表响应模型
from pydantic import BaseModel
class PaginatedReminderResponse(BaseModel):
    """分页提醒列表响应"""
    reminders: List[Reminder]
    total_count: int
    limit: int
    offset: int
from db.session import get_db

logger = logging.getLogger(__name__)

# 辅助函数：从认证用户信息中提取user_id
async def get_current_user_id(current_user: dict = Depends(get_current_user)) -> str:
    """从认证用户信息中提取user_id"""
    return current_user.get("sub")

# FastAPI路由器
router = APIRouter(prefix="/reminders", tags=["reminders"])
security = HTTPBearer()


@router.get("/", response_model=PaginatedReminderResponse)
async def get_reminders(
    status: Optional[ReminderStatus] = Query(None, description="按状态筛选提醒"),
    start_date: Optional[str] = Query(None, description="开始日期筛选 (ISO 8601格式)"),
    end_date: Optional[str] = Query(None, description="结束日期筛选 (ISO 8601格式)"),
    limit: int = Query(10, description="返回结果数量限制", ge=1, le=100),
    offset: int = Query(0, description="分页偏移量", ge=0),
    user_id: str = Depends(get_current_user_id),
    reminder_service: ReminderService = Depends(get_reminder_service),
) -> PaginatedReminderResponse:
    """
    获取用户的提醒列表

    支持按状态、时间范围筛选，以及分页查询。
    RLS策略确保用户只能查看自己的提醒。
    """
    try:
        logger.info(f"获取提醒列表 - 用户: {user_id}, 状态: {status}, 限制: {limit}")

        reminders = await reminder_service.get_user_reminders(
            user_id=user_id,
            status=status,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )

        total_count = await reminder_service.count_user_reminders(
            user_id=user_id,
            status=status,
            start_date=start_date,
            end_date=end_date
        )

        logger.info(f"返回提醒列表 - 用户: {user_id}, 数量: {len(reminders)}, 总计: {total_count}")

        return PaginatedReminderResponse(
            reminders=reminders,
            total_count=total_count,
            limit=limit,
            offset=offset
        )

    except Exception as e:
        logger.error(f"获取提醒列表失败 - 用户: {user_id}, 错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "service_unavailable",
                "message": "获取提醒列表失败，请稍后重试",
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.post("/", response_model=Reminder, status_code=201)
async def create_reminder(
    request: CreateReminderRequest,
    user_id: str = Depends(get_current_user_id),
    reminder_service: ReminderService = Depends(get_reminder_service),
) -> Reminder:
    """
    创建新的提醒

    支持手动创建提醒（除了Function Calling自动创建）。
    """
    try:
        logger.info(f"创建提醒 - 用户: {user_id}, 内容: {request.content[:50]}...")

        # 使用ReminderService的标准创建方法 - 传递request对象
        reminder = await reminder_service.create_reminder(
            user_id=user_id,
            request=request
        )

        logger.info(f"提醒创建成功 - 用户: {user_id}, ID: {reminder.id}")
        return reminder

    except ValueError as e:
        logger.warning(f"创建提醒参数错误 - 用户: {user_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={
                "error": "validation_error",
                "message": str(e),
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
    except Exception as e:
        logger.error(f"创建提醒失败 - 用户: {user_id}, 错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "service_unavailable",
                "message": "提醒服务暂时不可用，请稍后重试",
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.put("/{reminder_id}", response_model=Reminder)
async def update_reminder(
    reminder_id: str,
    request: UpdateReminderRequest,
    user_id: str = Depends(get_current_user_id),
    reminder_service: ReminderService = Depends(get_reminder_service),
) -> Reminder:
    """
    更新指定的提醒

    用户只能更新自己的提醒，RLS策略确保安全性。
    """
    try:
        logger.info(f"更新提醒 - 用户: {user_id}, 提醒ID: {reminder_id}")

        # 验证至少有一个字段需要更新
        if (request.content is None and request.reminder_time is None and
            request.description is None and request.status is None):
            raise HTTPException(
                status_code=400,
                detail="没有提供需要更新的字段"
            )

        # 直接传递request对象，与服务方法签名匹配
        reminder = await reminder_service.update_reminder(
            reminder_id=reminder_id,
            user_id=user_id,
            request=request
        )

        if not reminder:
            raise HTTPException(
                status_code=404,
                detail="提醒不存在或您无权访问"
            )

        logger.info(f"提醒更新成功 - 用户: {user_id}, ID: {reminder_id}")
        return reminder

    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"更新提醒参数错误 - 用户: {user_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={
                "error": "validation_error",
                "message": str(e),
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
    except Exception as e:
        logger.error(f"更新提醒失败 - 用户: {user_id}, 错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "service_unavailable",
                "message": "更新提醒失败，请稍后重试",
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.delete("/{reminder_id}", status_code=204)
async def delete_reminder(
    reminder_id: str,
    user_id: str = Depends(get_current_user_id),
    reminder_service: ReminderService = Depends(get_reminder_service),
):
    """
    删除指定的提醒

    用户只能删除自己的提醒，RLS策略确保安全性。
    """
    try:
        logger.info(f"删除提醒 - 用户: {user_id}, 提醒ID: {reminder_id}")

        success = await reminder_service.delete_reminder(
            reminder_id=reminder_id,
            user_id=user_id
        )

        if not success:
            raise HTTPException(
                status_code=404,
                detail="提醒不存在或您无权访问"
            )

        logger.info(f"提醒删除成功 - 用户: {user_id}, ID: {reminder_id}")
        # 204 No Content - 成功删除，无返回内容

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除提醒失败 - 用户: {user_id}, 错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "service_unavailable",
                "message": "删除提醒失败，请稍后重试",
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.get("/{reminder_id}", response_model=Reminder)
async def get_reminder(
    reminder_id: str,
    user_id: str = Depends(get_current_user_id),
    reminder_service: ReminderService = Depends(get_reminder_service),
) -> Reminder:
    """
    获取指定的提醒详情

    用户只能查看自己的提醒，RLS策略确保安全性。
    """
    try:
        logger.info(f"获取提醒详情 - 用户: {user_id}, 提醒ID: {reminder_id}")

        reminder = await reminder_service.get_reminder_by_id(
            reminder_id=reminder_id,
            user_id=user_id
        )

        if not reminder:
            raise HTTPException(
                status_code=404,
                detail="提醒不存在或您无权访问"
            )

        logger.info(f"返回提醒详情 - 用户: {user_id}, ID: {reminder_id}")
        return reminder

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取提醒详情失败 - 用户: {user_id}, 错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "service_unavailable",
                "message": "获取提醒详情失败，请稍后重试",
                "service": "reminder_service",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


# 健康检查端点（可选）
@router.get("/health", tags=["health"])
async def reminder_health_check():
    """
    提醒服务健康检查

    用于监控提醒API的可用性。
    """
    return {
        "status": "healthy",
        "service": "reminder_api",
        "timestamp": "2024-12-20T10:00:00Z"
    }
