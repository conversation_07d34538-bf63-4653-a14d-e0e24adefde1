# tests/test_security_hardening.py
"""
测试安全加固功能 - 故事1.13-B
包含JWT密钥强制配置和不安全端点移除的测试
"""
import pytest
import os
import sys
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
import tempfile
import subprocess
import requests
import importlib
from pydantic_core import ValidationError

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestJWTSecretKeyValidation:
    """AC-1: JWT密钥强制配置安全验证"""

    def test_app_startup_fails_without_jwt_secret_key(self):
        """JWT密钥缺失时应用启动失败"""
        # 备份当前环境变量
        env_backup = os.environ.copy()

        # 移除JWT_SECRET_KEY环境变量
        if 'JWT_SECRET_KEY' in os.environ:
            del os.environ['JWT_SECRET_KEY']

        try:
            # 重新加载settings模块以确保新的环境变量生效
            if 'api.settings' in sys.modules:
                # 当settings模块重新加载时，会触发验证错误
                with pytest.raises(ValidationError, match="JWT_SECRET_KEY must be set"):
                    importlib.reload(sys.modules['api.settings'])
            else:
                with pytest.raises(ValidationError, match="JWT_SECRET_KEY must be set"):
                    from api.settings import Settings
                    Settings()
        finally:
            # 恢复环境变量
            os.environ.clear()
            os.environ.update(env_backup)
            # 重新加载settings模块以恢复正常状态
            if 'api.settings' in sys.modules:
                importlib.reload(sys.modules['api.settings'])

    def test_app_startup_fails_with_empty_jwt_secret_key(self):
        """JWT密钥为空时应用启动失败"""
        with patch.dict(os.environ, {'JWT_SECRET_KEY': ''}):
            # 重新加载settings模块，期望抛出ValidationError
            if 'api.settings' in sys.modules:
                with pytest.raises(ValidationError, match="JWT_SECRET_KEY must be set to a strong, secret value"):
                    importlib.reload(sys.modules['api.settings'])

    def test_app_startup_fails_with_example_jwt_secret_key(self):
        """JWT密钥使用示例值时应用启动失败"""
        with patch.dict(os.environ, {'JWT_SECRET_KEY': 'your-secret-key-at-least-32-characters'}):
            # 重新加载settings模块，期望抛出ValidationError
            if 'api.settings' in sys.modules:
                with pytest.raises(ValidationError, match="JWT_SECRET_KEY must be set to a strong, secret value"):
                    importlib.reload(sys.modules['api.settings'])

    def test_app_startup_fails_with_short_jwt_secret_key(self):
        """JWT密钥长度不足时应用启动失败"""
        with patch.dict(os.environ, {'JWT_SECRET_KEY': 'short-key'}):
            # 重新加载settings模块，期望抛出ValidationError
            if 'api.settings' in sys.modules:
                with pytest.raises(ValidationError, match="JWT_SECRET_KEY must be at least 32 characters long"):
                    importlib.reload(sys.modules['api.settings'])

    def test_app_startup_succeeds_with_valid_jwt_secret_key(self):
        """有效JWT密钥应用正常启动"""
        valid_key = "a-very-strong-secret-key-that-is-definitely-32-chars-or-more"
        with patch.dict(os.environ, {'JWT_SECRET_KEY': valid_key}):
            # 重新加载settings模块
            if 'api.settings' in sys.modules:
                importlib.reload(sys.modules['api.settings'])

            # 这应该不抛出异常
            from api.settings import Settings
            settings = Settings()
            assert settings.JWT_SECRET_KEY == valid_key

    def test_auth_py_no_default_values(self):
        """代码审查验证auth.py中移除了默认值"""
        # 读取auth.py文件内容
        auth_file_path = os.path.join(os.path.dirname(__file__), '..', 'api', 'dependencies', 'auth.py')
        with open(auth_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 验证不包含硬编码的默认值
        assert 'your-secret-key-at-least-32-characters' not in content, "auth.py still contains hardcoded default JWT key"

        # 验证使用settings而不是os.getenv直接调用
        assert 'os.getenv("JWT_SECRET_KEY"' not in content, "auth.py should use settings.JWT_SECRET_KEY instead of os.getenv"

    def test_auth_service_py_no_default_values(self):
        """代码审查验证auth_service.py中移除了默认值"""
        auth_service_file_path = os.path.join(os.path.dirname(__file__), '..', 'api', 'services', 'auth_service.py')
        with open(auth_service_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 验证不包含硬编码的默认值
        assert 'your-secret-key-at-least-32-characters' not in content, "auth_service.py still contains hardcoded default JWT key"

        # 验证使用settings而不是os.getenv直接调用
        assert 'os.getenv("JWT_SECRET_KEY"' not in content, "auth_service.py should use settings.JWT_SECRET_KEY instead of os.getenv"


class TestUnsafeEndpointRemoval:
    """AC-2: 不安全端点移除验证"""

    def test_user_data_routes_file_deleted(self):
        """验证user_data_routes.py文件已删除"""
        routes_dir = os.path.join(os.path.dirname(__file__), '..', 'api', 'routes')
        user_data_routes_path = os.path.join(routes_dir, 'user_data_routes.py')

        # 这个测试现在会失败，因为文件还存在
        assert not os.path.exists(user_data_routes_path), "user_data_routes.py file should be deleted"

    def test_v1_router_no_user_data_registration(self):
        """验证路由注册已清理"""
        v1_router_path = os.path.join(os.path.dirname(__file__), '..', 'api', 'routes', 'v1_router.py')
        with open(v1_router_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 验证不再包含user_data_router的导入和注册
        assert 'user_data_routes' not in content, "v1_router.py should not import user_data_routes"
        assert 'user_data_router' not in content, "v1_router.py should not reference user_data_router"
