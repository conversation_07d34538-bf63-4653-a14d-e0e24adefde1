from fastapi import APIRouter
from datetime import datetime, timezone

######################################################
## API健康检查路由
######################################################

health_router = APIRouter(tags=["Health"])


@health_router.get("/health")
def get_health():
    """检查API的健康状态"""

    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
