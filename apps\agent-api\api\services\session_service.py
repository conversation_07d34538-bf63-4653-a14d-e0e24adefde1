# api/services/session_service.py
from typing import List, Dict, Any, Tu<PERSON>, Optional
from db.supabase_init import get_supabase_client
from api.settings import logger, get_settings
from datetime import datetime, timezone
import uuid
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from postgrest.exceptions import APIError
from api.models.session_models import CreateSessionRequest, ChatSessionResponse
from supabase._async.client import AsyncClient
from httpx import HTTPStatusError
from api.services.character_service import CharacterService
from fastapi import HTTPException
import math
from fastapi import status
from pydantic import ValidationError

class SessionService:
    ALL_MESSAGES_PAGE_SIZE = 50 # Define page size for fetching all messages

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def get_recent_messages(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取会话的最近聊天记录，用于构建短期上下文。
        只选择 'role' 和 'content' 字段。

        使用装饰器添加重试机制，最多尝试3次，指数级退避策略
        """
        try:
            uuid.UUID(session_id)
        except ValueError:
            logger.warning(f"SessionService: get_recent_messages called with invalid session_id format: {session_id}")
            # 对于GET请求，如果ID格式错误，通常404是合适的，暗示资源路径格式错误。
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found or invalid session ID format.")

        if not session_id: # 实际上，上面的uuid.UUID(session_id)会先捕获空字符串的情况
            logger.warning("SessionService: get_recent_messages called with no session_id.")
            return []
        if limit <= 0:
            limit = 1 # Default to 1 if invalid limit provided to avoid issues.

        try:
            logger.debug(f"SessionService: Getting recent messages for session_id: {session_id} with limit: {limit}")
            supabase = await get_supabase_client()
            response = (
                await supabase.table("chat_messages")
                .select("role, content, created_at, id, session_id, updated_at") # Select specific columns
                .eq("session_id", session_id)
                .order("created_at", desc=True) # ORDER BY created_at DESC
                .limit(limit)
                .execute()
            )
            # For Supabase V2, APIError is raised on HTTP error status codes.
            # So, if we reach here without an exception, the request was successful.
            # The 'response.error' attribute check is more for V1 or non-Postgrest standard.
            # if response.error:
            #     logger.error(
            #         f"SessionService: Supabase APIError in get_recent_messages for session {session_id}: "
            #         f"Code: {response.error.code}, Message: {response.error.message}, Details: {response.error.details}, Hint: {response.error.hint}"
            #     )
            #     # Consider raising a custom exception or re-raising APIError based on project's error handling strategy
            #     raise APIError( # Or some other appropriate exception
            #         {"message": str(response.error.message), "code": str(response.error.code)}
            #     )

            logger.debug(f"Supabase response data in get_recent_messages for session {session_id}: {response.data}") # Added log
            return response.data or []

        except APIError as e: # Catch APIError specifically
            logger.error(f"SessionService: APIError in get_recent_messages for session {session_id}: {e.message} (Code: {e.code}, Details: {e.details})")
            # Depending on the desired behavior, you might re-raise or return empty/error indicator
            raise # Re-raise to be handled by the route or a global error handler
        except Exception as e:
            logger.exception(f"SessionService: Unexpected error in get_recent_messages for session {session_id}: {e}", exc_info=True)
            # Depending on policy, you might return [] or raise a custom error
            return [] # Or raise CustomServiceError("Failed to retrieve messages")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def save_chat_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将聊天消息保存到数据库

        参数：
            message_data: Dict[str, Any] - 包含消息内容的字典

        返回：
            Dict[str, Any] - 保存后的消息数据

        异常：
            HTTPException - 如果保存失败
        """
        # MODIFED: Added type hint and doc string for clarity

        # Initialize Supabase client
        client = await get_supabase_client()

        # Initialize new db payload and explicitly remove any conversation_id
        db_payload = dict(message_data)
        if 'conversation_id' in db_payload:
            del db_payload['conversation_id']  # 显式删除任何可能存在的conversation_id字段
            logger.debug("SessionService: Removed 'conversation_id' field from payload")

        # Generate a UUID for the message if one doesn't exist
        if not db_payload.get('id'):
            db_payload['id'] = str(uuid.uuid4())
            logger.debug(f"SessionService: Generated UUID {db_payload['id']} for message")

        # Ensure role is lowercase for consistency
        if db_payload.get('role'):
            db_payload['role'] = db_payload['role'].lower()
            logger.debug(f"SessionService: Normalized role to lowercase: {db_payload['role']}")

        # Add timestamps for any message that doesn't already have them
        current_time_utc = datetime.now(timezone.utc)
        timestamp_iso = current_time_utc.isoformat()

        if not db_payload.get('created_at'):
            db_payload['created_at'] = timestamp_iso
            logger.debug(f"SessionService: Added created_at: {timestamp_iso}")

        if not db_payload.get('updated_at'):
            db_payload['updated_at'] = timestamp_iso
            logger.debug(f"SessionService: Added updated_at: {timestamp_iso}")

        # 确保只包含数据库表真实存在的列
        allowed_columns = {
            "id", "session_id", "role", "content", "content_type", "message_type",
            "emotion_category", "emotion_intensity", "tokens_used", "status",
            "quoted_message_id", "reactions", "is_edited", "edit_count",
            "deleted_at", "is_deleted", "structured_data",
            "created_at", "updated_at", "user_id"
        }

        # 创建最终的载荷，确保只包含允许的列
        final_db_payload = {k: v for k, v in db_payload.items() if k in allowed_columns}

        # 确保状态字段使用有效值
        if 'status' not in final_db_payload or not final_db_payload['status']:
            final_db_payload['status'] = 'sent'  # 默认设置为sent，符合数据库约束

        if not final_db_payload.get('session_id') or final_db_payload.get('content') is None:
            err_msg = f"SessionService: Critical data (session_id or content) missing for message ID {final_db_payload.get('id')}"
            logger.error(err_msg)
            raise ValueError(err_msg)

        logger.debug(f"SessionService: Saving chat message to DB. Session: {final_db_payload.get('session_id')}, Msg ID: {final_db_payload.get('id')}")
        try:
            # For supabase-py < 2.0, insert().execute() returns the inserted data (if RETURNING is configured or by default for single inserts)
            # If you need to select specific columns, it implies supabase-py v2.x or a subsequent select query.
            # Assuming the goal is to get the inserted record back and that the version is < 2.0 or handles RETURNING appropriately:
            response = await client.table("chat_messages").insert(final_db_payload).execute()

            logger.debug(f"Supabase insert response: {response}")

            if not response.data:
                logger.error(f"Supabase insert for chat_messages did not return data. Payload: {final_db_payload}")
                # This case should ideally be an APIError from Supabase if the insert truly failed with no data.
                # If it's a successful insert but an empty response, it's an unexpected state.
                raise HTTPException(status_code=500, detail="Supabase insert for chat_messages did not return data when data was expected.")


            saved_message = response.data[0]

            # Update session metadata AFTER successful message save
            await self.update_session_metadata(session_id=str(final_db_payload.get('session_id'))) # Ensure session_id is str

            logger.debug(f"SessionService: Chat message saved, DB returned ID: {saved_message.get('id') if saved_message else 'N/A'}")
            return saved_message if saved_message else {} # 返回保存的数据或空字典
        except APIError as e: # Specific handler for APIError
            logger.error(f"SessionService: APIError during Supabase insert for chat message: {e.message} (Code: {getattr(e, 'code', 'N/A')})", exc_info=True)
            raise HTTPException(status_code=getattr(e, 'status_code', 500), detail=e.message or "Database error saving message")
        except Exception as e: # Generic handler for other errors (and for retry decorator)
            logger.error(f"SessionService: Exception during Supabase insert for chat message: {e}", exc_info=True)
            # Re-raise to allow retry decorator to work, or if it's an unexpected non-APIError
            raise

    async def update_session_metadata(self, session_id: str) -> None:
        """更新 chat_sessions 表的 last_message_at 和 updated_at。"""
        try:
            uuid.UUID(session_id)
        except ValueError:
            logger.warning(f"SessionService: update_session_metadata called with invalid session_id format: {session_id}")
            return

        current_time_utc = datetime.now(timezone.utc)
        timestamp_iso = current_time_utc.isoformat()

        update_payload = {
            "last_message_at": timestamp_iso,
            "updated_at": timestamp_iso,
        }
        supabase_client = await get_supabase_client()
        try:
            response = (
                await supabase_client.table("chat_sessions")
                .update(update_payload)
                .eq("id", session_id)
                .execute()
            )
            logger.debug(f"SessionService: Successfully updated metadata for session_id {session_id}")

        except APIError as e:
            logger.error(
                f"SessionService: APIError updating metadata for session_id {session_id} in chat_sessions: {e.message} (Code: {e.code})",
            )
        except Exception as e:
            logger.error(
                f"SessionService: Exception updating metadata for session_id {session_id} in chat_sessions: {e}",
                exc_info=True,
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def create_session(self, user_id: str, create_request_model: CreateSessionRequest) -> Dict[str, Any]:
        """创建新的聊天会话，并将其保存到 chat_sessions 表中。"""
        client = await get_supabase_client()
        settings = get_settings()

        ai_character_id: Optional[str] = None
        if create_request_model.characterId:
            if create_request_model.characterId == "default":
                try:
                    # 修复: 正确地查询 is_default=true 的角色
                    response = await client.table("characters").select("id").eq("is_default", True).limit(1).maybe_single().execute()
                    if response.data:
                        ai_character_id = response.data.get("id")
                        logger.info(f"找到默认角色，ID: {ai_character_id}")
                    else:
                        logger.warning("SessionService: 在数据库中未找到默认角色 (is_default = true)。")
                except Exception as e:
                    logger.error(f"获取默认角色时出错: {str(e)}", exc_info=True)
            else:
                ai_character_id = create_request_model.characterId
        else:
            logger.warning("SessionService: 未提供 characterId，会话将没有特定的AI角色。")

        session_metadata: Dict[str, Any] = create_request_model.metadata or {}
        # 将角色ID直接存储在会话记录中，而不仅仅是元数据里
        session_metadata["character_id"] = ai_character_id

        session_id = str(uuid.uuid4())
        current_time_iso = datetime.now(timezone.utc).isoformat()

        session_data = {
            "id": session_id,
            "user_id": user_id,
            "character_id": ai_character_id,
            "topic": create_request_model.topic,
            "topic_type": create_request_model.topicType or "custom",
            "status": "active",
            "created_at": current_time_iso,
            "updated_at": current_time_iso,
            "last_message_at": current_time_iso,
            "metadata": session_metadata
        }

        try:
            insert_response = await client.table("chat_sessions").insert(session_data).execute()
            if not insert_response.data:
                raise HTTPException(status_code=500, detail="Failed to create session in database")

            logger.info(f"SessionService: Created session {session_id} for user {user_id}")
            # The returned data is a list containing one dictionary
            return insert_response.data[0]

        except APIError as e:
            logger.error(f"APIError creating session for user {user_id}: {e.message} (Code: {e.code})")
            raise HTTPException(status_code=500, detail=f"Database error on session creation: {e.message}")
        except Exception as e:
            logger.error(f"Exception creating session for user {user_id}: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="An unexpected error occurred during session creation.")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def get_user_sessions(self, user_id: str, page: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户的所有会话列表，从 chat_sessions 表分页查询。"""
        supabase_client: AsyncClient = await get_supabase_client()
        offset = (page - 1) * limit

        try:
            query = (
                supabase_client.table("chat_sessions")
                .select("*", count="exact")
                .eq("user_id", user_id)
                .order("last_message_at", desc=True)
                .range(offset, offset + limit - 1)
            )
            response = await query.execute()

            if response.data is not None:
                db_sessions_data = response.data
                total_items = response.count if response.count is not None else 0

                sessions_response_list = db_sessions_data

                logger.debug(f"SessionService: Retrieved {len(sessions_response_list)} sessions for user_id: {user_id}, page: {page}.")
                return sessions_response_list, total_items
            else:
                # 修复：新版supabase-py在出错时会直接抛出异常，不需要检查.error属性
                logger.error(f"SessionService: Failed to get user sessions for {user_id}. Response returned empty data.")
                raise HTTPException(status_code=500, detail="Could not retrieve user sessions.")

        except HTTPStatusError as e:
            # logger.error(f"Supabase HTTP error retrieving sessions for user {user_id}: {e.response.text}", exc_info=True)
            detail_msg = f"Database error: {e.response.status_code}. "
            try:
                error_content = e.response.json()
                detail_msg += error_content.get("message", e.response.text)
            except ValueError:
                detail_msg += e.response.text
            raise HTTPException(status_code=e.response.status_code, detail=detail_msg)
        except Exception as e:
            # logger.error(f"Unexpected error retrieving sessions for user {user_id}: {e}", exc_info=True)
            # This could be APIError from postgrest if not caught by HTTPStatusError
            if isinstance(e, APIError):
                 raise HTTPException(status_code=e.status_code if hasattr(e, 'status_code') else 500, detail=e.message)
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def verify_session_access(self, session_id: str, user_id: str) -> bool:
        """
        Verifies if a user has access to a given session.

        Args:
            session_id: The ID of the session.
            user_id: The ID of the user.

        Returns:
            True if the user has access, False otherwise.

        Raises:
            HTTPException: If session_id is invalid or a critical database error occurs.
        """
        try:
            uuid.UUID(session_id)
        except ValueError:
            logger.warning(f"SessionService: verify_session_access called with invalid UUID format {session_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid session ID format. Must be a valid UUID."
            )

        try:
            supabase_client = await get_supabase_client()
            response = await supabase_client.table("chat_sessions") \
                .select("id") \
                .eq("id", session_id) \
                .eq("user_id", user_id) \
                .execute()

            return len(response.data) > 0

        except Exception as e:
            logger.error(f"Unexpected error verifying session access: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error verifying session access: {str(e)}"
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def get_session_messages(self, session_id: str, page: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """
        Retrieves a paginated list of chat messages for a given session.
        Messages are ordered by creation time in ascending order (oldest first for a given page).

        Args:
            session_id: The ID of the session whose messages are to be retrieved.
            page: The page number for pagination (1-indexed).
            limit: The number of messages per page.

        Returns:
            A tuple containing a list of message dictionaries and the total number of messages for the session.

        Raises:
            HTTPException: If database query fails or returns an error.
        """
        try:
            offset = (page - 1) * limit
            range_start = offset
            range_end = offset + limit - 1  # Range is inclusive of both start and end

            # 首先获取总消息数
            supabase_client = await get_supabase_client()
            count_response = await supabase_client.table("chat_messages") \
                .select("id", count="exact") \
                .eq("session_id", session_id) \
                .execute()

            total_count = count_response.count if hasattr(count_response, 'count') else 0

            # 然后获取分页数据
            messages_response = await supabase_client.table("chat_messages") \
                .select("*") \
                .eq("session_id", session_id) \
                .order("created_at", desc=False) \
                .range(range_start, range_end) \
                .execute()

            # 添加重试逻辑，如果是最近的消息且消息数量少于预期，等待一下再重试
            # 这个逻辑主要用于处理异步消息保存的情况，等待消息被保存到数据库
            if page == 1 and total_count < 2 and messages_response.data:
                # 检查是否刚刚有消息发送但尚未保存
                # 等待短暂时间，让异步保存任务完成
                logger.info(f"可能有未保存的最新消息，等待短暂时间后重试。当前消息数: {total_count}")
                await asyncio.sleep(1)  # 等待1秒

                # 重新查询
                count_response = await supabase_client.table("chat_messages") \
                    .select("id", count="exact") \
                    .eq("session_id", session_id) \
                    .execute()

                new_total_count = count_response.count if hasattr(count_response, 'count') else 0

                if new_total_count > total_count:
                    logger.info(f"重新查询后发现新消息。更新前: {total_count}, 更新后: {new_total_count}")
                    # 发现新消息，重新获取
                    messages_response = await supabase_client.table("chat_messages") \
                        .select("*") \
                        .eq("session_id", session_id) \
                        .order("created_at", desc=False) \
                        .range(range_start, range_end) \
                        .execute()
                    total_count = new_total_count

            return messages_response.data, total_count

        except Exception as e:
            logger.error(f"Error retrieving messages for session {session_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve messages: {str(e)}"
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def get_all_messages_for_session(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Retrieves all chat messages for a given session.
        This method paginates internally to fetch all messages.

        Args:
            session_id: The ID of the session.

        Returns:
            A list of all message dictionaries for the session, ordered by creation time (oldest first).

        Raises:
            HTTPException: If there's a significant error during database operation.
        """
        all_messages: List[Dict[str, Any]] = []
        page = 1
        limit = 100 # Sensible limit for each fetch operation

        while True:
            try:
                messages_page, total_items = await self.get_session_messages(session_id, page, limit)
                if messages_page:
                    all_messages.extend(messages_page)

                # Calculate total_pages based on the first fetch that gives total_items
                # Subsequent calls to get_session_messages will also return total_items,
                # but we only need to calculate total_pages once.
                if page == 1 and total_items == 0:
                    break # No messages at all

                if not messages_page and page > 1:
                    # This case implies we've fetched beyond the last page of actual data
                    # or an issue where total_items was reported but a page came back empty.
                    logger.debug(f"No more messages found for session {session_id} at page {page}, stopping.")
                    break

                # More robust check for completion based on total_items
                current_fetched_count = len(all_messages)
                if total_items > 0 and current_fetched_count >= total_items:
                    logger.debug(f"Fetched {current_fetched_count}/{total_items} messages for session {session_id}. All messages retrieved.")
                    break

                if not messages_page and total_items == 0 and page ==1:
                     logger.debug(f"No messages found for session {session_id} on initial fetch.")
                     break

                # Safety break if total_items is not reported correctly or some other edge case
                if not messages_page and page > (total_items // limit) + 2: # Allow a couple of empty page fetches
                    logger.warning(f"Potentially stuck in get_all_messages_for_session for {session_id}. Fetched {len(all_messages)}, expected {total_items}. Breaking loop.")
                    break

                page += 1
                if page > 50: # Safety break to prevent infinite loops in unexpected scenarios (e.g. 5000 messages)
                    logger.warning(f"get_all_messages_for_session for session {session_id} fetched over 50 pages, breaking. Consider raising limit or alternative fetch method for very long sessions.")
                    break
            except HTTPException as e:
                # If get_session_messages raises an HTTPException, propagate it if it's severe
                # For this aggregate function, we might want to log and return what we have if it's partial
                logger.error(f"HTTPException while fetching page {page} for session {session_id}: {e.detail}. Propagating error.")
                # Decide if partial data is acceptable or if the whole operation should fail.
                # For summarization, partial data might be misleading. Let's re-raise for now.
                raise # Re-raise the HTTPException from get_session_messages
            except Exception as e: # Catch other unexpected errors during the loop logic itself
                logger.error(f"Unexpected error fetching all messages for session {session_id} on page {page}: {e}", exc_info=True)
                # Depending on the error, decide to break or re-raise.
                raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Unexpected error fetching all messages: {str(e)}")

        # Ensure messages are sorted chronologically, as get_session_messages returns them sorted per page.
        # If get_session_messages guarantees overall chronological order when paginating, this sort is redundant.
        # Given get_session_messages sorts by created_at ASC, extending lists should maintain order.
        # all_messages.sort(key=lambda x: x['created_at'])
        logger.info(f"Retrieved a total of {len(all_messages)} messages for session {session_id}.")
        return all_messages

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception),
        reraise=True
    )
    async def end_session(
        self,
        session_id: str,
        summary: Optional[str],
        topic: Optional[str],
        tags: Optional[List[str]],
    ) -> bool:
        """结束会话，更新 chat_sessions 表的状态和总结信息。"""
        try:
            uuid.UUID(session_id)
        except ValueError:
            logger.warning(f"SessionService: end_session called with invalid session_id format: {session_id}")
            # 如果 session_id 无效，则无法结束会话。
            return False

        supabase_client = await get_supabase_client()
        current_time_utc = datetime.now(timezone.utc)
        timestamp_iso = current_time_utc.isoformat()

        # 确保标签是有效的空列表而不是None
        tags_value = tags if tags is not None else []

        # 检查会话是否存在和当前状态
        try:
            logger.info(f"检查会话 {session_id} 是否存在及其当前状态...")
            check_response = await supabase_client.table("chat_sessions").select("id, status").eq("id", session_id).execute()

            if not check_response.data or len(check_response.data) == 0:
                logger.error(f"会话 {session_id} 不存在，无法结束。")
                return False

            current_status = check_response.data[0].get('status')
            logger.info(f"会话 {session_id} 当前状态: {current_status}")

            if current_status == 'completed':
                logger.info(f"会话 {session_id} 已经处于completed状态，无需更新。")
                return True

            valid_statuses = ['active', 'completed', 'archived', 'deleted']
            if 'completed' not in valid_statuses:
                logger.error(f"要设置的状态'completed'不在有效状态列表中: {valid_statuses}")
                return False
        except Exception as e:
            logger.error(f"检查会话状态时出错: {e}", exc_info=True)

        update_payload_main = {
            "status": "completed",
            "summary": summary,
            "topic": topic,
            "tags": tags_value,
            "ended_at": timestamp_iso,
            "updated_at": timestamp_iso,
        }

        try:
            logger.info(f"正在结束 chat_sessions 中的会话 {session_id}")
            logger.info(f"更新载荷: {update_payload_main}")

            try:
                response_main = (
                    await supabase_client.table("chat_sessions")
                    .update(update_payload_main)
                    .eq("id", session_id)
                    .execute()
                )

                logger.info(f"更新响应: {response_main}")
                logger.debug(f"响应数据: {response_main.data}")
                logger.debug(f"响应状态: {getattr(response_main, 'status', 'unknown')}")

                # 新版supabase-py在出错时会直接抛出异常，不需要检查error属性
                if not response_main.data:
                    row_count = getattr(response_main, 'count', 0)
                    logger.warning(f"更新返回空数据，受影响行数: {row_count}")
                    if row_count == 0:
                        logger.warning(f"没有行被更新，可能会话不存在或ID错误: {session_id}")
                        return False

                logger.info(f"会话 {session_id} 已成功标记为已完成")
                return True

            except Exception as update_err:
                logger.error(f"执行会话状态更新时发生错误: {update_err}", exc_info=True)

                error_str = str(update_err).lower()
                if "violates check constraint" in error_str and "status" in error_str:
                    logger.error(f"状态字段约束冲突! 尝试设置的值: '{update_payload_main['status']}'")

                    try:
                        constraints_query = """
                        SELECT conname, pg_get_constraintdef(c.oid)
                        FROM pg_constraint c
                        JOIN pg_class t ON c.conrelid = t.oid
                        WHERE t.relname = 'chat_sessions'
                        AND conname = 'chat_sessions_status_check';
                        """
                        constraints_response = await supabase_client.rpc(
                            "execute_sql",
                            {"query": constraints_query}
                        ).execute()
                        logger.info(f"约束信息: {constraints_response.data}")
                    except Exception as constraint_err:
                        logger.warning(f"获取约束信息失败: {constraint_err}")

                return False

        except Exception as e:
            logger.error(
                f"在 chat_sessions 中结束会话 {session_id} 时发生意外错误：{e}", exc_info=True
            )
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return False

# --- 依赖注入 --- #
_session_service_instance: Optional[SessionService] = None
_session_service_lock = asyncio.Lock()

async def get_session_service() -> SessionService:
    """FastAPI 依赖项，用于获取 SessionService 的单例。"""
    global _session_service_instance
    if _session_service_instance is None:
        async with _session_service_lock:
            if _session_service_instance is None:
                _session_service_instance = SessionService()
    return _session_service_instance
