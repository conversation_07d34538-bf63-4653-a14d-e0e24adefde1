name: <PERSON><PERSON> (Monorepo)

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      frontend: ${{ steps.changes.outputs.frontend }}
      backend: ${{ steps.changes.outputs.backend }}
      shared: ${{ steps.changes.outputs.shared }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            frontend:
              - 'apps/mobile-app/**'
            backend:
              - 'apps/agent-api/**'
            shared:
              - 'shared/**'

  frontend-checks:
    needs: changes
    if: ${{ needs.changes.outputs.frontend == 'true' || needs.changes.outputs.shared == 'true' }}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/mobile-app
    steps:
      - uses: actions/checkout@v4
      - name: 📦 Setup Node + PNPM + install deps
        uses: ./.github/actions/setup-node-pnpm-install
      - name: 🏃‍♂️ Lint
        run: pnpm run lint
      - name: 🏃‍♂️ Type Check
        run: pnpm run type-check
      - name: 🏃‍♂️ Test
        run: pnpm run test:ci

  backend-checks:
    needs: changes
    if: ${{ needs.changes.outputs.backend == 'true' }}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/agent-api
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "apps/agent-api/requirements**.txt"
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Create virtual environment
        run: uv venv --python ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          uv pip sync requirements.txt
          uv pip install ruff mypy pytest
      - name: 🏃‍♂️ Format Check
        run: uv run ruff format . --check
      - name: 🏃‍♂️ Lint
        run: uv run ruff check .
      - name: 🏃‍♂️ Type Check
        run: uv run mypy .
      - name: 🏃‍♂️ Test
        run: uv run python -m pytest tests/ -v 