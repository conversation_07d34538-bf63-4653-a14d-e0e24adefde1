#!/usr/bin/env python3
"""
E2E API测试日志查看器
帮助用户方便地查看和分析测试日志
"""

import os
import json
import argparse
import glob
from datetime import datetime
from typing import List, Dict, Any

class TestLogViewer:
    """测试日志查看器"""

    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = logs_dir

    def list_test_runs(self) -> List[str]:
        """列出所有的测试运行记录"""
        if not os.path.exists(self.logs_dir):
            print(f"❌ 日志目录不存在: {self.logs_dir}")
            return []

        # 查找所有的测试报告文件
        pattern = os.path.join(self.logs_dir, "e2e_test_report_*.json")
        report_files = glob.glob(pattern)

        timestamps = []
        for file in report_files:
            # 从文件名中提取时间戳
            filename = os.path.basename(file)
            timestamp = filename.replace("e2e_test_report_", "").replace(".json", "")
            timestamps.append(timestamp)

        return sorted(timestamps, reverse=True)

    def show_summary(self, timestamp: str = None):
        """显示测试摘要"""
        if timestamp is None:
            timestamps = self.list_test_runs()
            if not timestamps:
                print("❌ 没有找到测试记录")
                return
            timestamp = timestamps[0]  # 使用最新的

        summary_file = os.path.join(self.logs_dir, f"e2e_test_summary_{timestamp}.txt")

        if os.path.exists(summary_file):
            print(f"📄 测试摘要 - {timestamp}")
            print("=" * 80)
            with open(summary_file, 'r', encoding='utf-8') as f:
                print(f.read())
        else:
            print(f"❌ 摘要文件不存在: {summary_file}")

    def show_report(self, timestamp: str = None):
        """显示详细测试报告"""
        if timestamp is None:
            timestamps = self.list_test_runs()
            if not timestamps:
                print("❌ 没有找到测试记录")
                return
            timestamp = timestamps[0]  # 使用最新的

        report_file = os.path.join(self.logs_dir, f"e2e_test_report_{timestamp}.json")

        if os.path.exists(report_file):
            with open(report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)

            print(f"📊 详细测试报告 - {timestamp}")
            print("=" * 80)

            # 基本信息
            metadata = report.get("metadata", {})
            print(f"测试目标: {metadata.get('base_url', 'N/A')}")
            print(f"测试时间: {timestamp}")
            print(f"总耗时: {metadata.get('total_duration', 0):.2f}秒")
            print(f"版本: {metadata.get('version', 'N/A')}")
            print()

            # 统计信息
            summary = report.get("summary", {})
            statistics = report.get("statistics", {})
            print("📈 统计信息:")
            print(f"  总计: {summary.get('total', 0)}")
            print(f"  成功: {summary.get('passed', 0)}")
            print(f"  失败: {summary.get('failed', 0)}")
            print(f"  成功率: {statistics.get('success_rate', '0%')}")
            print(f"  API调用总数: {statistics.get('total_api_calls', 0)}")
            print(f"  平均响应时间: {statistics.get('avg_response_time', 0)}ms")
            print()

            # 详细测试结果
            detailed_tests = report.get("detailed_tests", [])
            print("🧪 详细测试结果:")
            for test in detailed_tests:
                status_emoji = "✅" if test["status"] == "PASSED" else "❌" if test["status"] == "FAILED" else "⚠️"
                duration = test.get("duration", 0)
                print(f"  {status_emoji} {test['test_name']} ({duration:.2f}s)")

                if test["status"] == "FAILED" and "details" in test:
                    error = test["details"].get("error", "未知错误")
                    print(f"      错误: {error}")
            print()

            # 错误汇总
            errors = summary.get("errors", [])
            if errors:
                print("❌ 错误汇总:")
                for i, error in enumerate(errors, 1):
                    print(f"  {i}. {error}")

        else:
            print(f"❌ 报告文件不存在: {report_file}")

    def show_api_calls(self, timestamp: str = None, filter_status: str = None):
        """显示API调用记录"""
        if timestamp is None:
            timestamps = self.list_test_runs()
            if not timestamps:
                print("❌ 没有找到测试记录")
                return
            timestamp = timestamps[0]  # 使用最新的

        report_file = os.path.join(self.logs_dir, f"e2e_test_report_{timestamp}.json")

        if os.path.exists(report_file):
            with open(report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)

            api_calls = report.get("api_calls", [])

            print(f"🌐 API调用记录 - {timestamp}")
            if filter_status:
                print(f"过滤条件: HTTP状态码 {filter_status}")
            print("=" * 80)

            for i, call in enumerate(api_calls, 1):
                status = call.get("response_status", 0)

                # 应用过滤条件
                if filter_status and str(status) != str(filter_status):
                    continue

                method = call.get("method", "")
                url = call.get("url", "")
                duration = call.get("duration_ms", 0)
                timestamp_str = call.get("timestamp", "")

                status_emoji = "✅" if 200 <= status < 300 else "❌" if status >= 400 else "⚠️"

                print(f"{i:3d}. {status_emoji} {method} {status} - {duration}ms")
                print(f"     URL: {url}")
                print(f"     时间: {timestamp_str}")

                # 显示请求数据（简化）
                if call.get("request_data"):
                    print(f"     请求: {json.dumps(call['request_data'], ensure_ascii=False)[:100]}...")

                print()

        else:
            print(f"❌ 报告文件不存在: {report_file}")

    def show_errors_only(self, timestamp: str = None):
        """只显示错误日志"""
        if timestamp is None:
            timestamps = self.list_test_runs()
            if not timestamps:
                print("❌ 没有找到测试记录")
                return
            timestamp = timestamps[0]

        error_file = os.path.join(self.logs_dir, f"e2e_test_errors_{timestamp}.log")

        if os.path.exists(error_file):
            print(f"❌ 错误日志 - {timestamp}")
            print("=" * 80)
            with open(error_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    print(content)
                else:
                    print("🎉 没有错误记录！")
        else:
            print(f"❌ 错误日志文件不存在: {error_file}")

    def list_runs(self):
        """列出所有测试运行"""
        timestamps = self.list_test_runs()

        if not timestamps:
            print("❌ 没有找到测试记录")
            return

        print("📋 测试运行记录:")
        print("=" * 50)

        for i, timestamp in enumerate(timestamps, 1):
            # 尝试读取基本信息
            report_file = os.path.join(self.logs_dir, f"e2e_test_report_{timestamp}.json")
            if os.path.exists(report_file):
                try:
                    with open(report_file, 'r', encoding='utf-8') as f:
                        report = json.load(f)

                    summary = report.get("summary", {})
                    total = summary.get("total", 0)
                    passed = summary.get("passed", 0)
                    failed = summary.get("failed", 0)

                    success_rate = f"{(passed / total * 100):.1f}%" if total > 0 else "0%"

                    print(f"{i:2d}. {timestamp}")
                    print(f"    测试: {total} | 成功: {passed} | 失败: {failed} | 成功率: {success_rate}")

                except Exception as e:
                    print(f"{i:2d}. {timestamp} (读取失败: {e})")
            else:
                print(f"{i:2d}. {timestamp} (报告文件缺失)")
            print()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="E2E API测试日志查看器")
    parser.add_argument("--logs-dir", default="logs", help="日志目录路径")
    parser.add_argument("--timestamp", help="指定时间戳")

    subparsers = parser.add_subparsers(dest="command", help="命令")

    # 列出所有测试运行
    subparsers.add_parser("list", help="列出所有测试运行记录")

    # 显示摘要
    subparsers.add_parser("summary", help="显示测试摘要")

    # 显示详细报告
    subparsers.add_parser("report", help="显示详细测试报告")

    # 显示API调用
    api_parser = subparsers.add_parser("api", help="显示API调用记录")
    api_parser.add_argument("--status", help="过滤HTTP状态码")

    # 显示错误
    subparsers.add_parser("errors", help="只显示错误日志")

    args = parser.parse_args()

    viewer = TestLogViewer(args.logs_dir)

    if args.command == "list":
        viewer.list_runs()
    elif args.command == "summary":
        viewer.show_summary(args.timestamp)
    elif args.command == "report":
        viewer.show_report(args.timestamp)
    elif args.command == "api":
        viewer.show_api_calls(args.timestamp, args.status)
    elif args.command == "errors":
        viewer.show_errors_only(args.timestamp)
    else:
        # 默认显示摘要
        viewer.show_summary(args.timestamp)

if __name__ == "__main__":
    main()
