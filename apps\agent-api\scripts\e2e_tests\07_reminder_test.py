#!/usr/bin/env python3
"""
提醒管理API测试
测试接口:
- GET /api/v1/reminders
- POST /api/v1/reminders
- PUT /api/v1/reminders/{reminder_id}
- DELETE /api/v1/reminders/{reminder_id}
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class ReminderTester(BaseAPITester):
    """提醒管理API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("07_reminder", base_url)
        self.reminder_id = None

    async def test_get_reminders_list(self):
        """测试获取提醒列表API"""
        self.logger.info("⏰ 测试获取提醒列表API")

        response = await self.make_request(
            "GET",
            "/api/v1/reminders",
            params={"limit": 10, "offset": 0}
        )

        # 验证响应结构
        if "reminders" in response:
            self.logger.info("✅ 响应包含提醒列表")

            reminders = response["reminders"]
            self.logger.info(f"✅ 获取到 {len(reminders)} 个提醒")

            # 如果有提醒，验证提醒数据结构
            if len(reminders) > 0:
                first_reminder = reminders[0]
                expected_fields = ["id", "content", "reminder_time", "status"]
                for field in expected_fields:
                    if field in first_reminder:
                        self.logger.info(f"✅ 提醒数据包含字段: {field}")
                    else:
                        self.logger.error(f"❌ 提醒数据缺少字段: {field}")

                # 可选字段验证
                optional_fields = ["description", "created_at", "updated_at"]
                for field in optional_fields:
                    if field in first_reminder:
                        self.logger.info(f"✅ 提醒数据包含可选字段: {field}")
                    else:
                        self.logger.info(f"ℹ️ 提醒数据不包含可选字段: {field}")
        else:
            self.logger.error("❌ 响应缺少提醒列表")

        # 验证分页信息
        pagination_fields = ["total_count", "limit", "offset"]
        for field in pagination_fields:
            if field in response:
                self.logger.info(f"✅ 响应包含分页字段: {field}")
            else:
                self.logger.error(f"❌ 响应缺少分页字段: {field}")

    async def test_create_reminder(self):
        """测试创建提醒API"""
        self.logger.info("📝 测试创建提醒API")

        # 设置提醒时间为1小时后
        reminder_time = (datetime.now() + timedelta(hours=1)).isoformat()

        reminder_data = {
            "content": "测试提醒内容",
            "reminder_time": reminder_time,
            "description": "这是一个测试提醒的详细描述"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/reminders",
            data=reminder_data,
            expected_status=201
        )

        # 验证响应并保存提醒ID
        if "id" in response:
            self.reminder_id = response["id"]
            self.logger.info(f"✅ 创建提醒成功，提醒ID: {self.reminder_id}")

            # 验证提醒数据
            expected_fields = ["id", "content", "reminder_time", "status"]
            for field in expected_fields:
                if field in response:
                    self.logger.info(f"✅ 提醒响应包含字段: {field}")
                else:
                    self.logger.error(f"❌ 提醒响应缺少字段: {field}")

            # 验证数据是否匹配
            if response.get("content") == reminder_data["content"]:
                self.logger.info("✅ 提醒内容匹配")
            else:
                self.logger.error("❌ 提醒内容不匹配")

            # 验证状态默认值
            if response.get("status") in ["pending", "scheduled"]:
                self.logger.info("✅ 提醒状态默认值正确")
            else:
                self.logger.warning(f"⚠️ 提醒状态可能异常: {response.get('status')}")

            # 保存提醒创建结果
            self.save_test_result("reminder_creation", {
                "reminder_created": True,
                "reminder_id": self.reminder_id,
                "content": reminder_data["content"],
                "reminder_time": reminder_data["reminder_time"],
                "description": reminder_data["description"],
                "status": response.get("status")
            })

        else:
            self.logger.error("❌ 提醒创建失败，未获取到提醒ID")

    async def test_update_reminder(self):
        """测试更新提醒API"""
        if not self.reminder_id:
            self.logger.warning("⚠️ 跳过更新提醒测试，因为没有提醒ID")
            return

        self.logger.info(f"✏️ 测试更新提醒API: {self.reminder_id}")

        # 设置新的提醒时间为2小时后
        new_reminder_time = (datetime.now() + timedelta(hours=2)).isoformat()

        update_data = {
            "content": "更新后的提醒内容",
            "reminder_time": new_reminder_time,
            "status": "pending",
            "description": "更新后的提醒描述"
        }

        response = await self.make_request(
            "PUT",
            f"/api/v1/reminders/{self.reminder_id}",
            data=update_data
        )

        # 验证更新成功
        if response and "error" not in response:
            self.logger.info("✅ 提醒更新成功")

            # 验证更新的数据
            if "content" in response and response["content"] == update_data["content"]:
                self.logger.info("✅ 提醒内容更新成功")
            else:
                self.logger.error("❌ 提醒内容更新失败")

            if "status" in response and response["status"] == update_data["status"]:
                self.logger.info("✅ 提醒状态更新成功")
            else:
                self.logger.error("❌ 提醒状态更新失败")
        else:
            self.logger.error("❌ 提醒更新失败")

    async def test_get_reminders_with_filters(self):
        """测试带筛选条件的提醒列表API"""
        self.logger.info("🔍 测试带筛选条件的提醒列表API")

        # 测试状态筛选
        response = await self.make_request(
            "GET",
            "/api/v1/reminders",
            params={
                "status": "pending",
                "limit": 5,
                "offset": 0
            }
        )

        if "reminders" in response:
            self.logger.info("✅ 状态筛选响应成功")

            # 验证筛选结果
            reminders = response["reminders"]
            if len(reminders) > 0:
                # 检查所有提醒的状态是否符合筛选条件
                all_pending = all(reminder.get("status") == "pending" for reminder in reminders)
                if all_pending:
                    self.logger.info("✅ 状态筛选结果正确")
                else:
                    self.logger.warning("⚠️ 状态筛选结果可能有问题")
            else:
                self.logger.info("ℹ️ 没有符合条件的提醒")
        else:
            self.logger.error("❌ 状态筛选响应失败")

        # 时间范围筛选暂时跳过 - 后端服务存在类型不匹配问题，等待修复
        self.logger.info("ℹ️ 时间范围筛选测试暂时跳过（后端服务待修复）")

        # TODO: 恢复时间范围筛选测试，当后端修复start_date/end_date类型问题后
        # start_date = datetime.now().isoformat()
        # end_date = (datetime.now() + timedelta(days=7)).isoformat()
        # response = await self.make_request(
        #     "GET",
        #     "/api/v1/reminders",
        #     params={
        #         "start_date": start_date,
        #         "end_date": end_date,
        #         "limit": 10,
        #         "offset": 0
        #     }
        # )
        # if "reminders" in response:
        #     self.logger.info("✅ 时间范围筛选响应成功")
        # else:
        #     self.logger.error("❌ 时间范围筛选响应失败")

    async def test_delete_reminder(self):
        """测试删除提醒API"""
        if not self.reminder_id:
            self.logger.warning("⚠️ 跳过删除提醒测试，因为没有提醒ID")
            return

        self.logger.info(f"🗑️ 测试删除提醒API: {self.reminder_id}")

        response = await self.make_request(
            "DELETE",
            f"/api/v1/reminders/{self.reminder_id}",
            expected_status=204
        )

        # 验证删除成功（204 No Content）
        self.logger.info("✅ 提醒删除成功")

        # 验证提醒确实被删除 - 尝试再次获取应该返回404
        response = await self.make_request(
            "PUT",
            f"/api/v1/reminders/{self.reminder_id}",
            data={"content": "尝试更新已删除的提醒"},
            expected_status=404
        )

        if response:
            self.logger.info("✅ 已删除提醒返回404，验证删除成功")
        else:
            self.logger.error("❌ 删除验证失败")

    async def test_reminder_not_found(self):
        """测试访问不存在提醒的API"""
        self.logger.info("🔍 测试访问不存在提醒API")

        fake_reminder_id = "non-existent-reminder-id"

        # 测试获取不存在的提醒
        response = await self.make_request(
            "PUT",
            f"/api/v1/reminders/{fake_reminder_id}",
            data={"content": "更新不存在的提醒"},
            expected_status=404
        )

        if response:
            self.logger.info("✅ 不存在提醒返回404状态码")
        else:
            self.logger.error("❌ 不存在提醒未返回预期状态码")

    async def test_create_reminder_invalid_data(self):
        """测试使用无效数据创建提醒"""
        self.logger.info("❌ 测试使用无效数据创建提醒")

        # 测试缺少必需字段
        invalid_data = {
            "description": "只有描述，没有内容和时间"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/reminders",
            data=invalid_data,
            expected_status=422  # FastAPI validation error
        )

        # 验证错误响应
        if response and ("error" in response or "detail" in response):
            self.logger.info("✅ 无效数据正确返回422")
        else:
            self.logger.warning("⚠️ 无效数据处理可能有问题")

        # 测试无效的时间格式
        invalid_time_data = {
            "content": "测试提醒",
            "reminder_time": "invalid-time-format"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/reminders",
            data=invalid_time_data,
            expected_status=422  # FastAPI validation error
        )

        if response and ("error" in response or "detail" in response):
            self.logger.info("✅ 无效时间格式正确返回422")
        else:
            self.logger.warning("⚠️ 无效时间格式处理可能有问题")

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始提醒管理API测试")

        # 确保用户已认证
        await self.ensure_authenticated()

        tests = [
            ("获取提醒列表", self.test_get_reminders_list),
            ("创建提醒", self.test_create_reminder),
            ("更新提醒", self.test_update_reminder),
            ("筛选提醒列表", self.test_get_reminders_with_filters),
            ("删除提醒", self.test_delete_reminder),
            ("访问不存在提醒", self.test_reminder_not_found),
            ("无效数据创建提醒", self.test_create_reminder_invalid_data),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(0.5)  # 测试间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='提醒管理API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with ReminderTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
