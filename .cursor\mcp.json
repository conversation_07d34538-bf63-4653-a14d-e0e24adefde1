{"mcpServers": {"deepwiki": {"url": "https://mcp.deepwiki.com/mcp"}, "time": {"command": "python", "args": ["-m", "mcp_server_time"]}, "mcp-pandoc": {"command": "uvx", "args": ["mcp-pandoc"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "FileScopeMCP": {"command": "node", "args": ["D:\\mcp\\FileScopeMCP\\dist\\mcp-server.js", "--base-dir=D:\\mcp\\FileScopeMCP"], "transport": "stdio"}, "postgres": {"command": "postgres-mcp", "args": ["--access-mode=unrestricted"], "env": {"DATABASE_URI": "postgresql://postgres:<EMAIL>:5432/postgres"}}, "zep-docs": {"transport": "sse", "url": "docs-mcp.getzep.com"}, "doc-query-server": {"command": "uv", "args": ["--directory", "D:/mcp/MCPDocSearch_git/", "run", "python", "-m", "mcp_server.main"], "env": {}}, "ragflow": {"type": "sse", "url": "http://127.0.0.1:9382/sse", "auth": {"apiKey": "ragflow-RlZDM4ZmNlNjM3NTExZjBhMzZjNDJmZG"}}, "grep": {"url": "https://mcp.grep.app"}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-20a1e1b9f43840d3aad4d330534649de"}}}}