# Crawled Documentation


## 启动智能体 StartVoiceChat--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1558163


  * 文档首页
/实时音视频/实时对话式 AI/服务端 OpenAPI/启动智能体 StartVoiceChat

启动智能体 StartVoiceChat
最近更新时间：2025.07.03 21:59:33首次发布时间：2025.05.15 13:15:32

文档反馈
本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考历史版本。​
在实时音视频场景中，你可以调用此接口在房间内引入一个智能体进行 AI 实时交互。​
RTC 提供语音识别（ASR）、语音合成（TTS）、大模型（LLM） 一站式接入，同时也支持通道服务，即可使用此接口灵活接入第三方大模型/Agent。​
注意事项​
  * 请求频率：单账号下 QPS 不得超过 60。​


  * 请求接入地址：仅支持 rtc.volcengineapi.com。​


  * 调用该接口启动智能体任务后，若真人用户退出房间，180 s 后该智能体任务会自动停止，但该 180s 内仍会计费。为避免不必要的费用，建议真人用户退出房间后，及时调用 StopVoiceChat 接口关闭智能体任务。​


前提条件​
调用该接口前，你需要开通语音识别、语音合成和大模型服务并配置相关权限策略，详情请参看方案集成前置准备。​
调用接口​
发送 HTTP(S) 请求时，你需要符合火山引擎规范。调用接口的请求结构、公共参数、签名机制、返回结构，参看调用方法。​
请求说明​
  * 请求方式：POST​


  * 请求地址：https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01​


调试​
API Explorer您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。去调试​
请求参数​
下表仅列出该接口特有的请求参数和部分公共参数。完整公共请求参数请见公共参数。​
Query​
Action String 必选 示例值：StartVoiceChat​接口名称。当前 API 的名称为 StartVoiceChat。​​
Version String 必选 示例值：2024-12-01​接口版本。当前 API 的版本为 2024-12-01。​​
Body​
AppId String 必选 示例值：661e****543cf​你的音视频应用的唯一标志，参看创建 RTC 应用获取或创建 AppId。​​
RoomId String 必选 示例值：Room1​智能体与真人进行通话的房间的 ID，需与真人用户使用客户端 SDK 进房时的使用的 RoomId 保持一致。​​
TaskId String 必选 示例值：task1​智能体任务 ID。由你自行定义，用于标识任务，且后续更新或结束此任务也需要使用该 TaskId。参数定义规则参看参数赋值规范。​一个 AppId 的 RoomId 下 TaskId 是唯一的，AppId + RoomId + TaskId 共同构成一个全局唯一的任务标识，用来标识指定 AppId 下某个房间内正在运行的任务，从而能在此任务运行中进行更新或者停止此任务。​不同 AppId 或者不同 RoomId 下 TaskId可以重复。​​
Config Object 必选 示例值：-​智能体交互服务配置，包括语音识别（ASR）、语音合成（TTS）、大模型(LLM)、字幕和函数调用（Function Calling）配置。​ASRConfig Object 必选 示例值：-​语音识别（ASR）相关配置。​Provider String 必选 示例值：volcano​语音识别服务的提供商。该参数固定取值：volcano，表示仅支持火山引擎语音识别服务。可使用以下模型：​火山引擎流式语音识别（识别速度更快）​火山引擎流式语音识别大模型（识别准确率更高）​两者详细差异（如可识别语种、支持的能力等），请参见流式语音识别和流式语音识别大模型。​​ProviderParams Object 必选 示例值：-​服务配置参数。​不同服务，该结构包含字段不同，具体参看：​火山引擎流式语音识别​火山引擎流式语音识别大模型​ASRConfig.ProviderParams（火山引擎流式语音识别） 可选​使用火山引擎流式语音识别时，识别速度快。你需要在 ASRConfig.ProviderParams里配置以下字段：​Mode String 必选 示例值：smallmodel​模型类型。该参数固定取值：smallmodel，表示火山引擎流式语音识别模型。​​AppId String 必选 示例值：93****21​开通流式语音识别服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​Cluster String 必选 示例值：volcengine_streaming_common​开通的流式语音识别服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​ASRConfig-ProviderParams（火山引擎流式语音识别大模型） 可选​使用火山引擎流式语音识别时，识别速度稍慢，但识别准确度更高，你需要在 ASRConfig.ProviderParams里配置以下字段：​Mode String 必选 示例值：bigmodel​模型类型。该参数固定取值：bigmodel，表示火山引擎语音识别大模型。​​AppId String 必选 示例值：93****21​开通火山引擎流式语音识别大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​AccessToken String 必选 示例值：MOaOaa_VQ6****1B34UHA4h5B​与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。​Access Token 查找方式，可参看如何获取 Token。​​ApiResourceId String 可选 示例值：volc.bigasr.sauc.duration​火山引擎流式语音识别大模型服务开通类型：​volc.bigasr.sauc.duration：小时版。​volc.bigasr.sauc.concurrent：并发版。​默认值为 volc.bigasr.sauc.duration。​​StreamMode Integer 可选 示例值：0​语音识别结果输出模式：​0：流式输出。即识别结果会分段、实时地返回。该模式下识别速度更快，适用于实时字幕场景。​1：非流式输出。即在完整接收并处理完整个语音片段后，一次性返回最终的识别结果。该模式下识别准确率更高，适用于不需要即时反馈的离线转录场景（如会议录音）。​默认值为 0。​​context String 可选 示例值："{\"hotwords\": [{\"word\": \"CO2\"},{\"word\": \"雨伞\"},{\"word\": \"鱼\"}]}"​热词直传（通过JSON 字符串直接传入）。​如果某些词汇（比如人名、产品名等）的识别准确率较低，可以将其作为热词传入 ASR 模型，提高输入词汇的识别准确率。例如传入"雨伞"热词，发音相似的词会优先识别为“雨伞”。​大小限制：热词传入最大值为 200 tokens，超出会自动截断。​格式要求（JSON 字符串）：​​json复制​​boosting_table_id String 可选 示例值：26603****1-adad​热词词表 ID。​你需要先在火山语音技术_热词管理创建热词词表，并获取热词 ID。​说明​执行优先级：热词词表低于热词直传。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。​​boosting_table_name String 可选 示例值：语音打断关键词​热词词表名称。​你需要先在火山语音技术_热词管理创建热词词表，并获取热词词表的文件名称。​说明​执行优先级：热词词表低于热词直传。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。​​context_history_length Integer 可选 示例值：0​上下文轮次。将最近指定轮数会话内容送入流式语音识别大模型，有助于模型理解当前对话的背景，从而提升大模型识别准确性。​取值范围为 0、[1,21)，0表示不开启该功能。​​correct_table_id String 可选 示例值：26603****1-adad​替换词 ID。将 ASR 识别出的特定词汇替换为预期的标准词汇，可用于纠错、脱敏或别名替换等。比如“智立”替换为“致力”。​你需要先在火山语音技术_替换词管理创建替换词，并获取替换词 ID。​说明​执行优先级：替换词低于热词。即如果一个词同时是热词和替换词的源词，则优先执行热词，再执行替换词。例如，原词为“智立”：​若热词有“致力”，替换词要求“智立→治理”，那最后结果为 “致力”。​若热词有“致力”，替换词要求“致力→治理”，那最后结果为 “治理”。​​correct_table_name String 可选 示例值：替换词​替换词文件名称。你需要先在火山语音技术_替换词管理创建替换词，并获取替换词文件名称。​说明​执行优先级：替换词低于热词。即如果一个词同时是热词和替换词的源词，则优先执行热词，再执行替换词。例如，原词为“智立”：​若热词有“致力”，替换词要求“智立→治理”，那最后结果为 “致力”。​若热词有“致力”，替换词要求“致力→治理”，那最后结果为 “治理”。​​​​VADConfig Object 可选 示例值：-​VAD（语音检测） 配置。​SilenceTime Integer 可选 示例值：600​判停时间。房间内真人用户停顿时间若高于该值设定时间，则认为一句话结束。​取值范围为 [500，3000)，单位为 ms，默认值为 600。​​​VolumeGain Float 可选 示例值：0.3​音量增益值。增益值越低，采集音量越低。适当低增益值可减少噪音引起的 ASR 错误识别。​默认值为 1.0，推荐值 0.3。​​InterruptConfig Object 可选 示例值：-​语音打断配置。​注意​该参数仅当 Config.InterruptMode=0（即开启语音打断）时生效。​InterruptSpeechDuration Integer 可选 示例值：500​自动打断触发阈值。房间内真人用户持续说话时间达到该参数设定值后，智能体自动停止输出。​取值范围为0，[200，3000]，单位为 ms，值越大智能体说话越不容易被打断。​默认值为 0，表示用户发出声音且包含真实语义时即打断智能体输出。​​InterruptKeywords String[] 可选 示例值：["停止", "停下"]​触发打断的关键词列表。当用户语音中识别到列表中的任意关键词时，智能体将立即停止输出。​若配置该参数，只有识别到配置的打断词时才会触发打断，以降低背景环境人声无打断的干扰。使用该参数时，建议 InterruptSpeechDuration 设置为 0，避免自动打断触发阈值过高，导致关键词打断不生效。​​​TurnDetectionMode Integer 可选 示例值：0​新一轮对话的触发方式。​0：服务端检测到完整的一句话后，自动触发新一轮对话。​1：收到输入结束信令或说话字幕结果后，你自行决定是否触发新一轮会话。​默认值为 0。​该功能使用方法参看配置对话触发模式。​​​TTSConfig Object 必选 示例值：-​语音合成（TTS）相关配置。​IgnoreBracketText Integer[] 可选 示例值：[1,2]​过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。你需要在大模型 Prompt 中自行定义哪些内容放在指定标点符号内。具体使用方法参看过滤指定内容。​支持取值及含义如下：​1：中文括号（）​2：英文括号()​3：中文方括号【】​4：英文方括号[]​5：英文花括号{}​默认值为空，表示不进行过滤。​说明​若大模型返回的内容中，包含标点符号里的内容在最末端，且为独立句子，其后无包含真实语义内容，该标点符号中的内容不会出现在字幕中。​如大模型返回的内容为：当然可以，尽管问，我知无不言！(自信满满)。​此时（自信满满）。不会出现在字幕里。​如大模型返回的内容为：当然可以，尽管问，我知无不言(自信满满)！​此时（自信满满）会出现在字幕里。​​Provider String 必选 示例值：-​语音合成服务提供商，使用不同语音合成服务时，取值不同。支持使用的语音合成服务及对应取值如下：​volcano（服务自上而下语音生成速度递减，情感表现力递增）​火山引擎流式语音合成​火山引擎语音合成大模型（非流式输入流式输出）​火山引擎声音复刻大模型（非流式输入流式输出）​volcano_bidirection（服务自上而下语音生成速度递减，情感表现力递增）​火山引擎语音合成大模型（流式输入流式输出）​火山引擎声音复刻大模型（流式输入流式输出）​minimax​MiniMax 语音合成​​ProviderParams Object 必选 示例值：-​配置所选的语音合成服务。不同服务下，该结构包含字段不同：​火山引擎语音合成​火山引擎语音合成大模型（非流式输入流式输出）​火山引擎语音合成大模型（流式输入流式输出）​火山引擎声音复刻大模型（非流式输入流式输出）​火山引擎声音复刻大模型（流式输入流式输出）​MiniMax 语音合成​TTSConfig.ProviderParams（火山引擎语音合成） 可选​使用火山引擎语音合成时，合成速度快，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 必选 示例值：-​火山引擎语音合成服务应用配置。​appid String 必选 示例值：94****11​开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​cluster String 必选 示例值：volcano_tts​已开通语音合成服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​audio Object 必选 示例值：-​火山引擎语音合成服务音频配置。​voice_type String 必选 示例值：BV001_streaming​音色。​填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​​speed_ratio Float 可选 示例值：1.0​语速。取值范围为 [0.2,3]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​​volume_ratio Float 可选 示例值：1.0​音量。取值范围为 [0.1,3]，默认值为 1.0，通常保留一位小数即可。取值越大，音量越高。​​pitch_ratio Float 可选 示例值：1.0​音高。取值范围为 [0.1,3]，默认值为 1.0，通常保留一位小数即可。取值越大，音调越高。​​​​TTSConfig.ProviderParams（火山引擎语音合成大模型非流式输入流式输出） 可选​使用火山引擎语音合成大模型服务非流式输入流式输出模式时，速度稍慢，但是更生动、更具情感表现力，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 必选 示例值：-​火山引擎语音合成大模型服务应用配置。​appid String 必选 示例值：94****11​开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​cluster String 必选 示例值：volcano_tts​已开通语音合成大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​audio Object 必选 示例值：-​火山引擎语音合成大模型服务音频配置。​voice_type String 必选 示例值：zh_female_meilinvyou_moon_bigtts​音色。​填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​​pitch_rate Integer 可选 示例值：0​音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。​​speech_rate Integer 可选 示例值：0​语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。​​​​TTSConfig.ProviderParams（火山引擎语音合成大模型流式输入流式输出） 可选​使用火山引擎语音合成大模型服务流式输入流式输出模式时，生动、情感表现力最佳，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 必选 示例值：-​火山引擎语音合成大模型服务应用配置。​appid String 必选 示例值：94****11​开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​token String 必选 示例值：OaO****ws1​与语音合成大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。Access Token 查找方式，可参看如何获取 Token。​​​audio Object 可选 示例值：-​火山引擎语音合成大模型服务音频配置。​voice_type String 必选 示例值：BV001_streaming​音色。​填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​​pitch_rate Integer 可选 示例值：0​音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。​​speech_rate Integer 可选 示例值：0​语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。​​​Additions Object 可选 示例值：-​火山引擎语音合成大模型服务高级配置。​enable_latex_tn Boolean 可选 示例值：true​是否播报 Latex 公式。​true：播报。 为true 时，disable_markdown_filter 也需为 true 才生效。​false：不播报。​默认值为 false。​​disable_markdown_filter Boolean 可选 示例值：true​是否对 Markdown 格式内容进行过滤。​true：过滤；例如，**你好**，会读为“你好”。​false：不过滤。例如，**你好**，会读为“星星你好星星”。​默认值为 false。​​enable_language_detector Boolean 可选 示例值：false​是否自动识别语种。支持哪些语种？​true：自动识别。​false：不自动识别。​默认值为 false。​​​ResourceId String 必选 示例值：volc.service_type.10029​调用服务的资源信息 ID，该参数固定取值：volc.service_type.10029；​​​TTSConfig.ProviderParams（火山引擎声音复刻大模型非流式输入流式输出） 可选​使用火山引擎语音合成大模型服务非流式输入流式输出模式时，可自定义音色，且生成速度较快，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 可选 示例值：-​火山引擎声音复刻大模型服务应用配置。​appid String 可选 示例值：94****11​开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​cluster String 必选 示例值：volcano_icl​已开通声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​audio Object 可选 示例值：-​火山引擎声音复刻大模型服务音频配置。​voice_type String 必选 示例值：S_N****T7k1​声音复刻声音 ID。你可登录语音技术控制台获取。​​speed_ratio Float 可选 示例值：1.0​语速。取值范围为 [0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​​​​TTSConfig.ProviderParams（火山引擎声音复刻大模型流式输入流式输出） 可选​使用火山引擎语音合成大模型服务非流式输入流式输出模式时，可自定义音色，且更生动、情感表现力更佳，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 可选 示例值：-​火山引擎声音复刻大模型服务应用配置。​appid String 可选 示例值：94****11​开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​token String 可选 示例值：OaO****ws1​与开通声音复刻大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。Access Token 查找方式，可参看如何获取 Token。​​​audio Object 可选 示例值：-​火山引擎声音复刻大模型服务音频配置。​voice_type String 可选 示例值：S_N****T7k1​声音复刻声音 ID。你可登录语音技术控制台获取。​​speed_ratio Float 可选 示例值：1.0​语速。取值范围为 [0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​​​Additions Object 可选 示例值：-​火山引擎声音复刻大模型服务高级配置。​enable_latex_tn Boolean 可选 示例值：true​是否播报 Latex 公式。​true：播报。 为true 时，disable_markdown_filter也需为 true才可生效。​false：不播报。​默认值为 false。​​disable_markdown_filter Boolean 可选 示例值：true​是否对 Markdown 格式内容进行过滤。​true：过滤；例如，**你好**，会读为“你好”。​false：不过滤。例如，**你好**，会读为“星星你好星星”。​默认值为 false。​​enable_language_detector Boolean 可选 示例值：false​是否自动识别语种。​true：自动识别。​false：不自动识别。​默认值为 false。​​​ResourceId String 必选 示例值：volc.megatts.default​调用服务的资源信息 ID，该参数固定取值：volc.megatts.default。​​​TTSConfig.ProviderParams（MiniMax 语音合成） 可选​使用MiniMax语音合成时，你需要在 TTSConfig.ProviderParams里配置以下字段：​Authorization String 必选 示例值：eyJhbG****SUzI1N​API 密钥。前往 Minimax 账户管理-接口密钥获取。​​Groupid String 必选 示例值：983*****669​用户所属组 ID。前往 Minimax 账号信息-基本信息获取。​​model String 必选 示例值：speech-01-turbo​发起请求的模型版本：​speech-01-turbo：最新模型，拥有出色的效果与时延表现。​speech-01-240228：稳定版本模型，效果出色。​speech-01-turbo-240228：稳定版本模型，时延更低。​​URL String 必选 示例值：https://api.minimax.chat/v1/t2a_v2​请求语音合成 URL，该参数固定取值：https://api.minimax.chat/v1/t2a_v2。​​voice_setting Object 可选 示例值：-​音频配置。​speed Float 可选 示例值：1.0​语速。取值越大，语速越快。​取值范围为 [0.5,2]，默认值为1.0。​​vol Float 可选 示例值：1.0​音量。取值越大，音调越高。​取值范围为 (0,10]，默认值为 1.0。​​pitch Float 可选 示例值：0​语调。取值越大，语调越高。​取值范围为 [-12,12]，且必须为整数。​默认值为 0，表示原音色输出。​​voice_id String 可选 示例值：male-qn-jingying​系统音色编号/复刻音色编号。​系统音色可前往 voice_setting.voice_id查询。​克隆音色参看FAQ。​注意​voice_id 与 timber_weights必须设置其中一个。​​​pronunciation_dict Object 可选 示例值：-​特殊标注配置。可对特殊文字、符号指定发音。​tone String[] 可选 示例值：["燕少飞/(yan4)(shao3)(fei1)","达菲/(da2)(fei1)"，"omg/oh my god"]​用于替换需要特殊标注的文字、符号及对应的发音，可用于调整声调或指定其他字符的发音。格式为 "原文字/注音"，注音部分根据语言类型采用不同方式标注：​英文注音：使用对应发音的英文单词，例如："omg/oh my god"。​中文注音：使用拼音，并在每个音节后以括号标注声调，音调用数字表示：​一声（阴平）：1​二声（阳平）：2​三声（上声）：3​四声（去声）：4​轻声：5​例如，"燕少飞/(yan4)(shao3)(fei1)"、"达菲/(da2)(fei1)"。​​​timber_weights Object[] 可选 示例值：-​合成音色权重设置。可通过该参数设置多种音色混合，并调整每个具体音色权重。最多支持 4 种音色混合。​注意​timber_weights与 VoiceSetting.voice_id必须设置其中一个。​voice_id String 可选 示例值：male-qn-jingying​音色编号。当前仅支持系统音色，可前往 voice_setting.voice_id查询。​​weight Integer 可选 示例值：1​权重。取值为整数，单一音色取值占比越高，合成音色越像。取值范围为[1,100]。​​​stream Boolean 可选 示例值：false​是否开启流式输出。​false：不开启流式输出。​true：开启流式输出。​默认值为 false。​​language_booststring String 可选 示例值：auto​增强指定小语种/方言场景下的语音表现。不同场景下取值及含义如下：​不明确小语种类型：auto。取值为auto时，模型将自主判断小语种类型。​小语种：​Spanish：西班牙语​French：法语​Portuguese：葡萄牙语​Korean：韩语​Indonesian：印度尼西亚语​German：德语​Japanese：日语​Italian：意大利语​auto：自动模式​方言：​Chinese,Yue：粤语。Chinese,Yue仅当MiniMaxTTSConfig.model=speech-01-turbo时生效。​默认值为空。​​​​​LLMConfig Object 必选 示例值：-​大模型相关配置。支持的大模型平台如下：​火山方舟平台​Coze平台​第三方大模型/Agent​LLMConfig（火山方舟平台） 可选​使用火山方舟平台时，你需要在 LLMConfig里配置以下字段：​Mode String 必选 示例值：ArkV3​大模型平台标识。使用火山方舟平台时，该参数固定取值： ArkV3。​​EndPointId String 可选 示例值：ep-22****212​自定义推理接入点 EndPointId。当需要使用模型推理功能（如直接调用部署的基础模型）时，此参数为必填。​可前往控制台创建或查询自定义推理接入点。​注意​EndPointId 与 BotId 不可同时填写，若同时填写，则 EndPointId 生效。​当前仅支持自定义推理接入点，不支持预置推理接入点。​​BotId String 可选 示例值：botid****212​应用 ID。需要使用方舟应用实验室功能时，为必填。​可前往控制台创建或查询应用 ID。​注意​请确保你的应用使用的是自定义推理接入点，目前暂不支持预置推理接入点。​​​​EndPointId 与 BotId 不可同时填写，若同时填写，则 EndPointId 生效。​​Temperature Float 可选 示例值：0.1​采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。​取值范围为 (0, 1]，默认值为 0.1。​​MaxTokens Integer 可选 示例值：1024​输出文本的最大 token 限制。默认值为 1024。​​TopP Float 可选 示例值：0.3​采样的选择范围，控制输出 token 的多样性。模型将从概率分布中累计概率超过该取值的标记中进行采样，以确保采样的选择范围不会过宽，值越大输出的 token 类型越丰富。​取值范围为 [0,1]，默认值为 0.3。​​SystemMessages String[] 可选 示例值：["你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"]​系统提示词。用于输入控制大模型行为方式的指令，定义了模型的角色、行为准则，特定的输出格式等。​​UserPrompts Object 可选 示例值：[ { "Role": "user", "Content": "你好" }, { "Role": "assistant", "Content": "有什么可以帮到你的？" }, { "Role": "user", "Content": "你是谁？" }, { "Role": "assistant", "Content": "我是你的智能问答助手。" }​用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。​UserPrompts 存储的对话轮数受 HistoryLength 控制。例如 UserPrompts 中预先存储了两轮对话，HistoryLength 设置为 3，用户已进行了三轮对话，第四轮会话开始时，UserPrompts 中存储的内容会被全部删除。​注意​UserPrompts 中 Role 的取值只包含 user 和 assistant，且必须成对出现，否则大模型可能会出现未定义行为。​​HistoryLength Integer 可选 示例值：3​历史问题轮数。默认值为 3。​在调用该接口时需要确保所有 UserPrompts 和 SystemMessage 消息文本总长度不超过大模型上下文长度。​例如：历史问题轮数为 3，使用 Skylark2-lite-8k 大模型，长度限制为 8k，UserPrompts 预先存储了两轮对话，用户输入了第一轮会话的问题，此时 SystemMessages+UserPrompts+第一轮会话问题总长度不超过8k。​​Tools Object[] 可选 示例值：-​定义一组可供模型在 Function Calling 功能中调用的工具。​目前仅支持函数作为工具。你需要提供每个函数的定义，模型会基于定义，在需要时生成调用特定函数的 JSON 指令。该功能使用方法参看 Function Calling。​注意​Function calling 功能不支持和联网插件或知识库插件同时使用。​type String 可选 示例值：function​工具类型。可取值及含义如下：​function：函数调用。​​function Object 可选 示例值：-​模型可以调用的工具列表。​name String 必选 示例值：get_current_weather​函数的名称。​​description String 可选 示例值：获取指定城市的天气信息​对函数用途的描述，供模型判断何时以及如何调用该工具函数。​​parameters JSONMap 可选 示例值：-​函数请求参数，以 JSON Schema 格式描述。具体格式请参考 JSON Schema 文档。​​json复制​​​​Prefill Boolean 可选 示例值：false​将 ASR 中间结果提前送入大模型进行处理。​true：开启。启用后，允许将 ASR 识别中间结果提前发送给大模型进行处理，以降低延时。​false：关闭。等待 ASR 模块识别出相对完整的一句话后再送入大模型处理。​默认值为 false。​注意​开启后会产生额外模型消耗。​​VisionConfig Object 可选 示例值：-​视觉理解能力配置。如何使用视觉理解能力？​注意​该功能仅在使用 EndPointId（推理点）接入大模型，且推理点选择 vision 系列模型时才生效，如 Doubao-vision-pro-32k。​Enable Boolean 可选 示例值：true​是否开启视觉理解功能。​false：不开启。​true：开启。​默认值为 false。​​SnapshotConfig Object 可选 示例值：-​截图相关配置。截图送入大模型以供理解信息。​StreamType Integer 可选 示例值：0​截图流类型。​0：主流。​1：屏幕流。​默认值为 0。​​ImageDetail String 可选 示例值：auto​图片处理模式。取值及含义如下：​high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。​low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。​auto：自动模式。根据图片分辨率，自动选择适合的模式。​默认值为 auto。​​Height Integer 可选 示例值：640​送入大模型截图视频帧高度，取值范围为 [0, 1792]，单位为像素。​不填或传 0时自动修改为 360。​传入大模型截图视频帧宽度自动按传入高度进行比例计算。​​Interval Integer 可选 示例值：1000​相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。​​ImagesLimit Integer 可选 示例值：2​单次送大模型截图数。取值范围为 [0, 50]。​不传或传 0 时自动修改为 2。​​​StorageConfig Object 可选 示例值：-​截图存储配置。​Type Integer 可选 示例值：0​存储类型。​0：按照 Base 64 编码存入服务端缓存，会话结束后自动删除。​1：存储至 TOS 平台。使用 TOS 存储前需前往控制台开通该服务。​​TosConfig Object 可选 示例值：-​TOS 存储配置。​AccountId String 可选 示例值：acc****_id​火山引擎平台账号 ID，例如：*********。查看路径参看查看和管理账号信息。​注意​此账号 ID 为火山引擎主账号 ID。​若你调用 OpenAPI 鉴权过程中使用的 AK、SK 为子用户 AK、SK，账号 ID 也必须为火山引擎主账号 ID，不能使用子用户账号 ID。​​Region Integer 可选 示例值：0​存储桶区域。不同存储桶区域对应的 Region 不同，具体参看 Region对照表。​默认值为 0。​注意​该字段填入的存储桶区域需要与你在 TOS 平台创建存储桶时选择的区域相同。​​Bucket String 可选 示例值：b****t​存储桶名称。前往控制台创建或查询。​​​​​​LLMConfig（Coze平台） 可选​使用 Coze 平台时，你需要在 LLMConfig 里配置以下字段。​使用前确保智能体已发布为 API 服务。详情参考准备工作。​Mode String 必选 示例值：CozeBot​大模型平台名称。该参数固定取值：CozeBot。​​CozeBotConfig Object 必选 示例值：-​Coze 智能体配置。​Url String 必选 示例值：https://api.coze.cn​请求地址。该参数固定取值：https://api.coze.cn​​BotId String 必选 示例值：73****68​Coze 智能体 ID。​可前往你需要调用的智能体开发页面获取。开发页面 URL 中 bot 参数后的数字即智能体ID。例如开发页面 URL 为：https://www.coze.cn/space/341/bot/73428668，则 BotId 为 73428668。​​APIKey String 必选 示例值：czu_UEE2mJn6****MHxLCVv9uQ7H​Coze 访问密钥。​你可以生成个人访问令牌以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考 OAuth 应用管理。​注意​创建个人访问令牌或 OAuth 应用时，你需要根据你的 Bot 使用场景勾选对应权限，否则会鉴权失败。​​UserId String 必选 示例值：123​标识当前与智能体对话的用户，由你自行定义、生成与维护。UserId 用于标识对话中的不同用户，不同的 UserId，其对话的上下文消息、数据库等对话记忆数据互相隔离。如果不需要用户数据隔离，可将此参数固定为一个任意字符串，例如 123，abc等。​​HistoryLength Integer 可选 示例值：3​历史问题轮数。默认值为 3。​在调用该接口时需要确保作为上下文的用户消息和智能体消息文本总长度小于模型上下文长度。​例如：历史问题轮数为 3，使用 Skylark2-lite-8k大模型，该模型上下文长度限制为 8k，询问第 10 个问题时，需保证第 10 个问题的长度与第八、九轮用户消息和智能体消息文本的总长度之和不得超过 8k。​​Prefill Boolean 可选 示例值：false​将 ASR 中间结果提前送入大模型进行处理。​开启该功能后可不等待 ASR 模块识别出完整的一句话再送入大模型处理，而是将 ASR 识别中间结果提前送入大模型进行处理以降低延时。​true：开启。​false：关闭。​默认值为 false。​注意​开启后会产生额外模型消耗。​​EnableConversation Boolean 可选 示例值：false​是否将上下文存储在 Coze 平台。​若需要使用 Coze 平台上下文管理相关功能，如将指定内容添加到会话中，可开启此功能。功能开启后 RTC 不再存储上下文内容。​false：不开启。​true：开启。​默认值为 false。​注意​EnableConversation 为 true 时会导致HistoryLength设置无效。​​​​LLMConfig（第三方大模型） 可选​使用第三方大模型时，你需要在 LLMConfig里配置以下字段：​Mode String 必选 示例值：CustomLLM​大模型平台名称。使用第三方大模型/Agent 时，该参数固定取值： CustomLLM。​​URL String 必选 示例值：https://test.com/path/to/app​第三方大模型/Agent 的请求 URL，需要使用 HTTPS 域名，且必须符合火山引擎标准。​验证是否符合标准：可前往体验 Demo->修改 AI 设定->第三方模型，并填入 URL 进行快速验证。​若验证失败可前往文档接入第三方大模型/Agent，查看接口标准并通过验证工具查看详细报错。​说明​如果需要在每次请求时传递一些简单的、非敏感的参数（如 session_id），可以直接将它们作为查询参数拼接到此 URL 中。​如需使用 HTTP 域名进行测试，可在下方 Feature 参数中填入 {"Http":true}，但无法保证服务质量。​​ModelName String 可选 示例值：name1​第三方大模型/Agent 的名称。​​APIKey String 可选 示例值：pat*****123231​Bearer Token 认证方式的大模型鉴权 Token。​​MaxTokens Integer 可选 示例值：1024​输出文本的最大 token 限制。默认值为 1024。​​Temperature Float 可选 示例值：0.1​采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为（0,1]，默认值为 0.1。​​TopP Float 可选 示例值：0.3​采样的选择范围，控制输出 token 的多样性。模型将从概率分布中累计概率超过该取值的标记中进行采样，以确保采样的选择范围不会过宽，值越大输出的 token 类型越丰富。​取值范围为[0,1]，默认值为 0.3。​​SystemMessages String[] 可选 示例值：["你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"]​大模型 System 角色预设指令，可用于控制模型输出。​​UserPrompts Object 可选 示例值：[ { "Role": "user", "Content": "你好" }, { "Role": "assistant", "Content": "有什么可以帮到你的？" }, { "Role": "user", "Content": "你是谁？" }, { "Role": "assistant", "Content": "我是你的智能问答助手。" }​大模型 User 角色预设 Prompt，可用于增强模型的回复质量，模型回复时会优先参考此处内容。​UserPrompts 存储的对话轮数受 HistoryLength 控制。例如UserPrompts 中预先存储了两轮对话，HistoryLength 设置为 3，用户已进行了三轮对话，第四轮会话开始时，UserPrompts 中存储的内容会被全部删除。​注意​UserPrompts 中 Role 的取值只包含 user 和 assistant，且必须成对出现，否则大模型可能会出现未定义行为。​​HistoryLength Integer 可选 示例值：3​历史问题轮数。默认值为 3。​在调用该接口时需要确保所有 UserPrompts 和 SystemMessage 消息文本总长度小于大模型上下文长度。​例如：历史问题轮数为 3，使用 Skylark2-lite-8k 大模型，长度限制为 8k，UserPrompts 预先存储了两轮对话，用户输入了第一轮会话的问题，此时 SystemMessages+UserPrompts+第一轮会话问题的总长度不超过 8k。​​Feature String 可选 示例值：{\"Http\":true}​使用 HTTP 域名进行测试，该参数固定取值：{\"Http\":true}。​​Prefill Boolean 可选 示例值：false​将 ASR 中间结果提前送入大模型进行处理：​true：开启。将 ASR 识别中间结果提前送入大模型进行处理，以降低延时。​false：关闭。需等待 ASR 模块识别出完整的一句话后，再将其整体送入大模型处理。​默认值为 false。​注意​开启后会产生额外模型消耗。​​Custom String 可选 示例值：-​自定义 JSON 字符串，可传入业务自定义参数。​​VisionConfig Object 可选 示例值：-​视觉理解能力配置。​该功能使用说明参看视觉理解能力。​Enable Boolean 可选 示例值：true​是否开启视觉理解功能。​false：不开启；​true：开启。​默认值为false。​​SnapshotConfig Object 可选 示例值：-​传给大模型截图相关配置。​StreamType Integer 可选 示例值：0​截图流类型。​0：主流。​1：屏幕流。​默认值为 0。​​ImageDetail String 可选 示例值：auto​图片处理模式。取值及含义如下：​high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。​low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。​auto：自动模式。根据图片分辨率，自动选择适合的模式。​默认值为 auto。​​Height Integer 可选 示例值：640​送入大模型视频帧高度，取值范围为 [0, 1792]，单位为像素。​不填或传 0时自动修改为 360。​传入大模型视频帧宽度自动按传入高度计算。​​Interval Integer 可选 示例值：1000​相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。​​ImagesLimit Integer 可选 示例值：2​单次送大模型图片数。取值范围为 [0, 50]。​不传或传 0时自动修改为 2。​​​StorageConfig Object 可选 示例值：-​截图存储相关配置。​Type Integer 可选 示例值：0​存储类型。​0：Base 64 编码存入本地，会话结束后自动删除。​1：TOS。使用 TOS 存储前需前往控制台开通该服务。​​TosConfig Object 可选 示例值：-​TOS 存储配置。​AccountId String 可选 示例值：acc****_id​火山引擎平台账号 ID，例如：*********。​火山引擎平台账号 ID 查看路径参看查看和管理账号信息。​此账号 ID 为火山引擎主账号 ID。​若你调用 OpenAPI 鉴权过程中使用的 AK、SK 为子用户 AK、SK，账号 ID 也必须为火山引擎主账号 ID，不能使用子用户账号 ID。​​Region Integer 可选 示例值：0​不同存储平台支持的 Region 不同，具体参看 Region对照表​默认值为0。​​Bucket String 可选 示例值：b****t​存储桶名称。前往控制台创建或查询。​​​​​​​SubtitleConfig Object 可选 示例值：-​配置字幕回调。​可通过客户端或服务端接收回调消息，消息格式为二进制，使用前需解析。详细说明参看实时对话式 AI 字幕。​DisableRTSSubtitle Boolean 可选 示例值：false​是否关闭房间内客户端字幕回调。​true：关闭，即不通过客户端接收字幕回调消息。​false：开启，通过客户端接收字幕回调消息。开启后，在客户端实现监听 onRoomBinaryMessageReceived（以 Android 为例），即可接收字幕回调消息。​默认值为 false。​注意​如需通过服务端接收字幕回调，请配置 ServerMessageUrl 和 ServerMessageSignature。​​ServerMessageUrl String 可选 示例值：https://example-domain.com/vertc/subtitle​接收字幕结果的 URL 地址。通过服务端接收字幕回调时必填。​接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​可通过 curl -v http(s)://yourexample-domain.com/vertc/subtitle 命令对域名进行快速校验：​若返回 HTTP 状态码为 301 或 302，则说明域名不可用，POST 方法可能会重定向为 GET。​若返回 307 或 308 则说明域名可用，且始终保持 POST 方法。​注意​如果你同时通过该接口接收任务状态变化回调和字幕回调，请确保在 ServerMessageUrl 和 ServerMessageURLForRTS中填入相同的 URL，否则会导致无法接收任务状态回调或字幕回调。​​ServerMessageSignature String 可选 示例值：b46ab5f1d8ad6a​鉴权签名。通过服务端接收字幕回调时必填。​在接收到字幕结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​​SubtitleMode Integer 可选 示例值：0​字幕回调时是否需要对齐音频时间戳。​0：对齐音频时间戳。​1：不对齐音频时间戳。取 1 时可更快回调字幕信息。​默认值为 0。​​​FunctionCallingConfig Object 可选 示例值：-​使用 Function calling 功能时，从服务端接受函数工具返回的信息指令配置。​Function calling 功能使用详情参看功能说明文档。​注意​该功能仅在使用火山方舟平台时生效。​ServerMessageUrl String 可选 示例值：https://example-domain8080/m2​服务端接收 Function Calling 函数工具返回的信息指令的 URL 地址。功能使用详情参看服务端实现 Function Calling 功能。​接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​ServerMessageUrl 和 ServerMessageSignature均填写正确才能开启该功能。​​ServerMessageSignature String 可选 示例值：TestSignature​鉴权签名。​在接收到函数调用信息结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​​​InterruptMode Integer 可选 示例值：0​是否启用语音打断：​0：开启语音打断。开启后，一旦检测到用户发出声音，智能体立刻停止输出。​1：关闭语音打断。关闭后，智能体说话期间，用户语音输入内容会被忽略不做处理，不会打断智能体讲话。​默认值为 0。​​​
AgentConfig Object 必选 示例值：-​智能体相关配置，包括欢迎词、任务状态回调等信息。​TargetUserId String[] 必选 示例值：["user1"]​真人用户 ID。需使用客户端 SDK 进房的真人用户的 UserId。仅支持传入一个 UserId，即单个房间内，仅支持一个用户与智能体一对一通话。​​WelcomeMessage String 可选 示例值：Hello​智能体启动后的欢迎词。​​UserId String 可选 示例值：BotName001​智能体 ID，用于标识智能体。​由你自行定义、生成与维护，支持由大小写字母（A-Z、a-z）、数字（0-9）、下划线（_）、短横线（-）、句点（.）和 @ 组成，最大长度为 128 个字符。​若不填则默认值为 voiceChat_$(TargetUserId)_$(timestamp_now)。​注意​同一 AppId 下 UserId 建议全局唯一。若同一 AppId 下不同房间内智能体名称相同，会导致使用服务端回调的功能异常，如字幕、Function Calling 和任务状态回调功能。​UserId 取值与 TargetUserId不能重复。​​EnableConversationStateCallback Boolean 可选 示例值：false​是否接收智能体状态变化回调，获取智能体关键状态，比如​“聆听中”、“思考中”、“说话中”、“被打断”等。功能详细说明，参看接收状态变化消息。​true：接收。可通过客户端或服务端接收智能体状态变化回调。​通过客户端接收：还需在客户端实现监听回调 onRoomBinaryMessageReceived（以 Android 端为例）。​通过服务端接收：还需配置字段 ServerMessageURLForRTS 和 ServerMessageSignatureForRTS。​false：不接收。​默认值为 false。​​ServerMessageSignatureForRTS String 可选 示例值：b46ab5f1d8ad6a​鉴权签名。通过服务端接受任务状态变化回调时必填。​你可传入该鉴权参数，在接收到回调结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​​ServerMessageURLForRTS String 可选 示例值：https://example-domain.com/vertc/callback​接收任务状态变化的 URL 地址。通过服务端接受任务状态变化回调时必填。​接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​注意​如果同时通过该接口接收任务状态变化回调和字幕回调，请确保在 ServerMessageURLForRTS 与 SubtitleConfig.ServerMessageUrl中填入相同的 URL，否则会导致无法接收任务状态回调或字幕回调。​​UseLicense Boolean 可选 示例值：true​是否为 License 用户。​true：是；​false：否。​默认值为 false。​若为 License 用户，你需要：​联系技术支持开通白名单。​前往控制台硬件场景服务获取你需要的 ASR、TTS 和 LLM 相关参数值。注意你必须使用在此处获取的 ASR、TTS 和 LLM 参数值，智能体才能正常工作。​如果你使用大模型流式语音识别和大模型语音合成，在调用 StartVoiceChat 接口时，ASRConfig.ProviderParams.AccessToken 和 TTSConfig.ProviderParams.AccessToken无需填入。​​Burst Object 可选 示例值：-​音频快速发送配置。​开启该功能后，可通过快速发送音频实现更好的抗弱网能力。​说明​该功能仅在嵌入式硬件场景下支持，且嵌入式 Linux SDK 版本不低于 1.57。​Enable Boolean 可选 示例值：true​是否开启音频快速发送。​false：开启。​true：关闭。​默认值为 false。​​BufferSize Integer 可选 示例值：10​接收音频快速发送片段时，客户端可缓存的最大音频时长。取值范围为[10,3600000]，单位为 ms，默认值为 10。​​Interval Integer 可选 示例值：10​音频快速发送结束后，其他音频内容发送间隔。取值范围为[10,600]，单位为 ms，默认值为10。​​​​
返回参数​
本接口无特有的返回参数。公共返回参数请见返回结构。​
其中返回值 Result 仅在请求成功时返回 ok，失败时为空。​
完整请求结构
火山引擎流式语音识别
火山引擎流式语音识别大模型
火山引擎语音合成
火山引擎语音合成大模型流式输入流式输出
MiniMax 语音合成
火山方舟大模型
第三方大模型
输入示例
```
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": { // 以火山引擎流式语音识别大模型为例，其他语音识别服务配置参看其他示例。
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": { // 以火山引擎语音合成流式输入流式输出合成为例，其他语音合成服务配置参看其他示例。
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": { // 以火山方舟大模型为例，其他大模型服务配置参看其他示例。
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserPrompts": [
        {
          "Role": "user",
          "Content": "你好"
        },
        {
          "Role": "assistant",
          "Content": "有什么可以帮到你的？"
        }
      ],
      "HistoryLength": 3
    }
  },
  "AgentConfig": {
    "TargetUserId": [
      "user1"
    ],
    "WelcomeMessage": "Hello",
    "UserId": "BotName001"
  }
}
```

输出示例
```
{
  "Result": "ok",
  "ResponseMetadata": {
    "RequestId": "20230****10420",
    "Action": "StartVoiceChat",
    "Version": "2024-12-01",
    "Service": "rtc",
    "Region": "cn-north-1"
  }
}
```





## Function Calling（非流式返回结果）--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1359441


  * 文档首页
/实时音视频/实时对话式 AI/体验进阶/Function Calling（非流式返回结果）

Function Calling（非流式返回结果）
最近更新时间：2025.05.29 11:18:34首次发布时间：2024.11.05 11:56:09

文档反馈
在实时对话式 AI场景下，通过使用 Function Calling 功能允许大模型识别用户对话中的特定需求，智能调用外部函数、API 等工具来执行它自身无法独立完成的任务，如处理实时数据检索、文件处理、数据库查询等。适用于天气查询、股票行情查询、数学计算等场景。
说明
该功能仅在使用火山方舟平台模型时生效。且只有在使用 doubao 非1.5 代系模型时，按照非流式返回 Function Calling 结果。
服务端和客户端均可实现该功能。你可根据业务请求端的类型选择对应的方式。例如你在开发 AI 应用时，选择服务端响应请求，建议使用服务端实现传入大模型上下文，降低请求延迟。
## 时序图
你可参看如下时序图在该场景下使用 Function Calling 功能：
步骤 1：开启 Function Calling 功能。 步骤 2：触发 Function Calling 后接收工具调用指令消息。 步骤 3：执行本地工具获取工具调用结果，并将结果信息传回 RTC 服务端。 步骤 4：收到音频回复。 其中步骤 2 、3 支持多轮 Function calling 调用。当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## 服务端实现
### 步骤 1：开启 Function Calling 功能
你需要调用 接口配置以下字段开启该功能：
  1. 通过 LLMConfig.Tools字段配置一组或多组 Function（函数）工具相关的功能和定义。
  2. 通过LLMConfig.FunctionCallingConfig字段配置接收 Function Calling 功能返回的消息的 URL 和鉴权签名，返回消息包括函数被调用时触发的通知消息和函数调用指令消息。 
    1. ServerMessageUrl：接收 Function Calling 功能返回消息的 URL 地址。你指定的 URL 地址将收到来自 RTC 服务器的 HTTP(S) POST 请求发送的指令消息，格式为 JSON。
    2. ServerMessageSignature：鉴权签名。你可传入该鉴权字段，在收到 Function Calling 功能返回消息时，与步骤 2 返回工具调用指令消息中的 signature 字段的值进行对比，用于鉴权，保证消息的可靠性与安全性。


你可以参考以下示例代码进行请求：
```
POST https: //rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserPrompts": [
        {
          "Role": "user",
          "Content": "你好"
        },
        {
          "Role": "assistant",
          "Content": "有什么可以帮到你的？"
        }
      ],
      "HistoryLength": 3,
      "Tools": [
        {
          "Type": "function",
          "function": {
            "name": "get_current_weather",
            "description": "获取给定地点的天气",
            "parameters": {
              "type": "object",
              "properties": {
                "location": {
                  "type": "string",
                  "description": "地理位置，比如北京市"
                },
                "unit": {
                  "type": "string",
                  "description": "",
                  "enum": [
                    "摄氏度",
                    "华氏度"
                  ]
                }
              },
              "required": [
                "location"
              ]
            }
          }
        }
      ]
    }
    "FunctionCallingConfig": {
      "ServerMessageUrl": "https://example-domain.com/vertc/fc",
      "ServerMessageSignature": "b46a****8ad6a",
    },
    "AgentConfig": {
      "TargetUserId": [
        "user1"
      ],
      "WelcomeMessage": "Hello",
      "UserId": "BotName001"
    }
  }
}

JSON

```

### 步骤 2：接收工具调用指令消息
当用户的问题触发 Function Calling 时，会通过你在步骤 1 配置的 URL 地址，使用 HTTP(S) 请求返回本次函数工具调用的指令消息，返回的格式为 JSON 格式，内容如下：
字段名| 类型| 描述  
---|---|---  
message| Array of | 调用指令消息详情。  
signature| String| StartVoiceChat.Config.FunctionCallingConfig 中设置的 signature的值，用于鉴权。  
message：
字段名| 类型| 描述  
---|---|---  
id| String| 本次 Function Calling 任务的标识 ID。  
type| String| Function Calling 调用工具类型，固定为 function，表示为函数调用。  
function| | 调用函数详情。  
function：
字段名| 类型| 描述  
---|---|---  
name| String| 函数名称。  
arguments| String| 函数调用指令详情。  
### 步骤 3：将工具调用的结果信息传回 RTC 服务端
在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。 你可调用 接口设置以下参数将工具调用的结果信息传回 RTC 服务端：
你可参看以下示例将工具调用的结果信息传回 RTC 服务端：
```
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-06-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Command": "function",
  "Message":"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
}

JSON

```

### 步骤 4：获取 Function Calling 最终答复
当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## 客户端实现
### 步骤 1：开启 Function Calling 功能
你需要调用 接口配置 LLMConfig.Tools 字段，输入你需要的一组或多组 Function（函数）工具相关的功能和定义开启该功能。 你可以参考以下示例代码进行请求：
```
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserMessages": [
        "user:\"你是谁\"",
        "assistant:\"我是问答助手\"",
        "user:\"你能干什么\"",
        "assistant:\"我能回答问题\""
      ],
      "HistoryLength": 3,
      "Tools": [
        {
          "Type": "function",
          "function": {
            "name": "get_current_weather",
            "description": "获取给定地点的天气",
            "parameters": {
              "type": "object",
              "properties": {
                "location": {
                  "type": "string",
                  "description": "地理位置，比如北京市"
                },
                "unit": {
                  "type": "string",
                  "description": "",
                  "enum": [
                    "摄氏度",
                    "华氏度"
                  ]
                }
              },
              "required": [
                "location"
              ]
            }
          }
        }
      ]
    }
  },
  "AgentConfig": {
    "TargetUserId": [
      "user1"
    ],
    "WelcomeMessage": "Hello",
    "UserId": "BotName001"
  }
}

JSON

```

### 步骤 2：接收工具调用指令消息
当用户的问题触发 Function Calling 时，你可通过 回调接收本次函数工具调用的指令消息。该回调中的 message 字段中的内容为函数调用指令消息，格式为二进制，使用前需解析。 message 的格式如下：
参数名| 类型| 描述  
---|---|---  
magic number| String| 消息格式，固定为 tool，表示此次消息为函数工具指令消息。  
length| String| 信息指令长度，单位为 bytes。存放方式为大端序。  
Tool_Calls| String| 信息指令详细信息。格式参看。  
Tool_Calls
参数名| 类型| 是否必填| 描述  
---|---|---|---  
tool_calls| Array of | 是| 工具调用信息列表。  
id| String| 是| 本次工具调用的唯一标识 ID。  
type| String| 是| 工具类型。  
tool_call
字段名| 类型| 描述  
---|---|---  
function| | 调用函数详情。  
function
字段名| 类型| 描述  
---|---|---  
name| String| 函数名称。  
arguments| String| 函数调用指令详情。  
你可参看以下示例代码对工具调用指令消息进行解析。
```
//定义结构体
struct function {
  std::string arguments;
  std::string name;
};
struct ToolCall {
  std::string id;
  std::string type;
  function func;
};
struct ToolCallsMsgData {
  std::string subscribe_user_id;
  std::vector<ToolCall> tool_calls
};
//回调事件
void onRoomBinaryMessageReceived(const char* uid, int size, const uint8_t* message) {
  std::string tool_calls;
  bool ret = Unpack(message, size, tool_calls);
  if(ret) {
    ParseData(tool_calls);
  }
}
//拆包校验
bool Unpack(const uint8_t *message, int size, std::string& tool_calls_msg) {
  int kToolCallsHeaderSize = 8;
  if(size < kToolCallsHeaderSize) { 
    return false;
  }
  // magic number "tool"
  if(static_cast<uint32_t>((static_cast<uint32_t>(message[0]) << 24) 
      | (static_cast<uint32_t>(message[1]) << 16) 
      | (static_cast<uint32_t>(message[2]) << 8) 
      | static_cast<uint32_t>(message[3])) != 0x746F6F6CU) {
    return false;
  }
  
  uint32_t length = static_cast<uint32_t>((static_cast<uint32_t>(message[4]) << 24) 
      | (static_cast<uint32_t>(message[5]) << 16) 
      | (static_cast<uint32_t>(message[6]) << 8) 
      | static_cast<uint32_t>(message[7]));
      
  if(size - kToolCallsHeaderSize != length) {
    return false;
  }
  if(length) {
    tool_calls_msg.assign((char*)message + kToolCallsHeaderSize, length);
  } else {
    tool_calls_msg = "";
  }
  return true;
}
//解析
void ParseData(const std::string& msg) {
  // 解析 JSON 字符串
  nlohmann::json json_data = nlohmann::json::parse(msg);
  ToolCallsMsgData toolcalls_data;
  // 存储解析后的数据
  toolcalls_data.subscribe_user_id = json_data["subscribe_user_id"];
  // 遍历 JSON 数据并填充结构体
  for (const auto& item : json_data["tool_calls"]) {
    ToolCall tool_call;
    tool_call.id = item["id"];
    tool_call.type = item["type"];
    auto fun_json = item["function"];
    tool_call.func.arguments = fun_json["arguments"];
    tool_call.func.name = fun_json["name"];
    toolcalls_data.push_back(tool_call);
  }
}

C++

```

### 步骤 3：将工具调用的结果信息传回 RTC 服务端
在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。 你可调用 接口设置以下参数将函数调用指令消息按照二进制格式传回 RTC 服务端：
  * userId：消息接收用户的 ID
  * buffer：工具调用的结果信息。
  * config：发送消息的可靠有序性。


指令消息格式如下：
参数名| 类型| 描述  
---|---|---  
magic number| String| 消息格式，固定为 func。  
length| String| 工具调用的结果信息长度，单位为 bytes。存放方式为大端序。  
Function_Response| String| 工具调用的结果信息。  
你可参看以下示例代码传回工具调用的结果信息。
TypeScript
Java
```
import VERTC from '@volcengine/rtc';
/**
 * @brief 将字符串包装成 TLV
 */
function stringToTLV(inputString: string) {
 const type = 'func';
 const typeBuffer = new Uint8Array(4);
 for (let i = 0; i < type.length; i++) {
  typeBuffer[i] = type.charCodeAt(i);
 }
 const lengthBuffer = new Uint32Array(1);
 const valueBuffer = new TextEncoder().encode(inputString);
 lengthBuffer[0] = valueBuffer.length;
 const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);
 tlvBuffer.set(typeBuffer, 0);
 tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
 tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
 tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
 tlvBuffer[7] = lengthBuffer[0] & 0xff;
 tlvBuffer.set(valueBuffer, 8);
 return tlvBuffer.buffer;
};
/**
 * @brief TLV 数据格式转换成字符串
 * @note TLV 数据格式
 * | magic number | length(big-endian) | value |
 * @param {ArrayBufferLike} tlvBuffer
 * @returns 
 */
function tlv2String(tlvBuffer: ArrayBufferLike) {
 const typeBuffer = new Uint8Array(tlvBuffer, 0, 4);
 const lengthBuffer = new Uint8Array(tlvBuffer, 4, 4);
 const valueBuffer = new Uint8Array(tlvBuffer, 8);
 let type = '';
 for (let i = 0; i < typeBuffer.length; i++) {
  type += String.fromCharCode(typeBuffer[i]);
 }
 const length =
  (lengthBuffer[0] << 24) | (lengthBuffer[1] << 16) | (lengthBuffer[2] << 8) | lengthBuffer[3];
 const value = new TextDecoder().decode(valueBuffer.subarray(0, length));
 return { type, value };
};
/**
 * @brief 通过 onRoomBinaryMessageReceived 接收 toolcall
 *    通过 sendUserBinaryMessage 发送 response
 */
function handleRoomBinaryMessageReceived(
 event: {
  userId: string;
  message: ArrayBuffer;
 },
) {
 const { message } = event;
 const { type, value } = tlv2String(message);
 const data = JSON.parse(value);
 const { tool_calls } = data || {};
 // 处理逻辑
 console.log(type);
 
 if (tool_calls?.length) {
  const name: string = tool_calls?.[0]?.function?.name;
  const map: Record<string, string> = {
   getcurrentweather: '今天下雪， 最低气温零下10度',
   musicplayer: '查询到李四的歌曲， 名称是千里之内',
   sendmessage: '发送成功',
  };
  this.engine.sendUserBinaryMessage(
   'Your AI Bot Name',
   stringToTLV(
    JSON.stringify({
     ToolCallID: tool_calls?.[0]?.id,
     Content: map[name.toLocaleLowerCase().replaceAll('_', '')],
    })
   )
  );
 }
};
/**
 * @brief 监听房间内二进制消息
 */
this.engine.on(VERTC.events.onRoomBinaryMessageReceived, handleRoomBinaryMessageReceived);

TypeScript

```

```
public void SendFunctionResponse(String ToolCallID, String Content) {
  JSONObject json = new JSONObject();
  try {
    json.put("ToolCallID", ToolCallID);
    json.put("Content", Content);
  } catch (JSONException e) {
    throw new RuntimeException(e);
  }
  String jsonString = json.toString();
  System.out.println(jsonString);
  app.rtcSdkWrap.sendUserBinaryMessage("RobotMan_", stringToTLV(jsonString));
}
public byte[] stringToTLV(String content) {
  String func_type = "func";
  byte[] prefixBytes = func_type.getBytes(StandardCharsets.UTF_8);
  byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
  int contentLength = contentBytes.length;
  ByteBuffer buffer = ByteBuffer.allocate(prefixBytes.length + 4 + contentLength);
  buffer.order(ByteOrder.BIG_ENDIAN);
  buffer.put(prefixBytes);
  buffer.putInt(contentLength);
  buffer.put(contentBytes);
  return buffer.array();
}

Java

```

### 步骤 4：获取 Function Calling 最终答复
当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## FAQ
Q1：配置了 Function Calling 后，通过 updateVoiceChat/sendUserBinaryMessage 返回给智能体的工具调用结果信息可以不读出来吗? A1：不可以。
Q2：配置了 Function Calling 后，相关配置如何更新？ A1：需要先调用 StopVoiceChat 接口停止智能体任务，然后再调用 StartVoiceChat 接口重新配置 Function Calling 功能。




## 什么是实时音视频--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/66812


  * 文档首页
/实时音视频/产品简介/什么是实时音视频

什么是实时音视频
最近更新时间：2025.04.29 20:34:57首次发布时间：2021.02.23 10:42:33

文档反馈
火山引擎实时音视频（Volcengine Real Time Communication，veRTC）提供全球范围内高可靠、高并发、低延时的实时音视频通信能力，实现多种类型的实时交流和互动。
通过在应用中接入 RTC SDK，并调用 RTC 提供的 API，可以快速构建音视频聊天、在线教育、直播连麦、视频会议、游戏语音、云游戏、云端渲染等丰富场景功能，覆盖互娱、教育、游戏、会议等各种行业需求。
## 为什么选择 veRTC
## 产品架构
火山引擎实时音视频提供优质、高效的音视频解决方案，主要从以下两方面来为应用实现实时音视频通信：
同时，火山引擎实时音视频还能与、、视频直播、视频点播等云产品之间实现联动。
## 平台支持
平台| 开发环境要求| 支持架构  
---|---|---  
iOS| 
  * iOS 11.0+
  * Xcode 14.1+

| 
* arm64（真机）
* x86_64 (模拟器)  
Android| 
  * Android 4.4+（SDK API Level 19）
  * Android Studio 3.5+

| 
* arm64-v8a（真机）
* armeabi-v7a（真机）
* x86（模拟器）
* x86_64 (模拟器)  
macOS| 
  * macOS 10.13+ 的 Mac 真机
  * Xcode 9.0+

| 
* arm64
* x86_64  
Windows| 
  * Windows 7+
  * Visual Studio 2017
  * .Net Framework 4.0 及以上

| 
* x86
* x86_64  
Linux| 
  * glibc 2.27+

| 
* arm64
* armhf
* x86_64  
Web| 参看 | ---  
Electron| 
  * 支持 Electron 6.1.7 以上版本，推荐使用 11.3.0 版本
  * Windows 7+、macOS 10.13+

| ---  
抖音小程序| 抖音 21.5.0+| ---  
抖音小游戏| 抖音 22.5.0+| ---  
Flutter| | 暂不支持 Android、iOS 模拟器  
Unity| | 
  * Android: armeabi-v7a, arm64-v8a, x86
  * iOS
  * Windows: x86_64

  
微信小程序| 
  * 微信 App iOS 最低版本要求：6.5.21
  * 微信 App Android 最低版本要求：6.5.19
  * 小程序基础库最低版本要求：1.9.6

| ---  
## 快速体验和集成
  * 体验线上 Demo：通过 veRTC 的 Demo，您可以快速体验实时音视频通信的核心功能。具体操作，请参见。
  * 跑通示例代码：获取示例工程，快速构建应用，体验实时音视频功能。具体操作，请参见。
  * 集成 SDK 实现音视频通话：下载RTC SDK，并按照开发指南集成到您的应用中实现基本音视频通话。具体操作，请参见。






## 开通服务--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/69865


  * 文档首页
/实时音视频/快速入门/开通服务

开通服务
最近更新时间：2025.05.07 12:09:47首次发布时间：2021.07.21 14:28:57

文档反馈
要为你的应用接入实时音视频服务，你必须先开通实时音视频服务。
要开通实时音视频服务，你必须遵循以下步骤：
## 步骤1：登录火山引擎控制台
登录 。
  * 如果你是首次登录，请先注册账号，参看。
  * 如果你已拥有火山引擎账号，请先登录，参看。


## 步骤2：实名认证
登录成功后，你必须先进行实名认证，参看：
## 步骤3：申请开通 RTC 服务
你必须在总览页选择 ，点击 领取礼包并开通 。
## 步骤4：创建 RTC 应用，获取 AppId
  1. 【可选】在多人团队协作中，你可能希望通过子账户（IAM）功能细化权限管理，参看创建和授权子账号应用管理权限。
  2. 登录 后，你可以在 「应用管理」 中管理应用，包括修改应用名称，查看 AppID、 AppKey、创建时间等。 其中，defaultAppName 为系统默认创建。


> 说明
>   1. AppId 是每个应用的唯一标识符，在调用 volcEngineRTC SDK 的 API 接口实现功能，如创建对象时，你必须填入你获取到的 AppId 。
>   2. AppKey 是每个应用对应的密钥，请妥善保管。AppKey 用于生成 Token 鉴权，一旦你的密钥泄露，可能会被盗用流量。
> 

  1. 如果你需要创建新的应用，可以在 「应用管理」 中点击创建应用 , 提交更多创建应用的申请。






## API 详情--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1390575


  * 文档首页
/实时音视频/客户端 API 参考/React Native 3.58/API 详情

API 详情
最近更新时间：2025.06.24 17:01:22首次发布时间：2025.02.26 11:44:49

文档反馈
## RTCManager 
类型：class
RTC 核心类, 负责管理创建的引擎实例和房间实例
### engine 
类型：IEngine | undefined
RTC 引擎实例
### room 
类型：IRoom | undefined
RTC 房间实例
### audioEffectPlayer 
类型：I_AudioEffectPlayer | undefined
RTC 音效播放器实例
### mediaPlayer 
类型：I_MediaPlayer | undefined
RTC 音乐播放器实例
### vodPlayer 
类型：unknown
点播播放器实例
### getSDKVersion() 
获取当前 ReactNative SDK 版本
类型
```
() => string | undefined

ts

```

### getBasicSDKVersion() 
获取 ReactNative 依赖的 Android/iOS SDK 版本
类型
```
() => string

ts

```

### createRTCEngine() 
创建引擎对象 如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 如果当前进程中已创建了引擎实例，再次调用此方法时，会返回已创建的引擎实例。
类型
```
(options: ICreateRTCEngineOptions) => Promise<IEngine>

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
options| ICreateRTCEngineOptions| 是| 无| 引擎初始化参数, 详情参考 ICreateRTCEngineOptions   
返回值
  * engine: 实例
  * null: ICreateRTCEngineOptions 无效。详见 ，so 文件加载失败。


### destroyRTCEngine() 
销毁所创建的引擎实例，并释放所有相关资源。
类型
```
() => void

ts

```

### setLogConfig() 
自定义 SDK 日志配置，包括日志输出等级、存储路径、日志文件总大小上限、日志文件名前缀。
类型
```
(config: ILogConfigs) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| ILogConfigs| 是| 无| SDK 日志配置。   
返回值
number
  * 0：成功。
  * –1：失败，本方法必须在创建引擎前调用。
  * –2：失败，参数填写错误。


### setVodPlayer() 
注入点播播放器实例
类型
```
(vodPlayer: unknown) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
vodPlayer| unknown| 是| 无| -  
## IEngine 
基于 Android/iOS SDK 的 类封装的引擎类。
类型
```
Omit<
 RTCVideo,
 | 'setLocalVideoCanvas'
 | 'createRTCRoom'
 | 'setCellularEnhancement'
 | 'enableAudioPropertiesReport'
 | 'setRemoteAudioPlaybackVolume'
 | 'getAudioEffectPlayer'
 | 'getMediaPlayer'
 | 'setScreenVideoEncoderConfig'
 | 'startScreenCapture'
 | 'setRemoteVideoCanvas'
 | 'requestRemoteVideoKeyFrame'
 | 'sendStreamSyncInfo'
> & {
 setLocalVideoCanvas: (
  streamIndex: StreamIndex,
  videoCanvas: IVideoCanvas,
 ) => number | undefined;
 setRemoteVideoCanvas: (
  remoteInfo: IRemoteInfo,
  videoCanvas: IVideoCanvas,
 ) => ReturnStatus;
 createRTCRoom: (roomId: string) => IRoom;
 getAudioEffectPlayer: () => I_AudioEffectPlayer;
 getMediaPlayer: (playerId: number) => I_MediaPlayer;
 setCellularEnhancement: (config: IEnhanceMentConfig) => number;
 enableAudioPropertiesReport: (config: IAudioPropertiesConfig) => number;
 setRemoteAudioPlaybackVolume: (
  remoteStreamInfo: IRemoteStreamInfo,
  volume: number,
 ) => number;
 setScreenVideoEncoderConfig: (config: IScreenSolution) => number;
 startScreenCapture: (
  type: ScreenMediaType,
  bundleId?: string,
 ) => Promise<number>;
 setRuntimeParameters: (params: Record<string, string>) => number;
 feedback(options: ProblemFeedbackOption[], info: IFeedbackProblemInfo): number;
 requestRemoteVideoKeyFrame(remoteInfo: IRemoteInfo): number;
 sendStreamSyncInfo(data: ArrayBuffer, info: IStreamSyncInfoConfig): number;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
setLocalVideoCanvas| (streamIndex: StreamIndex, videoCanvas: IVideoCanvas) => number | undefined| 设置本地视频渲染时使用的视图，并设置渲染模式。  
setRemoteVideoCanvas| (remoteInfo: IRemoteInfo, videoCanvas: IVideoCanvas) => ReturnStatus| 渲染来自指定远端用户的视频流时，设置使用的视图和渲染模式。要解除绑定，将 videoCanvas 设置为空。  
createRTCRoom| (roomId: string) => IRoom| 创建房间实例。 调用此方法仅返回一个房间实例，你仍需调用 joinRoom 才能真正地创建/加入房间。  
getAudioEffectPlayer| () => I_AudioEffectPlayer| 创建音效播放器实例。  
getMediaPlayer| (playerId: number) => I_MediaPlayer| 创建音乐播放器实例。  
setCellularEnhancement| (config: IEnhanceMentConfig) => number| 启用蜂窝网络辅助增强，改善通话质量。  
enableAudioPropertiesReport| (config: IAudioPropertiesConfig) => number| 启用音频信息提示。开启提示后，你可以收到 onLocalAudioPropertiesReportonRemoteAudioPropertiesReport 和 onActiveSpeaker。  
setRemoteAudioPlaybackVolume| (remoteStreamInfo: IRemoteStreamInfo, volume: number) => number| 调节来自指定远端用户的音频播放音量。  
setScreenVideoEncoderConfig| (config: IScreenSolution) => number| 为发布的屏幕共享视频流设置期望的编码参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。  
startScreenCapture| (type: ScreenMediaType, bundleId?: string) => Promise<number>| 使用 RTC SDK 内部采集模块开始采集屏幕音频流和（或）视频流。  
setRuntimeParameters| (params: Record<string, string>) => number| 设置运行时的参数  
## IRoom 
基于 Android/iOS SDK 的 类封装的房间类。
类型
```
Omit<RTCRoom, 'joinRoom'> & {
 joinRoom: (params: IJoinRoomProps) => number;
 setRemoteVideoConfig: (
  userId: string,
  remoteVideoConfig: IRemoteVideoConfig,
 ) => number;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
joinRoom| (params: IJoinRoomProps) => number| 加入房间。调用 createRTCRoom 创建房间后，调用此方法加入房间，同房间内其他用户进行音视频通话。  
setRemoteVideoConfig| (userId: string, remoteVideoConfig: IRemoteVideoConfig) => number| 设置期望订阅的远端视频流的参数。  
## I_AudioEffectPlayer 
基于 Android/iOS SDK 的 类封装的音效播放器类。
类型
```
Omit<AudioEffectPlayer, 'start'> & {
 start: (
  effectId: number,
  path: string,
  config: IAudioEffectPlayerConfig,
 ) => I_AudioEffectPlayer;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
start| (effectId: number, path: string, config: IAudioEffectPlayerConfig) => I_AudioEffectPlayer| 开始播放音效文件。可以通过传入不同的 ID 和 filepath 多次调用本方法，以实现同时播放多个音效文件，实现音效叠加。  
## I_MediaPlayer 
基于 Android/iOS SDK 的 类封装的音乐播放器类
类型
```
Omit<IMediaPlayer, 'open'> & {
 open: (filePath: string, config: IMediaPlayerConfig) => number;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
open| (filePath: string, config: IMediaPlayerConfig) => number| 打开音乐文件。一个播放器实例仅能够同时打开一个音乐文件。如果需要同时打开多个音乐文件，请创建多个音乐播放器实例。要播放 PCM 格式的音频数据，参看 openWithCustomSource。openWithCustomSource 和此 API 互斥。  
## ISpatialAudio 
类型：class
空间音频接口实例
### enableSpatialAudio() 
开启/关闭空间音频功能。
注意
该方法仅开启空间音频功能，你须调用 updatePosition 设置自身位置坐标后方可收听空间音频效果。空间音频相关 API 和调用时序详见。
类型
```
(enable: boolean) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启空间音频功能： true：开启 false：关闭（默认）   
### disableRemoteOrientation() 
关闭本地用户朝向对本地用户发声效果的影响。 调用此接口后，房间内的其他用户收听本地发声时，声源都在收听者正面。
注意
  * 调用本接口关闭朝向功能后，在当前的空间音频实例的生命周期内无法再次开启。
  * 调用此接口不影响本地用户收听朝向的音频效果。要改变本地用户收听朝向，参看 updateSelfPosition 和 updateRemotePosition 。


类型
```
() => void

ts

```

### updateSelfPosition() 
设置本地用户在自建空间直角坐标系中的收听坐标和收听朝向，以实现本地用户预期的空间音频收听效果。
注意
  * 该方法需在进房后调用。
  * 调用该接口更新坐标前，你需调用 enableSpatialAudio 开启空间音频功能。空间音频相关 API 和调用时序详见。
  * 调用此接口在本地进行的设定对其他用户的空间音频收听效果不会产生任何影响。


类型
```
(positionInfo: PositionInfo) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
positionInfo| PositionInfo| 是| 无| 空间音频位置信息。参看 PositionInfo。   
返回值
number
  * 0：成功。
  * <0：失败。
  * -2: 失败，原因是校验本地用户的三维朝向信息时，三个向量没有两两垂直。


### updateRemotePosition() 
设置房间内某一远端用户在本地用户自建的空间音频坐标系中的发声位置和发声朝向，以实现本地用户预期的空间音频收听效果。
注意
  * 该方法需在创建房间后调用。
  * 调用此接口在本地进行的设定对其他用户的空间音频收听效果不会产生任何影响。


类型
```
(uid: string, positionInfo: PositionInfo) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 用户 ID   
positionInfo| PositionInfo| 是| 无| 远端用户的空间音频位置信息。参看 PositionInfo。   
返回值
number
  * 0：成功。
  * <0：失败。
  * -2: 失败，原因是校验远端用户的三维朝向信息时，三个向量没有两两垂直。


### removeRemotePosition() 
移除调用 updateRemotePosition 为某一远端用户设置的空间音频效果。
类型
```
(uid: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 远端用户 ID。   
返回值
number
  * 0：成功。
  * <0：失败。


### removeAllRemotePosition() 
移除调用 updateRemotePosition 为所有远端用户设置的空间音频效果。
类型
```
() => number

ts

```

返回值
number
  * 0：成功。
  * <0：失败。


## IVideoDeviceManager 
类型：class
主要用于枚举、设置视频采集设备
### android_enumerateVideoCaptureDevices() 
获取当前系统内视频采集设备列表。
类型
```
() => $p_a.List<$p_a.VideoDeviceInfo>

ts

```

返回值
$p_a.List<$p_a.VideoDeviceInfo> 包含系统中所有视频采集设备的列表，参看 VideoDeviceInfo。
### setVideoCaptureDevice() 
设置当前视频采集设备
类型
```
(deviceId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceId| string| 是| 无| 视频设备 ID，可以通过 enumerateVideoCaptureDevices 获取   
返回值
number
  * 0：方法调用成功
  * !0：方法调用失败


### ios_getVideoCaptureDevice() 
获取当前 SDK 正在使用的视频采集设备信息
类型
```
(deviceID: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 视频设备 ID   
返回值
number
  * 0：方法调用成功
  * !0：方法调用失败


## IKTVPlayer 
类型：class
KTV 播放器接口。
### ios_delegate 
类型：$p_i.ByteRTCKTVPlayerDelegate
### android_setPlayerEventHandler() 
设置 KTV 播放器进度及状态回调接口。
类型
```
(playerEventHandler: $p_a.IKTVPlayerEventHandler) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerEventHandler| $p_a.IKTVPlayerEventHandler| 是| 无| KTV 播放器回调类，参看 IKTVPlayerEventHandler。   
### playMusic() 
播放歌曲。
注意
类型
```
(musicId: string, trackType: AudioTrackType, playType: AudioPlayType) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。若同一 musicId 的歌曲正在播放，再次调用接口会从开始位置重新播放。若 musicId 对应的音频文件不存在会触发报错。   
trackType| AudioTrackType| 是| 无| 原唱伴唱类型，参看 AudioTrackType。   
playType| AudioPlayType| 是| 无| 音乐播放类型。参看 AudioPlayType。   
### pauseMusic() 
暂停播放歌曲。
注意
  * 调用接口后，你会收到 onPlayStateChanged 回调歌曲播放状态。
  * 若音乐 ID 错误，会触发 onPlayStateChanged 回调，errorCode 为 –3023，playState 为 4。
  * 若未进房，会触发 onPlayStateChanged 回调，errorCode 为 –3022，playState 为 4。


类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### resumeMusic() 
继续播放歌曲。
注意
  * 调用接口后，你会收到 onPlayStateChanged 回调歌曲播放状态。
  * 若音乐 ID 错误，会触发 onPlayStateChanged 回调，errorCode 为 –3023，playState 为 4。
  * 若未进房，会触发 onPlayStateChanged 回调，errorCode 为 –3022，playState 为 4。


类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### stopMusic() 
停止播放歌曲。
注意
  * 调用接口后，你会收到 onPlayStateChanged 回调歌曲播放状态。
  * 若音乐 ID 错误，会触发 onPlayStateChanged 回调，errorCode 为 –3023，playState 为 4。
  * 若未进房，会触发 onPlayStateChanged 回调，errorCode 为 –3022，playState 为 4。


类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### seekMusic() 
设置音乐文件的起始播放位置。
注意
类型
```
(musicId: string, position: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
position| number| 是| 无| 音乐起始位置，单位为毫秒，取值小于音乐文件总时长。   
### setMusicVolume() 
设置歌曲播放音量，只能在开始播放后进行设置。
注意
类型
```
(musicId: string, volume: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
volume| number| 是| 无| 歌曲播放音量，调节范围：[0,400]。 0：静音。 100：原始音量。 - 400: 原始音量的 4 倍(自带溢出保护)。   
### switchAudioTrackType() 
切换歌曲原唱伴唱。
注意
调用本接口时音乐必须处于播放中状态。
类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### setMusicPitch() 
对播放中的音乐设置升降调信息。
注意
类型
```
(musicId: string, pitch: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
pitch| number| 是| 无| 相对于音乐文件原始音调的升高/降低值，取值范围 [-12，12]，默认值为 0，即不做调整。取值范围内每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调，设置的绝对值越大表示音调升高或降低越多。   
## IVideoEffect 
类型：class
高级视频特效，参看。
### initCVResource() 
检查视频特效证书，设置算法模型路径，并初始化特效模块。
类型
```
(licenseFile: string, algoModelDir: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
licenseFile| string| 是| 无| 证书文件的绝对路径，用于鉴权。   
algoModelDir| string| 是| 无| 算法模型绝对路径，即存放特效 SDK 所有算法模型的目录。   
返回值
number
### enableVideoEffect() 
开启高级美颜、滤镜等视频特效。
注意
  * 调用本方法前，必须先调用 initCVResource 进行初始化。
  * 调用该方法后，特效不直接生效，你还需调用 setEffectNodes 设置视频特效素材包或调用 setColorFilter 设置滤镜。
  * 调用 disableVideoEffect 关闭视频特效。


类型
```
() => number

ts

```

返回值
number
### disableVideoEffect() 
关闭视频特效。
注意
调用 enableVideoEffect 开启视频特效。
类型
```
() => number

ts

```

返回值
number
### setEffectNodes() 
设置视频特效素材包。
注意
调用本方法前，必须先调用 enableVideoEffect。
类型
```
(effectNodes: Array<string>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectNodes| string[]| 是| 无| 特效素材包绝对路径数组。要取消当前视频特效，将此参数设置为 null。   
返回值
number
### updateEffectNode() 
设置特效强度。
类型
```
(effectNode: string, key: string, value: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectNode| string| 是| 无| 特效素材包绝对路径，参考素材包结构说明。   
key| string| 是| 无| 需要设置的素材 key 名称，参考素材 key 对应说明。   
value| number| 是| 无| 特效强度值，取值范围 [0,1]，超出范围时设置无效。   
返回值
number
### setColorFilter() 
设置颜色滤镜。
注意
调用 setColorFilterIntensity 设置已启用颜色滤镜的强度。设置强度为 0 时即关闭颜色滤镜。
类型
```
(filterRes: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
filterRes| string| 是| 无| 滤镜资源包绝对路径。   
返回值
number
### setColorFilterIntensity() 
设置已启用颜色滤镜的强度。
类型
```
(intensity: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
intensity| number| 是| 无| 滤镜强度。取值范围 [0,1]，超出范围时设置无效。当设置滤镜强度为 0 时即关闭颜色滤镜。   
返回值
number
### enableVirtualBackground() 
将摄像头采集画面中的人像背景替换为指定图片或纯色背景。
注意
  * 调用本方法前，必须先调用 initCVResource 进行初始化。
  * 调用 disableVirtualBackground 关闭虚拟背景。


类型
```
(backgroundStickerRes: string, source: VirtualBackgroundSource) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
backgroundStickerRes| string| 是| 无| 背景贴纸特效素材绝对路径。   
source| VirtualBackgroundSource| 是| 无| 背景贴纸对象，参看 VirtualBackgroundSource。   
返回值
number
### disableVirtualBackground() 
关闭虚拟背景。
注意
调用 enableVirtualBackground 开启虚拟背景后，可以调用此接口关闭虚拟背景。
类型
```
() => number

ts

```

返回值
number
### enableFaceDetection() 
开启人脸识别功能，并设置人脸检测结果回调观察者。 此观察者后，你会周期性收到 onFaceDetectResult 回调。
类型
```
(observer: IFaceDetectionObserver, intervalMs: number, faceModelPath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IFaceDetectionObserver| 是| 无| 人脸检测结果回调观察者，参看 IFaceDetectionObserver。   
intervalMs| number| 是| 无| 两次回调之间的最小时间间隔，必须大于 0，单位为毫秒。实际收到回调的时间间隔大于 interval_ms，小于 interval_ms+视频采集帧间隔。   
faceModelPath| string| 是| 无| 人脸检测算法模型文件路径，一般为 ttfacemodel 文件夹中 tt_face_vXXX.model 文件的绝对路径。   
返回值
number
### disableFaceDetection() 
关闭人脸识别功能。
类型
```
() => number

ts

```

返回值
number
## RTCVideo 
类型：class
RTCVideo Class
### new RTCVideo()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### setRtcVideoEventHandler() 
设置引擎事件回调的接收类，必须继承自 IRTCVideoEventHandler 。
注意
请勿直接在回调函数的实现中直接进行操作。
  * 调用方需要自行实现一个继承自 IRTCVideoEventHandler 的类，并重载其中需要关注的事件。
  * 该回调为异步回调
  * 所有的事件回调均会在独立的回调线程内触发，请接收回调事件时注意所有与线程运行环境有关的操作，如需要在 UI 线程内执行的操作等，


类型
```
(engineEventHandler: RTCVideoEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
engineEventHandler| RTCVideoEventHandler| 是| 无| 事件处理器接口类，详见 IRTCVideoEventHandler 。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### android_getAudioDeviceManager() 
获取音频设备管理接口
类型
```
() => $p_a.IRTCAudioDeviceManager

ts

```

返回值
$p_a.IRTCAudioDeviceManager 音频设备管理接口 IRTCAudioDeviceManager
### startVideoCapture() 
立即开启内部视频采集。默认为关闭状态。 内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 调用该方法后，本地用户会收到 onVideoDeviceStateChanged 的回调。 本地用户在可见状态下调用该方法后，房间中的其他用户会收到 onUserStartVideoCapture 的回调。
注意
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopVideoCapture() 
立即关闭内部视频采集。默认为关闭状态。 内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 调用该方法，本地用户会收到 onVideoDeviceStateChanged 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStopVideoCapture 的回调。
注意
  * 调用 startVideoCapture 可以开启内部视频采集。
  * 如果不调用本方法停止内部视频采集，则只有当销毁引擎实例时，内部视频采集才会停止。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startAudioCapture() 
开启内部音频采集。默认为关闭状态。 内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 调用该方法开启后，本地用户会收到 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStartAudioCapture 的回调。
注意
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopAudioCapture() 
立即关闭内部音频采集。默认为关闭状态。 内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 调用该方法，本地用户会收到 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStopAudioCapture 的回调。
注意
  * 调用 startAudioCapture 可以开启内部音频采集设备。
  * 如果不调用本方法停止内部音频采集，则只有当销毁引擎实例时，内部音频采集才会停止。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAudioScenario() 
设置音频场景类型。 你可以根据你的应用所在场景，选择合适的音频场景类型。 选择音频场景后，SDK 会自动根据客户端音频采集播放设备和状态，适用通话音量/媒体音量，并同步变更对应音频相关的算法配置和采集配置。
注意
  * 建议在加入房间和调用音频相关接口之前，调用此接口设置音频场景类型。如果在此之后调用此接口，可能会引入音频卡顿。
  * 通话音量更适合通话、会议等对信息准确度更高的场景。通话音量会激活系统硬件信号处理，使通话声音更清晰。同时，音量无法降低到 0。
  * 媒体音量更适合娱乐场景，因其声音的表现力会更强。媒体音量下，最低音量可以为 0。


类型
```
(audioScenario: AudioScenarioType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioScenario| AudioScenarioType| 是| 无| 音频场景类型，参看 AudioScenarioType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAudioProfile() 
设置音质档位。 当所选的 中的音频参数无法满足你的场景需求时，调用本接口切换的音质档位。
注意
  * 该方法在进房前后均可调用；
  * 支持通话过程中动态切换音质档位。


类型
```
(audioProfile: AudioProfileType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioProfile| AudioProfileType| 是| 无| 音质档位，参看 AudioProfileType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAnsMode() 
支持根据业务场景，设置通话中的音频降噪模式。
注意
类型
```
(ansMode: AnsMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
ansMode| AnsMode| 是| 无| 降噪模式。具体参见 AnsMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVoiceChangerType() 
设置变声特效类型
注意
类型
```
(voiceChanger: VoiceChangerType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
voiceChanger| VoiceChangerType| 是| 无| 变声特效类型，参看 VoiceChangerType   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### setVoiceReverbType() 
设置混响特效类型
注意
类型
```
(voiceReverb: VoiceReverbType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
voiceReverb| VoiceReverbType| 是| 无| 混响特效类型，参看 VoiceReverbType   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### setLocalVoiceEqualization() 
设置本地采集语音的均衡效果。包含内部采集和外部采集，但不包含混音音频文件。
注意
根据奈奎斯特采样率，音频采样率必须大于等于设置的中心频率的两倍，否则，设置不生效。
类型
```
(voiceEqualizationConfig: VoiceEqualizationConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
voiceEqualizationConfig| VoiceEqualizationConfig| 是| 无| 语音均衡效果，参看 VoiceEqualizationConfig   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### setLocalVoiceReverbParam() 
设置本地采集音频的混响效果。包含内部采集和外部采集，但不包含混音音频文件。
注意
调用 enableLocalVoiceReverb 开启混响效果。
类型
```
(config: VoiceReverbConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| VoiceReverbConfig| 是| 无| 混响效果，参看 VoiceReverbConfig   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### enableLocalVoiceReverb() 
开启本地音效混响效果
注意
调用 setLocalVoiceReverbParam 设置混响效果。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### setLocalVideoCanvas() 
设置本地视频渲染时使用的视图，并设置渲染模式。
注意
  * 你应在加入房间前，绑定本地视图。退出房间后，此设置仍然有效。
  * 如果需要解除绑定，你可以调用本方法传入空视图。


类型
```
(streamIndex: StreamIndex, videoCanvas: VideoCanvas) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 视频流属性, 参看 StreamIndex   
videoCanvas| VideoCanvas| 是| 无| 视图信息和渲染模式, 参看 VideoCanvas   
返回值
number
  * 0: 成功
  * -1: videoCanvas 为空


### updateLocalVideoCanvas() 
修改本地视频渲染模式和背景色。
注意
你可以在本地视频渲染过程中，调用此接口。调用结果会实时生效。
类型
```
(streamIndex: StreamIndex, renderMode: RenderMode, backgroundColor: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 视频流属性。参看 StreamIndex   
renderMode| RenderMode| 是| 无| 渲染模式。参看 VideoCanvas.renderMode   
backgroundColor| number| 是| 无| 背景颜色。参看 VideoCanvas.backgroundColor   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### updateRemoteStreamVideoCanvas() 
使用 SDK 内部渲染时，修改远端视频帧的渲染设置，包括渲染模式、背景颜色和旋转角度。
注意
  * 调用 setRemoteVideoCanvas 设置了远端视频渲染模式后，你可以调用此接口更新渲染模式、背景颜色、旋转角度的设置。
  * 该接口可以在远端视频渲染过程中调用，调用结果会实时生效。


类型
```
(streamKey: RemoteStreamKey, remoteVideoRenderConfig: RemoteVideoRenderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息。参看 RemoteStreamKey。   
remoteVideoRenderConfig| RemoteVideoRenderConfig| 是| 无| 视频帧渲染设置。具体参看 RemoteVideoRenderConfig。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoCanvas() 
渲染来自指定远端用户的视频流时，设置使用的视图和渲染模式。 要解除绑定，将 videoCanvas 设置为空。
注意
本地用户离开房间时，会解除调用此 API 建立的绑定关系；远端用户离开房间则不会影响。
类型
```
(streamKey: RemoteStreamKey, videoCanvas: VideoCanvas) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息。参看 RemoteStreamKey   
videoCanvas| VideoCanvas| 是| 无| 视图信息和渲染模式，参看 VideoCanvas。3.56 版本起支持通过 renderRotation 设置远端视频渲染的旋转角度。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoSuperResolution() 
设置远端视频超分模式。
注意
类型
```
(streamKey: RemoteStreamKey, mode: VideoSuperResolutionMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息，用于指定需要设置超分的视频流来源及属性，参看 RemoteStreamKey。   
mode| VideoSuperResolutionMode| 是| 无| 超分模式，参看 VideoSuperResolutionMode。   
返回值
number
### android_setVideoDenoiser() 
设置视频降噪模式。
注意
该功能仅 arm 架构支持。
类型
```
(mode: $p_a.VideoDenoiseMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mode| $p_a.VideoDenoiseMode| 是| 无| 视频降噪模式。参看 VideoDenoiseMode。   
返回值
number
  * 0: API 调用成功。 用户可以根据回调函数 onVideoDenoiseModeChanged 判断视频降噪是否开启。
  * < 0: API 调用失败。


### setLocalVideoMirrorType() 
为采集到的视频流开启镜像
注意
前置摄像头| 后置摄像头| 自定义采集视频源 | 桌面端摄像头  
---|---|---|---  
移动端| 本地预览镜像，编码传输不镜像|  本地预览不镜像，编码传输不镜像 |  本地预览不镜像，编码传输不镜像 | /  
桌面端| /| /|  本地预览不镜像，编码传输不镜像 |  本地预览镜像，编码传输不镜像   
类型
```
(mirrorType: MirrorType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mirrorType| MirrorType| 是| 无| 镜像类型，参看 MirrorType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoMirrorType() 
使用内部渲染时，为远端流开启镜像。
类型
```
(remoteStreamKey: RemoteStreamKey, mirrorType: RemoteMirrorType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端流信息，用于指定需要镜像的视频流来源及属性，参看 RemoteStreamKey。   
mirrorType| RemoteMirrorType| 是| 无| 远端流的镜像类型，参看 RemoteMirrorType。   
返回值
number
  * 0: 调用成功。
  * < 0: 调用失败，参看 获得更多错误说明。


### setVideoRotationMode() 
设置采集视频的旋转模式。默认以 App 方向为旋转参考系。 接收端渲染视频时，将按照和发送端相同的方式进行旋转。
注意
  * 旋转仅对内部视频采集生效，不适用于外部视频源和屏幕源。
  * 调用该接口时已开启视频采集，将立即生效；调用该接口时未开启视频采集，则将在采集开启后生效。
  * 更多信息请参考。


类型
```
(rotationMode: VideoRotationMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
rotationMode| VideoRotationMode| 是| 无| 视频旋转参考系为 App 方向或重力方向，参看 VideoRotationMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### switchCamera() 
切换视频内部采集时使用的前置/后置摄像头 调用此接口后，在本地会触发 onVideoDeviceStateChanged 回调。
注意
  * 默认使用前置摄像头。
  * 如果你正在使用相机进行视频采集，切换操作当即生效；如果相机未启动，后续开启内部采集时，会打开设定的摄像头。


类型
```
(cameraId: CameraId) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
cameraId| CameraId| 是| 无| 摄像头 ID，参看 CameraId   
返回值
number
  * 0：方法调用成功
  * < 0：方法调用失败


### setAudioRoute() 
强制切换当前的音频播放路由。默认使用 setDefaultAudioRoute 中设置的音频路由。 音频播放路由发生变化时，会收到 onAudioRouteChanged 回调。
注意
  * 对于绝大多数音频场景，使用 setDefaultAudioRoute 设置默认音频路由，并借助 RTC SDK 的音频路由自动切换逻辑即可完成。切换逻辑参见。你应仅在例外的场景下，使用此接口，比如在接入外接音频设备时，手动切换音频路由。
  * 本接口仅支持在 AUDIO_SCENARIO_COMMUNICATION 音频场景下使用。你可以通过调用 setAudioScenario 切换音频场景。
  * 不同音频场景中，音频路由和发布订阅状态到音量类型的映射关系详见 。


类型
```
(audioRoute: AudioRoute) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioRoute| AudioRoute| 是| 无| 音频播放路由，参见 AudioRoute。对 Android 设备，不同的音频设备连接状态下，可切换的音频设备情况不同。参见移动端设置音频路由。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getAudioRoute() 
获取当前使用的音频播放路由。
注意
要设置音频路由，详见 setAudioRoute。
类型
```
() => AudioRoute

ts

```

返回值
AudioRoute 详见 
### setDefaultAudioRoute() 
将默认的音频播放设备设置为听筒或扬声器。
注意
对于音频路由切换逻辑，参见。
类型
```
(route: AudioRoute) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
route| AudioRoute| 是| 无| 音频播放设备。参看 AudioRoute。仅支持听筒或扬声器。   
返回值
number
  * 0: 方法调用成功。
  * < 0: 方法调用失败。


### enableExternalSoundCard() 
启用匹配外置声卡的音频处理模式
注意
  * 当采用外接声卡进行音频采集时，建议开启此模式，以获得更好的音质。
  * 开启此模式时，仅支持耳机播放。如果需要使用扬声器或者外置音箱播放，关闭此模式。


类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| true: 开启 - false: 不开启(默认)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### muteAudioCapture() 
设置是否将录音信号静音（不改变本端硬件）。
注意
  * 该方法支持选择静音或取消静音麦克风采集，而不影响 SDK 音频流发布状态。
  * 静音后通过 setCaptureVolume 调整音量不会取消静音状态，音量状态会保存至取消静音。
  * 调用 startAudioCapture 开启音频采集前后，都可以使用此接口设置采集音量。


类型
```
(index: StreamIndex, mute: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 流索引，指定调节主流/屏幕流音量，参看 StreamIndex。   
mute| boolean| 是| 无| 是否静音音频采集。 True：静音（关闭麦克风） False：（默认）开启麦克风   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。具体失败原因参看 。


### setCaptureVolume() 
调节音频采集音量
注意
在开启音频采集前后，你都可以使用此接口设定采集音量。
类型
```
(index: StreamIndex, volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 流索引，指定调节主流还是调节屏幕流的音量，参看 StreamIndex   
volume| number| 是| 无| 采集的音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 只改变音频数据的音量信息，不涉及本端硬件的音量调节。 为保证更好的通话质量，建议将 volume 值设为 [0,100]。 0：静音 100：原始音量 - 400: 最大可为原始音量的 4 倍(自带溢出保护)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setPlaybackVolume() 
调节本地播放的所有远端用户混音后的音量。 播放音频前或播放音频时，你都可以使用此接口设定播放音量。
注意
假设某远端用户 A 始终在被调节的目标用户范围内，当该方法与 setRemoteAudioPlaybackVolume 或 setRemoteRoomAudioPlaybackVolume 共同使用时，本地收听用户 A 的音量将为两次设置的音量效果的叠加。
类型
```
(volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 音频播放音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 为保证更好的通话质量，建议将 volume 值设为 [0,100]。 0：静音 100：原始音量 - 400: 最大可为原始音量的 4 倍(自带溢出保护)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setLocalVoicePitch() 
开启本地语音变调功能，多用于 K 歌场景。 使用该方法，你可以对本地语音的音调进行升调或降调等调整。
类型
```
(pitch: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
pitch| number| 是| 无| 相对于语音原始音调的升高/降低值，取值范围[-12，12]，默认值为 0，即不做调整。取值范围内每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调，设置的绝对值越大表示音调升高或降低越多。超出取值范围则设置失败，并且会触发 onWarning 回调，提示 WarningCode 错误码为 WARNING_CODE_SET_SCREEN_STREAM_INVALID_VOICE_PITCH 设置语音音调不合法   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enableVocalInstrumentBalance() 
开启/关闭音量均衡功能。 开启音量均衡功能后，人声的响度会调整为 -16lufs。如果已调用 setAudioMixingLoudness 传入了混音音乐的原始响度，此音乐播放时，响度会调整为 -20lufs。
注意
该接口须在调用 startAudioMixing 开始播放音频文件之前调用。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启音量均衡功能： true: 是 false: 否   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enablePlaybackDucking() 
打开/关闭音量闪避功能，适用于在 RTC 通话过程中会同时播放短视频或音乐的场景，如“一起看”、“在线 KTV”等。 开启该功能后，当检测到远端人声时，本地的媒体播放音量会自动减弱，从而保证远端人声的清晰可辨；当远端人声消失时，本地媒体音量会恢复到闪避前的音量水平。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启音量闪避： true: 是 false: 否   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### login() 
登陆 RTS 服务器。 必须先登录，才能调用 sendUserMessageOutsideRoom 和 sendServerMessage 发送房间外点对点消息和向应用服务器发送消息 在调用本接口登录后，如果想要登出，需要调用 logout。
注意
本地用户调用此方法登录成功后，会收到 onLoginResult 回调通登录结果，远端用户不会收到通知。
类型
```
(token: string, uid: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
token| string| 是| 无| 用户登录必须携带的 Token，用于鉴权验证。测试时可使用控制台生成临时 Token，roomId 填任意值。正式上线需要使用密钥 SDK 在你的服务端生成并下发 Token，roomId 置空，Token 有效期及生成方式参看使用 Token 完成鉴权。   
uid| string| 是| 无| 用户 ID用户 ID 在 appid 的维度下是唯一的。   
返回值
number
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### logout() 
登出 RTS 服务器。 调用本接口登出后，无法调用房间外消息以及端到服务器消息相关的方法或收到相关回调。
注意
  * 调用本接口登出后，必须先调用 login 登录。
  * 本地用户调用此方法登出后，会收到 onLogout 回调通知结果，远端用户不会收到通知。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### updateLoginToken() 
更新用户用于登录的 Token Token 有一定的有效期，当 Token 过期时，需调用此方法更新登录的 Token 信息。 调用 login 方法登录时，如果使用了过期的 Token 将导致登录失败，并会收到 onLoginResult 回调通知，错误码为 LOGIN_ERROR_CODE_INVALID_TOKEN。此时需要重新获取 Token，并调用此方法更新 Token。
注意
  * 如果 Token 无效导致登录失败，则调用此方法更新 Token 后，SDK 会自动重新登录，而用户不需要自己调用 login 方法。
  * Token 过期时，如果已经成功登录，则不会受到影响。Token 过期的错误会在下一次使用过期 Token 登录时，或因本地网络状况不佳导致断网重新登录时通知给用户。


类型
```
(token: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
token| string| 是| 无| 更新的动态密钥   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setServerParams() 
设置应用服务器参数 客户端调用 sendServerMessage 或 sendServerBinaryMessage 发送消息给应用服务器之前，必须需要设置有效签名和应用服务器地址。
注意
  * 用户必须调用 login 登录后，才能调用本接口。
  * 调用本接口后，SDK 会使用 onServerParamsSetResult 返回相应结果。


类型
```
(signature: string, url: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
signature| string| 是| 无| 动态签名，应用服务器可使用该签名验证消息来源。签名需自行定义，可传入任意非空字符串，建议将 uid 等信息编码为签名。设置的签名会以 post 形式发送至通过本方法中 url 参数设置的应用服务器地址。   
url| string| 是| 无| 应用服务器的地址   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getPeerOnlineStatus() 
查询对端用户或本端用户的登录状态
注意
  * 必须调用 login 登录后，才能调用本接口。
  * 调用本接口后，SDK 会使用 onGetPeerOnlineStatus 回调通知查询结果。
  * 在发送房间外消息之前，用户可以通过本接口了解对端用户是否登录，从而决定是否发送消息。也可以通过本接口查询自己查看自己的登录状态。


类型
```
(peerUserID: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
peerUserID| string| 是| 无| 需要查询的用户 ID   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### sendUserMessageOutsideRoom() 
给房间外指定的用户发送文本消息（P2P）
注意
  * 在发送房间外文本消息前，必须先调用 login 完成登录。
  * 用户调用本接口发送文本信息后，会收到一次 onUserMessageSendResultOutsideRoom 回调，得知消息是否成功发送。
  * 若文本消息发送成功，则 uid 所指定的用户会通过 onUserMessageReceivedOutsideRoom 回调收到该消息。


类型
```
(uid: string, message: string, config: MessageConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息接收用户的 ID   
message| string| 是| 无| 发送的文本消息内容。消息不超过 64 KB。   
config| MessageConfig| 是| 无| 消息类型，参看 MessageConfig。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建
  * -2：发送失败，uid 为空


### sendUserBinaryMessageOutsideRoom() 
给房间外指定的用户发送二进制消息（P2P）
注意
  * 在发送房间外二进制消息前，必须先调用 login 完成登录。
  * 用户调用本接口发送二进制消息后，会收到一次 onUserMessageSendResultOutsideRoom 回调，通知消息是否发送成功；
  * 若二进制消息发送成功，则 uid 所指定的用户会通过 onUserBinaryMessageReceivedOutsideRoom 回调收到该条消息。


类型
```
(uid: string, buffer: ArrayBuffer, config: MessageConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息接收用户的 ID   
buffer| ArrayBuffer| 是| 无| 发送的二进制消息内容消息不超过 46KB。   
config| MessageConfig| 是| 无| 消息类型，参看 MessageConfig。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建
  * -2：发送失败，uid 为空


### sendServerMessage() 
客户端给应用服务器发送文本消息（P2Server）
注意
  * 在向应用服务器发送文本消息前，必须先调用 login 完成登录，随后调用 setServerParams 设置应用服务器。
  * 调用本接口后会收到一次 onServerMessageSendResult 回调，通知消息发送方是否发送成功。
  * 若文本消息发送成功，则之前调用 setServerParams 设置的应用服务器会收到该条消息。


类型
```
(message: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
message| string| 是| 无| 发送的文本消息内容消息不超过 64 KB。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建


### sendServerBinaryMessage() 
客户端给应用服务器发送二进制消息（P2Server）
注意
  * 在向应用服务器发送二进制消息前，必须先调用 login 完成登录，随后调用 setServerParams 设置应用服务器。
  * 调用本接口后，会收到一次 onServerMessageSendResult 回调，通知消息发送方发送成功或失败；
  * 若二进制消息发送成功，则之前调用 setServerParams 设置的应用服务器会收到该条消息。


类型
```
(buffer: ArrayBuffer) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
buffer| ArrayBuffer| 是| 无| 发送的二进制消息内容消息不超过 46KB。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建


### startNetworkDetection() 
开启通话前网络探测
注意
  * 成功调用本接口后，会在 3s 内收到一次 onNetworkDetectionResult 回调，此后每 2s 收到一次该回调，通知探测结果；
  * 若探测停止，则会收到一次 onNetworkDetectionStopped 通知探测停止。


类型
```
(isTestUplink: boolean, expectedUplinkBitrate: number, isTestDownlink: boolean, expectedDownlinkBitrate: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
isTestUplink| boolean| 是| 无| 是否探测上行带宽   
expectedUplinkBitrate| number| 是| 无| 期望上行带宽，单位：kbps范围为 {0, [100-10000]}，其中， 0 表示由 SDK 指定最高码率。   
isTestDownlink| boolean| 是| 无| 是否探测下行带宽   
expectedDownlinkBitrate| number| 是| 无| 期望下行带宽，单位：kbps范围为 {0, [100-10000]}，其中， 0 表示由 SDK 指定最高码率。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopNetworkDetection() 
停止通话前网络探测
注意
  * 调用本接口后，会收到一次 onNetworkDetectionStopped 回调通知探测停止。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enableAudioFrameCallback() 
设置并开启指定的音频数据帧回调
注意
开启音频回调并调用 registerAudioFrameObserver 后 会收到对应的音频回调。两者调用顺序没有限制且相互独立。
类型
```
(method: AudioFrameCallbackMethod, format: AudioFormat) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
method| AudioFrameCallbackMethod| 是| 无| 音频回调方法，参看 AudioFrameCallbackMethod。当音频回调方法设置为 AUDIO_FRAME_CALLBACK_RECORD(0)、AUDIO_FRAME_CALLBACK_PLAYBACK(1)、AUDIO_FRAME_CALLBACK_MIXED(2)时，你需要在参数 format 中指定准确的采样率和声道，暂不支持设置为自动。当音频回调方法设置为 AUDIO_FRAME_CALLBACK_REMOTE_USER(3)时，将 format 中的各个字段设置为默认值。   
format| AudioFormat| 是| 无| 音频参数格式，参看 AudioFormat。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### disableAudioFrameCallback() 
关闭音频回调
注意
该方法需要在调用 enableAudioFrameCallback 之后调用。
类型
```
(method: AudioFrameCallbackMethod) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
method| AudioFrameCallbackMethod| 是| 无| 音频回调方法，参看 AudioFrameCallbackMethod。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerAudioFrameObserver() 
注册音频数据回调观察者。
注意
注册音频数据回调观察者并调用 enableAudioFrameCallback 后 会收到对应的音频回调。对回调中收到的音频数据进行处理，不会影响 RTC 的编码发送或渲染。
类型
```
(observer: IAudioFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IAudioFrameObserver| 是| 无| 音频数据观察者，参看 IAudioFrameObserver。如果传入 null，则取消注册。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerAudioProcessor() 
注册自定义音频处理器。 注册完成后，你可以调用 enableAudioProcessor，对本地采集到的音频进行处理，RTC SDK 将对处理后的音频进行编码和发送。也可以对接收到的远端音频进行自定义处理，RTC SDK 将对处理后的音频进行渲染。
注意
  * 重复调用此接口时，仅最后一次调用生效。
  * 更多相关信息，详见。


类型
```
(processor: IAudioFrameProcessor) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
processor| IAudioFrameProcessor| 是| 无| 自定义音频处理器，详见 IAudioFrameProcessor。SDK 只持有 processor 的弱引用，你应保证其生命周期。需要取消注册时，设置此参数为 nullptr。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoDigitalZoomConfig() 
设置本地摄像头数码变焦参数，包括缩放倍数，移动步长。
注意
  * 每次调用本接口只能设置一种参数。如果缩放系数和移动步长都需要设置，分别调用本接口传入相应参数。
  * 由于移动步长的默认值为 0 ，在调用 setVideoDigitalZoomControl 或 startVideoDigitalZoomControl 进行数码变焦操作前，应先调用本接口。


类型
```
(type: ZoomConfigType, size: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| ZoomConfigType| 是| 无| 数码变焦参数类型，缩放系数或移动步长。参看 ZoomConfigType。必填。   
size| number| 是| 无| 缩放系数或移动步长，保留到小数点后三位。默认值为 0。必填。 选择不同 type 时有不同的取值范围。当计算后的结果超过缩放和移动边界时，取临界值。 ZOOM_FOCUS_OFFSET(0)：缩放系数增量，范围为 [0, 7]。例如，设置为 0.5 时，如果调用 setVideoDigitalZoomControl 选择 Zoom in，则缩放系数增加 0.5。缩放系数范围 [1，8]，默认为 1，原始大小。 - ZOOM_MOVE_OFFSET(1)：移动百分比，范围为 [0, 0.5]，默认为 0，不移动。如果调用 setVideoDigitalZoomControl 选择的是左右移动，则移动距离为 size x 原始视频宽度；如果选择的是上下移动，则移动距离为 size x 原始视频高度。例如，视频帧边长为 1080 px，设置为 0.5 时，实际移动距离为 0.5 x 1080 px = 540 px。  
返回值
number
  * 0：成功。
  * !0：失败。


### setVideoDigitalZoomControl() 
控制本地摄像头数码变焦，缩放或移动一次。设置对本地预览画面和发布到远端的视频都生效。
注意
类型
```
(direction: ZoomDirectionType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
direction| ZoomDirectionType| 是| 无| 数码变焦操作类型，参看 ZoomDirectionType。   
返回值
number
  * 0：成功。
  * !0：失败。


### startVideoDigitalZoomControl() 
开启本地摄像头持续数码变焦，缩放或移动。设置对本地预览画面和发布到远端的视频都生效。
注意
类型
```
(direction: ZoomDirectionType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
direction| ZoomDirectionType| 是| 无| 数码变焦操作类型，参看 ZoomDirectionType。   
返回值
number
  * 0：成功。
  * !0：失败。


### stopVideoDigitalZoomControl() 
停止本地摄像头持续数码变焦。
注意
关于开始数码变焦，参看 startVideoDigitalZoomControl。
类型
```
() => number

ts

```

返回值
number
  * 0：成功。
  * !0：失败。


### registerLocalEncodedVideoFrameObserver() 
注册本地视频帧监测器。 无论使用内部采集还是自定义采集，调用该方法后，SDK 每监测到一帧本地视频帧时，都会将视频帧信息通过 onLocalEncodedVideoFrame 回调给用户。
注意
该方法可在进房前后的任意时间调用，在进房前调用可保证尽可能早地监测视频帧并触发回调
类型
```
(observer: ILocalEncodedVideoFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| ILocalEncodedVideoFrameObserver| 是| 无| 本地频帧监测器，参看 ILocalEncodedVideoFrameObserver 。将参数设置为 null 则取消注册。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerRemoteEncodedVideoFrameObserver() 
注册远端编码后视频数据回调。 完成注册后，当 SDK 监测到远端编码后视频帧时，会触发 onRemoteEncodedVideoFrame 回调
注意
  * 更多自定义解码功能说明参看 。
  * 该方法适用于手动订阅，并且进房前后均可调用，建议在进房前调用。
  * 引擎销毁前需取消注册，调用该方法将参数设置为 "null" 即可。


类型
```
(observer: IRemoteEncodedVideoFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IRemoteEncodedVideoFrameObserver| 是| 无| 远端编码后视频数据监测器，参看 IRemoteEncodedVideoFrameObserver   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoSourceType() 
设置向 SDK 输入的视频源，包括屏幕流 默认使用内部采集。内部采集指：使用 RTC SDK 内置的视频采集机制进行视频采集。
注意
类型
```
(index: StreamIndex, type: VideoSourceType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 视频流的属性，参看 StreamIndex   
type| VideoSourceType| 是| 无| 视频输入源类型，参看 VideoSourceType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startPushMixedStreamToCDN() 
新增合流转推直播任务，并设置合流的图片、视频视图布局和音频属性。 同一个任务中转推多路直播流时，SDK 会先将多路流合成一路流，然后再进行转推。
注意
类型
```
(taskId: string, mixedConfig: MixedStreamConfig, observer: IMixedStreamObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 转推直播任务 ID，长度不超过 126 字节。你可以在同一房间内发起多个转推直播任务，并用不同的任务 ID 加以区分。当你需要发起多个转推直播任务时，应使用多个 ID；当你仅需发起一个转推直播任务时，建议使用空字符串。   
mixedConfig| MixedStreamConfig| 是| 无| 转推直播配置参数。详见 MixedStreamConfig。   
observer| IMixedStreamObserver| 是| 无| 无效参数。请忽略。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### updatePushMixedStreamToCDN() 
更新合流转推直播参数，会收到 onMixingEvent 回调。 使用 startPushMixedStreamToCDN 启用转推直播功能后，使用此方法更新功能配置参数。
类型
```
(taskId: string, mixedConfig: MixedStreamConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 转推直播任务 ID。指定想要更新参数设置的转推直播任务。   
mixedConfig| MixedStreamConfig| 是| 无| 转推直播配置参数，详见 MixedStreamConfig。除特殊说明外，均支持过程中更新。调用时，结构体中没有传入值的属性，会被更新为默认值。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### startPushSingleStreamToCDN() 
新增单流转推直播任务。
注意
类型
```
(taskId: string, param: PushSingleStreamParam, observer: IPushSingleStreamToCDNObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 任务 ID。你可以发起多个转推直播任务，并用不同的任务 ID 加以区分。当你需要发起多个转推直播任务时，应使用多个 ID；当你仅需发起一个转推直播任务时，建议使用空字符串。   
param| PushSingleStreamParam| 是| 无| 转推直播配置参数。详见 PushSingleStreamParam。   
observer| IPushSingleStreamToCDNObserver| 是| 无| 单流转推直播观察者。详见 IPushSingleStreamToCDNObserver。通过注册 observer 接收单流转推直播相关的回调。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopPushStreamToCDN() 
停止转推直播。 该方法可用于停止单流转推直播或停止合流转推直播，通过 taskId 区分需要停止的任务。
注意
  * 关于启动单流转推直播，参看 startPushSingleStreamToCDN。
  * 关于启动合流转推直播，参看 startPushMixedStreamToCDN。


类型
```
(taskId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 任务 ID。可以指定想要停止的单流转推直播或合流转推直播任务。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoCaptureConfig() 
设置 RTC SDK 内部采集时的视频采集参数。 如果你的项目使用了 SDK 内部采集模块，可以通过本接口指定视频采集参数包括模式、分辨率、帧率。
注意
  * 本接口在引擎创建后即可调用，建议在调用 startVideoCapture 前调用本接口。
  * 建议同一设备上的不同引擎使用相同的视频采集参数。
  * 如果调用本接口前使用内部模块开始视频采集，采集参数默认为 Auto 模式。


类型
```
(videoCaptureConfig: VideoCaptureConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
videoCaptureConfig| VideoCaptureConfig| 是| 无| 视频采集参数。参看: VideoCaptureConfig。   
返回值
number
  * 0: 成功
  * < 0: 失败


### enableSimulcastMode() 
该方法设置视频流发布端是否开启发布多路编码参数不同的视频流的模式。
注意
类型
```
(enabled: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enabled| boolean| 是| 无| 是否开启推送多路视频流模式： true：开启 false：关闭（默认）   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoEncoderConfig() 
视频发布端设置推送多路流时各路流的参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。
注意
类型
```
(channelSolutions: Array<VideoEncoderConfig>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
channelSolutions| VideoEncoderConfig[]| 是| 无| 要推送的多路视频流的参数，最多支持设置 3 路参数，超过 3 路时默认取前 3 路的值。当设置了多路参数时，分辨率和帧率必须是从大到小排列。需注意，所设置的分辨率是各路流的最大分辨率。参看 VideoEncoderConfig。   
返回值
number 方法调用结果：
  * 0：成功
  * !0：失败


### setScreenVideoEncoderConfig() 
为发布的屏幕共享视频流设置期望的编码参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。
注意
建议在采集视频前设置编码参数。若采集前未设置编码参数，则使用默认编码参数: 分辨率 1920px × 1080px，帧率 15fps。
类型
```
(screenSolution: ScreenVideoEncoderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
screenSolution| ScreenVideoEncoderConfig| 是| 无| 屏幕共享视频流参数。参看 ScreenVideoEncoderConfig。   
返回值
number
  * 0：成功。
  * !0：失败。


### enableAlphaChannelVideoEncode() 
开启自定义采集视频帧的 Alpha 通道编码功能。 适用于需要分离推流端视频主体与背景，且在拉流端可自定义渲染背景的场景。
注意
  * 该接口仅作用于自定义采集的、并且使用 RGBA 色彩模型的视频帧，包括 VideoPixelFormat.TEXTURE_2D、VideoPixelFormat.TEXTURE_OES、VideoPixelFormat.RGBA。
  * 该接口须在发布视频流之前调用。
  * 调用本接口开启 Alpha 通道编码后，你需调用 pushExternalVideoFrame 把自定义采集的视频帧推送至 RTC SDK。若推送了不支持的视频帧格式，则调用 pushExternalVideoFrame 时会返回错误码 ReturnStatus.RETURN_STATUS_PARAMETER_ERR。


类型
```
(streamIndex: StreamIndex, alphaLayout: AlphaLayout) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 需开启该功能的视频流类型，当前仅支持对 StreamIndex.STREAM_INDEX_MAIN 即主流开启。   
alphaLayout| AlphaLayout| 是| 无| 分离后的 Alpha 通道相对于 RGB 通道信息的排列位置。当前仅支持 AlphaLayout.TOP，即置于 RGB 通道信息上方。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### disableAlphaChannelVideoEncode() 
关闭外部采集视频帧的 Alpha 通道编码功能。
注意
该接口须在停止发布视频流之后调用。
类型
```
(streamIndex: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 需关闭该功能的视频流类型，当前仅支持设置为 StreamIndex.STREAM_INDEX_MAIN 即主流。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### setAudioSourceType() 
切换音频采集方式
注意
  * 进房前后调用此方法均有效。
  * 如果你调用此方法由内部采集切换至自定义采集，SDK 会自动关闭内部采集。然后，调用 pushExternalAudioFrame 推送自定义采集的音频数据到 RTC SDK 用于传输。
  * 如果你调用此方法由自定义采集切换至内部采集，你必须再调用 startAudioCapture 手动开启内部采集。


类型
```
(type: AudioSourceType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| AudioSourceType| 是| 无| 音频数据源，详见 AudioSourceType。默认使用内部音频采集。音频采集和渲染方式无需对应。   
返回值
number 方法调用结果：
  * ≥0: 切换成功。
  * -1：切换失败。


### setAudioRenderType() 
切换音频渲染方式
注意
  * 进房前后调用此方法均有效。
  * 如果你调用此方法切换至自定义渲染，调用 pullExternalAudioFrame 获取音频数据。


类型
```
(type: AudioRenderType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| AudioRenderType| 是| 无| 音频输出类型，详见 AudioRenderType默认使用内部音频渲染。音频采集和渲染方式无需对应。   
返回值
number 方法调用结果：
  * >0: 切换成功。
  * -1：切换失败。


### pullExternalAudioFrame() 
拉取下行音频数据用于自定义音频渲染。 调用该方法后，SDK 会主动拉取待播放的音频数据，包括远端已解码和混音后的音频数据，用于外部播放。
注意
  * 拉取外部音频数据前，必须先调用 setAudioRenderType 启用自定义音频渲染。
  * 由于 RTC SDK 的帧长为 10 毫秒，你应当每隔 10 毫秒拉取一次音频数据。确保音频采样点数（sample）x 拉取频率等于 audioFrame 的采样率 （sampleRate）。如设置采样率为 48000 时，每 10 毫秒调用本接口拉取数据，每次应拉取 480 个采样点。
  * 音频采样格式为 S16。音频缓冲区内的数据格式为 PCM 数据，其容量大小为 audioFrame.samples × audioFrame.channel × 2。


类型
```
(audioFrame: AudioFrame) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioFrame| AudioFrame| 是| 无| 音频数据帧，详见 AudioFrame   
返回值
number 方法调用结果
  * 0: 设置成功
  * < 0: 设置失败


### createRTCRoom() 
创建房间实例。 调用此方法仅返回一个房间实例，你仍需调用 才能真正地创建/加入房间。 多次调用此方法以创建多个 实例。分别调用各 RTCRoom 实例中的 方法，同时加入多个房间。 多房间模式下，用户可以同时订阅各房间的音视频流。
注意
  * 如果需要加入的房间已存在，你仍需先调用本方法来获取 RTCRoom 实例，再调用 加入房间。
  * 请勿使用同样的 roomId 创建多个房间，否则后创建的房间实例会替换先创建的房间实例。
  * 如果你需要在多个房间发布音视频流，无须创建多房间，直接调用 startForwardStreamToRooms 开始跨房间转发媒体流。


类型
```
(roomId: string) => RTCRoom

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 标识通话房间的房间 ID。该字符串符合正则表达式：[a-zA-Z0-9_@\\-\\.]{1,128}。   
返回值
RTCRoom 创建的 房间实例。
### setPublishFallbackOption() 
设置发布的音视频流的回退选项。 你可以调用该接口设置网络不佳或设备性能不足时从大流起进行降级处理，以保证通话质量。
注意
类型
```
(option: PublishFallbackOption) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
option| PublishFallbackOption| 是| 无| 本地发布的音视频流回退选项，参看 PublishFallbackOption。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteUserPriority() 
设置用户优先级。
注意
  * 该方法与 setSubscribeFallbackOption 搭配使用。
  * 如果开启了订阅流回退选项，弱网或性能不足时会优先保证收到的高优先级用户的流的质量。
  * 该方法在进房前后都可以使用，可以修改远端用户的优先级。


类型
```
(roomid: RemoteUserPriority, uid: string, priority: RemoteUserPriority) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomid| RemoteUserPriority| 是| 无| 房间 ID   
uid| string| 是| 无| 远端用户的 ID 。   
priority| RemoteUserPriority| 是| 无| 远端用户的优先级，详见枚举类型 RemoteUserPriority 。   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### sendSEIMessage() 
通过视频帧发送 SEI 数据。 在视频通话场景下，SEI 数据会随视频帧发送；在语音通话场景下，SDK 会自动生成一路 16px × 16px 的黑帧视频流用来发送 SEI 数据。
注意
类型
```
(streamIndex: StreamIndex, message: ArrayBuffer, repeatCount: number, mode: SEICountPerFrame) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 指定携带 SEI 数据的媒体流类型，参看 StreamIndex。语音通话场景下，该值需设为 STREAM_INDEX_MAIN，否则 SEI 数据会被丢弃从而无法送达远端。   
message| ArrayBuffer| 是| 无| SEI 消息，建议每帧 SEI 数据总长度不超过 4 KB。超过长度限制的消息会被丢弃。   
repeatCount| number| 是| 无| 消息发送重复次数。取值范围是 [0, max{29, %{视频帧率}-1}]。推荐范围 [2,4]。调用此接口后，这些 SEI 数据会添加到从当前视频帧开始的连续 %{repeatCount}+1 个视频帧中。   
mode| SEICountPerFrame| 是| 无| SEI 发送模式，参看 SEICountPerFrame。   
返回值
number
  * >= 0: 将被添加到视频帧中的 SEI 的数量。
  * < 0: 发送失败。


### setBusinessId() 
设置业务标识参数 可通过 businessId 区分不同的业务场景。businessId 由客户自定义，相当于一个“标签”，可以分担和细化现在 AppId 的逻辑划分的功能，但不需要鉴权。
注意
  * 需要在进房前调用，进房后调用该方法无效。


类型
```
(businessId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
businessId| string| 是| 无| 用户设置的自己的 businessId 值businessId 只是一个标签，颗粒度需要用户自定义。   
返回值
number
### getVideoEffectInterface() 
获取视频特效接口。
类型
```
() => IVideoEffect

ts

```

返回值
IVideoEffect 视频特效接口，参看 。
### enableEffectBeauty() 
开启/关闭基础美颜。
注意
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 基础美颜开关 true: 开启基础美颜 false: 关闭基础美颜（默认）   
返回值
number
### setBeautyIntensity() 
调整基础美颜强度
注意
  * 若在调用 enableEffectBeauty 前设置美颜强度，则对应美颜功能的强度初始值会根据设置更新。
  * 销毁引擎后，美颜功能强度恢复默认值。


类型
```
(beautyMode: EffectBeautyMode, intensity: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
beautyMode| EffectBeautyMode| 是| 无| 基础美颜模式，参看 EffectBeautyMode。   
intensity| number| 是| 无| 美颜强度，取值范围为 [0,1]。强度为 0 表示关闭。各基础美颜模式的强度默认值分别为：美白 0.7，磨皮 0.8，锐化 0.5，清晰 0.7。   
返回值
number
### setVideoOrientation() 
在自定义视频前处理及编码前，设置 RTC 链路中的视频帧朝向，默认为 Adaptive 模式。 移动端开启视频特效贴纸，或使用自定义视频前处理时，建议固定视频帧朝向为 Portrait 模式。单流转推场景下，建议根据业务需要固定视频帧朝向为 Portrait 或 Landscape 模式。不同模式的具体显示效果参看。
注意
  * 视频帧朝向设置仅适用于内部采集视频源。对于自定义采集视频源，设置视频帧朝向可能会导致错误，例如宽高对调。屏幕源不支持设置视频帧朝向。
  * 编码分辨率的更新与视频帧处理是异步操作，进房后切换视频帧朝向可能导致画面出现短暂的裁切异常，因此建议在进房前设置视频帧朝向，且不在进房后进行切换。


类型
```
(orientation: VideoOrientation) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
orientation| VideoOrientation| 是| 无| 视频帧朝向，参看 VideoOrientation。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRuntimeParameters() 
设置运行时的参数
注意
该接口需在 和 startAudioCapture 之前调用。
类型
```
{ (params: JSONObject): number; (params: Record<string, string>): number; }

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
params| JSONObject| 是| 无| 保留参数   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getNativeHandle() 
获取 C++ 层 。
注意
在一些场景下，获取 C++ 层 IRTCVideo，并通过其完成操作，相较于通过 Java 封装层完成有显著更高的执行效率。典型的场景有：视频/音频帧自定义处理，音视频通话加密等。
类型
```
() => number

ts

```

返回值
number
  * >0：方法调用成功, 返回 C++ 层 IRTCVideo 的地址。
  * -1：方法调用失败


### stopASR() 
关闭语音识别服务
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startFileRecording() 
该方法将通话过程中的音视频数据录制到本地的文件中。
注意
  * 调用该方法后，你会收到 onRecordingStateUpdate 回调。
  * 如果录制正常，系统每秒钟会通过 onRecordingProgressUpdate 回调通知录制进度。


类型
```
(type: StreamIndex, config: RecordingConfig, recordingType: RecordingType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| StreamIndex| 是| 无| 流属性，指定录制主流还是屏幕流，参看 StreamIndex   
config| RecordingConfig| 是| 无| 本地录制参数配置，参看 RecordingConfig   
recordingType| RecordingType| 是| 无| 本地录制的媒体类型，参看 RecordingType   
返回值
number 0: 正常 -1: 参数设置异常 -2: 当前版本 SDK 不支持该特性，请联系技术支持人员
### stopFileRecording() 
停止本地录制
注意
  * 调用 startFileRecording 开启本地录制后，你必须调用该方法停止录制。
  * 调用该方法后，你会收到 onRecordingStateUpdate 回调提示录制结果。


类型
```
(type: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| StreamIndex| 是| 无| 流属性，指定停止主流或者屏幕流录制，参看 StreamIndex   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startAudioRecording() 
开启录制语音通话，生成本地文件。 在进房前后开启录制，如果未打开麦克风采集，录制任务正常进行，只是不会将数据写入生成的本地文件；只有调用 startAudioCapture 接口打开麦克风采集后，才会将录制数据写入本地文件。
注意
类型
```
(config: AudioRecordingConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| AudioRecordingConfig| 是| 无| 参看 AudioRecordingConfig   
返回值
number
  * 0: 正常
  * -2: 参数设置异常
  * -3: 当前版本 SDK 不支持该特性，请联系技术支持人员


### stopAudioRecording() 
停止音频文件录制
注意
调用 startAudioRecording 开启本地录制后，你必须调用该方法停止录制。
类型
```
() => number

ts

```

返回值
number
  * 0: 正常
  * -3: 当前版本 SDK 不支持该特性，请联系技术支持人员


### getAudioEffectPlayer() 
创建音效播放器实例。
类型
```
() => IAudioEffectPlayer

ts

```

返回值
IAudioEffectPlayer 音效播放器。详见 。
### getMediaPlayer() 
创建音乐播放器实例。
类型
```
(playerId: number) => IMediaPlayer

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerId| number| 是| 无| 音乐播放器实例 id。取值范围为 [0, 3]。最多同时存在 4 个实例，超出取值范围时返回 nullptr。   
返回值
IMediaPlayer 音乐播放器实例，详见 
### setScreenAudioSourceType() 
在屏幕共享时，设置屏幕音频的采集方式（内部采集/自定义采集）
注意
类型
```
(sourceType: AudioSourceType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
sourceType| AudioSourceType| 是| 无| 屏幕音频输入源类型, 参看 AudioSourceType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setScreenAudioStreamIndex() 
在屏幕共享时，设置屏幕音频流和麦克风采集到的音频流的混流方式
注意
你应该在 publishScreen 之前，调用此方法。否则，你将收到 onWarning 的报错：WARNING_CODE_SET_SCREEN_STREAM_INDEX_FAILED
类型
```
(index: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 混流方式，参看 StreamIndex STREAM_INDEX_MAIN: 将屏幕音频流和麦克风采集到的音频流混流 STREAM_INDEX_SCREEN: 默认值，将屏幕音频流和麦克风采集到的音频流分为两路音频流   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startScreenCapture() 
使用 RTC SDK 内部采集模块开始采集屏幕音频流和（或）视频流。
注意
类型
```
(type: ScreenMediaType, mediaProjectionResultData: Intent) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| ScreenMediaType| 是| 无| 媒体类型，参看 ScreenMediaType。   
mediaProjectionResultData| unknown| 是| 无| 向 Android 设备申请屏幕共享权限后，拿到的 Intent 数据，参看 getMediaProjection。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### updateScreenCapture() 
使用 RTC SDK 内部屏幕采集后，更新采集的媒体类型。
注意
在 startScreenCapture 后调用该方法。
类型
```
(type: ScreenMediaType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| ScreenMediaType| 是| 无| 媒体类型，指定屏幕采集媒体类型，参看 ScreenMediaType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopScreenCapture() 
在屏幕共享时，停止使用 RTC SDK 内部采集方式采集屏幕音视频。
注意
  * 调用本接口时，采集模式应为内部模式。在外部采集模式下调用无效，并将触发 onVideoDeviceWarning 或 onAudioDeviceWarning 回调。
  * 要开始屏幕音视频内部采集，调用 startScreenCapture。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setExternalVideoEncoderEventHandler() 
注册自定义编码帧推送事件回调
注意
  * 该方法需在进房前调用。
  * 引擎销毁前需取消注册，调用该方法将参数设置为 "null" 即可。


类型
```
(handler: IExternalVideoEncoderEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
handler| IExternalVideoEncoderEventHandler| 是| 无| 自定义编码帧回调类，参看 IExternalVideoEncoderEventHandler   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoDecoderConfig() 
在订阅远端视频流之前，设置远端视频数据解码方式
注意
  * 当你想要对远端流进行自定义解码时，你需要先调用 registerRemoteEncodedVideoFrameObserver 注册远端视频流监测器，然后再调用该接口将解码方式设置为自定义解码。监测到的视频数据会通过 onRemoteEncodedVideoFrame 回调出来。
  * 自 3.56 起，要用于自动订阅场景下，你可以设置 key 中的 RoomId 和 UserId 为 nullptr，此时，通过此接口设置的解码方式根据 key 中的 StreamIndex 值，适用于所有的远端主流或屏幕流的解码方式。


类型
```
(key: RemoteStreamKey, config: VideoDecoderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
key| RemoteStreamKey| 是| 无| 远端流信息，指定对哪一路视频流进行解码方式设置，参看 RemoteStreamKey。   
config| VideoDecoderConfig| 是| 无| 视频解码方式，参看 VideoDecoderConfig。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### requestRemoteVideoKeyFrame() 
在订阅远端视频流之后，向远端请求关键帧
注意
  * 该方法仅适用于手动订阅模式，并且在成功订阅远端流之后使用。
  * 该方法适用于调用 setVideoDecoderConfig 开启自定义解码功能后，并且自定义解码失败的情况下使用


类型
```
(streamKey: RemoteStreamKey) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setEarMonitorMode() 
打开/关闭耳返功能。
注意
  * 耳返功能仅适用于由 RTC SDK 内部采集的音频。
  * 使用耳返功能必须佩戴耳机。为保证低延时耳返最佳体验，建议佩戴有线耳机。蓝牙耳机不支持硬件耳返。
  * RTC SDK 支持硬件耳返和软件耳返。一般来说，硬件耳返延时低且音质好。如果 App 在手机厂商的硬件耳返白名单内，且运行环境存在支持硬件耳返的 SDK，RTC SDK 默认启用硬件耳返。使用华为手机硬件耳返功能时，请添加。


类型
```
(mode: EarMonitorMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mode| EarMonitorMode| 是| 无| 耳返功能是否开启，详见 EarMonitorMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setEarMonitorVolume() 
设置耳返音量。
注意
  * 设置耳返音量前，你必须先调用 setEarMonitorMode 打开耳返功能。


类型
```
(volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 耳返音量，调节范围：[0,100]，单位：%   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enableAudioPropertiesReport() 
启用音频信息提示。开启提示后，你可以收到 onLocalAudioPropertiesReport，onRemoteAudioPropertiesReport onRemoteAudioPropertiesReport 和 onActiveSpeaker。
类型
```
(config: AudioPropertiesConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| AudioPropertiesConfig| 是| 无| 详见 AudioPropertiesConfig   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### sendStreamSyncInfo() 
发送音频流同步信息。将消息通过音频流发送到远端，并实现与音频流同步，该接口调用成功后，远端用户会收到 onStreamSyncInfoReceived 回调。
注意
  * 调用本接口的频率建议不超过 50 次每秒。
  * 在 CHANNEL_PROFILE_INTERACTIVE_PODCAST 房间模式下，此消息一定会送达。在其他房间模式下，如果本地用户未说话，此消息不一定会送达。


类型
```
(data: ArrayBuffer, config: StreamSycnInfoConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
data| ArrayBuffer| 是| 无| 消息内容。   
config| StreamSycnInfoConfig| 是| 无| 音频流同步信息的相关配置。详见 StreamSycnInfoConfig 。   
返回值
number
### isCameraTorchSupported() 
检测当前使用的摄像头（前置/后置），是否支持闪光灯。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检测闪光能力。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持
  * false: 不支持


### isCameraZoomSupported() 
检测当前使用的摄像头（前置/后置），是否支持变焦（数码/光学变焦）。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检测摄像头变焦能力。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持
  * false: 不支持


### setCameraZoomRatio() 
设置当前使用的摄像头（前置/后置）的光学变焦倍数
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能设置摄像头变焦倍数。
  * 设置结果在调用 stopVideoCapture 关闭内部采集后失效。
  * 你可以调用 setVideoDigitalZoomConfig 设置数码变焦参数， 调用 setVideoDigitalZoomControl 进行数码变焦。


类型
```
(zoom: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
zoom| number| 是| 无| 变焦倍数。取值范围是 [1, <最大变焦倍数>]。最大变焦倍数可以通过调用 getCameraZoomMaxRatio 获取。   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### getCameraZoomMaxRatio() 
获取当前使用的摄像头（前置/后置）的最大变焦倍数
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检测摄像头最大变焦倍数。
类型
```
() => number

ts

```

返回值
number 最大变焦倍数
### setCameraTorch() 
打开/关闭当前使用的摄像头（前置/后置）的闪光灯
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能设置闪光灯。
  * 设置结果在调用 stopVideoCapture 关闭内部采集后失效。


类型
```
(torchState: TorchState) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
torchState| TorchState| 是| 无| 闪光灯状态。参考 TorchState   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### isCameraFocusPositionSupported() 
检查当前使用的摄像头是否支持手动对焦。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集，才能检查摄像头是否支持手动对焦。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持。
  * false: 不支持。


### setCameraFocusPosition() 
设置当前使用的摄像头的对焦点。
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集，并且使用 SDK 内部渲染时，才能设置对焦点。
  * 移动设备时，自动取消对焦点设置。
  * 调用 stopVideoCapture 关闭内部采集后，设置的对焦点失效。


类型
```
(x: number, y: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
x| number| 是| 无| 对焦点水平方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最左边，1 表示最右边。   
y| number| 是| 无| 对焦点垂直方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最上边，1 表示最下边。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### isCameraExposurePositionSupported() 
检查当前使用的摄像头是否支持手动设置曝光点。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检查曝光点设置能力。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持。
  * false: 不支持。


### setCameraExposurePosition() 
设置当前使用的摄像头的曝光点
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集，并且使用 SDK 内部渲染时，才能设置曝光点。
  * 移动设备时，自动取消曝光点设置。
  * 调用 stopVideoCapture 关闭内部采集后，设置的曝光点失效。


类型
```
(x: number, y: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
x| number| 是| 无| 曝光点水平方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最左边，1 表示最右边。   
y| number| 是| 无| 曝光点垂直方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最上边，1 表示最下边。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### setCameraExposureCompensation() 
设置当前使用的摄像头的曝光补偿。
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能设置曝光补偿。
  * 调用 stopVideoCapture 关闭内部采集后，设置的曝光补偿失效。


类型
```
(val: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
val| number| 是| 无| 曝光补偿值，取值范围 [-1, 1]，0 为系统默认值(没有曝光补偿)。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### enableCameraAutoExposureFaceMode() 
启用或禁用内部采集时人脸自动曝光模式。此模式会改善强逆光下，脸部过暗的问题；但也会导致 ROI 以外区域过亮/过暗的问题。
注意
你必须在调用 startVideoCapture 开启内部采集前，调用此接口方可生效。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否启用。默认开启。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### startPushPublicStream() 
发布一路公共流 用户可以指定房间内多个用户发布的媒体流合成一路公共流。使用同一 appID 的用户，可以调用 startPlayPublicStream 获取和播放指定的公共流。
注意
类型
```
(publicStreamId: string, publicStream: PublicStreaming) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
publicStream| PublicStreaming| 是| 无| 公共流参数。详见 PublicStreaming。一路公共流可以包含多路房间内的媒体流，按照指定的布局方式进行聚合。如果指定的媒体流还未发布，则公共流将在指定流开始发布后实时更新。   
返回值
number 0: 成功。同时将收到 onPushPublicStreamResult 回调。
  * !0: 失败。当参数不合法或参数为空，调用失败。


### stopPushPublicStream() 
停止发布当前用户发布的公共流 关于发布公共流，查看 startPushPublicStream
类型
```
(publicStreamId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID指定的流必须为当前用户所发布。   
返回值
number
  * 0: 成功
  * !0: 失败


### updatePublicStreamParam() 
更新公共流参数 关于发布公共流，查看 startPushPublicStream。
注意
调用本接口前需要通过 onPushPublicStreamResult 确认公共流是否已经成功启动。
类型
```
(publicStreamId: string, transcoding: PublicStreaming) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID指定的流必须为当前用户所发布。   
transcoding| PublicStreaming| 是| 无| 配置参数，详见 PublicStreaming。   
返回值
number
  * 0: 成功
  * !0: 失败


### startPlayPublicStream() 
订阅指定公共流 无论用户是否在房间内，都可以调用本接口获取和播放指定的公共流。
注意
类型
```
(publicStreamId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID，如果指定流暂未发布，则本地客户端将在其开始发布后接收到流数据。   
返回值
number
  * 0: 成功。同时将收到 onPlayPublicStreamResult 回调。
  * !0: 失败。当参数不合法或参数为空，调用失败。


### stopPlayPublicStream() 
取消订阅指定公共流 关于订阅公共流，查看 startPlayPublicStream。
类型
```
(publicStreamId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
返回值
number
  * 0：成功
  * !0：失败


### setPublicStreamVideoCanvas() 
为指定公共流绑定内部渲染视图。
类型
```
(publicStreamId: string, canvas: VideoCanvas) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
canvas| VideoCanvas| 是| 无| 内部渲染视图，如果需要解除视频的绑定视图，把 videoCanvas 设置为空。详见 VideoCanvas   
返回值
number
  * 0：成功
  * <0：失败


### setPublicStreamAudioPlaybackVolume() 
调节公共流的音频播放音量。
类型
```
(publicStreamId: string, volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
volume| number| 是| 无| 音频播放音量值和原始音量值的比值，该比值的范围是 [0, 400]，单位为 %，且自带溢出保护。为保证更好的音频质量，建议设定在 [0, 100] 之间，其中 100 为系统默认值。   
返回值
number
  * 0: 成功调用。
  * -2: 参数错误。


### startEchoTest() 
开启音视频回路测试。 在进房前，用户可调用该接口对音视频通话全链路进行检测，包括对音视频设备以及用户上下行网络的检测，从而帮助用户判断是否可以正常发布和接收音视频流。 开始检测后，SDK 会录制你声音或视频。如果你在设置的延时范围内收到了回放，则视为音视频回路测试正常。
注意
类型
```
(config: EchoTestConfig, delayTime: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| EchoTestConfig| 是| 无| 回路测试参数设置，参看 EchoTestConfig。   
delayTime| number| 是| 无| 音视频延迟播放的时间间隔，用于指定在开始检测多长时间后期望收到回放。取值范围为 [2,10]，单位为秒，默认为 2 秒。   
返回值
number 方法调用结果：
### stopEchoTest() 
停止音视频回路测试。 调用 startEchoTest 开启音视频回路检测后，你必须调用该方法停止检测。
注意
音视频回路检测结束后，所有对系统设备及音视频流的控制均会恢复到开始检测前的状态。
类型
```
() => number

ts

```

返回值
number 方法调用结果：
  * 0：成功
  * -1：失败，未开启回路检测


### clearVideoWatermark() 
移除指定视频流的水印。
类型
```
(streamIndex: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 需要移除水印的视频流属性，参看 StreamIndex。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setDummyCaptureImagePath() 
摄像头处于关闭状态时，使用静态图片填充本地推送的视频流。 调用 stopVideoCapture 接口时，会开始推静态图片。若要停止发送图片，可传入空字符串或启用内部摄像头采集。 可重复调用该接口来更新图片。
注意
类型
```
(filePath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
filePath| string| 是| 无| 设置静态图片的路径。支持本地文件绝对路径和 Asset 资源路径(/assets/xx.png)，不支持网络链接，长度限制为 512 字节。静态图片支持类型为 JPEG/JPG、PNG、BMP。若图片宽高比与设置的编码宽高比不一致，图片会被等比缩放，黑边填充空白区域。推流帧率与码率与设置的编码参数一致。   
返回值
number
  * 0: 成功。
  * -1: 失败。


### startCloudProxy() 
开启云代理
注意
类型
```
(cloudProxiesInfo: Array<CloudProxyInfo>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
cloudProxiesInfo| CloudProxyInfo[]| 是| 无| 云代理服务器信息列表。参看 CloudProxyInfo。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopCloudProxy() 
关闭云代理
注意
要开启云代理，调用 startCloudProxy。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getSingScoringManager() 
创建 K 歌评分管理接口。
注意
如需使用 K 歌评分功能，即调用该方法以及 ISingScoringManager 类下全部方法，需集成 SAMI 动态库，详情参看文档。
类型
```
() => ISingScoringManager

ts

```

返回值
ISingScoringManager K 歌评分管理接口，参看 。
### getNetworkTimeInfo() 
通过 NTP 协议，获取网络时间。
注意
  * 第一次调用此接口会启动网络时间同步功能，并返回 0。同步完成后，会收到 onNetworkTimeSynchronized，此后，再次调用此 API，即可获取准确的网络时间。
  * 在合唱场景下，合唱参与者应在相同的网络时间播放背景音乐。


类型
```
() => NetworkTimeInfo

ts

```

返回值
NetworkTimeInfo 网络时间。参看 。
### getKTVManager() 
创建 KTV 管理接口。
类型
```
() => IKTVManager

ts

```

返回值
IKTVManager KTV 管理接口，参看 。
### startHardwareEchoDetection() 
开启通话前回声检测
注意
类型
```
(testAudioFilePath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
testAudioFilePath| string| 是| 无| 用于回声检测的音频文件的绝对路径，路径字符串使用 UTF-8 编码格式，支持以下音频格式: mp3，aac，m4a，3gp，wav。音频文件不为静音文件，推荐时长为 10 ～ 20 秒。   
返回值
number 方法调用结果：
  * 0: 成功。
  * -1：失败。上一次检测未结束，请先调用 stopHardwareEchoDetection 停止检测 后重新调用本接口。
  * -2：失败。路径不合法或音频文件格式不支持。


### stopHardwareEchoDetection() 
停止通话前回声检测
注意
  * 关于开启通话前回声检测，参看 startHardwareEchoDetection 。
  * 建议在收到 onHardwareEchoDetectionResult 通知的检测结果后，调用本接口停止检测。
  * 在用户进入房间前结束回声检测，释放对音频设备的占用，以免影响正常通话。


类型
```
() => number

ts

```

返回值
number 方法调用结果：
  * 0: 成功。
  * -1：失败。


### setCellularEnhancement() 
启用蜂窝网络辅助增强，改善通话质量。
注意
此功能默认不开启。
类型
```
(config: MediaTypeEnhancementConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| MediaTypeEnhancementConfig| 是| 无| 参看 MediaTypeEnhancementConfig。   
返回值
number 方法调用结果：
  * 0: 成功。
  * -1：失败，内部错误。
  * -2: 失败，输入参数错误。


### android_getVideoDeviceManager() 
创建视频设备管理实例
类型
```
() => $p_a.IVideoDeviceManager

ts

```

返回值
$p_a.IVideoDeviceManager 视频设备管理实例，详见 
### setVideoCaptureRotation() 
设置本端采集的视频帧的旋转角度。 当摄像头倒置或者倾斜安装时，可调用本接口进行调整。对于手机等普通设备，可调用 setVideoRotationMode 实现旋转。
注意
类型
```
(rotation: VideoRotation) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
rotation| VideoRotation| 是| 无| 相机朝向角度，默认为 VIDEO_ROTATION_0(0)，无旋转角度。详见 VideoRotation。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### ios_setMaxVideoEncoderConfig() 
视频发布端设置期望发布的最大分辨率视频流参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。 该接口支持设置一路视频流参数，设置多路参数请使用 。
注意
类型
```
(encoderConfig: $p_i.ByteRTCVideoEncoderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
encoderConfig| $p_i.ByteRTCVideoEncoderConfig| 是| 无| 期望发布的最大分辨率视频流参数。参看 ByteRTCVideoEncoderConfig。   
返回值
number 方法调用结果：
  * 0：成功
  * !0：失败


### ios_setCustomizeEncryptHandler() 
设置自定义加密和解密方式。
注意
类型
```
(handler: $p_i.id<$p_i.ByteRTCEncryptHandler>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
handler| $p_i.ByteRTCEncryptHandler| 是| 无| 自定义加密 handler，需要实现里面的加密和解密方法。参看 ByteRTCEncryptHandler。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 ByteRTCReturnStatus 获得更多错误说明


### static android_createRTCVideo_context$appId$handler$eglContext$parameters() 
创建引擎对象 如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 如果当前进程中已创建了引擎实例，再次调用此方法时，会返回已创建的引擎实例。
注意
你应注意保持 handler 的生命周期必须大于 的生命周期，即 handler 必须在调用 destroyRTCVideo 之后销毁。
类型
```
(context: $p_a.Context, appId: string, handler: $p_a.IRTCVideoEventHandler, eglContext: Object, parameters: $p_a.JSONObject) => $p_a.RTCVideo

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
context| unknown| 是| 无| Android Application Context   
appId| string| 是| 无| 每个应用的唯一标识符，由 RTC 控制台随机生成的。不同的 AppId 生成的实例在 RTC 中进行音视频通话完全独立，无法互通。   
handler| $p_a.IRTCVideoEventHandler| 是| 无| SDK 回调给应用层的 Handler，详见 IRTCVideoEventHandler   
eglContext| Object| 是| 无| 如果需要支持外部纹理硬编码，则需要以 JObject 方式传入 eglContext。   
parameters| $p_a.JSONObject| 是| 无| 私有参数。如需使用请联系技术支持人员。   
返回值
$p_a.RTCVideo
  * RTCVideo：创建成功。返回一个可用的 实例
  * Null：appId 或者 context 参数为空，.so 文件加载失败。


### static destroyRTCVideo() 
销毁由 createRTCVideo 所创建的引擎实例，并释放所有相关资源。
注意
  * 请确保和需要销毁的 实例相关的业务场景全部结束后，才调用此方法
  * 该方法在调用之后，会销毁所有和此 实例相关的内存，并且停止与媒体服务器的任何交互
  * 调用本方法会启动 SDK 退出逻辑。引擎线程会保留，直到退出逻辑完成。因此，不要在回调线程中直接调用此 API，会导致死锁。同时此方法是耗时操作，不建议在主线程调用本方法，避免主线程阻塞。


类型
```
() => void

ts

```

### static getSDKVersion() 
获取 SDK 当前的版本号。
类型
```
() => string

ts

```

返回值
string SDK 当前的版本号。
### static setLogConfig() 
自定义 SDK 日志配置，包括日志输出等级、存储路径、日志文件总大小上限、日志文件名前缀。
注意
本方法必须在调用 createRTCVideo 之前调用。
类型
```
(logConfig: RTCLogConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
logConfig| RTCLogConfig| 是| 无| 日志配置，参看 RTCLogConfig。   
返回值
number
  * 0：成功。
  * –1：失败，本方法必须在创建引擎前调用。
  * –2：失败，参数填写错误。


### static getErrorDescription() 
获取 SDK 内各种错误码、警告码的描述文字。
类型
```
(code: number) => string

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
code| number| 是| 无| 通过 onWarning 和 onError 回调获得的值，具体可以参考 ErrorCode 和 WarningCode   
返回值
string String 描述文字
## RTCRoom 
类型：class
RTC 房间类
### ios_delegate 
类型：$p_i.ByteRTCRoomDelegate
### setRTCRoomEventHandler() 
通过设置 对象的事件句柄，监听此对象对应的回调事件。
类型
```
(rtcRoomEventHandler: RTCRoomEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
rtcRoomEventHandler| RTCRoomEventHandler| 是| 无| 参看 IRTCRoomEventHandler   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### subscribeAllStreams() 
订阅房间内所有通过摄像头/麦克风采集的媒体流，或更新订阅选项。
注意
类型
```
(type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| MediaStreamType| 是| 无| 媒体流类型，用于指定订阅音频/视频。参看 MediaStreamType。   
返回值
number
  * 0: 方法调用成功
  * !0: 方法调用失败


### unsubscribeAllStreams() 
取消订阅房间内所有的通过摄像头/麦克风采集的媒体流。 自动订阅和手动订阅的流都可以通过本方法取消订阅。
注意
  * 调用该方法后，你会收到 onStreamSubscribed 通知方法调用结果。
  * 关于其他调用异常，你会收到 onStreamStateChanged 回调通知，具体失败原因参看 。


类型
```
(type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| MediaStreamType| 是| 无| 媒体流类型，用于指定取消订阅音频/视频。参看 MediaStreamType。   
返回值
number 方法调用结果：
  * 0：成功
  * !0：失败


### destroy() 
退出并销毁调用 createRTCRoom 所创建的房间实例。
类型
```
() => void

ts

```

### joinRoom() 
加入房间。 调用 createRTCRoom 创建房间后，调用此方法加入房间，同房间内其他用户进行音视频通话。
注意
类型
```
(token: string, userInfo: UserInfo, roomConfig: RTCRoomConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
token| string| 是| 无| 动态密钥。用于对进房用户进行鉴权验证。进入房间需要携带 Token。测试时可使用控制台生成临时 Token，正式上线需要使用密钥 SDK 在你的服务端生成并下发 Token。Token 有效期及生成方式参看使用 Token 完成鉴权。使用不同 AppID 的 App 是不能互通的。请务必保证生成 Token 使用的 AppID 和创建引擎时使用的 AppID 相同，否则会导致加入房间失败。   
userInfo| UserInfo| 是| 无| 用户信息。参看 UserInfo。   
roomConfig| RTCRoomConfig| 是| 无| 房间参数配置，设置房间模式以及是否自动发布或订阅流。具体配置模式参看 RTCRoomConfig。   
返回值
number
  * 0：方法调用成功。
  * -1：roomID / userInfo.uid 包含了无效的参数。
  * -2：已经在房间内。接口调用成功后，只要收到返回值为 0 ，且未调用 leaveRoom 成功，则再次调用进房接口时，无论填写的房间 ID 和用户 ID 是否重复，均触发此返回值。 调用失败时，具体失败原因会通过 onRoomStateChanged 回调告知。


### leaveRoom() 
离开房间。 用户调用此方法离开房间，结束通话过程，释放所有通话相关的资源。 此方法是异步操作，调用返回时并没有真正退出房间。真正退出房间后，本地会收到 onLeaveRoom 回调通知。
注意
  * 调用 setUserVisibility 将自身设为可见的用户离开房间后，房间内其他用户会收到 onUserLeave 回调通知。
  * 如果调用此方法后立即销毁引擎，SDK 将无法触发 onLeaveRoom 回调。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setUserVisibility() 
设置用户可见性。未调用该接口前，本地用户默认对他人可见。 默认情况下，一个 RTC 房间最多同时容纳 50 名可见用户，最多 30 人可同时上麦。更多信息参看。
注意
  * 在加入房间前后，用户均可调用此方法设置用户可见性。
  * 设置用户可见性，会收到设置成功/失败回调 onUserVisibilityChanged。（v3.54 新增） • 在加入房间前设置用户可见性，若设置的可见性与默认值不同，将在加入房间时触发本回调。 • 在加入房间后设置用户可见性，若可见性前后不同，会触发本回调。 • 在断网重连后，若可见性发生改变，会触发本回调。
  * 在房间内，调用此方法成功切换用户可见性后，房间内其他用户会收到相应的回调。 • 从可见换至不可见时，房间内其他用户会收到 onUserLeave。 • 从不可见切换至可见时，房间内其他用户会收到 onUserJoined。 • 若调用该方法将可见性设为 false，此时尝试发布流会收到 WARNING_CODE_PUBLISH_STREAM_FORBIDEN 警告。


类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 设置用户是否对房间内其他用户可见： true: 可见，用户可以在房间内发布音视频流，房间中的其他用户将收到用户的行为通知，例如进房、开启视频采集和退房。 false: 不可见，用户不可以在房间内发布音视频流，房间中的其他用户不会收到用户的行为通知，例如进房、开启视频采集和退房。   
返回值
number
  * 0: 调用成功。
  * < 0: 调用失败。参看 获得更多错误说明。


### updateToken() 
更新 Token。 收到 onTokenWillExpire，onPublishPrivilegeTokenWillExpire onPublishPrivilegeTokenWillExpire， 或 onSubscribePrivilegeTokenWillExpire 时，你必须重新获取 Token，并调用此方法更新 Token，以保证通话的正常进行。
注意
  * 3.50（不含）以前的版本中，Token 中的发布和订阅权限为保留参数，无实际控制权限；3.50 及以后版本开放 Token 发布订阅控制权限，如需通过 Token 控制连麦权限，请联系技术支持团队开通白名单后支持。
  * 请勿同时调用 updateToken 和 方法更新 Token。若因 Token 过期或无效导致加入房间失败或已被移出房间，你应该在获取新的有效 Token 后调用 重新加入房间。


类型
```
(token: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
token| string| 是| 无| 重新获取的有效 Token。如果 Token 无效，你会收到 onRoomStateChanged，错误码是 -1010。   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### sendUserMessage() 
给房间内指定的用户发送点对点文本消息（P2P）。
注意
  * 在发送房间内文本消息前，必须先调用 加入房间。
  * 调用后，会收到 onUserMessageSendResult 回调，通知消息发送成功或失败；
  * 若消息发送成功，则 userId 所指定的用户会收到 onUserMessageReceived 回调。


类型
```
(userId: string, messageStr: string, config: MessageConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
userId| string| 是| 无| 消息接收用户的 ID   
messageStr| string| 是| 无| 发送的文本消息内容。消息不超过 64 KB。   
config| MessageConfig| 是| 无| 消息发送的可靠/有序类型，参看 MessageConfig   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCRoom 实例未创建
  * -2：发送失败，uid 为空


### sendUserBinaryMessage() 
给房间内指定的用户发送点对点二进制消息（P2P）。
注意
  * 在发送房间内二进制消息前，必须先调用 加入房间。
  * 调用后，会收到 onUserMessageSendResult 回调，通知消息发送成功或失败；
  * 若消息发送成功，则 userId 所指定的用户会收到 onUserBinaryMessageReceived 回调。


类型
```
(userId: string, buffer: ArrayBuffer, config: MessageConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
userId| string| 是| 无| 消息接收用户的 ID   
buffer| ArrayBuffer| 是| 无| 发送的二进制消息内容。消息不超过 46KB。   
config| MessageConfig| 是| 无| 消息发送的可靠/有序类型，参看 MessageConfig。   
返回值
number
### sendRoomMessage() 
给房间内的所有其他用户群发文本消息。
注意
  * 在房间内广播文本消息前，必须先调用 加入房间。
  * 调用后，会收到 onRoomMessageSendResult 回调；
  * 同一房间内的其他用户会收到 onRoomMessageReceived 回调。


类型
```
(messageStr: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
messageStr| string| 是| 无| 发送的文本消息内容，消息不超过 64 KB。   
返回值
number
### sendRoomBinaryMessage() 
给房间内的所有其他用户群发二进制消息。
注意
  * 在房间内广播二进制消息前，必须先调用 加入房间。
  * 调用后，会收到 onRoomMessageSendResult 回调；
  * 同一房间内的其他用户会收到 onRoomBinaryMessageReceived 回调。


类型
```
(buffer: ArrayBuffer) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
buffer| ArrayBuffer| 是| 无| 发送的二进制消息内容，消息不超过 46KB。   
返回值
number
### setMultiDeviceAVSync() 
设置发流端音画同步。 当同一用户同时使用两个通话设备分别采集发送音频和视频时，有可能会因两个设备所处的网络环境不一致而导致发布的流不同步，此时你可以在视频发送端调用该接口，SDK 会根据音频流的时间戳自动校准视频流，以保证接收端听到音频和看到视频在时间上的同步性。
注意
类型
```
(audioUserId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioUserId| string| 是| 无| 音频发送端的用户 ID，将该参数设为空则可解除当前音视频的同步关系。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoConfig() 
设置期望订阅的远端视频流的参数。
注意
类型
```
(userId: string, remoteVideoConfig: RemoteVideoConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
userId| string| 是| 无| 期望配置订阅参数的远端视频流发布用户的 ID。   
remoteVideoConfig| RemoteVideoConfig| 是| 无| 期望配置的远端视频流参数，参看 RemoteVideoConfig。   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### publishStream() 
在当前所在房间内发布本地通过摄像头/麦克风采集的媒体流
注意
类型
```
(type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| MediaStreamType| 是| 无| 媒体流类型，用于指定发布音频/视频，参看 MediaStreamType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### unpublishStream() 
停止将本地摄像头/麦克风采集的媒体流发布到当前所在房间中
注意
  * 调用 手动发布摄像头音视频流后，你需调用此接口停止发布。
  * 调用此方法停止发布音视频流后，房间中的其他用户将会收到 回调通知。


类型
```
(type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| MediaStreamType| 是| 无| 媒体流类型，用于指定停止发布音频/视频，参看 MediaStreamType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### publishScreen() 
在当前所在房间内发布本地屏幕共享音视频流
注意
类型
```
(type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| MediaStreamType| 是| 无| 媒体流类型，用于指定发布屏幕音频/视频，参看 MediaStreamType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### unpublishScreen() 
停止将本地屏幕共享音视频流发布到当前所在房间中
注意
  * 调用 publishScreen 发布屏幕流后，你需调用此接口停止发布。
  * 调用此方法停止发布屏幕音视频流后，房间中的其他用户将会收到 onUserUnpublishScreen 回调。


类型
```
(type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| MediaStreamType| 是| 无| 媒体流类型，用于指定停止发布屏幕音频/视频，参看 MediaStreamType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### subscribeStream() 
订阅房间内指定的通过摄像头/麦克风采集的媒体流，或更新对指定远端用户的订阅选项
注意
类型
```
(uid: string, type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 指定订阅的远端发布音视频流的用户 ID。   
type| MediaStreamType| 是| 无| 媒体流类型，用于指定订阅音频/视频。参看 MediaStreamType。   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### unsubscribeStream() 
取消订阅房间内指定的通过摄像头/麦克风采集的媒体流。 该方法对自动订阅和手动订阅模式均适用。
注意
  * 调用该方法后，你会收到 onStreamSubscribed 通知方法调用结果。
  * 关于其他调用异常，你会收到 onStreamStateChanged 回调通知，具体失败原因参看 。


类型
```
(uid: string, type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 指定取消订阅的远端发布音视频流的用户 ID。   
type| MediaStreamType| 是| 无| 媒体流类型，用于指定取消订阅音频/视频。参看 MediaStreamType。   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### subscribeScreen() 
订阅房间内指定的远端屏幕共享音视频流，或更新对指定远端用户的订阅选项
注意
类型
```
(uid: string, type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 指定订阅的远端发布屏幕流的用户 ID。   
type| MediaStreamType| 是| 无| 媒体流类型，用于指定订阅音频/视频。参看 MediaStreamType。   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### unsubscribeScreen() 
取消订阅房间内指定的远端屏幕共享音视频流。 该方法对自动订阅和手动订阅模式均适用。
注意
  * 调用该方法后，你会收到 onStreamSubscribed 通知流的退订结果。
  * 关于其他调用异常，你会收到 onStreamStateChanged 回调通知，具体失败原因参看 。


类型
```
(uid: string, type: MediaStreamType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 指定取消订阅的远端发布屏幕流的用户 ID。   
type| MediaStreamType| 是| 无| 媒体流类型，用于指定取消订阅音频/视频。参看 MediaStreamType。   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### pauseAllSubscribedStream() 
暂停接收来自远端的媒体流。
注意
类型
```
(mediaType: PauseResumeControlMediaType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mediaType| PauseResumeControlMediaType| 是| 无| 媒体流类型，指定需要暂停接收音频还是视频流，参看 PauseResumeControlMediaType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### resumeAllSubscribedStream() 
恢复接收来自远端的媒体流
注意
  * 该方法仅恢复远端流的接收，并不影响远端流的采集和发送；
  * 该方法不改变用户的订阅状态以及订阅流的属性。


类型
```
(mediaType: PauseResumeControlMediaType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mediaType| PauseResumeControlMediaType| 是| 无| 媒体流类型，指定需要暂停接收音频还是视频流，参看 PauseResumeControlMediaType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopForwardStreamToRooms() 
停止跨房间媒体流转发。 通过 startForwardStreamToRooms 发起媒体流转发后，可调用本方法停止向所有目标房间转发媒体流。
注意
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### pauseForwardStreamToAllRooms() 
暂停跨房间媒体流转发。 通过 startForwardStreamToRooms 发起媒体流转发后，可调用本方法暂停向所有目标房间转发媒体流。 调用本方法暂停向所有目标房间转发后，你可以随时调用 快速恢复转发。
注意
调用本方法后，目标房间中的用户将接收到本地用户停止发布 /onUserUnpublishScreen onUserUnpublishScreen 和退房 onUserLeave 的回调。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### resumeForwardStreamToAllRooms() 
恢复跨房间媒体流转发。 调用 pauseForwardStreamToAllRooms 暂停转发之后，调用本方法恢复向所有目标房间转发媒体流。
注意
目标房间中的用户将接收到本地用户进房 onUserJoined 和发布 onUserPublishStream/onUserPublishScreen onUserPublishScreen 的回调。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getRangeAudio() 
获取范围语音接口实例。
注意
首次调用该方法须在创建房间后、加入房间前。范围语音相关 API 和调用时序详见。
类型
```
() => IRangeAudio

ts

```

返回值
IRangeAudio 方法调用结果：
  * IRangeAudio：成功，返回一个 实例。
  * null：失败，当前 SDK 不支持范围语音功能。


### getSpatialAudio() 
获取空间音频接口实例。
注意
类型
```
() => ISpatialAudio

ts

```

返回值
ISpatialAudio 空间音频管理接口实例。如果返回 NULL，则表示不支持空间音频，详见 。
### setRemoteRoomAudioPlaybackVolume() 
调节某个房间内所有远端用户的音频播放音量。
注意
假设某远端用户 A 始终在被调节的目标用户范围内，
  * 当该方法与 setRemoteAudioPlaybackVolume 共同使用时，本地收听用户 A 的音量为后调用的方法设置的音量；
  * 当该方法与 setPlaybackVolume 方法共同使用时，本地收听用户 A 的音量将为两次设置的音量效果的叠加。


类型
```
(volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 音频播放音量和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 为保证更好的通话质量，建议将 volume 值设为 [0,100]。 0: 静音 100: 原始音量，默认值 - 400: 最大可为原始音量的 4 倍(自带溢出保护)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAudioSelectionConfig() 
设置本端发布流在音频选路中的优先级。
注意
在控制台上为本 appId 开启音频选路后，调用本接口才会生效。进房前后调用均可生效。更多信息参见。 如果本端用户同时加入不同房间，使用本接口进行的设置相互独立。
类型
```
(audioSelectionPriority: AudioSelectionPriority) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioSelectionPriority| AudioSelectionPriority| 是| 无| 本端发布流在音频选路中的优先级，默认正常参与音频选路。参见 AudioSelectionPriority。   
返回值
number
### setRoomExtraInfo() 
设置/更新房间附加信息，可用于标识房间状态或属性，或灵活实现各种业务逻辑。
注意
类型
```
(key: string, value: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
key| string| 是| 无| 房间附加信息键值，长度小于 10 字节。同一房间内最多可存在 5 个 key，超出则会从第一个 key 起进行替换。   
value| string| 是| 无| 房间附加信息内容，长度小于 128 字节。   
返回值
number
  * 0: 方法调用成功，返回本次调用的任务编号；
  * <0: 方法调用失败，具体原因详见 。


### startSubtitle() 
识别或翻译房间内所有用户的语音，形成字幕。 调用该方法时，可以在 中选择语音识别或翻译模式。如果选择识别模式，语音识别文本会通过 onSubtitleMessageReceived 事件回调给你； 如果选择翻译模式，你会同时收到两个 onSubtitleMessageReceived 回调，分别包含字幕原文及字幕译文。 调用该方法后，你会收到 onSubtitleStateChanged 回调，通知字幕是否开启。
注意
类型
```
(subtitleConfig: SubtitleConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
subtitleConfig| SubtitleConfig| 是| 无| 字幕配置信息。参看 SubtitleConfig。   
返回值
number
  * 0: 调用成功。
  * !0: 调用失败。


### stopSubtitle() 
关闭字幕。 调用该方法后，用户会收到 onSubtitleStateChanged 回调，通知字幕是否关闭。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * !0: 调用失败。


### getRoomId() 
获取房间 ID。
类型
```
() => string

ts

```

返回值
string 房间 ID。
## IRangeAudio 
类型：class
范围语音接口实例
### enableRangeAudio() 
开启/关闭范围语音功能。 范围语音是指，在同一 RTC 房间中设定的音频接收距离范围内，本地用户收听到的远端用户音频音量会随着远端用户的靠近/远离而放大/衰减；若远端用户在房间内的位置超出设定范围，则本地用户无法接收其音频。音频接收范围设置参看 updateReceiveRange。
注意
该方法进房前后都可调用，为保证进房后范围语音效果的平滑切换，你需在该方法前先调用 updatePosition 设置自身位置坐标，然后开启该方法收听范围语音效果。
类型
```
(enable: boolean) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启范围语音功能： true: 开启 false: 关闭（默认）   
### updatePosition() 
更新本地用户在房间内空间直角坐标系中的位置坐标。
注意
  * 调用该接口更新坐标后，你需调用 enableRangeAudio 开启范围语音功能以收听范围语音效果。


类型
```
(pos: Position) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
pos| Position| 是| 无| 三维坐标的值，默认为 [0, 0, 0]，参看 Position。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### updateReceiveRange() 
更新本地用户的音频收听范围。
类型
```
(range: ReceiveRange) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
range| ReceiveRange| 是| 无| 音频收听范围，参看 ReceiveRange。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0: 失败。


### setAttenuationModel() 
设置范围语音的音量衰减模式。
注意
音量衰减范围通过 updateReceiveRange 进行设置。
类型
```
(type: AttenuationType, coefficient: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| AttenuationType| 是| 无| 音量衰减模式。默认为线性衰减。详见 AttenuationType。   
coefficient| number| 是| 无| 指数衰减模式下的音量衰减系数，默认值为 1。范围 [0.1,100]，推荐设置为 50。数值越大，音量的衰减速度越快。   
返回值
number 调用是否成功
  * 0:调用成功
  * -1:调用失败。原因为在调用 enableRangeAudio 开启范围语音前或进房前调用本接口


### setNoAttenuationFlags() 
添加标签组，用于标记相互之间通话不衰减的用户组。 在同一个 RTC 房间中，如果多个用户的标签组之间有交集，那么，他们之间互相通话时，通话不衰减。 比如，用户身处多个队伍，队伍成员间通话不衰减。那么，可以为每个队伍绑定专属标签，每个用户的标签组包含用户所属各个队伍的标签。
类型
```
(flags: Array<string>) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
flags| string[]| 是| 无| 标签组   
## IAudioEffectPlayer 
类型：class
音效播放器 调用 setEventHandler 设置回调句柄以获取相关回调。
### start() 
开始播放音效文件。 可以通过传入不同的 ID 和 filepath 多次调用本方法，以实现同时播放多个音效文件，实现音效叠加。
注意
  * 如果已经通过 preload 将文件加载至内存，确保此处的 ID 与 preload 设置的 ID 相同。
  * 开始播放音效文件后，可以调用 stop 方法停止播放音效文件。


类型
```
(effectId: number, filePath: string, config: AudioEffectPlayerConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID。用于标识音效，请保证音效 ID 唯一性。如果使用相同的 ID 重复调用本方法后，上一个音效会停止，下一个音效开始，并收到 onAudioEffectPlayerStateChanged。   
filePath| string| 是| 无| 音效文件路径。支持在线文件的 URL、本地文件的 URI、本地文件的绝对路径或以 /assets/ 开头的本地文件路径。对于在线文件的 URL，仅支持 https 协议。推荐的音效文件采样率：8KHz、16KHz、22.05KHz、44.1KHz、48KHz。不同平台支持的本地音效文件格式:mp3mp4aacm4a3gpwavoggtswmaAndroidYYYYYYYiOS/macOSYYYYYYWindowsYYYYYYYY不同平台支持的在线音效文件格式:mp3mp4aacm4a3gpwavoggtswmaAndroidYYYYYiOS/macOSYYYYWindowsYYYYYYY   
config| AudioEffectPlayerConfig| 是| 无| 音效配置，详见 AudioEffectPlayerConfig。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stop() 
停止播放音效文件。
注意
  * 调用 start 方法开始播放音效文件后，可以调用本方法停止播放音效文件。
  * 调用本方法停止播放音效文件后，该音效文件会被自动卸载。


类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopAll() 
停止播放所有音效文件。
注意
  * 调用 start 方法开始播放音效文件后，可以调用本方法停止播放所有音效文件。
  * 调用本方法停止播放所有音效文件后，该音效文件会被自动卸载。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### preload() 
预加载指定音乐文件到内存中，以避免频繁播放同一文件时的重复加载，减少 CPU 占用。
注意
  * 本方法只是预加载指定音效文件，只有调用 start 方法才开始播放指定音效文件。
  * 调用本方法预加载的指定音效文件可以通过 unload 卸载。


类型
```
(effectId: number, filePath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID。用于标识音效，请保证音效 ID 唯一性。如果使用相同的 ID 重复调用本方法，后一次会覆盖前一次。如果先调用 start，再使用相同的 ID 调用本方法 ，会收到回调 onAudioEffectPlayerStateChanged ，通知前一个音效停止，然后加载下一个音效。调用本方法预加载 A.mp3 后，如果需要使用相同的 ID 调用 start 播放 B.mp3，请先调用 unload 卸载 A.mp3 ，否则会报错 AUDIO_MIXING_ERROR_LOAD_CONFLICT。   
filePath| string| 是| 无| 音效文件路径。支持本地文件的 URI、本地文件的绝对路径或以 /assets/ 开头的本地文件路径。预加载的文件长度不得超过 20s。不同平台支持的音效文件格式和 start 一致。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### unload() 
卸载指定音效文件。
注意
仅在调用 start 或 preload 后调用此接口。
类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### unloadAll() 
卸载所有音效文件。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### pause() 
暂停播放音效文件。
注意
  * 调用 start 方法开始播放音效文件后，可以通过调用本方法暂停播放音效文件。
  * 调用本方法暂停播放音效文件后，可调用 resume 方法恢复播放。


类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### pauseAll() 
暂停播放所有音效文件。
注意
  * 调用 start 方法开始播放音效文件后，可以通过调用本方法暂停播放所有音效文件。
  * 调用本方法暂停播放所有音效文件后，可调用 resumeAll 方法恢复所有播放。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### resume() 
恢复播放音效文件。
注意
调用 pause 方法暂停播放音效文件后，可以通过调用本方法恢复播放。
类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### resumeAll() 
恢复播放所有音效文件。
注意
调用 pauseAll 方法暂停所有正在播放音效文件后，可以通过调用本方法恢复播放。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setPosition() 
设置音效文件的起始播放位置。
注意
  * 在播放在线文件时，调用此接口可能造成播放延迟的现象。
  * 仅在调用 start 后调用此接口。


类型
```
(effectId: number, position: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
position| number| 是| 无| 音效文件起始播放位置，单位为毫秒。你可以通过 getDuration 获取音效文件总时长，position 的值应小于音效文件总时长。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getPosition() 
获取音效文件播放进度。
注意
  * 在播放在线文件时，调用此接口可能造成播放延迟的现象。
  * 仅在调用 start 后调用此接口。


类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * >0: 成功, 音效文件播放进度，单位为毫秒。
  * < 0: 失败


### setVolume() 
调节指定音效的音量大小，包括音效文件和 PCM 音频。
注意
仅在调用 start 后调用此接口。
类型
```
(effectId: number, volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
volume| number| 是| 无| 播放音量相对原音量的比值。单位为 %。范围为 [0, 400]，建议范围是 [0, 100]。带溢出保护。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVolumeAll() 
设置所有音效的音量大小，包括音效文件和 PCM 音效。
注意
该接口的优先级低于 setVolume，即通过 setVolume 单独设置了音量的音效 ID，不受该接口设置的影响。
类型
```
(volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 播放音量相对原音量的比值。单位为 %。范围为 [0, 400]，建议范围是 [0, 100]。带溢出保护。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getVolume() 
获取当前音量。
注意
仅在调用 start 后调用此接口。
类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * >0: 成功, 当前音量值。
  * < 0: 失败


### getDuration() 
获取音效文件时长。
注意
仅在调用 start 后调用此接口。
类型
```
(effectId: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| 音效 ID   
返回值
number
  * >0: 成功, 音效文件时长，单位为毫秒。
  * < 0: 失败


### setEventHandler() 
设置回调句柄。
类型
```
(handler: IAudioEffectPlayerEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
handler| IAudioEffectPlayerEventHandler| 是| 无| 参看 IAudioEffectPlayerEventHandler。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


## IMediaPlayer 
类型：class
音乐播放器 调用 setEventHandler 设置回调句柄以获取相关回调。
### open() 
打开音乐文件。 一个播放器实例仅能够同时打开一个音乐文件。如果需要同时打开多个音乐文件，请创建多个音乐播放器实例。 要播放 PCM 格式的音频数据，参看 openWithCustomSource。openWithCustomSource 和此 API 互斥。
类型
```
(filePath: string, config: MediaPlayerConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
filePath| string| 是| 无| 音乐文件路径。支持在线文件的 URL、本地文件的 URI、本地文件的绝对路径或以 /assets/ 开头的本地文件路径。对于在线文件的 URL，仅支持 https 协议。推荐的采样率：8KHz、16KHz、22.05KHz、44.1KHz、48KHz。不同平台支持的本地文件格式:mp3mp4aacm4a3gpwavoggtswmaAndroidYYYYYYYiOS/macOSYYYYYYWindowsYYYYYYYY不同平台支持的在线文件格式:mp3mp4aacm4a3gpwavoggtswmaAndroidYYYYYiOS/macOSYYYYWindowsYYYYYYY   
config| MediaPlayerConfig| 是| 无| 详见 MediaPlayerConfig。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### start() 
播放音乐。你仅需要在调用 open，且未开启自动播放时，调用此方法。
注意
  * 要播放 PCM 格式的音频数据，参看 openWithCustomSource。openWithCustomSource 和此 API 互斥。
  * 调用本方法播放音频文件后，可调用 stop 方法暂停播放。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### openWithCustomSource() 
启动音频裸数据混音。 要播放音乐文件，参看 open。open 与此 API 互斥。
注意
  * 调用本方法启动后，再调用 pushExternalAudioFrame 推送音频数据，才会开始混音。
  * 如要结束 PCM 音频数据混音，调用 stop。


类型
```
(source: MediaPlayerCustomSource, config: MediaPlayerConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
source| MediaPlayerCustomSource| 是| 无| 数据源，详见 MediaPlayerCustomSource   
config| MediaPlayerConfig| 是| 无| 详见 MediaPlayerConfig   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stop() 
调用 open, start, 或 openWithCustomSource 开始播放后，可以调用本方法停止。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### pause() 
调用 open，或 start 开始播放音频文件后，调用本方法暂停播放。
注意
  * 调用本方法暂停播放后，可调用 resume 恢复播放。
  * 此接口仅支持音频文件，不支持 PCM 数据。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### resume() 
调用 pause 暂停音频播放后，调用本方法恢复播放。
注意
此接口仅支持音频文件，不支持 PCM 数据。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVolume() 
调节指定混音的音量大小，包括音乐文件混音和 PCM 混音。
注意
仅在音频播放进行状态时，调用此方法。
类型
```
(volume: number, type: AudioMixingType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 播放音量相对原音量的比值。单位为 %。范围为 [0, 400]，建议范围是 [0, 100]。带溢出保护。   
type| AudioMixingType| 是| 无| 详见 AudioMixingType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getVolume() 
获取当前音量
注意
仅在音频播放进行状态时，调用此方法。包括音乐文件混音和 PCM 混音。
类型
```
(type: AudioMixingType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| AudioMixingType| 是| 无| 详见 AudioMixingType。   
返回值
number
  * >0: 成功, 当前音量值。
  * < 0: 失败


### getTotalDuration() 
获取音乐文件时长。
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此接口仅支持音频文件，不支持 PCM 数据。


类型
```
() => number

ts

```

返回值
number
  * >0: 成功, 音乐文件时长，单位为毫秒。
  * < 0: 失败


### getPlaybackDuration() 
获取混音音乐文件的实际播放时长，单位为毫秒。
注意
  * 实际播放时长指的是歌曲不受停止、跳转、倍速、卡顿影响的播放时长。例如，若歌曲正常播放到 1:30 时停止播放 30s 或跳转进度到 2:00, 随后继续正常播放 2 分钟，则实际播放时长为 3 分 30 秒。
  * 仅在音频播放进行状态，且 setProgressInterval 设置间隔大于 0 时，调用此方法。
  * 此接口仅支持音频文件，不支持 PCM 数据。


类型
```
() => number

ts

```

返回值
number
  * >0: 实际播放时长。
  * < 0: 失败。


### getPosition() 
获取音乐文件播放进度。
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此接口仅支持音频文件，不支持 PCM 数据。


类型
```
() => number

ts

```

返回值
number
  * >0: 成功, 音乐文件播放进度，单位为毫秒。
  * < 0: 失败


### setAudioPitch() 
开启变调功能，多用于 K 歌场景。
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 仅支持音乐文件混音，不支持 PCM 数据。


类型
```
(pitch: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
pitch| number| 是| 无| 与音乐文件原始音调相比的升高/降低值，取值范围为 [-12，12]，默认值为 0。每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setPosition() 
设置音乐文件的起始播放位置。
注意
  * 此接口仅支持音频文件，不支持 PCM 数据。
  * 在播放在线文件时，调用此接口可能造成播放延迟的现象。


类型
```
(position: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
position| number| 是| 无| 音乐文件起始播放位置，单位为毫秒。你可以通过 getTotalDuration 获取音乐文件总时长，position 的值应小于音乐文件总时长。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAudioDualMonoMode() 
设置当前音乐文件的声道模式
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 仅支持音频文件，不支持 PCM 数据。


类型
```
(mode: AudioMixingDualMonoMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mode| AudioMixingDualMonoMode| 是| 无| 声道模式。默认的声道模式和源文件一致，详见 AudioMixingDualMonoMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getAudioTrackCount() 
获取当前音乐文件的音轨数
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此方法仅支持音乐文件，不支持 PCM 数据。


类型
```
() => number

ts

```

返回值
number
  * > = 0：成功，返回当前音乐文件的音轨数


  * < 0：方法调用失败


### selectAudioTrack() 
指定当前音乐文件的播放音轨
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此方法仅支持音乐文件，不支持 PCM 数据。


类型
```
(index: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| number| 是| 无| 指定的播放音轨，从 0 开始，取值范围为 [0, getAudioTrackCount()-1]。设置的参数值需要小于 getAudioTrackCount 的返回值   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setPlaybackSpeed() 
设置播放速度
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此方法对音频文件可用，不支持 PCM 数据。


类型
```
(speed: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
speed| number| 是| 无| 播放速度与原始文件速度的比例，单位：%，取值范围为 [50,200]，默认值为 100。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setProgressInterval() 
设置音频文件混音时，收到 onMediaPlayerPlayingProgress 的间隔。
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此方法仅支持音频文件，不支持 PCM 数据。


类型
```
(interval: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
interval| number| 是| 无| 时间间隔，单位毫秒。 interval > 0 时，触发回调。实际间隔为 10 的倍数。如果输入数值不能被 10 整除，将自动向上取整。例如传入 52，实际间隔为 60 ms。 interval <= 0 时，不会触发回调。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setLoudness() 
如果你需要使用 enableVocalInstrumentBalance 对音频文件/PCM 音频数据设置音量均衡，你必须通过此接口传入其原始响度。
注意
  * 仅在音频播放进行状态时，调用此方法。
  * 此方法对音频文件和音频裸数据播放都可用。


类型
```
(loudness: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
loudness| number| 是| 无| 原始响度，单位：lufs，取值范围为 [-70.0, 0.0]。当设置的值小于 -70.0lufs 时，则默认调整为 -70.0lufs，大于 0.0lufs 时，则不对该响度做音量均衡处理。默认值为 1.0lufs，即不做处理。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerAudioFrameObserver() 
注册回调句柄以在本地音乐文件混音时，收到相关回调。
注意
此接口仅支持音频文件，不支持 PCM 数据。
类型
```
(observer: IMediaPlayerAudioFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IMediaPlayerAudioFrameObserver| 是| 无| 参看 IMediaPlayerAudioFrameObserver。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### pushExternalAudioFrame() 
推送用于混音的 PCM 音频帧数据
注意
  * 调用该方法前，须通过 openWithCustomSource 启动外部音频流混音。
  * 使用参考建议：首次推送数据，请在应用侧先缓存一定数据（如 200 毫秒），然后一次性推送过去；此后的推送操作定时 10 毫秒一次，并且每次的音频数据量为 10 毫秒数据量。
  * 如果要暂停播放，暂停推送即可。


类型
```
(audioFrame: AudioFrame) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioFrame| AudioFrame| 是| 无| 音频帧，详见 AudioFrame。 音频采样格式必须为 S16。音频缓冲区内的数据格式必须为 PCM，其容量大小应该为 audioFrame.samples × audioFrame.channel × 2。 必须指定具体的采样率和声道数，不支持设置为自动。   
返回值
number
  * 0: 成功
  * < 0: 失败


### setEventHandler() 
设置回调句柄。
类型
```
(handler: IMediaPlayerEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
handler| IMediaPlayerEventHandler| 是| 无| 参看 IMediaPlayerEventHandler。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


## ISingScoringManager 
类型：class
K 歌评分管理接口。
### setSingScoringConfig() 
设置 K 歌评分参数。
类型
```
(config: SingScoringConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| SingScoringConfig| 是| 无| K 歌评分的各项参数，详见 SingScoringConfig。   
返回值
number
### startSingScoring() 
开始 K 歌评分。
注意
  * 在调用 initSingScoring 初始化 K 歌评分功能后调用该接口。
  * 调用该接口后，将会根据设置的回调时间间隔，收到评分结果 onCurrentScoringInfo 回调。
  * 如果调用 startAudioMixing 接口播放音频文件，请在收到 onAudioMixingStateChanged(AUDIO_MIXING_STATE_PLAYING(1)) 之后调用此接口。


类型
```
(position: number, scoringInfoInterval: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
position| number| 是| 无| 开始评分时，音乐的播放进度，单位：ms。   
scoringInfoInterval| number| 是| 无| 实时回调的时间间隔，单位：ms；默认 50 ms。最低间隔为 20 ms。   
返回值
number
### stopSingScoring() 
停止 K 歌评分。
类型
```
() => number

ts

```

返回值
number
  * 0：成功。
  * <0：失败。


### getLastSentenceScore() 
获取上一句的演唱评分。调用 startSingScoring 开始评分后可以调用该接口。
类型
```
() => number

ts

```

返回值
number
  * <0：获取评分失败。
  * >=0：上一句歌词的演唱评分。


### getTotalScore() 
获取当前演唱总分。调用 startSingScoring 开始评分后可以调用该接口。
类型
```
() => number

ts

```

返回值
number
  * <0：获取总分失败。
  * >=0：当前演唱总分。


### getAverageScore() 
获取当前演唱歌曲的平均分。
类型
```
() => number

ts

```

返回值
number
  * <0：获取平均分失败。
  * >=0：当前演唱平均分。


## IKTVManager 
类型：class
KTV 管理接口。
### ios_delegate 
类型：$p_i.ByteRTCKTVManagerDelegate
### android_setKTVManagerEventHandler() 
设置 KTV 回调接口。
类型
```
(ktvManagerEventHandler: $p_a.IKTVManagerEventHandler) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
ktvManagerEventHandler| $p_a.IKTVManagerEventHandler| 是| 无| KTV 回调类，参看 IKTVManagerEventHandler。   
### setMaxCacheSize() 
设置歌曲文件最大占用的本地缓存。
类型
```
(maxCacheSizeMB: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
maxCacheSizeMB| number| 是| 无| 本地缓存，单位 MB。设置值小于等于 0 时，使用默认值 1024 MB。   
### getMusicDetail() 
获取音乐详细信息。
注意
调用接口后，你会收到 onMusicDetailResult 回调。
类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### downloadMusic() 
下载音乐。
注意
  * 若音乐下载成功，你会收到 onDownloadSuccess 回调。
  * 若音乐下载失败，你会收到 onDownloadFailed 回调。
  * 音乐下载进度更新时，你会收到 onDownloadMusicProgress 回调。


类型
```
(musicId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
返回值
number 下载任务 ID。
### downloadLyric() 
下载歌词。
注意
  * 若歌词下载成功，你会收到 onDownloadSuccess 回调。
  * 若歌词下载失败，你会收到 onDownloadFailed 回调。


类型
```
(musicId: string, lyricType: DownloadLyricType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
lyricType| DownloadLyricType| 是| 无| 歌词文件类型，参看 DownloadLyricType。   
返回值
number 下载任务 ID。
### downloadMidi() 
下载 MIDI 文件。
注意
  * 若文件下载成功，你会收到 onDownloadSuccess 回调。
  * 若文件下载失败，你会收到 onDownloadFailed 回调。


类型
```
(musicId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
返回值
number 下载任务 ID。
### cancelDownload() 
取消下载任务。
类型
```
(downloadId: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
downloadId| number| 是| 无| 下载任务 ID。   
### clearCache() 
清除当前音乐缓存文件，包括音乐音频和歌词。
类型
```
() => void

ts

```

### getKTVPlayer() 
获取 KTV 播放器。
类型
```
() => IKTVPlayer

ts

```

返回值
IKTVPlayer KTV 播放器接口，参看 。
## IAudioMixingManager 
类型：class
混音管理类
已弃用
since 353. Use and instead.




## 使用 Token 完成鉴权--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/70121


  * 文档首页
/实时音视频/开发指南/鉴权/使用 Token 完成鉴权

使用 Token 完成鉴权
最近更新时间：2025.06.27 14:28:46首次发布时间：2021.07.21 14:29:15

文档反馈
Token 是 RTC 用于验证客户端身份和权限的安全凭证，由您的应用服务端生成，并下发给应用客户端。客户端在加入 RTC 房间或登录实时消息服务时，必须携带有效的 Token。RTC 服务端在收到进房请求时会校验此 Token，以确保请求的合法性。如果校验不成功，进房会失败。
## 鉴权流程
整个鉴权生命周期分为两个阶段：首次进房和 Token 过期更新。
说明
你需要自行实现步骤 1，2，3，4，11 的代码逻辑。
  1. 客户端向你的应用服务端申请 Token。
  2. 应用服务端根据 AppID、RTC AppKey、RoomID、UserID、时间戳等信息生成 Token。
  3. 应用服务端将生成的 Token 下发给客户端。
  4. 客户端使用获取到的 Token 申请加入房间。 
> 加入房间时设置的 uid 和 roomid 需与用于生成 Token 的 uid 和 roomid 保持一致，否则会加入房间失败，并收到错误提示为 ERROR_CODE_INVALID_TOKEN 的 onRoomStateChanged 回调。
  5. RTC 服务端验证 Token 的合法性。
  6. 应用客户端收到来自 RTC SDK 的回调，获取加入房间的结果（成功/失败）。
  7. 若生成 Token 时设置了有效期，当 SDK 检测到 Token 的进房权限将在 30 秒内过期时，触发 onTokenWillExpire 回调。 
> Token 过期后，用户将被移出房间，并收到 ERROR_CODE_INVALID_TOKEN 回调，错误码是 ERROR_CODE_TOKEN_EXPIRED。需要在申请新的 Token 之后调用 JoinRoom 加入房间。
  8. 此时，如果客户端需要继续进行音视频通话，需要申请新的 Token。
  9. 如步骤 2。
  10. 如步骤 3。
  11. 调用 updateToken 接口，使用新的 Token，更新 Token。


## 生成 Token
在您的应用服务端实现 Token 的生成，可以参考以下示例代码（包含了多种语言）：
RTC_Token.zip
47.53KB
以 Golang 为例：
```
var (
// 确保通话时使用的 appID， roomID 和 userID 与用于生成 Token 的相同，否则会导致进房失败。
  appID = "xxxxx" 
  appKey = "xxxxx" 
  roomID = "room" // 生成用于登录实时消息服务的 Token 时传空值
  userID = "uid"
)
t := AccessToken.New(appID, appKey, roomID, userID)
// 添加此 Token 的有效时间，两小时过期。过期后，你无法使用此 Token 进房。
t.ExpireTime(time.Now().Add(time.Hour * 2))
// 添加订阅流权限
t.AddPrivilege(AccessToken.PrivSubscribeStream, time.Time{})
// 添加发布流权限
t.AddPrivilege(AccessToken.PrivPublishStream, time.Time{})
// 获取最终生成的 token
token,err := t.Serialize()

go

```

## 生成通配 Token
在需要频繁切换房间的场景下，可以使用通配 Token，解决频繁请求 Token 可能造成的进房延误和 Token 服务端压力过大等问题。同一用户使用通配 Token 可以加入不同的 RTC 房间。
## 生成临时 Token
在开发和测试阶段，为快速验证业务逻辑，你可以在 RTC 控制台上生成临时 Token，无需在应用服务端部署 Token 生成服务。
> 临时 Token 有效期仅为 7 天且安全性低， 仅适用于测试阶段，严禁用于生产环境。项目正式上线前，请务必切换为由你应用服务端生成的正式 Token。
获取方式：前往 ，选择您的应用，在操作栏单击临时Token。
  * 测试音视频通话时：当需要多个设备加入同一个房间进行通话时，请为每个设备都生成一个独立的 Token，且房间 ID 需相同、用户 ID 必须不同。
  * 仅测试实时消息时：房间 ID 可填写任意值，用户 ID 需为目标登录用户。


## Token 使用常见问题
常见问题参看。




## 管理 AppKey--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/128824


  * 文档首页
/实时音视频/开发指南/鉴权/管理 AppKey

管理 AppKey
最近更新时间：2025.03.03 17:06:18首次发布时间：2022.08.18 21:06:19

文档反馈
无论是在开发调试，还是正式生产环境中，你都需要使用 AppKey 来生成 Token。 调试时，在控制台生成临时 Token；正式上线后，Token 在业务服务器中生成，详见 。你可以根据项目的安全需求，选择是否启用副 AppKey。 激活副 AppKey 后，一主一副两个 AppKey，同时有效。即，使用其中任意一个 AppKey 生成的 Token 均能正常使用 RTC 服务。 当主 AppKey 遭遇泄漏时，切换主 AppKey 和副 AppKey，当所有业务切换到新的主 AppKey 后，再停用原来的主 Appkey，及时减少损失。
## 更换 AppKey
在业务上线后，更换 AppKey 可能对业务产生较大影响，我们建议只在必要情况下更换 AppKey，例如遭遇 AppKey 泄漏问题。切换主/副 AppKey 会带来以下影响：
  * 已停用的 AppKey 生成的 Token 将全部失效，用户无法使用这些 Token 加入房间。在停用原 AppKey 之前，请确保业务服务端用于生成 Token 的 AppKey 是有效的。
  * AppKey 一旦删除就无法恢复。


确认需要更换 AppKey 后，按照如下流程进行操作
  1. 在 控制台 > 应用管理，找到应用，点击 应用配置。
  2. 在副 AppKey 标签上点击开关按钮，激活副 AppKey。激活后，主和副 AppKey 生成的 Token 都可用于进房和登录的鉴权。
  3. 点击切换按钮，切换主/副 AppKey。


> 说明：主 AppKey 无法停用和删除，因此，需要先切换主/副 AppKey。
  1. 上个步骤执行 3 天之后，点击开关按钮停用副 AppKey。如果账号已绑定安全手机，二次确认后验证手机号，验证成功后停用。之后，使用该 Appkey 生成的 Token 均为无效 Token。


> 说明：停用前使用该 Appkey 生成的 Token 进入房间内的用户不受影响。
  1. 删除副 AppKey。
  2. 按照步骤 2，重新创建并激活新的副 AppKey。






## 服务端 OpenAPI 简介--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/69827


  * 文档首页
/实时音视频/服务端 API 参考/服务端 OpenAPI 简介

服务端 OpenAPI 简介
最近更新时间：2023.12.13 16:13:01首次发布时间：2021.07.16 22:20:01

文档反馈
已经成功在你的应用中使用 RTC 实现音视频通信后，如果你需要在服务端控制音视频通话，你可以使用 OpenAPI。
关于如何调用 OpenAPI，参看 。
## 前提条件
  1. 注册账号，获取对应的 AK/SK。相关信息在对应环境的 页面可以看到。
  2. 在控制台开通 RTC 服务。


> 我们强烈推荐您以主账号权限，创建用户（子账号），并使用用户的 AK/SK 发起 OpenAPI 调用。要创建用户，访问控制台-访问控制-用户管理 路径。




## 请求结构--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/69828


  * 文档首页
/实时音视频/服务端 API 参考/调用方法/请求结构

请求结构
最近更新时间：2024.10.14 17:27:07首次发布时间：2021.07.16 22:21:13

文档反馈
您可使用 OpenAPI 向 RTC 的服务端地址发送请求，并按照接口说明在请求中加入相应请求参数。系统会根据请求的处理情况，返回处理结果。本章节为您介绍 RTC OpenAPI 的调用方法，包括但不限于请求结构、公共参数、签名机制和公共错误码
### 服务接入地址
RTC 在全球多个地域部署，请您参考下表来使用对应的接入地址。如果接口不支持该表中的所有地域，则会在接口文档中单独说明。
地域名称| Region| 接入地址  
---|---|---  
华北| cn-north-1| rtc.volcengineapi.com  
亚太东南（柔佛）| ap-southeast-1| open-ap-singapore-1.volcengineapi.com  
### 通信协议
你可以使用 HTTP 和 HTTPS 两种协议进行请求通信。我们强烈推荐你使用安全性更高的 HTTPS 方式发送请求。
### 字符编码
请求及返回结果使用 UTF-8 字符集进行编码。
### 接口限制
具体值请参看各个接口描述。
当前以火山引擎账号维度进行限制, 账号下多个 AppId 之间共享限流额度。
### 请求方法
根据各个接口的具体需求，选择 Get 或 Post 方式发起请求。
### 请求参数
公共请求参数参看公共参数。
各接口特有请求参数参看各接口描述。
对于 ID 类请求参数（BusinessId，TaskId，RoomId 和 UserId），必须遵循统一的 ID 命名规范： 字符串长度不超过 128 字符； 字符串中仅包含以下的字符： a~z （小写英文字符） A~Z （大写英文字符） 0~9 （数字） @ . _ -
### 构造 URI
请求 URI 的组成结构：{URI-scheme}://{Endpoint}/?{Query-string}。
参数说明如下表所示。
参数| 描述  
---|---  
URI-scheme| 表示用于传输请求的协议，支持通过 HTTP 和 HTTPS 2 种方式进行请求通信。  
Endpoint| API 的服务接入地址。
  * 中国大陆：rtc.volcengineapi.com。
  * 其他国家/地区：open-ap-singapore-1.volcengineapi.com

  
Query-string| 查询字符串，包含公共参数和 GET 请求中的查询参数。
* 公共参数：需要包含 Action 和 Version 参数；参数前面需要带一个“?”，公共参数之间用“&”相连。
* 查询参数(GET)：从具体的 OpenAPI 接口文档中获取；查询参数前面需要带一个“?”，形式为“参数名=参数取值”，参数之间用“&”相连。例如“?limit=5”，表示查询不超过 5 条数据。  
请求示例
```
POST https://rtc.volcengineapi.com?Action=BanRoomUser&Version=2020-12-01
{
  "AppId": "Your_AppId",
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "ForbiddenInterval": 0
}

```

返回结果
```
{
  "ResponseMetadata": {
    "RequestId": "Your_RequestId",
    "Action": "BanRoomUser",
    "Version": "2020-12-01",
    "Service": "rtc",
    "Region": "cn-north-1"
  },
  "Result": {
    "message": "success"
  }
}

```





## 签名方法--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/69859


  * 文档首页
/实时音视频/服务端 API 参考/调用方法/签名方法

签名方法
最近更新时间：2025.06.30 17:50:02首次发布时间：2021.07.16 22:21:54

文档反馈
为了保证请求者身份的合法性以及请求在传输过程中不被恶意篡改，火山引擎签名机制要求请求者对请求参数进行哈希值计算，经过加密后同 API 请求一起发送到服务器中，服务器将以同样的机制对收到的请求进行签名计算，并以此与请求者传来的签名进行比对，若签名未通过验证，请求将被拒绝。
如何进行签名计算及签名 SDK 示例，请参见。




## 返回结构--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1178322


  * 文档首页
/实时音视频/服务端 API 参考/调用方法/返回结构

返回结构
最近更新时间：2024.07.16 17:04:59首次发布时间：2023.12.08 15:01:59

文档反馈
## 2020-12-01
  * 房间管理、云端媒体处理、公共流、应用管理、业务标识管理、实时消息通信模块下： 当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。 当 HTTP 响应状态码 ==200 时，你仍需解析返回结构中的 BaseResponse.ResponseMetaData.Error 结构体。当 Error 为空时，表示调用成功。


### 返回结构
BaseResponse 返回结构如下：
参数名| 类型| 描述  
---|---|---  
ResponseMetadata|   
Result| interface{}| 仅在请求成功时返回, 具体值参考每个 API 的说明。  
ResponseMetadata 
参数名| 类型| 示例值| 描述  
---|---|---|---  
RequestId| String| Your_RequestId| 请求标识  
Action| String| StartRecord| 接口名称  
Version| String| 2020-12-01| 接口版本  
Service| String| rtc| 接口所属服务  
Region| String| cn-north-1| 地域参数：
* cn-north-1 (华北)
* ap-singapore-1 (新加坡)
* us-east-1 (美东)  
Error| | -| 仅在请求失败时返回。  
ErrorInfo 错误的结构定义。
参数名称| 类型| 描述  
---|---|---  
CodeN| Uint32| 网关的错误码。（请求失败时返回）  
Code| String| API 的错误码，参看。  
Message| String| 具体的错误信息  
### 返回结构示例
#### 调用成功：
```
{
    "ResponseMetadata": {
      "RequestId": "Your_RequestId",
       "Action": "DismissRoom",
      "Version": "2020-12-01",
      "Service": "rtc",
      "Region": "cn-north-1"
    },
    "Result":{
      "Message": "success"
    }
  }

json

```

#### 调用失败：
```
{
     "ResponseMetadata": {
      "RequestId": "Your_RequestId",
      "Action": "DismissRoom",
      "Version": "2020-12-01",
      "Service": "rtc",
      "Region": "cn-north-1"
      "Error":{
        "Code": "InvalidParameter.AccountIdMismatch",
        "Message": "Check owner failed"
    }
  }，
    "Result": {}    
  }

json

```

  * 获取数据指标和歌曲查询模块下：


当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 和 BaseResponse.Result 字段解析错误原因。 当 HTTP 响应状态码 ==200 时，表示调用成功。
### 返回结构
BaseResponse 返回结构如下：
参数名| 类型| 描述  
---|---|---  
ResponseMetadata|   
Result| interface{}| 具体值参考每个 API 的说明。  
ResponseMetadata 
参数名| 类型| 示例值| 描述  
---|---|---|---  
RequestId| String| Your_RequestId| 请求标识  
Action| String| StartRecord| 接口名称  
Version| String| 2020-12-01| 接口版本  
Service| String| rtc| 接口所属服务  
Region| String| cn-north-1| 地域参数：
* cn-north-1 (华北)
* ap-singapore-1 (新加坡)
* us-east-1 (美东)  
Error| | -| 仅在请求失败时返回。  
ErrorInfo 错误的结构定义。
参数名称| 类型| 描述  
---|---|---  
CodeN| Uint32| 网关的错误码  
Code| String| API 的错误码，参看。  
Message| String| 具体的错误信息  
### 返回结构示例
#### 调用成功：
```
{
    "ResponseMetadata": {
      "RequestId": "Your_RequestId",
       "Action": "DismissRoom",
      "Version": "2020-12-01",
      "Service": "rtc",
      "Region": "cn-north-1"
    },
    "Result":{
      "Message": "success"
    }
  }

json

```

#### 调用失败：
```
{
     "ResponseMetadata": {
      "RequestId": "Your_RequestId",
      "Action": "DismissRoom",
      "Version": "2020-12-01",
      "Service": "rtc",
      "Region": "cn-north-1"
      "Error":{
        "Code": "InvalidParameter.AccountIdMismatch",
        "Message": "Check owner failed"
    }
  }，
    "Result": {}    
  }

json

```

## 2022-06-01
当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。 当 HTTP 响应状态码 ==200 时，你仍需解析返回结构中的 BaseResponse.ResponseMetaData.Error 结构体。当 Error 为空时，表示调用成功。
### 返回结构
BaseResponse 返回结构如下：
参数名| 类型| 描述  
---|---|---  
ResponseMetadata|   
Result| interface{}| 仅在请求成功时返回, 具体值参考每个 API 的说明。  
ResponseMetadata 
参数名| 类型| 示例值| 描述  
---|---|---|---  
RequestId| String| Your_RequestId| 请求标识  
Action| String| StartRecord| 接口名称  
Version| String| 2022-06-01| 接口版本  
Service| String| rtc| 接口所属服务  
Region| String| cn-north-1| 地域参数：
* cn-north-1 (华北)
* ap-singapore-1 (新加坡)
* us-east-1 (美东)  
Error| | -| 仅在请求失败时返回。  
ErrorInfo 错误的结构定义。
参数名称| 类型| 描述  
---|---|---  
CodeN| Uint32| 网关的错误码  
Code| String| API 的错误码，参看。  
Message| String| 具体的错误信息  
### 返回结构示例
#### 调用成功：
```
{
    "ResponseMetadata": {
      "RequestId": "Your_RequestId",
       "Action": "DismissRoom",
      "Version": "2022-06-01",
      "Service": "rtc",
      "Region": "cn-north-1"
    },
    "Result":{
      "Message": "success"
    }
  }

json

```

#### 调用失败：
```
{
     "ResponseMetadata": {
      "RequestId": "Your_RequestId",
      "Action": "DismissRoom",
      "Version": "2022-06-01",
      "Service": "rtc",
      "Region": "cn-north-1"
      "Error":{
        "CodeN": 10009,
        "Code": "InvalidParameter.AccountIdMismatch",
        "Message": "Check owner failed"
    }
  }，
    "Result": {}    
  }

json

```

## 2023-06-01
当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。 当 HTTP 响应状态码 ==200 时，你仍需解析返回结构中的 BaseResponse.ResponseMetaData.Error 结构体。当 Error 为空时，表示调用成功。
### 返回结构
BaseResponse 返回结构如下：
参数名| 类型| 描述  
---|---|---  
ResponseMetadata|   
Result| interface{}| 仅在请求成功时返回, 具体值参考每个 API 的说明。  
ResponseMetadata 
参数名| 类型| 示例值| 描述  
---|---|---|---  
RequestId| String| Your_RequestId| 请求标识  
Action| String| StartRecord| 接口名称  
Version| String| 2023-06-01| 接口版本  
Service| String| rtc| 接口所属服务  
Region| String| cn-north-1| 地域参数：
* cn-north-1 (华北)
* ap-singapore-1 (新加坡)
* us-east-1 (美东)  
Error| | -| 仅在请求失败时返回。  
ErrorInfo 错误的结构定义。
参数名称| 类型| 描述  
---|---|---  
CodeN| Uint32| 网关的错误码  
Code| String| API 的错误码，参看。  
Message| String| 具体的错误信息  
### 返回结构示例
#### 调用成功：
```
{
    "ResponseMetadata": {
      "RequestId": "Your_RequestId",
       "Action": "DismissRoom",
      "Version": "2023-06-01",
      "Service": "rtc",
      "Region": "cn-north-1"
    },
    "Result":{
      "Message": "success"
    }
  }

json

```

#### 调用失败：
```
{
     "ResponseMetadata": {
      "RequestId": "Your_RequestId",
      "Action": "DismissRoom",
      "Version": "2023-06-01",
      "Service": "rtc",
      "Region": "cn-north-1"
      "Error":{
        "CodeN": 10009,
        "Code": "InvalidParameter.AccountIdMismatch",
        "Message": "Check owner failed"
    }
  }，
    "Result": {}    
  }

json

```

## 2023-07-20
当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。 当 HTTP 响应状态码 ==200 时，表示调用成功。
### 返回结构
BaseResponse 返回结构如下：
参数名| 类型| 描述  
---|---|---  
ResponseMetadata|   
Result| interface{}| 具体值参考每个 API 的说明。  
ResponseMetadata 
参数名| 类型| 示例值| 描述  
---|---|---|---  
RequestId| String| Your_RequestId| 请求标识  
Action| String| StartRecord| 接口名称  
Version| String| 2023-07-20| 接口版本  
Service| String| rtc| 接口所属服务  
Region| String| cn-north-1| 地域参数：
* cn-north-1 (华北)
* ap-singapore-1 (新加坡)
* us-east-1 (美东)  
Error| | -| 仅在请求失败时返回。  
ErrorInfo 错误的结构定义。
参数名称| 类型| 描述  
---|---|---  
Code| String| API 的错误码，参看。  
Message| String| 具体的错误信息  
### 返回结构示例
#### 调用成功：
```
{
    "ResponseMetadata": {
      "RequestId": "Your_RequestId",
       "Action": "DismissRoom",
      "Version": "2023-07-20",
      "Service": "rtc",
      "Region": "cn-north-1"
    },
    "Result":{
      "Message": "success"
    }
  }

json

```

#### 调用失败：
```
{
     "ResponseMetadata": {
      "RequestId": "Your_RequestId",
      "Action": "DismissRoom",
      "Version": "2023-07-20",
      "Service": "rtc",
      "Region": "cn-north-1"
      "Error":{
        "Code": "InvalidParameter.AccountIdMismatch",
        "Message": "Check owner failed"
    }
  }，
    "Result": {}    
  }

json

```

## 2023-11-01 & 2024-06-01
当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。 当 HTTP 响应状态码 ==200 时，表示调用成功。
### 返回结构
BaseResponse 返回结构如下：
参数名| 类型| 描述  
---|---|---  
ResponseMetadata|   
Result| interface{}| 具体值参考每个 API 的说明。  
ResponseMetadata 
参数名| 类型| 示例值| 描述  
---|---|---|---  
RequestId| String| Your_RequestId| 请求标识  
Action| String| StartRecord| 接口名称  
Version| String| 2023-11-01| 接口版本  
Service| String| rtc| 接口所属服务  
Region| String| cn-north-1| 地域参数：
* cn-north-1 (华北)
* ap-singapore-1 (新加坡)
* us-east-1 (美东)  
Error| | -| 仅在请求失败时返回。  
ErrorInfo 错误的结构定义。
参数名称| 类型| 描述  
---|---|---  
Code| String| API 的错误码，参看。  
Message| String| 具体的错误信息  
### 返回结构示例
#### 调用成功：
```
{
    "ResponseMetadata": {
      "RequestId": "Your_RequestId",
       "Action": "DismissRoom",
      "Version": "2023-11-01",
      "Service": "rtc",
      "Region": "cn-north-1"
    },
    "Result":{
      "Message": "success"
    }
  }

json

```

#### 调用失败：
```
{
     "ResponseMetadata": {
      "RequestId": "Your_RequestId",
      "Action": "DismissRoom",
      "Version": "2023-11-01",
      "Service": "rtc",
      "Region": "cn-north-1"
      "Error":{
        "Code": "InvalidParameter.AccountIdMismatch",
        "Message": "Check owner failed"
    }
  }，
    "Result": {}    
  }

json

```





## 封禁音视频流 BanUserStream--

Source: https://www.volcengine.com/docs/6348/1188354





## 解封音视频流 UnbanUserStream--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1188356


  * 文档首页
/实时音视频/服务端 API 参考/房间管理/解封音视频流 UnbanUserStream

解封音视频流 UnbanUserStream
最近更新时间：2025.07.03 18:15:27首次发布时间：2024.01.03 20:25:19

文档反馈
> 本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考。
在实时音视频通话场景中，若需恢复特定用户在房间内的音视频流发布权限，你可通过调用此接口实现。
通过指定应用标识、房间ID及用户ID，并提供相应的权限验证Token，可选择性地解封用户的视频或音频流。房间内其他用户将通过回调通知获知变更。调用后，接口将返回操作结果，确认用户流是否成功解封。
  * 房间内指定用户被解禁视频流发送时，房间内所有用户都会收到 onVideoStreamBanned 回调。
  * 房间内指定用户被解禁音频流发送时，房间内所有用户都会收到 onAudioStreamBanned 回调


## 注意事项
请求频率：QPS 不得超过 60。
## 请求说明
  * 请求方式：POST
  * 请求地址：https://rtc.volcengineapi.com?Action=UnbanUserStream&Version=2023-11-01


## 调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
## 请求参数
下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见。
### Query
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
Action| String| 是| UnbanUserStream| 接口名称。当前 API 的名称为 UnbanUserStream。  
Version| String| 是| 2023-11-01| 接口版本。当前 API 的版本为 2023-11-01。  
### Body
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
AppId| String| 是| 661e****543cf| 你的音视频应用的唯一标志，参看获取 AppId。  
RoomId| String| 是| Room1| 房间的 ID，是房间的唯一标志  
UserId| String| 是| User1| 需要被解封音/视频流的用户的 ID  
Video| Boolean| 否| true| 是否解封视频流。 true：解封视频流。 false：视频流维持当前状态。 默认值为 false。  
Audio| Boolean| 否| true| 是否解封音频流。 true：解封音频流。 false：音频流维持当前状态。 默认值为 false。  
## 返回参数
下表仅列出本接口特有的返回参数，公共返回参数请参见。
参数| 类型| 示例值| 描述  
---|---|---|---  
Message| String| success| 仅在请求成功时返回消息 "success", 失败时为空。  
## 请求示例
```
POST https://rtc.volcengineapi.com?Action=UnbanUserStream&Version=2023-11-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "UserId": "user1",
  "Token": "001661e****543cf80",
  "Video": true,
  "Audio": false
}

json

```

## 返回示例
```
{
  "ResponseMetadata": {
    "RequestId": "20230****10420",
    "Action": "UnbanUserStream",
    "Version": "2023-11-01",
    "Service": "rtc",
    "Region": "cn-north-1"
  },
  "Result": {
    "Message": "success"
  }
}

json

```

## 错误码
您可访问，获取更多错误码信息。
相关文档
相关产品
业务咨询



## 方案集成前置准备--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1315561


  * 文档首页
/实时音视频/实时对话式 AI/方案集成/方案集成前置准备

方案集成前置准备
最近更新时间：2025.06.12 17:48:16首次发布时间：2024.07.16 17:04:59

文档反馈
本文提供集成实时对话式 AI 场景的接入准备指南，涵盖服务开通、权限配置等步骤，确保 RTC 具备基础的资源接入能力与调用权限。
操作流程
集成前准备包括开通服务和配置权限 2 步：
  1. 开通服务：依次开通 RTC、ASR、LLM、TTS 服务。
  2. 配置权限：根据业务需要，配置不同权限的主账号或子账号以调用智能体与真人用户进行对话。


## 一、开通服务
### 步骤 1：开通 RTC 服务
参看，开通 RTC 服务。开通后系统会自动新建一个默认应用，可直接使用且享受赠送额度。如需多个应用可自行创建。
### 步骤 2：开通 ASR 和 TTS 服务
  1. 登录。
  2. 单击创建应用按钮，在弹出的抽屉栏中填写应用名称和应用简介，并选择服务。和本方案相关的服务、选择要求和建议如下表所示。

类别| 服务| 服务选择建议| 要求  
---|---|---|---  
ASR| 流式语音识别| 响应延迟较小，适用于响应速度要求高的语音控制场景，例如，实时问答、语音控制和会议速记| 至少选择 1 项  
流式语音识别大模型| 识别准确率更高，适用于内容复杂或背景噪音较多的场景，例如，嘈杂场景对话、专业术语识别、多语种混合  
TTS| 语音合成| 常规语音播报需求，适合短语或标准回复，例如，提醒、系统反馈、数字播报| 至少选择 1 项  
语音合成大模型| 注重语音自然度与情感表现的交互，适用于对话中需要自然语调与情感表达的场景，例如，客户服务、教育陪练、故事讲述  
声音复刻大模型| 可生成近似真人语音，适用于需要构建定制化语音形象时使用，例如，品牌数字人、虚拟主播、游戏角色  
  1. 点击确定按钮，完成应用创建。


注意
  * 开通后默认为试用版，享有免费使用额度。
  * 为保证线上长期正常使用，建议后续请开通正式版并购买资源包。但注意开通正式版后，试用版赠送的免费使用额度将自动清空。


### 步骤 3：开通 LLM 服务
火山引擎提供 3 种方式调用大模型服务。
调用方法| 服务选择建议  
---|---  
使用火山方舟在线推理| 可快速调用火山方舟大模型。详情可参看在火山方舟平台创建并获取自定义推理接入点进行调用。说明仅支持使用自定义推理接入点接入大模型，不支持使用预置推理接入点。  
使用火山方舟应用| 可通过零代码组合插件、知识库、工作流等功能调用大模型或基于应用 SDK 实现高代码编排方式。详情可参看使用火山方舟创建应用并获取应用 ID 进行调用。  
使用 Coze 应用| 可在零代码或低代码的基础上，组合插件、知识库、工作流等功能调用大模型。详情可参看 创建 Coze 智能体并获取 Coze 智能体 ID进行调用。  
你也可以接入第三方大模型，但是第三方大模型接口需要符合约定标准规范，详情查看。
## 二、配置不同权限账号调用智能体
你可以根据业务需要选择以下方式调用智能体：
主账号| 子账号  
---|---  
服务端(推荐)| 使用主账号在服务端调用操作简单，鉴权仅需获取 AK SK 密钥信息，且密钥信息存储在服务端不易泄漏，但注意主账户具有账户的完全权限。| 使用子账号在服务端调用操作相对复杂，但是密钥信息存储在服务端不易泄漏，且子账号只拥有赋予的调用智能体的权限，实现了权限的隔离和管控，操作安全。  
客户端| 使用主账号在客户端调用操作简单，鉴权仅需获取 AK SK 密钥信息，但注意 AK SK 存储在客户端容易泄漏，且注意主账户具有账户的完全权限，| 使用子账号在客户端调用操作复杂，鉴权需要获取临时AK、SK、临时 Token 密钥信息，避免了密钥信息一经泄漏造成重大损失，且子账号只在临时 Token 生效期间拥有调用智能体的权限，实现了权限最小化管控。  
使用主账号在服务端调用
使用主账号在客户端调用
使用子账号在服务端调用
使用子账号在客户端调用
你可按照以下步骤实现使用主账号在服务端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 调用接口。


此时使用主账号的 AK、SK 即可调用 StartVoiceChat 接口，实现房间内智能体与真人用户互动。
你可按照以下步骤实现使用主账号在客户端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 调用接口。


此时使用主账号的 AK、SK 即可调用 StartVoiceChat 接口，实现房间内智能体与真人用户互动。
你可按照以下步骤实现使用子账号在服务端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 为子账号添加调用实时对话式 AI 接口权限。
    1. 登录主账号 。
    2. 前往，点击**为子账号添加权限，**找到你需要授权的子账号，点击添加权限。


  1. 调用接口。


此时使用子账号的 AK、SK 即可调用 StartVoiceChat 接口，实现房间内智能体与真人用户互动。
你可按照以下步骤实现使用子账号在客户端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 为子账号赋予获取临时密钥权限。 子账号获取临时密钥权限后，即可在密钥有效期内调用实时对话式 AI 接口。
    1. 登录主账号 。
    2. 点击用户头像选择访问控制。
    1. 在左侧导航栏选择角色点击VoiceChatRoleForRTC。
    1. 点击信任关系，填入以下代码为子账号添加信任关系。
将信任关系中的代码修改为如下代码，以便子账号拥有使用VoiceChatRoleForRTC角色的权限。
```
{
  "Statement": [
   {
      "Effect": "Allow",
      "Action": [
        "sts:AssumeRole"
      ],
      "Principal": {
        "Service": [
         "rtc"
        ],
        "IAM": [
         "trn:iam::2100********:user/cody" //格式为：trn:iam::${主账号AccountId}:user/${子账号UserName}
        ]
      }
   }
  ]
}

JSON

```

    1. 在左侧导航栏选择用户，点击待授权用户，即子账号用户名。
    1. 点击权限，选择 添加权限，，添加 STSAssumeRoleAccess 权限。
  3. 调用接口


使用子账号调用 OpenAPI 接口获取临时 AK、SK 及 Token。获取成功后，即可在密钥有效期内通过临时 AK、SK 及 Token 调用 StartVoiceChat 接口实现房间内智能体与真人用户互动。 调用AssumeRole接口时 RoleSessionName 和 RoleTrn为必填项。RoleSessionName可自定义，用来标志此次AssumeRole请求。RoleTrn填写规则为 trn:iam::${AccountId}:role/${RoleName}，例如trn:iam::2100****3232:user/cody。




## 启动智能体 StartVoiceChat--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1558163


  * 文档首页
/实时音视频/实时对话式 AI/服务端 OpenAPI/启动智能体 StartVoiceChat

启动智能体 StartVoiceChat
最近更新时间：2025.07.03 21:59:33首次发布时间：2025.05.15 13:15:32

文档反馈
本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考历史版本。​
在实时音视频场景中，你可以调用此接口在房间内引入一个智能体进行 AI 实时交互。​
RTC 提供语音识别（ASR）、语音合成（TTS）、大模型（LLM） 一站式接入，同时也支持通道服务，即可使用此接口灵活接入第三方大模型/Agent。​
注意事项​
  * 请求频率：单账号下 QPS 不得超过 60。​


  * 请求接入地址：仅支持 rtc.volcengineapi.com。​


  * 调用该接口启动智能体任务后，若真人用户退出房间，180 s 后该智能体任务会自动停止，但该 180s 内仍会计费。为避免不必要的费用，建议真人用户退出房间后，及时调用 StopVoiceChat 接口关闭智能体任务。​


前提条件​
调用该接口前，你需要开通语音识别、语音合成和大模型服务并配置相关权限策略，详情请参看方案集成前置准备。​
调用接口​
发送 HTTP(S) 请求时，你需要符合火山引擎规范。调用接口的请求结构、公共参数、签名机制、返回结构，参看调用方法。​
请求说明​
  * 请求方式：POST​


  * 请求地址：https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01​


调试​
API Explorer您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。去调试​
请求参数​
下表仅列出该接口特有的请求参数和部分公共参数。完整公共请求参数请见公共参数。​
Query​
Action String 必选 示例值：StartVoiceChat​接口名称。当前 API 的名称为 StartVoiceChat。​​
Version String 必选 示例值：2024-12-01​接口版本。当前 API 的版本为 2024-12-01。​​
Body​
AppId String 必选 示例值：661e****543cf​你的音视频应用的唯一标志，参看创建 RTC 应用获取或创建 AppId。​​
RoomId String 必选 示例值：Room1​智能体与真人进行通话的房间的 ID，需与真人用户使用客户端 SDK 进房时的使用的 RoomId 保持一致。​​
TaskId String 必选 示例值：task1​智能体任务 ID。由你自行定义，用于标识任务，且后续更新或结束此任务也需要使用该 TaskId。参数定义规则参看参数赋值规范。​一个 AppId 的 RoomId 下 TaskId 是唯一的，AppId + RoomId + TaskId 共同构成一个全局唯一的任务标识，用来标识指定 AppId 下某个房间内正在运行的任务，从而能在此任务运行中进行更新或者停止此任务。​不同 AppId 或者不同 RoomId 下 TaskId可以重复。​​
Config Object 必选 示例值：-​智能体交互服务配置，包括语音识别（ASR）、语音合成（TTS）、大模型(LLM)、字幕和函数调用（Function Calling）配置。​ASRConfig Object 必选 示例值：-​语音识别（ASR）相关配置。​Provider String 必选 示例值：volcano​语音识别服务的提供商。该参数固定取值：volcano，表示仅支持火山引擎语音识别服务。可使用以下模型：​火山引擎流式语音识别（识别速度更快）​火山引擎流式语音识别大模型（识别准确率更高）​两者详细差异（如可识别语种、支持的能力等），请参见流式语音识别和流式语音识别大模型。​​ProviderParams Object 必选 示例值：-​服务配置参数。​不同服务，该结构包含字段不同，具体参看：​火山引擎流式语音识别​火山引擎流式语音识别大模型​ASRConfig.ProviderParams（火山引擎流式语音识别） 可选​使用火山引擎流式语音识别时，识别速度快。你需要在 ASRConfig.ProviderParams里配置以下字段：​Mode String 必选 示例值：smallmodel​模型类型。该参数固定取值：smallmodel，表示火山引擎流式语音识别模型。​​AppId String 必选 示例值：93****21​开通流式语音识别服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​Cluster String 必选 示例值：volcengine_streaming_common​开通的流式语音识别服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​ASRConfig-ProviderParams（火山引擎流式语音识别大模型） 可选​使用火山引擎流式语音识别时，识别速度稍慢，但识别准确度更高，你需要在 ASRConfig.ProviderParams里配置以下字段：​Mode String 必选 示例值：bigmodel​模型类型。该参数固定取值：bigmodel，表示火山引擎语音识别大模型。​​AppId String 必选 示例值：93****21​开通火山引擎流式语音识别大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​AccessToken String 必选 示例值：MOaOaa_VQ6****1B34UHA4h5B​与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。​Access Token 查找方式，可参看如何获取 Token。​​ApiResourceId String 可选 示例值：volc.bigasr.sauc.duration​火山引擎流式语音识别大模型服务开通类型：​volc.bigasr.sauc.duration：小时版。​volc.bigasr.sauc.concurrent：并发版。​默认值为 volc.bigasr.sauc.duration。​​StreamMode Integer 可选 示例值：0​语音识别结果输出模式：​0：流式输出。即识别结果会分段、实时地返回。该模式下识别速度更快，适用于实时字幕场景。​1：非流式输出。即在完整接收并处理完整个语音片段后，一次性返回最终的识别结果。该模式下识别准确率更高，适用于不需要即时反馈的离线转录场景（如会议录音）。​默认值为 0。​​context String 可选 示例值："{\"hotwords\": [{\"word\": \"CO2\"},{\"word\": \"雨伞\"},{\"word\": \"鱼\"}]}"​热词直传（通过JSON 字符串直接传入）。​如果某些词汇（比如人名、产品名等）的识别准确率较低，可以将其作为热词传入 ASR 模型，提高输入词汇的识别准确率。例如传入"雨伞"热词，发音相似的词会优先识别为“雨伞”。​大小限制：热词传入最大值为 200 tokens，超出会自动截断。​格式要求（JSON 字符串）：​​json复制​​boosting_table_id String 可选 示例值：26603****1-adad​热词词表 ID。​你需要先在火山语音技术_热词管理创建热词词表，并获取热词 ID。​说明​执行优先级：热词词表低于热词直传。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。​​boosting_table_name String 可选 示例值：语音打断关键词​热词词表名称。​你需要先在火山语音技术_热词管理创建热词词表，并获取热词词表的文件名称。​说明​执行优先级：热词词表低于热词直传。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。​​context_history_length Integer 可选 示例值：0​上下文轮次。将最近指定轮数会话内容送入流式语音识别大模型，有助于模型理解当前对话的背景，从而提升大模型识别准确性。​取值范围为 0、[1,21)，0表示不开启该功能。​​correct_table_id String 可选 示例值：26603****1-adad​替换词 ID。将 ASR 识别出的特定词汇替换为预期的标准词汇，可用于纠错、脱敏或别名替换等。比如“智立”替换为“致力”。​你需要先在火山语音技术_替换词管理创建替换词，并获取替换词 ID。​说明​执行优先级：替换词低于热词。即如果一个词同时是热词和替换词的源词，则优先执行热词，再执行替换词。例如，原词为“智立”：​若热词有“致力”，替换词要求“智立→治理”，那最后结果为 “致力”。​若热词有“致力”，替换词要求“致力→治理”，那最后结果为 “治理”。​​correct_table_name String 可选 示例值：替换词​替换词文件名称。你需要先在火山语音技术_替换词管理创建替换词，并获取替换词文件名称。​说明​执行优先级：替换词低于热词。即如果一个词同时是热词和替换词的源词，则优先执行热词，再执行替换词。例如，原词为“智立”：​若热词有“致力”，替换词要求“智立→治理”，那最后结果为 “致力”。​若热词有“致力”，替换词要求“致力→治理”，那最后结果为 “治理”。​​​​VADConfig Object 可选 示例值：-​VAD（语音检测） 配置。​SilenceTime Integer 可选 示例值：600​判停时间。房间内真人用户停顿时间若高于该值设定时间，则认为一句话结束。​取值范围为 [500，3000)，单位为 ms，默认值为 600。​​​VolumeGain Float 可选 示例值：0.3​音量增益值。增益值越低，采集音量越低。适当低增益值可减少噪音引起的 ASR 错误识别。​默认值为 1.0，推荐值 0.3。​​InterruptConfig Object 可选 示例值：-​语音打断配置。​注意​该参数仅当 Config.InterruptMode=0（即开启语音打断）时生效。​InterruptSpeechDuration Integer 可选 示例值：500​自动打断触发阈值。房间内真人用户持续说话时间达到该参数设定值后，智能体自动停止输出。​取值范围为0，[200，3000]，单位为 ms，值越大智能体说话越不容易被打断。​默认值为 0，表示用户发出声音且包含真实语义时即打断智能体输出。​​InterruptKeywords String[] 可选 示例值：["停止", "停下"]​触发打断的关键词列表。当用户语音中识别到列表中的任意关键词时，智能体将立即停止输出。​若配置该参数，只有识别到配置的打断词时才会触发打断，以降低背景环境人声无打断的干扰。使用该参数时，建议 InterruptSpeechDuration 设置为 0，避免自动打断触发阈值过高，导致关键词打断不生效。​​​TurnDetectionMode Integer 可选 示例值：0​新一轮对话的触发方式。​0：服务端检测到完整的一句话后，自动触发新一轮对话。​1：收到输入结束信令或说话字幕结果后，你自行决定是否触发新一轮会话。​默认值为 0。​该功能使用方法参看配置对话触发模式。​​​TTSConfig Object 必选 示例值：-​语音合成（TTS）相关配置。​IgnoreBracketText Integer[] 可选 示例值：[1,2]​过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。你需要在大模型 Prompt 中自行定义哪些内容放在指定标点符号内。具体使用方法参看过滤指定内容。​支持取值及含义如下：​1：中文括号（）​2：英文括号()​3：中文方括号【】​4：英文方括号[]​5：英文花括号{}​默认值为空，表示不进行过滤。​说明​若大模型返回的内容中，包含标点符号里的内容在最末端，且为独立句子，其后无包含真实语义内容，该标点符号中的内容不会出现在字幕中。​如大模型返回的内容为：当然可以，尽管问，我知无不言！(自信满满)。​此时（自信满满）。不会出现在字幕里。​如大模型返回的内容为：当然可以，尽管问，我知无不言(自信满满)！​此时（自信满满）会出现在字幕里。​​Provider String 必选 示例值：-​语音合成服务提供商，使用不同语音合成服务时，取值不同。支持使用的语音合成服务及对应取值如下：​volcano（服务自上而下语音生成速度递减，情感表现力递增）​火山引擎流式语音合成​火山引擎语音合成大模型（非流式输入流式输出）​火山引擎声音复刻大模型（非流式输入流式输出）​volcano_bidirection（服务自上而下语音生成速度递减，情感表现力递增）​火山引擎语音合成大模型（流式输入流式输出）​火山引擎声音复刻大模型（流式输入流式输出）​minimax​MiniMax 语音合成​​ProviderParams Object 必选 示例值：-​配置所选的语音合成服务。不同服务下，该结构包含字段不同：​火山引擎语音合成​火山引擎语音合成大模型（非流式输入流式输出）​火山引擎语音合成大模型（流式输入流式输出）​火山引擎声音复刻大模型（非流式输入流式输出）​火山引擎声音复刻大模型（流式输入流式输出）​MiniMax 语音合成​TTSConfig.ProviderParams（火山引擎语音合成） 可选​使用火山引擎语音合成时，合成速度快，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 必选 示例值：-​火山引擎语音合成服务应用配置。​appid String 必选 示例值：94****11​开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​cluster String 必选 示例值：volcano_tts​已开通语音合成服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​audio Object 必选 示例值：-​火山引擎语音合成服务音频配置。​voice_type String 必选 示例值：BV001_streaming​音色。​填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​​speed_ratio Float 可选 示例值：1.0​语速。取值范围为 [0.2,3]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​​volume_ratio Float 可选 示例值：1.0​音量。取值范围为 [0.1,3]，默认值为 1.0，通常保留一位小数即可。取值越大，音量越高。​​pitch_ratio Float 可选 示例值：1.0​音高。取值范围为 [0.1,3]，默认值为 1.0，通常保留一位小数即可。取值越大，音调越高。​​​​TTSConfig.ProviderParams（火山引擎语音合成大模型非流式输入流式输出） 可选​使用火山引擎语音合成大模型服务非流式输入流式输出模式时，速度稍慢，但是更生动、更具情感表现力，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 必选 示例值：-​火山引擎语音合成大模型服务应用配置。​appid String 必选 示例值：94****11​开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​cluster String 必选 示例值：volcano_tts​已开通语音合成大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​audio Object 必选 示例值：-​火山引擎语音合成大模型服务音频配置。​voice_type String 必选 示例值：zh_female_meilinvyou_moon_bigtts​音色。​填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​​pitch_rate Integer 可选 示例值：0​音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。​​speech_rate Integer 可选 示例值：0​语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。​​​​TTSConfig.ProviderParams（火山引擎语音合成大模型流式输入流式输出） 可选​使用火山引擎语音合成大模型服务流式输入流式输出模式时，生动、情感表现力最佳，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 必选 示例值：-​火山引擎语音合成大模型服务应用配置。​appid String 必选 示例值：94****11​开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​token String 必选 示例值：OaO****ws1​与语音合成大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。Access Token 查找方式，可参看如何获取 Token。​​​audio Object 可选 示例值：-​火山引擎语音合成大模型服务音频配置。​voice_type String 必选 示例值：BV001_streaming​音色。​填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​​pitch_rate Integer 可选 示例值：0​音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。​​speech_rate Integer 可选 示例值：0​语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。​​​Additions Object 可选 示例值：-​火山引擎语音合成大模型服务高级配置。​enable_latex_tn Boolean 可选 示例值：true​是否播报 Latex 公式。​true：播报。 为true 时，disable_markdown_filter 也需为 true 才生效。​false：不播报。​默认值为 false。​​disable_markdown_filter Boolean 可选 示例值：true​是否对 Markdown 格式内容进行过滤。​true：过滤；例如，**你好**，会读为“你好”。​false：不过滤。例如，**你好**，会读为“星星你好星星”。​默认值为 false。​​enable_language_detector Boolean 可选 示例值：false​是否自动识别语种。支持哪些语种？​true：自动识别。​false：不自动识别。​默认值为 false。​​​ResourceId String 必选 示例值：volc.service_type.10029​调用服务的资源信息 ID，该参数固定取值：volc.service_type.10029；​​​TTSConfig.ProviderParams（火山引擎声音复刻大模型非流式输入流式输出） 可选​使用火山引擎语音合成大模型服务非流式输入流式输出模式时，可自定义音色，且生成速度较快，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 可选 示例值：-​火山引擎声音复刻大模型服务应用配置。​appid String 可选 示例值：94****11​开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​cluster String 必选 示例值：volcano_icl​已开通声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​​​audio Object 可选 示例值：-​火山引擎声音复刻大模型服务音频配置。​voice_type String 必选 示例值：S_N****T7k1​声音复刻声音 ID。你可登录语音技术控制台获取。​​speed_ratio Float 可选 示例值：1.0​语速。取值范围为 [0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​​​​TTSConfig.ProviderParams（火山引擎声音复刻大模型流式输入流式输出） 可选​使用火山引擎语音合成大模型服务非流式输入流式输出模式时，可自定义音色，且更生动、情感表现力更佳，你需要在 TTSConfig.ProviderParams里配置以下字段：​app Object 可选 示例值：-​火山引擎声音复刻大模型服务应用配置。​appid String 可选 示例值：94****11​开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​​token String 可选 示例值：OaO****ws1​与开通声音复刻大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。Access Token 查找方式，可参看如何获取 Token。​​​audio Object 可选 示例值：-​火山引擎声音复刻大模型服务音频配置。​voice_type String 可选 示例值：S_N****T7k1​声音复刻声音 ID。你可登录语音技术控制台获取。​​speed_ratio Float 可选 示例值：1.0​语速。取值范围为 [0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​​​Additions Object 可选 示例值：-​火山引擎声音复刻大模型服务高级配置。​enable_latex_tn Boolean 可选 示例值：true​是否播报 Latex 公式。​true：播报。 为true 时，disable_markdown_filter也需为 true才可生效。​false：不播报。​默认值为 false。​​disable_markdown_filter Boolean 可选 示例值：true​是否对 Markdown 格式内容进行过滤。​true：过滤；例如，**你好**，会读为“你好”。​false：不过滤。例如，**你好**，会读为“星星你好星星”。​默认值为 false。​​enable_language_detector Boolean 可选 示例值：false​是否自动识别语种。​true：自动识别。​false：不自动识别。​默认值为 false。​​​ResourceId String 必选 示例值：volc.megatts.default​调用服务的资源信息 ID，该参数固定取值：volc.megatts.default。​​​TTSConfig.ProviderParams（MiniMax 语音合成） 可选​使用MiniMax语音合成时，你需要在 TTSConfig.ProviderParams里配置以下字段：​Authorization String 必选 示例值：eyJhbG****SUzI1N​API 密钥。前往 Minimax 账户管理-接口密钥获取。​​Groupid String 必选 示例值：983*****669​用户所属组 ID。前往 Minimax 账号信息-基本信息获取。​​model String 必选 示例值：speech-01-turbo​发起请求的模型版本：​speech-01-turbo：最新模型，拥有出色的效果与时延表现。​speech-01-240228：稳定版本模型，效果出色。​speech-01-turbo-240228：稳定版本模型，时延更低。​​URL String 必选 示例值：https://api.minimax.chat/v1/t2a_v2​请求语音合成 URL，该参数固定取值：https://api.minimax.chat/v1/t2a_v2。​​voice_setting Object 可选 示例值：-​音频配置。​speed Float 可选 示例值：1.0​语速。取值越大，语速越快。​取值范围为 [0.5,2]，默认值为1.0。​​vol Float 可选 示例值：1.0​音量。取值越大，音调越高。​取值范围为 (0,10]，默认值为 1.0。​​pitch Float 可选 示例值：0​语调。取值越大，语调越高。​取值范围为 [-12,12]，且必须为整数。​默认值为 0，表示原音色输出。​​voice_id String 可选 示例值：male-qn-jingying​系统音色编号/复刻音色编号。​系统音色可前往 voice_setting.voice_id查询。​克隆音色参看FAQ。​注意​voice_id 与 timber_weights必须设置其中一个。​​​pronunciation_dict Object 可选 示例值：-​特殊标注配置。可对特殊文字、符号指定发音。​tone String[] 可选 示例值：["燕少飞/(yan4)(shao3)(fei1)","达菲/(da2)(fei1)"，"omg/oh my god"]​用于替换需要特殊标注的文字、符号及对应的发音，可用于调整声调或指定其他字符的发音。格式为 "原文字/注音"，注音部分根据语言类型采用不同方式标注：​英文注音：使用对应发音的英文单词，例如："omg/oh my god"。​中文注音：使用拼音，并在每个音节后以括号标注声调，音调用数字表示：​一声（阴平）：1​二声（阳平）：2​三声（上声）：3​四声（去声）：4​轻声：5​例如，"燕少飞/(yan4)(shao3)(fei1)"、"达菲/(da2)(fei1)"。​​​timber_weights Object[] 可选 示例值：-​合成音色权重设置。可通过该参数设置多种音色混合，并调整每个具体音色权重。最多支持 4 种音色混合。​注意​timber_weights与 VoiceSetting.voice_id必须设置其中一个。​voice_id String 可选 示例值：male-qn-jingying​音色编号。当前仅支持系统音色，可前往 voice_setting.voice_id查询。​​weight Integer 可选 示例值：1​权重。取值为整数，单一音色取值占比越高，合成音色越像。取值范围为[1,100]。​​​stream Boolean 可选 示例值：false​是否开启流式输出。​false：不开启流式输出。​true：开启流式输出。​默认值为 false。​​language_booststring String 可选 示例值：auto​增强指定小语种/方言场景下的语音表现。不同场景下取值及含义如下：​不明确小语种类型：auto。取值为auto时，模型将自主判断小语种类型。​小语种：​Spanish：西班牙语​French：法语​Portuguese：葡萄牙语​Korean：韩语​Indonesian：印度尼西亚语​German：德语​Japanese：日语​Italian：意大利语​auto：自动模式​方言：​Chinese,Yue：粤语。Chinese,Yue仅当MiniMaxTTSConfig.model=speech-01-turbo时生效。​默认值为空。​​​​​LLMConfig Object 必选 示例值：-​大模型相关配置。支持的大模型平台如下：​火山方舟平台​Coze平台​第三方大模型/Agent​LLMConfig（火山方舟平台） 可选​使用火山方舟平台时，你需要在 LLMConfig里配置以下字段：​Mode String 必选 示例值：ArkV3​大模型平台标识。使用火山方舟平台时，该参数固定取值： ArkV3。​​EndPointId String 可选 示例值：ep-22****212​自定义推理接入点 EndPointId。当需要使用模型推理功能（如直接调用部署的基础模型）时，此参数为必填。​可前往控制台创建或查询自定义推理接入点。​注意​EndPointId 与 BotId 不可同时填写，若同时填写，则 EndPointId 生效。​当前仅支持自定义推理接入点，不支持预置推理接入点。​​BotId String 可选 示例值：botid****212​应用 ID。需要使用方舟应用实验室功能时，为必填。​可前往控制台创建或查询应用 ID。​注意​请确保你的应用使用的是自定义推理接入点，目前暂不支持预置推理接入点。​​​​EndPointId 与 BotId 不可同时填写，若同时填写，则 EndPointId 生效。​​Temperature Float 可选 示例值：0.1​采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。​取值范围为 (0, 1]，默认值为 0.1。​​MaxTokens Integer 可选 示例值：1024​输出文本的最大 token 限制。默认值为 1024。​​TopP Float 可选 示例值：0.3​采样的选择范围，控制输出 token 的多样性。模型将从概率分布中累计概率超过该取值的标记中进行采样，以确保采样的选择范围不会过宽，值越大输出的 token 类型越丰富。​取值范围为 [0,1]，默认值为 0.3。​​SystemMessages String[] 可选 示例值：["你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"]​系统提示词。用于输入控制大模型行为方式的指令，定义了模型的角色、行为准则，特定的输出格式等。​​UserPrompts Object 可选 示例值：[ { "Role": "user", "Content": "你好" }, { "Role": "assistant", "Content": "有什么可以帮到你的？" }, { "Role": "user", "Content": "你是谁？" }, { "Role": "assistant", "Content": "我是你的智能问答助手。" }​用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。​UserPrompts 存储的对话轮数受 HistoryLength 控制。例如 UserPrompts 中预先存储了两轮对话，HistoryLength 设置为 3，用户已进行了三轮对话，第四轮会话开始时，UserPrompts 中存储的内容会被全部删除。​注意​UserPrompts 中 Role 的取值只包含 user 和 assistant，且必须成对出现，否则大模型可能会出现未定义行为。​​HistoryLength Integer 可选 示例值：3​历史问题轮数。默认值为 3。​在调用该接口时需要确保所有 UserPrompts 和 SystemMessage 消息文本总长度不超过大模型上下文长度。​例如：历史问题轮数为 3，使用 Skylark2-lite-8k 大模型，长度限制为 8k，UserPrompts 预先存储了两轮对话，用户输入了第一轮会话的问题，此时 SystemMessages+UserPrompts+第一轮会话问题总长度不超过8k。​​Tools Object[] 可选 示例值：-​定义一组可供模型在 Function Calling 功能中调用的工具。​目前仅支持函数作为工具。你需要提供每个函数的定义，模型会基于定义，在需要时生成调用特定函数的 JSON 指令。该功能使用方法参看 Function Calling。​注意​Function calling 功能不支持和联网插件或知识库插件同时使用。​type String 可选 示例值：function​工具类型。可取值及含义如下：​function：函数调用。​​function Object 可选 示例值：-​模型可以调用的工具列表。​name String 必选 示例值：get_current_weather​函数的名称。​​description String 可选 示例值：获取指定城市的天气信息​对函数用途的描述，供模型判断何时以及如何调用该工具函数。​​parameters JSONMap 可选 示例值：-​函数请求参数，以 JSON Schema 格式描述。具体格式请参考 JSON Schema 文档。​​json复制​​​​Prefill Boolean 可选 示例值：false​将 ASR 中间结果提前送入大模型进行处理。​true：开启。启用后，允许将 ASR 识别中间结果提前发送给大模型进行处理，以降低延时。​false：关闭。等待 ASR 模块识别出相对完整的一句话后再送入大模型处理。​默认值为 false。​注意​开启后会产生额外模型消耗。​​VisionConfig Object 可选 示例值：-​视觉理解能力配置。如何使用视觉理解能力？​注意​该功能仅在使用 EndPointId（推理点）接入大模型，且推理点选择 vision 系列模型时才生效，如 Doubao-vision-pro-32k。​Enable Boolean 可选 示例值：true​是否开启视觉理解功能。​false：不开启。​true：开启。​默认值为 false。​​SnapshotConfig Object 可选 示例值：-​截图相关配置。截图送入大模型以供理解信息。​StreamType Integer 可选 示例值：0​截图流类型。​0：主流。​1：屏幕流。​默认值为 0。​​ImageDetail String 可选 示例值：auto​图片处理模式。取值及含义如下：​high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。​low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。​auto：自动模式。根据图片分辨率，自动选择适合的模式。​默认值为 auto。​​Height Integer 可选 示例值：640​送入大模型截图视频帧高度，取值范围为 [0, 1792]，单位为像素。​不填或传 0时自动修改为 360。​传入大模型截图视频帧宽度自动按传入高度进行比例计算。​​Interval Integer 可选 示例值：1000​相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。​​ImagesLimit Integer 可选 示例值：2​单次送大模型截图数。取值范围为 [0, 50]。​不传或传 0 时自动修改为 2。​​​StorageConfig Object 可选 示例值：-​截图存储配置。​Type Integer 可选 示例值：0​存储类型。​0：按照 Base 64 编码存入服务端缓存，会话结束后自动删除。​1：存储至 TOS 平台。使用 TOS 存储前需前往控制台开通该服务。​​TosConfig Object 可选 示例值：-​TOS 存储配置。​AccountId String 可选 示例值：acc****_id​火山引擎平台账号 ID，例如：*********。查看路径参看查看和管理账号信息。​注意​此账号 ID 为火山引擎主账号 ID。​若你调用 OpenAPI 鉴权过程中使用的 AK、SK 为子用户 AK、SK，账号 ID 也必须为火山引擎主账号 ID，不能使用子用户账号 ID。​​Region Integer 可选 示例值：0​存储桶区域。不同存储桶区域对应的 Region 不同，具体参看 Region对照表。​默认值为 0。​注意​该字段填入的存储桶区域需要与你在 TOS 平台创建存储桶时选择的区域相同。​​Bucket String 可选 示例值：b****t​存储桶名称。前往控制台创建或查询。​​​​​​LLMConfig（Coze平台） 可选​使用 Coze 平台时，你需要在 LLMConfig 里配置以下字段。​使用前确保智能体已发布为 API 服务。详情参考准备工作。​Mode String 必选 示例值：CozeBot​大模型平台名称。该参数固定取值：CozeBot。​​CozeBotConfig Object 必选 示例值：-​Coze 智能体配置。​Url String 必选 示例值：https://api.coze.cn​请求地址。该参数固定取值：https://api.coze.cn​​BotId String 必选 示例值：73****68​Coze 智能体 ID。​可前往你需要调用的智能体开发页面获取。开发页面 URL 中 bot 参数后的数字即智能体ID。例如开发页面 URL 为：https://www.coze.cn/space/341/bot/73428668，则 BotId 为 73428668。​​APIKey String 必选 示例值：czu_UEE2mJn6****MHxLCVv9uQ7H​Coze 访问密钥。​你可以生成个人访问令牌以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考 OAuth 应用管理。​注意​创建个人访问令牌或 OAuth 应用时，你需要根据你的 Bot 使用场景勾选对应权限，否则会鉴权失败。​​UserId String 必选 示例值：123​标识当前与智能体对话的用户，由你自行定义、生成与维护。UserId 用于标识对话中的不同用户，不同的 UserId，其对话的上下文消息、数据库等对话记忆数据互相隔离。如果不需要用户数据隔离，可将此参数固定为一个任意字符串，例如 123，abc等。​​HistoryLength Integer 可选 示例值：3​历史问题轮数。默认值为 3。​在调用该接口时需要确保作为上下文的用户消息和智能体消息文本总长度小于模型上下文长度。​例如：历史问题轮数为 3，使用 Skylark2-lite-8k大模型，该模型上下文长度限制为 8k，询问第 10 个问题时，需保证第 10 个问题的长度与第八、九轮用户消息和智能体消息文本的总长度之和不得超过 8k。​​Prefill Boolean 可选 示例值：false​将 ASR 中间结果提前送入大模型进行处理。​开启该功能后可不等待 ASR 模块识别出完整的一句话再送入大模型处理，而是将 ASR 识别中间结果提前送入大模型进行处理以降低延时。​true：开启。​false：关闭。​默认值为 false。​注意​开启后会产生额外模型消耗。​​EnableConversation Boolean 可选 示例值：false​是否将上下文存储在 Coze 平台。​若需要使用 Coze 平台上下文管理相关功能，如将指定内容添加到会话中，可开启此功能。功能开启后 RTC 不再存储上下文内容。​false：不开启。​true：开启。​默认值为 false。​注意​EnableConversation 为 true 时会导致HistoryLength设置无效。​​​​LLMConfig（第三方大模型） 可选​使用第三方大模型时，你需要在 LLMConfig里配置以下字段：​Mode String 必选 示例值：CustomLLM​大模型平台名称。使用第三方大模型/Agent 时，该参数固定取值： CustomLLM。​​URL String 必选 示例值：https://test.com/path/to/app​第三方大模型/Agent 的请求 URL，需要使用 HTTPS 域名，且必须符合火山引擎标准。​验证是否符合标准：可前往体验 Demo->修改 AI 设定->第三方模型，并填入 URL 进行快速验证。​若验证失败可前往文档接入第三方大模型/Agent，查看接口标准并通过验证工具查看详细报错。​说明​如果需要在每次请求时传递一些简单的、非敏感的参数（如 session_id），可以直接将它们作为查询参数拼接到此 URL 中。​如需使用 HTTP 域名进行测试，可在下方 Feature 参数中填入 {"Http":true}，但无法保证服务质量。​​ModelName String 可选 示例值：name1​第三方大模型/Agent 的名称。​​APIKey String 可选 示例值：pat*****123231​Bearer Token 认证方式的大模型鉴权 Token。​​MaxTokens Integer 可选 示例值：1024​输出文本的最大 token 限制。默认值为 1024。​​Temperature Float 可选 示例值：0.1​采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为（0,1]，默认值为 0.1。​​TopP Float 可选 示例值：0.3​采样的选择范围，控制输出 token 的多样性。模型将从概率分布中累计概率超过该取值的标记中进行采样，以确保采样的选择范围不会过宽，值越大输出的 token 类型越丰富。​取值范围为[0,1]，默认值为 0.3。​​SystemMessages String[] 可选 示例值：["你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"]​大模型 System 角色预设指令，可用于控制模型输出。​​UserPrompts Object 可选 示例值：[ { "Role": "user", "Content": "你好" }, { "Role": "assistant", "Content": "有什么可以帮到你的？" }, { "Role": "user", "Content": "你是谁？" }, { "Role": "assistant", "Content": "我是你的智能问答助手。" }​大模型 User 角色预设 Prompt，可用于增强模型的回复质量，模型回复时会优先参考此处内容。​UserPrompts 存储的对话轮数受 HistoryLength 控制。例如UserPrompts 中预先存储了两轮对话，HistoryLength 设置为 3，用户已进行了三轮对话，第四轮会话开始时，UserPrompts 中存储的内容会被全部删除。​注意​UserPrompts 中 Role 的取值只包含 user 和 assistant，且必须成对出现，否则大模型可能会出现未定义行为。​​HistoryLength Integer 可选 示例值：3​历史问题轮数。默认值为 3。​在调用该接口时需要确保所有 UserPrompts 和 SystemMessage 消息文本总长度小于大模型上下文长度。​例如：历史问题轮数为 3，使用 Skylark2-lite-8k 大模型，长度限制为 8k，UserPrompts 预先存储了两轮对话，用户输入了第一轮会话的问题，此时 SystemMessages+UserPrompts+第一轮会话问题的总长度不超过 8k。​​Feature String 可选 示例值：{\"Http\":true}​使用 HTTP 域名进行测试，该参数固定取值：{\"Http\":true}。​​Prefill Boolean 可选 示例值：false​将 ASR 中间结果提前送入大模型进行处理：​true：开启。将 ASR 识别中间结果提前送入大模型进行处理，以降低延时。​false：关闭。需等待 ASR 模块识别出完整的一句话后，再将其整体送入大模型处理。​默认值为 false。​注意​开启后会产生额外模型消耗。​​Custom String 可选 示例值：-​自定义 JSON 字符串，可传入业务自定义参数。​​VisionConfig Object 可选 示例值：-​视觉理解能力配置。​该功能使用说明参看视觉理解能力。​Enable Boolean 可选 示例值：true​是否开启视觉理解功能。​false：不开启；​true：开启。​默认值为false。​​SnapshotConfig Object 可选 示例值：-​传给大模型截图相关配置。​StreamType Integer 可选 示例值：0​截图流类型。​0：主流。​1：屏幕流。​默认值为 0。​​ImageDetail String 可选 示例值：auto​图片处理模式。取值及含义如下：​high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。​low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。​auto：自动模式。根据图片分辨率，自动选择适合的模式。​默认值为 auto。​​Height Integer 可选 示例值：640​送入大模型视频帧高度，取值范围为 [0, 1792]，单位为像素。​不填或传 0时自动修改为 360。​传入大模型视频帧宽度自动按传入高度计算。​​Interval Integer 可选 示例值：1000​相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。​​ImagesLimit Integer 可选 示例值：2​单次送大模型图片数。取值范围为 [0, 50]。​不传或传 0时自动修改为 2。​​​StorageConfig Object 可选 示例值：-​截图存储相关配置。​Type Integer 可选 示例值：0​存储类型。​0：Base 64 编码存入本地，会话结束后自动删除。​1：TOS。使用 TOS 存储前需前往控制台开通该服务。​​TosConfig Object 可选 示例值：-​TOS 存储配置。​AccountId String 可选 示例值：acc****_id​火山引擎平台账号 ID，例如：*********。​火山引擎平台账号 ID 查看路径参看查看和管理账号信息。​此账号 ID 为火山引擎主账号 ID。​若你调用 OpenAPI 鉴权过程中使用的 AK、SK 为子用户 AK、SK，账号 ID 也必须为火山引擎主账号 ID，不能使用子用户账号 ID。​​Region Integer 可选 示例值：0​不同存储平台支持的 Region 不同，具体参看 Region对照表​默认值为0。​​Bucket String 可选 示例值：b****t​存储桶名称。前往控制台创建或查询。​​​​​​​SubtitleConfig Object 可选 示例值：-​配置字幕回调。​可通过客户端或服务端接收回调消息，消息格式为二进制，使用前需解析。详细说明参看实时对话式 AI 字幕。​DisableRTSSubtitle Boolean 可选 示例值：false​是否关闭房间内客户端字幕回调。​true：关闭，即不通过客户端接收字幕回调消息。​false：开启，通过客户端接收字幕回调消息。开启后，在客户端实现监听 onRoomBinaryMessageReceived（以 Android 为例），即可接收字幕回调消息。​默认值为 false。​注意​如需通过服务端接收字幕回调，请配置 ServerMessageUrl 和 ServerMessageSignature。​​ServerMessageUrl String 可选 示例值：https://example-domain.com/vertc/subtitle​接收字幕结果的 URL 地址。通过服务端接收字幕回调时必填。​接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​可通过 curl -v http(s)://yourexample-domain.com/vertc/subtitle 命令对域名进行快速校验：​若返回 HTTP 状态码为 301 或 302，则说明域名不可用，POST 方法可能会重定向为 GET。​若返回 307 或 308 则说明域名可用，且始终保持 POST 方法。​注意​如果你同时通过该接口接收任务状态变化回调和字幕回调，请确保在 ServerMessageUrl 和 ServerMessageURLForRTS中填入相同的 URL，否则会导致无法接收任务状态回调或字幕回调。​​ServerMessageSignature String 可选 示例值：b46ab5f1d8ad6a​鉴权签名。通过服务端接收字幕回调时必填。​在接收到字幕结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​​SubtitleMode Integer 可选 示例值：0​字幕回调时是否需要对齐音频时间戳。​0：对齐音频时间戳。​1：不对齐音频时间戳。取 1 时可更快回调字幕信息。​默认值为 0。​​​FunctionCallingConfig Object 可选 示例值：-​使用 Function calling 功能时，从服务端接受函数工具返回的信息指令配置。​Function calling 功能使用详情参看功能说明文档。​注意​该功能仅在使用火山方舟平台时生效。​ServerMessageUrl String 可选 示例值：https://example-domain8080/m2​服务端接收 Function Calling 函数工具返回的信息指令的 URL 地址。功能使用详情参看服务端实现 Function Calling 功能。​接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​ServerMessageUrl 和 ServerMessageSignature均填写正确才能开启该功能。​​ServerMessageSignature String 可选 示例值：TestSignature​鉴权签名。​在接收到函数调用信息结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​​​InterruptMode Integer 可选 示例值：0​是否启用语音打断：​0：开启语音打断。开启后，一旦检测到用户发出声音，智能体立刻停止输出。​1：关闭语音打断。关闭后，智能体说话期间，用户语音输入内容会被忽略不做处理，不会打断智能体讲话。​默认值为 0。​​​
AgentConfig Object 必选 示例值：-​智能体相关配置，包括欢迎词、任务状态回调等信息。​TargetUserId String[] 必选 示例值：["user1"]​真人用户 ID。需使用客户端 SDK 进房的真人用户的 UserId。仅支持传入一个 UserId，即单个房间内，仅支持一个用户与智能体一对一通话。​​WelcomeMessage String 可选 示例值：Hello​智能体启动后的欢迎词。​​UserId String 可选 示例值：BotName001​智能体 ID，用于标识智能体。​由你自行定义、生成与维护，支持由大小写字母（A-Z、a-z）、数字（0-9）、下划线（_）、短横线（-）、句点（.）和 @ 组成，最大长度为 128 个字符。​若不填则默认值为 voiceChat_$(TargetUserId)_$(timestamp_now)。​注意​同一 AppId 下 UserId 建议全局唯一。若同一 AppId 下不同房间内智能体名称相同，会导致使用服务端回调的功能异常，如字幕、Function Calling 和任务状态回调功能。​UserId 取值与 TargetUserId不能重复。​​EnableConversationStateCallback Boolean 可选 示例值：false​是否接收智能体状态变化回调，获取智能体关键状态，比如​“聆听中”、“思考中”、“说话中”、“被打断”等。功能详细说明，参看接收状态变化消息。​true：接收。可通过客户端或服务端接收智能体状态变化回调。​通过客户端接收：还需在客户端实现监听回调 onRoomBinaryMessageReceived（以 Android 端为例）。​通过服务端接收：还需配置字段 ServerMessageURLForRTS 和 ServerMessageSignatureForRTS。​false：不接收。​默认值为 false。​​ServerMessageSignatureForRTS String 可选 示例值：b46ab5f1d8ad6a​鉴权签名。通过服务端接受任务状态变化回调时必填。​你可传入该鉴权参数，在接收到回调结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​​ServerMessageURLForRTS String 可选 示例值：https://example-domain.com/vertc/callback​接收任务状态变化的 URL 地址。通过服务端接受任务状态变化回调时必填。​接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​注意​如果同时通过该接口接收任务状态变化回调和字幕回调，请确保在 ServerMessageURLForRTS 与 SubtitleConfig.ServerMessageUrl中填入相同的 URL，否则会导致无法接收任务状态回调或字幕回调。​​UseLicense Boolean 可选 示例值：true​是否为 License 用户。​true：是；​false：否。​默认值为 false。​若为 License 用户，你需要：​联系技术支持开通白名单。​前往控制台硬件场景服务获取你需要的 ASR、TTS 和 LLM 相关参数值。注意你必须使用在此处获取的 ASR、TTS 和 LLM 参数值，智能体才能正常工作。​如果你使用大模型流式语音识别和大模型语音合成，在调用 StartVoiceChat 接口时，ASRConfig.ProviderParams.AccessToken 和 TTSConfig.ProviderParams.AccessToken无需填入。​​Burst Object 可选 示例值：-​音频快速发送配置。​开启该功能后，可通过快速发送音频实现更好的抗弱网能力。​说明​该功能仅在嵌入式硬件场景下支持，且嵌入式 Linux SDK 版本不低于 1.57。​Enable Boolean 可选 示例值：true​是否开启音频快速发送。​false：开启。​true：关闭。​默认值为 false。​​BufferSize Integer 可选 示例值：10​接收音频快速发送片段时，客户端可缓存的最大音频时长。取值范围为[10,3600000]，单位为 ms，默认值为 10。​​Interval Integer 可选 示例值：10​音频快速发送结束后，其他音频内容发送间隔。取值范围为[10,600]，单位为 ms，默认值为10。​​​​
返回参数​
本接口无特有的返回参数。公共返回参数请见返回结构。​
其中返回值 Result 仅在请求成功时返回 ok，失败时为空。​
完整请求结构
火山引擎流式语音识别
火山引擎流式语音识别大模型
火山引擎语音合成
火山引擎语音合成大模型流式输入流式输出
MiniMax 语音合成
火山方舟大模型
第三方大模型
输入示例
```
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": { // 以火山引擎流式语音识别大模型为例，其他语音识别服务配置参看其他示例。
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": { // 以火山引擎语音合成流式输入流式输出合成为例，其他语音合成服务配置参看其他示例。
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": { // 以火山方舟大模型为例，其他大模型服务配置参看其他示例。
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserPrompts": [
        {
          "Role": "user",
          "Content": "你好"
        },
        {
          "Role": "assistant",
          "Content": "有什么可以帮到你的？"
        }
      ],
      "HistoryLength": 3
    }
  },
  "AgentConfig": {
    "TargetUserId": [
      "user1"
    ],
    "WelcomeMessage": "Hello",
    "UserId": "BotName001"
  }
}
```

输出示例
```
{
  "Result": "ok",
  "ResponseMetadata": {
    "RequestId": "20230****10420",
    "Action": "StartVoiceChat",
    "Version": "2024-12-01",
    "Service": "rtc",
    "Region": "cn-north-1"
  }
}
```





## None

Source: https://www.volcengine.com/docs/6348/1404671





## 关闭智能体 StopVoiceChat--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1404672


  * 文档首页
/实时音视频/实时对话式 AI/服务端 OpenAPI/关闭智能体 StopVoiceChat

关闭智能体 StopVoiceChat
最近更新时间：2025.02.12 15:24:08首次发布时间：2024.12.31 16:11:14

文档反馈
> 本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考。
在实时音视频通话场景中，若你需要结束智能体的语音聊天服务，可以通过调用此接口实现。
## 使用说明
### 调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看。
## 注意事项
  * 请求频率：QPS 不得超过 60。
  * 该接口请求接入地址仅支持 rtc.volcengineapi.com。


## 请求说明
  * 请求方式：POST
  * 请求地址：https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01


## 调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
## 请求参数
### Query
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
Action| String| 是| StopVoiceChat| 接口名称。当前 API 的名称为 StopVoiceChat。  
Version| String| 是| 2024-12-01| 接口版本。当前 API 的版本为 2024-12-01。  
### Body
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
AppId| String| 是| 661e****543cf| 你的音视频应用的唯一标志，参看获取 AppId  
RoomId| String| 是| Room1| 房间的 ID，是房间的唯一标志。赋值规则参看参数赋值规范。  
TaskId| String| 是| task1| 智能体任务 ID  
## 返回参数
本接口无特有的返回参数。公共返回参数请见。 其中返回值 Result 仅在请求成功时返回 ok,失败时为空。
## 请求示例
```
POST https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1"
}

json

```

## 返回示例
```
{
  "Result": "ok",
  "ResponseMetadata": {
    "RequestId": "Your_Re20230****10420questId",
    "Action": "StopVoiceChat",
    "Version": "2024-12-01",
    "Service": "rtc",
    "Region": "cn-north-1"
  }
}

json

```





## Function Calling（非流式返回结果）--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1359441


  * 文档首页
/实时音视频/实时对话式 AI/体验进阶/Function Calling（非流式返回结果）

Function Calling（非流式返回结果）
最近更新时间：2025.05.29 11:18:34首次发布时间：2024.11.05 11:56:09

文档反馈
在实时对话式 AI场景下，通过使用 Function Calling 功能允许大模型识别用户对话中的特定需求，智能调用外部函数、API 等工具来执行它自身无法独立完成的任务，如处理实时数据检索、文件处理、数据库查询等。适用于天气查询、股票行情查询、数学计算等场景。
说明
该功能仅在使用火山方舟平台模型时生效。且只有在使用 doubao 非1.5 代系模型时，按照非流式返回 Function Calling 结果。
服务端和客户端均可实现该功能。你可根据业务请求端的类型选择对应的方式。例如你在开发 AI 应用时，选择服务端响应请求，建议使用服务端实现传入大模型上下文，降低请求延迟。
## 时序图
你可参看如下时序图在该场景下使用 Function Calling 功能：
步骤 1：开启 Function Calling 功能。 步骤 2：触发 Function Calling 后接收工具调用指令消息。 步骤 3：执行本地工具获取工具调用结果，并将结果信息传回 RTC 服务端。 步骤 4：收到音频回复。 其中步骤 2 、3 支持多轮 Function calling 调用。当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## 服务端实现
### 步骤 1：开启 Function Calling 功能
你需要调用 接口配置以下字段开启该功能：
  1. 通过 LLMConfig.Tools字段配置一组或多组 Function（函数）工具相关的功能和定义。
  2. 通过LLMConfig.FunctionCallingConfig字段配置接收 Function Calling 功能返回的消息的 URL 和鉴权签名，返回消息包括函数被调用时触发的通知消息和函数调用指令消息。 
    1. ServerMessageUrl：接收 Function Calling 功能返回消息的 URL 地址。你指定的 URL 地址将收到来自 RTC 服务器的 HTTP(S) POST 请求发送的指令消息，格式为 JSON。
    2. ServerMessageSignature：鉴权签名。你可传入该鉴权字段，在收到 Function Calling 功能返回消息时，与步骤 2 返回工具调用指令消息中的 signature 字段的值进行对比，用于鉴权，保证消息的可靠性与安全性。


你可以参考以下示例代码进行请求：
```
POST https: //rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserPrompts": [
        {
          "Role": "user",
          "Content": "你好"
        },
        {
          "Role": "assistant",
          "Content": "有什么可以帮到你的？"
        }
      ],
      "HistoryLength": 3,
      "Tools": [
        {
          "Type": "function",
          "function": {
            "name": "get_current_weather",
            "description": "获取给定地点的天气",
            "parameters": {
              "type": "object",
              "properties": {
                "location": {
                  "type": "string",
                  "description": "地理位置，比如北京市"
                },
                "unit": {
                  "type": "string",
                  "description": "",
                  "enum": [
                    "摄氏度",
                    "华氏度"
                  ]
                }
              },
              "required": [
                "location"
              ]
            }
          }
        }
      ]
    }
    "FunctionCallingConfig": {
      "ServerMessageUrl": "https://example-domain.com/vertc/fc",
      "ServerMessageSignature": "b46a****8ad6a",
    },
    "AgentConfig": {
      "TargetUserId": [
        "user1"
      ],
      "WelcomeMessage": "Hello",
      "UserId": "BotName001"
    }
  }
}

JSON

```

### 步骤 2：接收工具调用指令消息
当用户的问题触发 Function Calling 时，会通过你在步骤 1 配置的 URL 地址，使用 HTTP(S) 请求返回本次函数工具调用的指令消息，返回的格式为 JSON 格式，内容如下：
字段名| 类型| 描述  
---|---|---  
message| Array of | 调用指令消息详情。  
signature| String| StartVoiceChat.Config.FunctionCallingConfig 中设置的 signature的值，用于鉴权。  
message：
字段名| 类型| 描述  
---|---|---  
id| String| 本次 Function Calling 任务的标识 ID。  
type| String| Function Calling 调用工具类型，固定为 function，表示为函数调用。  
function| | 调用函数详情。  
function：
字段名| 类型| 描述  
---|---|---  
name| String| 函数名称。  
arguments| String| 函数调用指令详情。  
### 步骤 3：将工具调用的结果信息传回 RTC 服务端
在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。 你可调用 接口设置以下参数将工具调用的结果信息传回 RTC 服务端：
你可参看以下示例将工具调用的结果信息传回 RTC 服务端：
```
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-06-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Command": "function",
  "Message":"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
}

JSON

```

### 步骤 4：获取 Function Calling 最终答复
当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## 客户端实现
### 步骤 1：开启 Function Calling 功能
你需要调用 接口配置 LLMConfig.Tools 字段，输入你需要的一组或多组 Function（函数）工具相关的功能和定义开启该功能。 你可以参考以下示例代码进行请求：
```
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserMessages": [
        "user:\"你是谁\"",
        "assistant:\"我是问答助手\"",
        "user:\"你能干什么\"",
        "assistant:\"我能回答问题\""
      ],
      "HistoryLength": 3,
      "Tools": [
        {
          "Type": "function",
          "function": {
            "name": "get_current_weather",
            "description": "获取给定地点的天气",
            "parameters": {
              "type": "object",
              "properties": {
                "location": {
                  "type": "string",
                  "description": "地理位置，比如北京市"
                },
                "unit": {
                  "type": "string",
                  "description": "",
                  "enum": [
                    "摄氏度",
                    "华氏度"
                  ]
                }
              },
              "required": [
                "location"
              ]
            }
          }
        }
      ]
    }
  },
  "AgentConfig": {
    "TargetUserId": [
      "user1"
    ],
    "WelcomeMessage": "Hello",
    "UserId": "BotName001"
  }
}

JSON

```

### 步骤 2：接收工具调用指令消息
当用户的问题触发 Function Calling 时，你可通过 回调接收本次函数工具调用的指令消息。该回调中的 message 字段中的内容为函数调用指令消息，格式为二进制，使用前需解析。 message 的格式如下：
参数名| 类型| 描述  
---|---|---  
magic number| String| 消息格式，固定为 tool，表示此次消息为函数工具指令消息。  
length| String| 信息指令长度，单位为 bytes。存放方式为大端序。  
Tool_Calls| String| 信息指令详细信息。格式参看。  
Tool_Calls
参数名| 类型| 是否必填| 描述  
---|---|---|---  
tool_calls| Array of | 是| 工具调用信息列表。  
id| String| 是| 本次工具调用的唯一标识 ID。  
type| String| 是| 工具类型。  
tool_call
字段名| 类型| 描述  
---|---|---  
function| | 调用函数详情。  
function
字段名| 类型| 描述  
---|---|---  
name| String| 函数名称。  
arguments| String| 函数调用指令详情。  
你可参看以下示例代码对工具调用指令消息进行解析。
```
//定义结构体
struct function {
  std::string arguments;
  std::string name;
};
struct ToolCall {
  std::string id;
  std::string type;
  function func;
};
struct ToolCallsMsgData {
  std::string subscribe_user_id;
  std::vector<ToolCall> tool_calls
};
//回调事件
void onRoomBinaryMessageReceived(const char* uid, int size, const uint8_t* message) {
  std::string tool_calls;
  bool ret = Unpack(message, size, tool_calls);
  if(ret) {
    ParseData(tool_calls);
  }
}
//拆包校验
bool Unpack(const uint8_t *message, int size, std::string& tool_calls_msg) {
  int kToolCallsHeaderSize = 8;
  if(size < kToolCallsHeaderSize) { 
    return false;
  }
  // magic number "tool"
  if(static_cast<uint32_t>((static_cast<uint32_t>(message[0]) << 24) 
      | (static_cast<uint32_t>(message[1]) << 16) 
      | (static_cast<uint32_t>(message[2]) << 8) 
      | static_cast<uint32_t>(message[3])) != 0x746F6F6CU) {
    return false;
  }
  
  uint32_t length = static_cast<uint32_t>((static_cast<uint32_t>(message[4]) << 24) 
      | (static_cast<uint32_t>(message[5]) << 16) 
      | (static_cast<uint32_t>(message[6]) << 8) 
      | static_cast<uint32_t>(message[7]));
      
  if(size - kToolCallsHeaderSize != length) {
    return false;
  }
  if(length) {
    tool_calls_msg.assign((char*)message + kToolCallsHeaderSize, length);
  } else {
    tool_calls_msg = "";
  }
  return true;
}
//解析
void ParseData(const std::string& msg) {
  // 解析 JSON 字符串
  nlohmann::json json_data = nlohmann::json::parse(msg);
  ToolCallsMsgData toolcalls_data;
  // 存储解析后的数据
  toolcalls_data.subscribe_user_id = json_data["subscribe_user_id"];
  // 遍历 JSON 数据并填充结构体
  for (const auto& item : json_data["tool_calls"]) {
    ToolCall tool_call;
    tool_call.id = item["id"];
    tool_call.type = item["type"];
    auto fun_json = item["function"];
    tool_call.func.arguments = fun_json["arguments"];
    tool_call.func.name = fun_json["name"];
    toolcalls_data.push_back(tool_call);
  }
}

C++

```

### 步骤 3：将工具调用的结果信息传回 RTC 服务端
在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。 你可调用 接口设置以下参数将函数调用指令消息按照二进制格式传回 RTC 服务端：
  * userId：消息接收用户的 ID
  * buffer：工具调用的结果信息。
  * config：发送消息的可靠有序性。


指令消息格式如下：
参数名| 类型| 描述  
---|---|---  
magic number| String| 消息格式，固定为 func。  
length| String| 工具调用的结果信息长度，单位为 bytes。存放方式为大端序。  
Function_Response| String| 工具调用的结果信息。  
你可参看以下示例代码传回工具调用的结果信息。
TypeScript
Java
```
import VERTC from '@volcengine/rtc';
/**
 * @brief 将字符串包装成 TLV
 */
function stringToTLV(inputString: string) {
 const type = 'func';
 const typeBuffer = new Uint8Array(4);
 for (let i = 0; i < type.length; i++) {
  typeBuffer[i] = type.charCodeAt(i);
 }
 const lengthBuffer = new Uint32Array(1);
 const valueBuffer = new TextEncoder().encode(inputString);
 lengthBuffer[0] = valueBuffer.length;
 const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);
 tlvBuffer.set(typeBuffer, 0);
 tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
 tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
 tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
 tlvBuffer[7] = lengthBuffer[0] & 0xff;
 tlvBuffer.set(valueBuffer, 8);
 return tlvBuffer.buffer;
};
/**
 * @brief TLV 数据格式转换成字符串
 * @note TLV 数据格式
 * | magic number | length(big-endian) | value |
 * @param {ArrayBufferLike} tlvBuffer
 * @returns 
 */
function tlv2String(tlvBuffer: ArrayBufferLike) {
 const typeBuffer = new Uint8Array(tlvBuffer, 0, 4);
 const lengthBuffer = new Uint8Array(tlvBuffer, 4, 4);
 const valueBuffer = new Uint8Array(tlvBuffer, 8);
 let type = '';
 for (let i = 0; i < typeBuffer.length; i++) {
  type += String.fromCharCode(typeBuffer[i]);
 }
 const length =
  (lengthBuffer[0] << 24) | (lengthBuffer[1] << 16) | (lengthBuffer[2] << 8) | lengthBuffer[3];
 const value = new TextDecoder().decode(valueBuffer.subarray(0, length));
 return { type, value };
};
/**
 * @brief 通过 onRoomBinaryMessageReceived 接收 toolcall
 *    通过 sendUserBinaryMessage 发送 response
 */
function handleRoomBinaryMessageReceived(
 event: {
  userId: string;
  message: ArrayBuffer;
 },
) {
 const { message } = event;
 const { type, value } = tlv2String(message);
 const data = JSON.parse(value);
 const { tool_calls } = data || {};
 // 处理逻辑
 console.log(type);
 
 if (tool_calls?.length) {
  const name: string = tool_calls?.[0]?.function?.name;
  const map: Record<string, string> = {
   getcurrentweather: '今天下雪， 最低气温零下10度',
   musicplayer: '查询到李四的歌曲， 名称是千里之内',
   sendmessage: '发送成功',
  };
  this.engine.sendUserBinaryMessage(
   'Your AI Bot Name',
   stringToTLV(
    JSON.stringify({
     ToolCallID: tool_calls?.[0]?.id,
     Content: map[name.toLocaleLowerCase().replaceAll('_', '')],
    })
   )
  );
 }
};
/**
 * @brief 监听房间内二进制消息
 */
this.engine.on(VERTC.events.onRoomBinaryMessageReceived, handleRoomBinaryMessageReceived);

TypeScript

```

```
public void SendFunctionResponse(String ToolCallID, String Content) {
  JSONObject json = new JSONObject();
  try {
    json.put("ToolCallID", ToolCallID);
    json.put("Content", Content);
  } catch (JSONException e) {
    throw new RuntimeException(e);
  }
  String jsonString = json.toString();
  System.out.println(jsonString);
  app.rtcSdkWrap.sendUserBinaryMessage("RobotMan_", stringToTLV(jsonString));
}
public byte[] stringToTLV(String content) {
  String func_type = "func";
  byte[] prefixBytes = func_type.getBytes(StandardCharsets.UTF_8);
  byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
  int contentLength = contentBytes.length;
  ByteBuffer buffer = ByteBuffer.allocate(prefixBytes.length + 4 + contentLength);
  buffer.order(ByteOrder.BIG_ENDIAN);
  buffer.put(prefixBytes);
  buffer.putInt(contentLength);
  buffer.put(contentBytes);
  return buffer.array();
}

Java

```

### 步骤 4：获取 Function Calling 最终答复
当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## FAQ
Q1：配置了 Function Calling 后，通过 updateVoiceChat/sendUserBinaryMessage 返回给智能体的工具调用结果信息可以不读出来吗? A1：不可以。
Q2：配置了 Function Calling 后，相关配置如何更新？ A1：需要先调用 StopVoiceChat 接口停止智能体任务，然后再调用 StartVoiceChat 接口重新配置 Function Calling 功能。




## 语音识别配置--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1581712


  * 文档首页
/实时音视频/实时对话式 AI/体验进阶/语音识别配置

语音识别配置
最近更新时间：2025.06.12 17:48:16首次发布时间：2025.05.29 11:18:34

文档反馈
在实时对话式 AI 场景中，你需要配置语音识别（ASR）能力，将用户的语音实时转换为文本，以便智能体进行理解和处理。RTC 提供一站式接入方案，只需在 StartVoiceChat 接口配置 ASRConfig 即可完成语音识别能力接入。本文将详细介绍不同服务的配置参数、注意事项及适用场景，帮助你根据需求选择合适的语音识别方案。
火山引擎提供以下两种语音识别接入方案，分别适用于不同场景：
  * 火山引擎流式语音识别大模型：识别准确率更高，适用于对识别准确率要求较高的场景（如会议记录、智能客服等）。
  * 火山引擎流式语音识别：识别速度更快，适用于响应速度要求高的语音控制场景。


## 火山引擎流式语音识别大模型
该接入方案由大模型能力加持，识别准确率更高，适用于对识别准确率要求较高的场景（如会议记录、智能客服等）。详细功能特性可参看。
### 核心配置参数
使用火山引擎流式语音识别大模型时，StartVoiceChat.ASRConfig 结构核心配置参数如下：
> 完整参数及说明可参看 接口。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
Provider| String| 是| volcano| 语音识别服务提供商。该参数固定取值：volcano，表示仅支持火山引擎语音识别服务  
ProviderParams.Mode| String| 是| bigmodel| 模型类型，固定取值 bigmodel。  
ProviderParams.AppId| String| 是| 93****21| 开通流式语音识别大模型服务的 App ID。从获取。  
ProviderParams.AccessToken| String| 是| MOaOaa_VQ6****1B34UHA4h5B| 与 App ID 对应的鉴权 Token。从获取。  
ProviderParams.ApiResourceId| String| 否| volc.bigasr.sauc.duration| 服务开通类型：volc.bigasr.sauc.duration（小时版）；volc.bigasr.sauc.concurrent（并发版）。默认 duration。  
ProviderParams.StreamMode| Integer| 否| 0| 输出模式：
  * 0（流式输入流式输出，实时性高）；
  * 1（流式输入非流式输出，准确率高）。默认 0。

  
### 请求示例代码
你可参看以下示例，使用火山引擎流式语音识别大模型进行语音识别：
```
{
  "ASRConfig": {
    "Provider": "volcano",
    "ProviderParams": {
      "Mode": "bigmodel",
      "AppId": "93****21",
      "AccessToken": "MOaOaa_VQ6****1B34UHA4h5B",
      "ApiResourceId": "volc.bigasr.sauc.duration",
      "StreamMode": 0,
    },
    "VADConfig": {
      "SilenceTime": 800,
      "VolumeGain": 0.3
    },
    "TurnDetectionMode": 0
  }
}

json

```

### 注意事项
#### 提升语音识别准确性
在使用中，如果你觉得识别结果不够准确，可通过调整音量增强、添加热词、替换词等方式提升识别准确率。详细使用说明参看。
#### 输出模式选择建议
你可以使用 StreamMode 参数控制语音识别大模型输出模式。
  * 取值为 0 时，表示流式输入流式输出。此时识别结果会分段、实时地返回。该模式下识别速度更快，适用于实时字幕场景。
  * 取值为 1 时，表示流式输入非流式输出。即在完整接收并处理完整个语音片段后，一次性返回最终的识别结果。该模式下识别准确率更高，适用于不需要语音打断功能的场景。


#### 打断配置优化
  * 若需通过关键词触发打断（如“停止”），建议将 InterruptSpeechDuration 设为 0，避免自动阈值覆盖关键词触发逻辑。
  * 背景噪音较多时，可通过 VolumeGain 调节音量增益值，当低增益值可减少噪音引起的 ASR 错误识别，减少误触发。


#### 处理较长音频流式断句问题
在使用流式语音识别大模型时，如果智能体输出音频过长，可能会出现输出未结束便被截断。为避免这种情况，你可以使用功能手动控制智能体结束输出的时机。
## 火山引擎流式语音识别
该接入方案采用业端到端算法模型，识别速度更快，适用于响应速度要求高的语音控制场景。详细功能特性可参看。
### 核心配置参数
使用 StartVoiceChat 接口配置时，需通过 Config.ASRConfig 结构设置参数。以下为核心配置参数说明：
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
Provider| String| 是| volcano| 语音识别服务提供商，固定取值 volcano（仅支持火山引擎服务）。  
TurnDetectionMode| Integer| 否| 0| 对话触发方式：
  * 0（服务端自动检测完整语句触发）；
  * 1（手动触发）。

默认 0。  
ProviderParams.Mode| String| 是| smallmodel| 模型类型，固定取值 smallmodel。  
ProviderParams.AppId| String| 是| 93****21| 开通流式语音识别服务的 App ID。从获取。  
ProviderParams.Cluster| String| 是| volcengine_streaming_common| 服务集群标识。从开通后获取。  
### 请求示例代码
你可参看以下示例，使用火山引擎流式语音识别进行语音识别：
```
{
  "ASRConfig": {
    "Provider": "volcano",
    "ProviderParams": {
      "Mode": "smallmodel",
      "AppId": "93****21",
      "Cluster": "volcengine_streaming_common"
    },
    "VADConfig": {
      "SilenceTime": 800,
      "VolumeGain": 0.3
    },
    "TurnDetectionMode": 0
  }
}

json

```

### 注意事项
#### 打断配置优化
  * 若需通过关键词触发打断（如“停止”），建议将 InterruptSpeechDuration 设为 0，避免自动阈值覆盖关键词触发逻辑。
  * 背景噪音较多时，可通过 VolumeGain 调节音量增益值，当低增益值可减少噪音引起的 ASR 错误识别，减少误触发。






## 语音合成配置--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1581713


  * 文档首页
/实时音视频/实时对话式 AI/体验进阶/语音合成配置

语音合成配置
最近更新时间：2025.06.12 17:48:16首次发布时间：2025.05.29 11:18:34

文档反馈
在实时对话式 AI 场景中，你需要配置语音合成（TTS）能力，将大模型生成的文本转换为自然流畅的语音输出，实现智能体与真人用户的语音交互。RTC 提供一站式接入方案，只需在 StartVoiceChat 接口配置 TTSConfig 即可完成语音合成能力接入。本文将详细介绍不同服务的配置参数、注意事项及适用场景，帮助你根据需求选择合适的方案。
火山引擎提供以下六种语音合成接入方案，分别适用于不同场景：
## 火山流式语音合成
火山引擎流式语音合成采用端到端合成方案，生成速度快，满足常规语音播报需求，适合短语或标准回复，例如，提醒、系统反馈、数字播报。详细功能特性参看。
### 核心配置参数
使用 StartVoiceChat 接口配置火山引擎流式语音合成时，需通过 Config.TTSConfig 结构设置参数，以下为核心配置参数说明：
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
IgnoreBracketText| Int[]| 否| [1,2]| 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。  
Provider| String| 是| volcano| 语音合成服务提供商，固定取值 volcano。  
ProviderParams.app.appid| String| 是| 94****11| 开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录获取。  
ProviderParams.app.cluster| String| 是| volcano_tts| 已开通语音合成服务对应的集群标识（Cluster ID）。你可登录开通服务后获取。  
ProviderParams.audio.voice_type| String| 是| BV001_streaming| 已开通音色对应的音色种类（Voice_type）。你可登录购买音色后获取。。  
ProviderParams.audio.speed_ratio| Float| 否| 1.0| 语速。取值 [0.2,3]，默认 1.0，值越大语速越快。  
ProviderParams.audio.volume_ratio| Float| 否| 1.0| 音量。取值 [0.1,3]，默认 1.0，值越大音量越高。  
ProviderParams.audio.pitch_ratio| Float| 否| 1.0| 音高。取值 [0.1,3]，默认 1.0，值越大音调越高。  
### 请求示例代码
你可参看以下示例，使用火山流式语音合成进行语音合成：
```
{
  "TTSConfig": {
    "IgnoreBracketText":[
      1,
      2
    ],
    "Provider": "volcano",
    "ProviderParams": {
      "app": {
        "appid": "94****11",
        "cluster": "volcano_tts"
      },
      "audio": {
        "voice_type": "BV001_streaming",
        "speed_ratio": 1.2,
        "volume_ratio": 1.1,
        "pitch_ratio": 1.0
      }
    }
  }
}

json

```

## 火山语音合成大模型（非流式输入流式输出）
该方案基于大模型实现非流式文本输入、流式语音输出，支持 SSML 标记语言，相较于传统语音合成技术，大语音模型在口语自然度、连贯性、拟人度、音质、韵律、气口、情感、语气词表达等各方面更强。详细功能特性参看。
### 核心配置参数
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
IgnoreBracketText| Int[]| 否| [1,2]| 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。  
Provider| String| 是| volcano| 语音合成服务提供商，固定取值 volcano。  
ProviderParams.app.appid| String| 是| 94****11| 开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录获取。  
ProviderParams.app.cluster| String| 是| volcano_tts| 已开通语音合成大模型服务对应的集群标识（Cluster ID）。你可登录开通服务后获取。  
ProviderParams.audio.voice_type| String| 是| zh_female_meilinvyou_moon_bigtts| 已开通音色对应的音色种类（Voice_type）。你可登录购买音色后获取。  
ProviderParams.audio.pitch_rate| Integer| 否| 0| 音调。取值 [-12,12]，默认 0，值越大音调越高。  
ProviderParams.audio.speech_rate| Integer| 否| 0| 语速。取值 [-50,100]，默认 0，100 为 2 倍速，-50 为 0.5 倍速。  
### 请求示例代码
你可参看以下示例，使用火山语音合成大模型（非流式输入流式输出）进行语音合成：
```
{
  "TTSConfig": {
    "IgnoreBracketText":[
      1,
      2
    ],
    "Provider": "volcano",
    "ProviderParams": {
      "app": {
        "appid": "94****11",
        "cluster": "volcano_tts"
      },
      "audio": {
        "voice_type": "zh_female_meilinvyou_moon_bigtts",
        "pitch_rate": 2,
        "speech_rate": 30
      }
    }
  }
}

json

```

## 火山语音合成大模型（流式输入流式输出）
该方案基于大模型支持流式文本输入和流式语音输出，支持流式逐字级别输入级输出，进一步降低基于大模型的语音交互时延，用户体感延迟低，且支持 Markdown 标记过滤、公式播报 和 Latex 能力。详细功能特性参看。
### 核心配置参数
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
IgnoreBracketText| Int[]| 否| [1,2]| 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。  
Provider| String| 是| volcano_bidirection| 语音合成服务提供商，固定取值 volcano_bidirection。  
ProviderParams.app.appid| String| 是| 94****11| 开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录获取。  
ProviderParams.app.token| String| 是| OaO****ws1| 与语音合成大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录获取。Access Token 查找方式，可参看。  
ProviderParams.audio.voice_type| String| 是| BV001_streaming| 已开通音色对应的音色种类（Voice_type）。你可登录购买音色后获取。  
ProviderParams.audio.pitch_rate| Integer| 否| 0| 音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。  
ProviderParams.audio.speech_rate| Integer| 否| 0| 语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。  
ProviderParams.Additions.disable_markdown_filter| Boolean| 否| true| 是否过滤 Markdown 标记。  
ProviderParams.Additions.enable_latex_tn| Boolean| 否| true| 是否播报 Latex 公式（需 disable_markdown_filter 为 true 生效）。  
ProviderParams.ResourceId| String| 是| volc.service_type.10029| 调用服务的资源信息 ID，该参数固定取值：volc.service_type.10029。  
### 请求示例代码
你可参看以下示例，使用火山语音合成大模型（流式输入流式输出）进行语音合成：
```
{
  "TTSConfig": {
    "IgnoreBracketText":[
      1,
      2
    ],
    "Provider": "volcano_bidirection",
    "ProviderParams": {
      "app": {
        "appid": "94****11",
        "token": "OaO****ws1",
      },
      "audio": {
        "voice_type": "BV001_streaming",
        "pitch_rate": 1,
        "speech_rate": 20
      },
      "Additions": {
        "enable_latex_tn": true,
        "disable_markdown_filter": true
      },
      "ResourceId": "volc.service_type.10029"
    }
  }
}

json

```

## 火山声音复刻大模型（非流式输入流式输出）
该方案支持复刻真人音色，非流式文本输入、流式语音输出，语音合成速度更快，适用于需要定制化音色的文本场景（如企业客服、虚拟主播）。详细功能特性参看。
### 核心配置参数
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
IgnoreBracketText| Int[]| 否| [1,2]| 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。  
Provider| String| 是| volcano| 语音合成服务提供商，固定取值 volcano。  
ProviderParams.app.appid| String| 是| 94****11| 开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录获取。  
ProviderParams.app.cluster| String| 是| volcano_icl| 已开通声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录开通服务后获取。  
ProviderParams.audio.voice_type| String| 是| S_N****T7k1| 声音复刻声音 ID。你可登录获取。  
ProviderParams.audio.speed_ratio| Float| 否| 1.0| 语速。取值范围为[0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。  
### 请求示例代码
你可参看以下示例，使用火山声音复刻大模型（非流式输入流式输出）进行语音合成：
```
{
  "TTSConfig": {
    "IgnoreBracketText":[
      1,
      2
    ],
    "Provider": "volcano",
    "ProviderParams": {
      "app": {
        "appid": "94****11",
        "cluster": "volcano_icl"
      },
      "audio": {
        "voice_type": "S_N****T7k1",
        "speed_ratio": 1.1
      }
    }
  }
}

json

```

### 注意事项
使用前请参看及了解如何实现声音复刻最佳效果。
## 火山声音复刻大模型（流式输入流式输出）
该方案支持复刻真人音色，流式文本输入、流式语音输出，语音合成效果更自然。详细功能特性参看。
### 核心配置参数
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
IgnoreBracketText| Int[]| 否| [1,2]| 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。  
Provider| String| 是| volcano_bidirection| 语音合成服务提供商，固定取值 volcano_bidirection。  
ProviderParams.app.appid| String| 是| 94****11| 开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录获取。  
ProviderParams.app.token| String| 是| OaO****ws1| 与开通声音复刻大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录获取。Access Token 查找方式，可参看。  
ProviderParams.audio.voice_type| String| 是| S_N****T7k1| 声音复刻声音 ID。你可登录获取。  
ProviderParams.audio.speed_ratio| Float| 否| 1.0| 语速。取值范围为[0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。  
ProviderParams.ResourceId| String| 是| volc.megatts.default| 调用服务的资源信息 ID，该参数固定取值：volc.megatts.default。  
### 请求示例代码
你可参看以下示例，使用火山声音复刻大模型（流式输入流式输出）进行语音合成：
```
{
  "TTSConfig": {
    "IgnoreBracketText":[
      1,
      2
    ],
    "Provider": "volcano_bidirection",
    "ProviderParams": {
      "app": {
        "appid": "94****11",
        "token": "OaO****ws1",
      },
      "audio": {
        "voice_type": "S_N****T7k1",
        "speed_ratio": 1.1
      },
      "ResourceId": "volc.megatts.default"
    }
  }
}

json

```

### 注意事项
使用前请参看 及了解如何实现声音复刻最佳效果。
## MiniMax 语音合成
MiniMax 语音合成支持多语言、混合音色和特殊发音标注。详细功能特性参看。
### 核心配置参数
> 完整参数及说明可参看 。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
IgnoreBracketText| Int[]| 否| [1,2]| 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。  
Provider| String| 是| minimax| 语音合成服务提供商，固定取值 minimax。  
ProviderParams.Authorization| String| 是| eyJhbG****SUzI1N| API 密钥。前往 获取。  
ProviderParams.Groupid| String| 是| 983*****669| 用户所属组 ID。前往 获取。  
ProviderParams.model| String| 是| speech-01-turbo| 模型版本（否 speech-01-turbo/speech-01-240228/speech-01-turbo-240228）。  
ProviderParams.URL| String| 是| https://api.minimax.chat/v1/t2a_v2| 请求语音合成 URL，该参数固定取值：https://api.minimax.chat/v1/t2a_v2。  
ProviderParams.stream| Boolean| 是| false| 是否流式输出。  
ProviderParams.voice_setting| Object| 否| {"speed":1.0,"vol":1.0,"pitch":1.0}| 语速、音量、音调配置。  
ProviderParams.pronunciation_dict| Object| 否| {"tone": ["处理/(chu3)(li3)", "危险/dangerous"]}| 特殊发音标注。  
ProviderParams.language_boost| String| 否| Chinese,Yue| 语言偏好。取值范围：Chinese/Yue，默认值为 Chinese。  
ProviderParams.timber_weights| Array| 否| [{"voice_id":"male-qn-jingying","weight":70},{"voice_id":"wumei_yujie","weight":30}]| 合成音色权重设置。  
### 请求示例代码
你可参看以下示例，使用MiniMax 语音合成进行语音合成：
```
{
  "TTSConfig": {
    "IgnoreBracketText": [
      1,
      2
    ],
    "Provider": "minimax",
    "ProviderParams": {
      "Authorization": "eyJhbG****SUzI1N",
      "Groupid": "983*****669",
      "model": "speech-01-turbo",
      "URL": "https://api.minimax.chat/v1/t2a_v2",
      "stream": false,
      "voice_setting": {
        "speed": 1.0,
        "vol": 1.0,
        "pitch": 1.0
      },
      "pronunciation_dict": {
        "tone": [
          "处理/(chu3)(li3)",
          "危险/dangerous"
        ]
      },
      "language_boost": "Chinese,Yue",
      "timber_weights": [
        {
          "voice_id": "male-qn-jingying",
          "weight": 70
        },
        {
          "voice_id": "wumei_yujie",
          "weight": 30
        }
      ]
    }
  }
}

json

```

## 语音合成配置 FAQ
如何更换音色？ 你需按照以下步骤进行操作： a. 前往，选择你使用的语音合成服务，如语音合成大模型。 b. 在服务详情页，单击音色购买，选择你需要的音色。 c. 购买完成后，在服务详情页查找要更换音色的 Voice_type。 d. 调用 StopVoiceChat 接口停止当前智能体任务，使用 StartVoiceChat 接口，在 TTSConfig 中的 Voice_type 参数中填入更换音色的 Voice_type 重新启动智能体任务。




## 大模型配置--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1581714


  * 文档首页
/实时音视频/实时对话式 AI/体验进阶/大模型配置

大模型配置
最近更新时间：2025.05.29 13:46:05首次发布时间：2025.05.29 11:18:34

文档反馈
在实时对话式 AI 场景中，你需要配置大模型（LLM）能力，解析用户输入，生成智能回复，管理上下文等。RTC 提供一站式接入方案，只需在 StartVoiceChat接口配置 LLMConfig 即可完成大模型接入。本文将详细介绍不同平台的配置参数、注意事项及适用场景，帮助你根据需求选择合适的大模型方案。
火山引擎提供三种大模型接入平台，分别适用于不同场景：
  * 火山方舟平台：适用于需要调用火山引擎官方基础模型（如 Doubao、DeepSeek 等）或使用应用实验室功能（零代码组合插件）的场景，支持视觉理解、Function Calling 等高级能力。
  * Coze 平台：适用于快速搭建零代码/低代码 AI 应用的场景。
  * 第三方大模型：适用于需要集成自有大模型或特定第三方服务（如开源大模型、垂直领域专用模型）的定制化场景。


## 火山方舟平台
火山方舟平台是火山引擎提供的大模型服务平台，支持直接调用多种火山引擎部署的基础模型（如 Doubao、DeepSeek 等）或应用实验室功能（如零代码组合插件调用大模型）。该平台模型除支持基本文字处理能力外，还支持视觉理解能力和 Function Calling 功能。
### 核心配置参数
使用火山方舟平台时，StartVoiceChat.LLMConfig 结构核心配置参数如下：
> 完整参数及说明可参看 接口。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
Mode| String| 是| ArkV3| 大模型平台标识。使用火山方舟平台时，该参数固定取值：ArkV3。  
EndPointId| String| 选填（与 BotId 二选一）| ep-22****212| 自定义推理接入点 ID（用于调用基础模型推理功能）。  
BotId| String| 选填（与 EndPointId 二选一）| botid****212| 应用 ID（用于使用方舟应用实验室功能）。  
Temperature| Float| 选填| 0.1| 采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为 （0,1]，默认值为 0.1。  
MaxTokens| Integer| 选填| 1024| 输出文本的最大 token 限制。默认值为 1024。  
TopP| Float| 选填| 0.3| 采样选择范围。控制输出 token 多样性，值越大类型越丰富。取值范围为[0,1]，默认值为 0.3。  
SystemMessages| String[]| 选填| ["你是小宁，性格幽默又善解人意。"]| 系统提示词。用于输入控制大模型行为方式的指令，定义了模型的角色、行为准则，特定的输出格式等。  
UserPrompts| Object[]| 选填| [{"Role": "user", "Content": "你好"}, {"Role": "assistant", "Content": "有什么可以帮到你？"}]| 用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。  
HistoryLength| Integer| 选填| 3| 历史问题轮数，控制上下文存储长度。默认值为 3。  
Tools| Object[]| 选填| -| 使用 Function calling 功能时，模型可以调用的工具列表。仅支持调用函数。该功能使用方法参看。  
Prefill| Boolean| 选填| false| 是否将 ASR 中间结果提前送入大模型，以降低延时，但会增加模型消耗。  
VisionConfig| Object| 选填| -| 视觉理解能力配置，仅 Doubao vision 系列模型生效。该功能使用方法参看  
### 请求示例代码
你可参看以下示例，使用火山方舟平台通过自定义推理接入点进行大模型调用：
```
{
  "LLMConfig": {
    "Mode": "ArkV3",
    "EndPointId": "epid****212",
    "MaxTokens": 1024,
    "Temperature": 0.1,
    "TopP": 0.3,
    "SystemMessages": [
      "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
    ],
    "UserPrompts": [
      {
        "Role": "user",
        "Content": "你好"
      },
      {
        "Role": "assistant",
        "Content": "有什么可以帮到你的？"
      }
    ],
    "HistoryLength": 3,
    "Tools": [
      {
        "Type": "function",
        "function": {
          "name": "get_current_weather",
          "description": "获取给定地点的天气",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "地理位置，比如北京市"
              },
              "unit": {
                "type": "string",
                "description": "",
                "enum": [
                  "摄氏度",
                  "华氏度"
                ]
              }
            },
            "required": [
              "location"
            ]
          }
        }
      }
    ]
  }
      "FunctionCallingConfig": {
        "ServerMessageUrl": "https://example-domain.com/vertc/fc",
        "ServerMessageSignature": "b46a****8ad6a",
  }
}

json

```

### 注意事项
#### EndPointId 与 BotId 区别
EndPointId 与 BotId 使用场景不同：EndPointId：在需要使用模型推理功能（如直接调用部署的基础模型）时，填入 EndPointId。且仅支持自定义推理接入点，不支持预置推理接入点。BotId：在需要使用应用实验室功能（如零代码组合插件调用大模型）时，填入 BotId。且创建应用时仅支持使用自定义推理接入点，不支持预置推理接入点。
#### 对话上下文管理
使用火山方舟平台时，模型对话上下文生成逻辑由系统提示词（SystemMessages）、用户提示词（UserPrompts 和 UserMessages）和 历史问题轮数（HistoryLength）共同控制。具体参看。
#### Function Calling 功能
使用 Function Calling 功能允许大模型识别用户对话中的特定需求，智能调用外部函数、API 等工具来执行它自身无法独立完成的任务，如处理实时数据检索、文件处理、数据库查询等。该功能不支持与联网插件、知识库插件同时开启。该功能详细说明参看。 推荐使用 doubao-1.5 代系模型或 DeepSeek 模型，可更快收到返回结果。
#### 视觉理解能力
仅在使用 EndPointId 调用 Doubao 大模型时支持使用视觉理解能力，且创建自定义推理接入点时需选择 vision 系列模型，如 Doubao-vision-pro-32k。该功能详细说明参看。
## Coze 平台
Coze 平台是 AI Agent 开发平台。支持零代码/低代码快速搭建基于大模型的各类 AI 应用。
### 核心配置参数
使用 Coze 平台时，StartVoiceChat.LLMConfig 结构核心配置参数如下：
> 完整参数及说明可参看 接口。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
Mode| String| 是| CozeBot| 大模型平台标识。固定取值 CozeBot，标识使用 Coze 平台。  
CozeBotConfig.Url| String| 是| https://api.coze.cn| 固定请求地址。该参数固定取值：https://api.coze.cn。  
CozeBotConfig.BotId| String| 是| 73****68| Coze 智能体 ID（从智能体开发页面获取）。  
CozeBotConfig.APIKey| String| 是| czu_UEE2mJn6****MHxLCVv9uQ7H| Coze 访问密钥（需授权对应权限）。  
CozeBotConfig.UserId| String| 是| 123| 用户标识（用于隔离对话上下文）。  
CozeBotConfig.HistoryLength| Integer| 选填| 3| 历史问题轮数（控制上下文存储长度）。  
CozeBotConfig.Prefill| Boolean| 选填| false| 是否将 ASR 中间结果提前送入大模型（降低延时但增加消耗）。  
CozeBotConfig.EnableConversation| Boolean| 选填| false| 是否使用 Coze 平台上下文管理（开启后 HistoryLength 无效）。  
### 请求示例代码
你可参看以下示例，使用Coze 平台进行大模型调用：
```
{
  "LLMConfig": {
    "Mode": "CozeBot",
    "CozeBotConfig": {
      "Url": "https://api.coze.cn",
      "BotId": "73****68",
      "APIKey": "czu_UEE2mJn6****MHxLCVv9uQ7H",
      "UserId": "123",
      "HistoryLength": 3,
      "Prefill": false,
      "EnableConversation": false
    }
  }
}

json

```

### 注意事项
#### 智能体发布
在使用 BotId 调用 Coze 智能体前，该智能体必须发布为 API。发布操作可参看。
#### 访问密钥
你可以生成以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考 。
说明
创建个人访问令牌或 OAuth 应用时，你需要根据你的 Bot 使用场景勾选对应权限，否则会鉴权失败。
#### 上下文管理
你可选择将上下文管理交由 Coze 平台管理，或由 RTC 管理。
  * RTC 管理：EnableConversation 为 false 时，上下文由 RTC 管理，默认配置。
  * Coze 平台管理：EnableConversation 为 true 时，上下文由 Coze 管理。此时你可以使用 Coze 平台上下文管理相关功能，如将指定内容添加到会话中。


不同平台管理上下文时控制参数不同，具体参看 。
#### 能力限制
以下 Coze 能力， RTC 对话式 AI 场景下暂不支持：
如需使用以上能力，可使用 。
#### 降低请求时延
在输入请求地址时，可使用 https://bot-open-api.bytedance.net内网域名，时延更小更稳定。
## 第三方大模型/Agent
火山引擎支持接入第三方大模型或 Agent，以满足需要集成自有大模型或特定第三方服务的场景。
说明
集成前需要你提供第三方大模型或 Agent 的服务请求接口，并确保该接口符合火山引擎 RTC 标准规范，否则需要对其进行改造。具体可参看。
### 核心配置参数
使用第三方大模型时，StartVoiceChat.LLMConfig 结构核心配置参数如下：
> 完整参数及说明可参看 接口。
参数名| 类型| 是否必填| 示例值| 说明  
---|---|---|---|---  
Mode| String| 是| CustomLLM| 大模型平台名称。使用第三方大模型时，该参数固定取值： CustomLLM。  
URL| String| 是| https://api.***.com/v1/chat/completions| 第三方大模型请求地址（需 HTTPS 域名）。  
ModelName| String| 选填| name1| 第三方大模型名称。  
APIKey| String| 选填| pat*****123231| 鉴权 Token（Bearer Token 认证方式）。  
Temperature| Float| 选填| 0.1| 采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为 （0,1]，默认值为 0.1。  
MaxTokens| Integer| 选填| 1024| 输出文本的最大 token 限制。默认值为 1024  
TopP| Float| 选填| 0.3| 采样选择范围。控制输出 token 多样性，值越大类型越丰富。取值范围为[0,1]，默认值为 0.3。  
SystemMessages| String[]| 选填| ["你是小宁，性格幽默又善解人意。"]| 系统提示词（定义模型角色、行为准则）。  
UserPrompts| Object[]| 选填| [{"Role": "user", "Content": "你好"}, {"Role": "assistant", "Content": "有什么可以帮到你？"}]| 用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。  
HistoryLength| Integer| 选填| 3| 历史问题轮数，控制上下文存储长度。默认值为 3。  
Feature| String| 选填| {"Http":true}| 测试标记（如 {"Http":true} 允许 HTTP 域名测试）。  
Prefill| Boolean| 选填| false| 是否将 ASR 中间结果提前送入大模型（降低延时但增加消耗）。  
### 请求示例代码
你可参看以下示例，使用第三方大模型/Agent进行大模型调用：
```
{
  "LLMConfig": {
      "Mode": "CustomLLM",
      "URL": "https://api.***.com/v1/chat/completions",
      "ModelName": "name1",
      "APIKey": "pat*****123231",
      "MaxTokens": 1024,
      "Temperature": 0,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserPrompts": [
        {
          "Role": "user",
          "Content": "你好"
        },
        {
          "Role": "assistant",
          "Content": "有什么可以帮到你的？"
        }
      ],
      "HistoryLength": 3
    }
}

json

```

### 注意事项
#### 1. 接口验证
若需要验证第三方大模型 URL 是否符合火山引擎标准，可前往，点击修改 AI 设定，选择第三方模型填入 URL 进行快速验证，若验证失败可前往下载验证工具查看详细报错。
#### 2. 对话上下文管理
使用符合火山接口标准的第三方大模型时，模型对话上下文生成逻辑由系统提示词（SystemMessages）、用户提示词（UserPrompts 和 UserMessages）和 历史问题轮数（HistoryLength）共同控制。具体参看。
## 大模型配置 FAQ
Q1：如何更换大模型配置？ A1：你需要先调用 StopVoiceChat 接口停止当前智能体任务，随后在 StartVoiceChat.LLMConfig 中传入新的大模型配置，并重新启动智能体任务。 Q2：对话过程中，一分钟前生成的结果，现在再提问，智能体已经不记得之前回答结果。 A2：可适当调大 LLMConfig.HistoryLength 参数，增大上下文存储长度。




## 开通消息通知服务--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/75110


  * 文档首页
/实时音视频/服务端 API 参考/服务端回调/开通消息通知服务

开通消息通知服务
最近更新时间：2025.01.21 18:47:29首次发布时间：2021.09.27 11:20:02

文档反馈
如果你希望在你的 RTC 应用的业务服务端直接获取 RTC 的使用状态，你可以使用消息通知服务。 开通消息通知服务后，当指定的事件发生时，你的应用的业务服务端会收到来自 RTC 服务端的消息通知。
## 工作原理
工作原理如图：
## 开通服务
遵循以下步骤，开通消息通知服务:
  1. 访问 RTC 控制台。
  2. 访问 功能配置-回调配置 页面。
  3. 填写回调相关信息：


  * 回调 Url：接收回调的 URL 地址。关于如何接收并验证回调，参考 。
  * 回调密钥：输入的秘钥将被用来对回调信息进行签名。
  * 回调事件：你希望接收的回调事件。关于目前支持哪些事件，参考 。


## 开通结果
开通服务后，当你订阅的事件发生时，你指定的 URL 地址会收到 HTTP(s) 调用。调用中包含相关的事件信息，参考 。




## 接收消息通知回调--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/69820


  * 文档首页
/实时音视频/服务端 API 参考/服务端回调/接收消息通知回调

接收消息通知回调
最近更新时间：2024.11.26 19:13:39首次发布时间：2021.07.18 15:05:57

文档反馈
你必须在你的业务服务端部署 HTTP(s) 服务以接收回调。
接收回调响应的 HTTP 状态码为 200 时，RTC 服务端即认为回调成功；状态码不为 200，或响应时间超过5秒，都视为回调失败，RTC 服务端会重试。重试最多2次（总计回调不超过3次）。当前不保证回调事件的唯一性。建议你可以使用 对事件回调进行去重。
RTC 会为服务端回调签名。如果你希望确定来源是否是火山以及内容是否被篡改，你可以检验回调签名；否则，你可以忽略回调签名。如果需要检验签名，参考以下签名算法和实现。
## 签名算法
RTC 服务端发起回调时，会使用你设置的回调密钥对回调签名。你可以参考以下签名算法进行验签：
1、创建参数字符数组
将用户回调密钥SecretKey和接收到回调字段值：EventType、EventData、EventTime、EventId、Version、AppId、Nonce、SecretKey 组成数组：
```
type Event struct {
	EventType string `json:"EventType"`
	EventData string `json:"EventData"`
	EventTime string `json:"EventTime"`
	EventId  string `json:"EventId"`
	AppId   string `json:"AppId"`
	Version  string `json:"Version"` 
	Nonce   string `json:"Nonce"`
	Signature string `json:"Signature"`
}
var event Event
data := []string{
	event.EventType,
	event.EventData,
	event.EventTime,
	event.EventId,
	event.AppId,
	event.Version,
	event.Nonce,
	secretKey,
}

```

示例 ： [EventTypeVal EventDataVal EventTimeVal EventIdVal VersionVal AppIDVal NonceVal SecretKeyVal]
2、按字母序排列步骤一字符数组
```
SortedData = Sort(data)

```

示例 ： [AppIDVal EventDataVal EventIdVal EventTimeVal EventTypeVal NonceVal SecretKeyVal VersionVal]
3、将排序后字符数组元素直接拼接成字符串得到 PayloadData：
```
PayloadData=Join(SortedData)

```

示例 ： [AppIDValEventDataValEventIdValEventTimeValEventTypeValNonceValSecretKeyValVersionVal]
4、对拼接好的字符串进行 SHA256 哈希算法：
```
HashData=Hash(PayloadData)

```

示例 ： [20 170 104 20 32 203 70 171 95 29 138 214 172 154 178 31 52 30 169 219 249 213 35 112 89 195 196 192 231 49 48 15]
5、进行十六进制编码，得到回调签名
```
Signature=HexEncode(HashData)

```

示例 ： 14aa681420cb46ab5f1d8ad6ac9ab21f341ea9dbf9d5237059c3c4c0e731300f
> 备注：SecretKey 是你在控制台上配置的回调密钥。`
## 签名算法示例
RTC 提供了 Golang 和 Java 语言的示例代码，你可以参考以下示例进行验签：
### Golang
```
import (
  "crypto/sha256"
  "fmt"
  "sort"
  "strings"
)
func main() {
  event := &Event{
    EventType: "RoomCreate",
    EventData: "{\"RoomId\":\"room1\",\"Timestamp\":1679383924691}",
    EventTime: "2023-03-21T15:32:04+08:00",
    EventId:  "123456",
    Version:  "2020-12-01",
    AppId:   "appId",
    Nonce:   "aaBc",
    Signature: "1c7200723842eff514b65fc3f065597432bbb4249e10d33db79b3853d05f3691",
  }
  
  secretKey := "1234"
  ok := CheckSignature(event, secretKey)
  fmt.Printf("the result is %t", ok)
}
type Event struct {
  EventType string `json:"EventType"`
  EventData string `json:"EventData"`
  EventTime string `json:"EventTime"`
  EventId  string `json:"EventId"`
  AppId   string `json:"AppId"`
  Version  string `json:"Version"`
  Signature string `json:"Signature"`
  Nonce   string `json:"Nonce"`
}
func CheckSignature(event *Event, secretKey string) bool {
  data := []string{
    event.EventType,
    event.EventData,
    event.EventTime,
    event.EventId,
    event.AppId,
    event.Version,
    event.Nonce,
    secretKey,
  }
  sort.Strings(data)
  payload := strings.Join(data, "")
  hashData := sha256.Sum256([]byte(payload))
  signature := fmt.Sprintf("%x", hashData)
  fmt.Println("sign:" + signature)
  return signature == event.Signature
}

Go

```

### Java
```
import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.security.NoSuchAlgorithmException;
class Event{
  public String EventType;
  public String EventData;
  public String EventTime;
  public String EventId;
  public String AppId;
  public String Version;
  public String Nonce;
  public String Signature;
}
public class CheckSignature {
  public static void main(String[] args) {
    Event event=new Event();
    event.EventType="RoomCreate";
    event.EventData="{\"RoomId\":\"room1\",\"Timestamp\":1679383924691}";
    event.EventTime="2023-03-21T15:32:04+08:00";
    event.EventId="123456";
    event.Version="2020-12-01";
    event.AppId="appId";
    event.Nonce="aaBc";
    event.Signature="1c7200723842eff514b65fc3f065597432bbb4249e10d33db79b3853d05f3691";
    String secretKey="1234";
    boolean ret=Check(event,secretKey);
    System.out.println(ret);
  }
  private static boolean Check(Event event,String secretKey){
    try {
      List<String> data = new ArrayList<>();
      data.add(event.EventType);
      data.add(event.EventData);
      data.add(event.EventTime);
      data.add(event.EventId);
      data.add(event.AppId);
      data.add(event.Version);
      data.add(event.Nonce);
      data.add(secretKey);
      Collections.sort(data);
      final String payloadData=String.join("",data);
      MessageDigest digest = MessageDigest.getInstance("SHA-256");
      digest.update(payloadData.getBytes());
      String signature=byteToHexString(digest.digest());
      System.out.println(signature);
      if(event.Signature.equals(signature)){
        return true;
      }
    } catch (NoSuchAlgorithmException e){
      e.printStackTrace();
    }
    return false;
  }
  private static String byteToHexString(byte[] bytes){
    return String.format("%064x", new BigInteger(1,bytes));
  }
}

Java

```





## 回调格式参考--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/75124


  * 文档首页
/实时音视频/服务端 API 参考/服务端回调/回调格式参考

回调格式参考
最近更新时间：2024.09.25 17:59:09首次发布时间：2021.09.27 11:20:13

文档反馈
开通消息通知服务，并且订阅的事件发生后，你指定的 URL 会收到来自 RTC 服务端的消息回调。相关事件的信息包含在回调字段中。
参考本文，了解消息回调的格式。
### 回调行为
当你设置关注的回调事件发生时，RTC 服务端会向你指定接收回调的 URL 地址发起 HTTP POST 请求。具体回调信息包含在 request Body 中。
说明
接收回调的 URL 必须以域名开头。 如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。
### 回调字段
request body 中以 Json 格式包含回调信息，具体字段如下：
参数名| 类型| 示例值| 描述  
---|---|---|---  
EventType| String| RecordStarted| 事件类型  
EventData| String| /| 具体的事件内容，格式为 Json  
EventTime| String| 1970-07-01T00:00:00Z| 事件产生时间，日期格式遵守 ISO-8601 标准。  
EventId| String| /| 事件 Id，具有唯一性，可用于去重  
AppId| String| Your_AppId| RTC 应用的唯一标识  
Version| String| 2020-12-01| 事件的版本号  
Signature| String| /| 回调签名。  
Nonce| String| /| 签名随机数 4位  
> 注：
>   * 关于目前支持消息回调的事件（EventType）和对应的事件内容 (EventData)，参看 。
>   * 当前不保证回调事件的唯一性。建议你可以使用 EventId 对事件回调进行去重。
> 





## 消息事件参考--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/75125


  * 文档首页
/实时音视频/服务端 API 参考/服务端回调/消息事件参考

消息事件参考
最近更新时间：2025.04.14 11:00:43首次发布时间：2021.09.27 11:20:21

文档反馈
消息通知服务支持的事件有：
功能| EventType| 事件描述  
---|---|---  
音频流| | 音频流开始推送  
| 音频流结束推送  
视频流| | 视频流开始推送  
| 视频流结束推送  
屏幕音频流| | 屏幕音频流开始推送  
| 屏幕音频流结束推送  
屏幕视频流| | 屏幕视频流开始推送  
| 屏幕视频流结束推送  
云端录制| | 录制任务开始  
| 录制任务结束（2020-12-01）  
| 录制任务结束（2022-06-01）  
| 录制任务结束（2023-06-01）  
| 录制任务结束（2023-11-01）  
| 上传任务启动  
| 上传进度  
| 已完成上传至存储  
| 上传失败转存备份  
| 录制音频流状态变化  
| 录制视频流状态变化  
| 录制生成 m3u8 文件且上传成功  
| 录制生成 m3u8 文件且上传失败  
房间| | 可见用户进房  
| 可见用户退房  
| 不可见用户进房  
| 不可见用户退房  
| 用户身份切换（不可见 -> 可见）  
| 用户身份切换（可见 -> 不可见）  
| 房间创建  
| 房间销毁  
音频切片| | 切片结果（2020-12-01）  
| 切片结果（2022-06-01）  
| 切片结果（2023-11-01）  
抽帧截图| | 截图结果（2020-12-01）  
| 截图结果（2022-06-01）  
| 截图结果（2023-11-01）  
在线媒体流| | 在线媒体流任务状态变化  
云录屏| | 云录屏任务状态变化  
转推直播| | 转推直播任务开始  
| 转推直播任务结束  
| 转推直播任务更新  
| 转推直播任务状态变化  
公共流| | 公共流任务状态变化  
歌曲查询| | 曲库下架歌曲列表  
互动白板文件转码| | 文件转码进度  
| 文件转码结束  
实时对话式 AI| | 智能体任务状态变化。  
## 音频流相关
### UserAudioStreamStart
EventType
UserAudioStreamStart
事件详情
音频流开始推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Timestamp": 1611736812853
}

json

```

### UserAudioStreamStop
EventType
UserAudioStreamStop
事件详情
音频流结束推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Reason| String| 音频流结束推送原因，参看  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Reason
值| 含义  
---|---  
StreamStop| 正常流停止  
LeaveRoom| 用户离开房间导致流停止  
BannedByAdmin| 服务端封禁该音频流导致流停止  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Reason": "StreamStop",
  "Timestamp": 1611736812853
}

json

```

## 视频流相关
### UserVideoStreamStart
EventType
UserVideoStreamStart
事件详情
视频流开始推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Timestamp": 1611736812853
}

json

```

### UserVideoStreamStop
EventType
UserVideoStreamStop
事件详情
视频流结束推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Reason| String| 视频流结束推送原因，参看  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Reason
值| 含义  
---|---  
StreamStop| 正常流停止  
LeaveRoom| 用户离开房间导致流停止  
BannedByAdmin| 服务端封禁该视频流导致流停止  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Reason": "StreamStop",
  "Timestamp": 1611736812853
}

json

```

## 屏幕音频流相关
### UserScreenAudioStreamStart
EventType
UserScreenAudioStreamStart
事件详情
屏幕音频流开始推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Timestamp": 1611736812853
}

json

```

### UserScreenAudioStreamStop
EventType
UserScreenAudioStreamStop
事件详情
屏幕音频流结束推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Reason| String| 屏幕音频流结束推送原因，参看  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Reason
值| 含义  
---|---  
StreamStop| 正常流停止  
LeaveRoom| 用户离开房间导致流停止  
BannedByAdmin| 服务端封禁该屏幕音频流导致流停止  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Reason": "StreamStop",
  "Timestamp": 1611736812853
}

json

```

## 屏幕视频流相关
### UserScreenVideoStreamStart
EventType
UserScreenVideoStreamStart
事件详情
屏幕视频流开始推送。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Timestamp": 1611736812853
}

json

```

### UserScreenVideoStreamStop
EventType
UserScreenVideoStreamStop
事件详情
屏幕视频流结束推送。
> 注：在 Electron 1.4.0 版本中结束屏幕视频流推送时，不会触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID  
UserId| String| Your_UserId| 用户 ID  
DeviceType| String| android、ios、web、mac、windows| 终端类型  
Reason| String| 屏幕视频流结束推送原因，参看  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Reason
值| 含义  
---|---  
StreamStop| 正常流停止  
LeaveRoom| 用户离开房间导致流停止  
BannedByAdmin| 服务端封禁该屏幕视频流导致流停止  
参数示例
```
{
  "RoomId": "Your_RoomId",
  "UserId": "Your_UserId",
  "DeviceType": "android",
  "Reason": "StreamStop",
  "Timestamp": 1611736812853
}

json

```

## 录制相关
### RecordStarted
EventType
RecordStarted
事件详情
录制任务开始。
不同版本录制功能在任务开始时均触发此回调。
无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务开始时，你都会收到录制任务开始的消息通知。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": ""
}

json

```

### RecordStopped(2020-12-01)
EventType
RecordStopped
事件详情
录制任务结束。
2020-12-01 版本录制功能在任务结束时触发此回调。
无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态。
  * 0：任务结束，录制文件上传目标存储平台成功。
  * 1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
  * 2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
RecordFileList| Array of RecordFile| 录制生成的音视频文件列表  
RecordFile
参数名称| 类型| 描述  
---|---|---  
Vid| String| 文件在点播平台的唯一标识。你可以根据 vid 可以在点播平台上找到对应的文件。  
Duration| Uint64| 文件时长，单位为毫秒。  
Size| Uint64| 文件大小，单位为 byte。  
StartTime| Uint64| 文件开始录制的 UTC 时间，单位为毫秒。  
StreamList| Array of Stream| 录制文件中包含流的列表。  
VideoCodec| String| 视频录制编码协议。默认值为 0，可以取 0 或 1。取 0 时使用 H.264,取 1 时使用 ByteVC1 编码器。  
AudioCodec| String| 音频录制编码器  
VideoWidth| Int| 录制视频宽度，单位为像素  
VideoHeight| Int| 录制视频高度，单位为像素  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
> 注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": "",
  "RecordFileList": [
    {
      "Vid": "Your_Vid",
      "Duration": 57472,
      "Size": 5018305,
      "StartTime": 1611736812853,
      "StreamList": [
        {
          "UserId": "TestUserId",
          "StreamType": 0
        }
      ],
      "VideoCodec": "h264",
      "AudioCodec": "aac",
      "VideoWidth": 640,
      "VideoHeight": 360
    }
  ]
}

json

```

### RecordStopped(2022-06-01)
EventType
RecordStopped
事件详情
录制任务结束。
2022-06-01 版本录制功能在任务结束时触发此回调。
无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态。
  * 0：任务结束，录制文件上传目标存储平台成功。
  * 1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
  * 2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

  
ErrorMessage| String| /| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
RecordFileList| Array of RecordFile| /| 录制生成的音视频文件列表  
RecordFile
参数名称| 类型| 描述  
---|---|---  
Vid| String| 文件在 平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。  
ObjectKey| String| 文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。  
Duration| Uint64| 文件时长，单位为毫秒。  
Size| Uint64| 文件大小，单位为 byte。  
StartTime| Uint64| 文件开始录制的 UTC 时间，单位为毫秒。  
StreamList| Array of | 录制文件中包含流的列表。  
VideoCodec| String| 视频录制编码协议  
AudioCodec| String| 音频录制编码器  
VideoWidth| Int| 录制视频宽度，单位为像素  
VideoHeight| Int| 录制视频高度，单位为像素  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
> 注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": "",
  "RecordFileList": [
    {
      "Vid": "Your_Vid",
      "ObjectKey": "TestObjectKey",
      "Duration": 57472,
      "Size": 5018305,
      "StartTime": 1611736812853,
      "StreamList": [
        {
          "UserId": "TestUserId",
          "StreamType": 0
        }
      ],
      "VideoCodec": "h264",
      "AudioCodec": "aac",
      "VideoWidth": 640,
      "VideoHeight": 360
    }
  ]
}

json

```

### RecordStopped(2023-06-01)
EventType
RecordStopped
事件详情
录制任务结束。
2023-06-01 版本录制功能在任务结束时触发此回调。
无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态。
  * 0：任务结束，录制文件上传目标存储平台成功。
  * 1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
  * 2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

  
ErrorMessage| String| /| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
RecordFileList| Array of RecordFile| /| 录制生成的音视频文件列表  
RecordFile
参数名称| 类型| 描述  
---|---|---  
Vid| String| 文件在 平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。  
ObjectKey| String| 文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。  
Duration| Uint64| 文件时长，单位为毫秒。  
Size| Uint64| 文件大小，单位为 byte。  
StartTime| Uint64| 文件开始录制的 UTC 时间，单位为毫秒。  
StreamList| Array of | 录制文件中包含流的列表。  
VideoCodec| String| 视频录制编码协议  
AudioCodec| String| 音频录制编码器  
VideoWidth| Int| 录制视频宽度，单位为像素  
VideoHeight| Int| 录制视频高度，单位为像素  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
> 注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": "",
  "RecordFileList": [
    {
      "Vid": "Your_Vid",
      "ObjectKey": "TestObjectKey",
      "Duration": 57472,
      "Size": 5018305,
      "StartTime": 1611736812853,
      "StreamList": [
        {
          "UserId": "TestUserId",
          "StreamType": 0
        }
      ],
      "VideoCodec": "h264",
      "AudioCodec": "aac",
      "VideoWidth": 640,
      "VideoHeight": 360
    }
  ]
}

json

```

### RecordStopped(2023-11-01)
EventType
RecordStopped
事件详情
录制任务结束。2023-11-01 版本录制功能在任务结束时触发此回调。
无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态。
  * 0：任务结束，录制文件上传目标存储平台成功。
  * 1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
  * 2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

  
ErrorMessage| String| /| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
RecordFileList| Array of RecordFile| /| 录制生成的音视频文件列表  
RecordFile
参数名称| 类型| 描述  
---|---|---  
Vid| String| 文件在 平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。  
ObjectKey| String| 文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。  
Duration| Uint64| 文件时长，单位为毫秒。  
Size| Uint64| 文件大小，单位为 byte。  
StartTime| Uint64| 文件开始录制的 UTC 时间，单位为毫秒。  
StreamList| Array of | 录制文件中包含流的列表。  
VideoCodec| String| 视频录制编码协议  
AudioCodec| String| 音频录制编码器  
VideoWidth| Int| 录制视频宽度，单位为像素  
VideoHeight| Int| 录制视频高度，单位为像素  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
> 注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": "",
  "RecordFileList": [
    {
      "Vid": "Your_Vid",
      "ObjectKey": "TestObjectKey",
      "Duration": 57472,
      "Size": 5018305,
      "StartTime": 1611736812853,
      "StreamList": [
        {
          "UserId": "TestUserId",
          "StreamType": 0
        }
      ],
      "VideoCodec": "h264",
      "AudioCodec": "aac",
      "VideoWidth": 640,
      "VideoHeight": 360
    }
  ]
}

json

```

### RecordUploadStarted
EventType
RecordUploadStarted
事件详情
上传任务启动。
不同版本录制功能在上传任务启动时均触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 1,
  "ErrorMessage": ""
}

json

```

### RecordUploadProcessing
EventType
RecordUploadProcessing
事件详情 上传任务进度。
> 上传进程持续一分钟及以上，才会有此回调。每隔一分钟上传一次。
不同版本录制功能在上传任务进度发生变化时均触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
Progress| Int| 100| 0 到 10000 之间的数字，当前已上传文件与已录制的文件的比例乘以 10000。这个数字是不断变动的，录制完成后，到达 10000 表示上传完成。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 1,
  "ErrorMessage": "",
  "Progress" : 100
}

json

```

### RecordUploadDone
EventType
RecordUploadDone
事件详情
录制文件已上传至存储平台。
不同版本录制功能在录制文件已上传至存储平台时均触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 1,
  "ErrorMessage": ""
}

json

```

### RecordUploadBackuped
EventType
RecordUploadBackuped
事件详情
录制文件上传失败转存备份
不同版本录制功能在录制文件上传失败转存备份时均触发此回调。
> 备份使用的 TOS Bucket 需要开启版本控制，防止文件名重复导致文件被覆盖。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 1,
  "ErrorMessage": ""
}

json

```

### RecordAudioStreamStateChanged
EventType
RecordAudioStreamStateChanged
事件详情
录制音频流状态变化
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
UserId| String| Your_UserId| 用户 ID  
StreamType| Int| 0| 音频流类型。
  * 0：麦克风采集流
  * 1：屏幕流

  
State| Int| 0| 任务状态。
  * 0：音频流停止发布
  * 1：音频流正在发布

  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "UserId": "Your_UserId",
  "StreamType": 0,
  "State": 0, 
  "Timestamp": 1611736812853   
}

json

```

### RecordVideoStreamStateChanged
EventType
RecordVideoStreamStateChanged
事件详情
录制视频流状态变化
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
UserId| String| Your_UserId| 用户 ID  
StreamType| Int| 0| 视频流类型。
  * 0：摄像头采集流
  * 1：屏幕流

  
State| Int| 0| 任务状态。
  * 0：视频流停止发布
  * 1：视频流正在发布

  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "UserId": "Your_UserId,
  "StreamType": 0,
  "State": 0, 
  "Timestamp": 1611736812853   
}

json

```

### RecordSyncUploadStarted
EventType
RecordSyncUploadStarted
事件详情
录制生成 m3u8 文件且上传至指定存储平台。 此事件仅在第一次上传成功后回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
RecordFileList| Array of | 录制生成的音视频文件列表  
RecordFile
参数名称| 类型| 描述  
---|---|---  
Vid| String| 文件在 平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。  
ObjectKey| String| 文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。  
StartTime| Uint64| 文件开始录制的 UTC 时间，单位为毫秒。  
StreamList| Array of | 录制文件中包含流的列表。  
VideoCodec| String| 视频录制编码协议  
AudioCodec| String| 音频录制编码器  
VideoWidth| Int| 录制视频宽度，单位为像素  
VideoHeight| Int| 录制视频高度，单位为像素  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": "",
  "RecordFileList": [
    {
      "Vid": "Your_Vid",
      "ObjectKey": "TestObjectKey",
      "StartTime": 1611736812853,
      "StreamList": [
        {
          "UserId": "TestUserId",
          "StreamType": 0
        }
      ],
      "VideoCodec": "h264",
      "AudioCodec": "aac",
      "VideoWidth": 640,
      "VideoHeight": 360
    }
  ]
}

json

```

### RecordSyncUploadFailed
EventType
RecordSyncUploadFailed
事件详情 录制生成 m3u8 文件且上传至指定存储平台失败。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 音视频应用的唯一标识  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 用户创建的房间 ID，房间的唯一标识  
TaskId| String| Your_TaskId| 任务 ID，同一房间内的录制任务通过 TaskId 来区分  
Code| Int32| 0| 任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败  
ErrorMessage| String| 具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",  
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Code": 0,
  "ErrorMessage": "" 
}

json

```

## 房间相关
### UserJoinRoom
EventType
UserJoinRoom
事件详情
可见用户进入房间。
相同 UserId 用户重复进房时，每一次进房都会触发该回调，建议进行去重操作。
> 注：关于可见用户和不可见用户，参看 。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
UserId| String| 4398491447867063| 用户 ID  
DeviceType| String| android/ios/web/mac/windows| 终端类型  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
ExtraInfo| String| Your_ExtraInfo| 客户端 SDK 通过 joinRoom 传入的用户附加信息。  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "UserId": "4398491447867063",
  "DeviceType": "android",
  "Timestamp": 1611736812853,
  "ExtraInfo":"Your_ExtraInfo"
}

json

```

### UserLeaveRoom
EventType
UserLeaveRoom
事件详情
可见用户退出房间。
相同 UserId 用户反复登录造成被踢，被踢掉用户不会触发此回调。只有在最后一次退房时会触发该回调。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
UserId| String| 4398491447867063| 用户 ID  
DeviceType| String| android/ios/web/mac/windows| 终端类型  
Reason| String| 原因。参看 Reason  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Duration| Int64| 20| 用户在房间里的通话时长(s)。  
Reason:
值| 含义  
---|---  
userLeave| 正常退房  
connectionLost| 因断网、杀进程等异常原因离开房间。用户离开房间 30 s 后触发此回调。  
kickedByAdmin| 使用 OpenAPI 接口将某用户踢出房间  
roomDismissByAdmin| 使用 OpenAPI 接口解散房间，将房间内所有用户踢出房间  
onUserTokenDidExpire| Token 过期被踢出房间  
other| 其他原因  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "UserId": "4398491447867063",
  "DeviceType": "android",
  "Reason": "userLeave",
  "Timestamp": 1611736812853,
  "Duration": 20
}

json

```

### InvisibleUserJoinRoom
EventType
InvisibleUserJoinRoom
事件详情
不可见用户进入房间。
相同 UserId 用户重复进房时，每一次进房都会触发该回调，建议进行去重操作。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
UserId| String| 4398491447867063| 用户 ID  
DeviceType| String| android/ios/web/mac/windows| 终端类型  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
ExtraInfo| String| Your_ExtraInfo| 客户端 SDK 通过 joinRoom 传入的用户附加信息。  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "UserId": "4398491447867063",
  "DeviceType": "android",
  "Timestamp": 1611736812853,
  "ExtraInfo":"Your_ExtraInfo"
}

json

```

### InvisibleUserLeaveRoom
EventType
InvisibleUserLeaveRoom
事件详情
不可见用户退出房间。
相同 UserId 用户反复登录造成被踢，被踢掉用户不会触发此回调。只有在最后一次退房时会触发该回调。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
UserId| String| 4398491447867063| 用户 ID  
DeviceType| String| android/ios/web/mac/windows| 终端类型  
Reason| String| 原因。参看 Reason  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Duration| Int64| 20| 用户在房间里的通话时长(s)。  
Reason:
值| 含义  
---|---  
userLeave| 正常退房  
connectionLost| 因断网、杀进程等异常原因离开房间。用户离开房间 30 s 后触发此回调。  
kickedByAdmin| 使用 OpenAPI 接口将某用户踢出房间  
roomDismissByAdmin| 使用 OpenAPI 接口解散房间，将房间内所有用户踢出房间  
onUserTokenDidExpire| Token 过期被踢出房间  
other| 其他原因  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "UserId": "4398491447867063",
  "DeviceType": "android",
  "Reason": "userLeave",
  "Timestamp": 1611736812853,
  "Duration": 20
}

json

```

### RoleChangeInvisible2Visible
EventType
RoleChangeInvisible2Visible
事件详情
可见用户调用 setUserVisibility ，不可见 -> 可见。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
UserId| String| 4398491447867063| 用户 ID  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "UserId": "4398491447867063",
  "Timestamp": 1611736812853
}

json

```

### RoleChangeVisible2Invisible
EventType
RoleChangeVisible2Invisible
事件详情
可见用户调用 setUserVisibility ，可见 -> 不可见。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
UserId| String| 4398491447867063| 用户 ID  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "UserId": "4398491447867063",
  "Timestamp": 1611736812853
}

json

```

### RoomCreate
EventType
RoomCreate
事件详情
RTC 房间创建。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "Timestamp": 1611736812853
}

json

```

### RoomDestroy
EventType
RoomDestroy
事件详情
RTC 房间销毁，房间中所有用户全部离开房间。
EventData
参数| 类型| 示例值| 说明  
---|---|---|---  
RoomId| String| 6992870232038591758| 房间 ID  
Timestamp| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
参数示例
```
{
  "RoomId": "6992870232038591758",
  "Timestamp": 1611736812853
}

json

```

## 音频切片相关
### SegmentRealTimeData(2020-12-01)
EventType
SegmentRealTimeData
事件详情
指定音频流切片结果。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 切片任务 ID。  
TosBucket| String| Your_TosBucket| 储存切片的 tos bucket  
Data| array of | /  
Identifier| String| Your_Identifier| 切片任务标志。若未自定义切片名，切片名默认为 UUID，自定义切片名时为自定义名称。自定义切片文件名由 Identifier + UserId + 时间戳 + 序号组成。默认情况下 Identifier 为 随机生成的 UUID。在自定义文件名时，Identifier 的命名规则符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。  
AudioData
参数名| 类型| 示例值| 描述  
---|---|---|---  
Stream| | /| 音频切片对应的流的信息。  
ObjectKey| String| Your_ObjectKey| 音频切片的文件名。  
SampleRate| Int| 44100| 音频采样率，单位 Hz。  
BitsPerSample| Int| 16| 每个音频采样的比特数。  
Channels| Int| 2| 音频声道数。  
Size| Uint64| 256044| 音频切片大小，单位为 byte。  
TimeStamp| Int64| 1647317680214| 音频文件生成时对应的UNIX时间戳。单位为毫秒  
Duration| Uint32| 8000| 音频的时长，单位为毫秒。  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "TosBucket": "Your_TosBucket",
  "Data": [
    {
      "Stream": {
        "UserId": "Your_UserId",
        "StreamType": 0
      },
      "ObjectKey": "Your_ObjectKey",
      "SampleRate": "44100",
      "BitsPerSample": "16",
      "Channels": "2",
      "Size": 256044,
      "TimeStamp": 1647317680214,
      "Duration": 8000
    }
  ], 
  "Identifier": "Your_Identifier" }

json

```

### SegmentRealTimeData(2022-06-01)
EventType
SegmentRealTimeData
事件详情
指定音频流切片结果。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 切片任务 ID。  
Bucket| String| Your_Bucket| 存储音频切片的桶名称  
Data| array of   
Identifier| String| 切片任务标志。若未自定义切片名，切片名默认为 UUID，自定义切片名时为自定义名称。自定义切片文件名由 Identifier + UserId + 时间戳 + 序号组成。默认情况下 Identifier 为 随机生成的 UUID。在自定义文件名时，Identifier 的命名规则符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。  
AudioData
参数名| 类型| 示例值| 描述  
---|---|---|---  
Stream| | /| 音频切片对应的流的信息。  
ObjectKey| String| Your_ObjectKey| 音频切片的对象键。  
SampleRate| Int| 44100| 音频采样率，单位 Hz。  
BitsPerSample| Int| 16| 每个音频采样的比特数。  
Channels| Int| 2| 音频声道数。  
Size| Uint64| 256044| 音频切片大小，单位为 byte。  
TimeStamp| Int64| 1647317680214| 音频文件生成时对应的UNIX时间戳。单位为毫秒  
Duration| Uint32| 8000| 音频的时长，单位为毫秒。  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Bucket": "Your_Bucket",
  "Data": [
    {
      "Stream": {
        "UserId": "Your_UserId",
        "StreamType": 0
      },
      "ObjectKey": "Your_ObjectKey",
      "SampleRate": "44100",
      "BitsPerSample": "16",
      "Channels": "2",
      "Size": 256044,
      "TimeStamp": 1647317680214,
      "Duration": 8000
    }
  ], 
  "Identifier": "Your_Identifier" 
  }

json

```

### SegmentRealTimeData(2023-11-01)
EventType
SegmentRealTimeData
事件详情
指定音频流切片结果。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 切片任务 ID。  
Bucket| String| Your_Bucket| 存储音频切片的桶名称  
Data| array of   
Identifier| String| 切片任务标志。若未自定义切片名，切片名默认为 UUID，自定义切片名时为自定义名称。自定义切片文件名由 Identifier + UserId + 时间戳 + 序号组成。默认情况下 Identifier 为 随机生成的 UUID。在自定义文件名时，Identifier 的命名规则符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。  
AudioData
参数名| 类型| 示例值| 描述  
---|---|---|---  
Stream| | /| 音频切片对应的流的信息。  
ObjectKey| String| Your_ObjectKey| 音频切片的对象键。  
SampleRate| Int| 44100| 音频采样率，单位 Hz。  
BitsPerSample| Int| 16| 每个音频采样的比特数。  
Channels| Int| 2| 音频声道数。  
Size| Uint64| 256044| 音频切片大小，单位为 byte。  
TimeStamp| Int64| 1647317680214| 音频文件生成时对应的UNIX时间戳。单位为毫秒  
Duration| Uint32| 8000| 音频的时长，单位为毫秒。  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Bucket": "Your_Bucket",
  "Data": [
    {
      "Stream": {
        "UserId": "Your_UserId",
        "StreamType": 0
      },
      "ObjectKey": "Your_ObjectKey",
      "SampleRate": "44100",
      "BitsPerSample": "16",
      "Channels": "2",
      "Size": 256044,
      "TimeStamp": 1647317680214,
      "Duration": 8000
    }
  ], 
  "Identifier": "Your_Identifier" 
  }

json

```

## 抽帧截图相关
### SnapshotRealTimeData(2020-12-01)
EventType
SnapshotRealTimeData
事件详情
指定视频流截图结果。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 截图任务 ID  
TosBucket| String| Your_TosBucket| 储存截图的 TOS bucket  
Data| array of   
ImageData
参数名| 类型| 示例值| 描述  
---|---|---|---  
Stream| | /| 截图对应的流的信息。  
ObjectKey| String| Your_ObjectKey| 截图对应的 TOS key。  
Format| Uint32| 0| 截图格式。
  * 0: JPEG
  * 1: PNG

  
Width| Uint32| 640| 截图宽度，单位为像素。  
Height| Uint32| 360| 截图高度，单位为像素。  
Size| Uint64| 28802| 截图大小，单位为 byte。  
TimeStamp| Uint64| 1647316896085| 截图生成时对应的 UNIX 时间戳。  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "TosBucket": "Your_TosBucket",
  "Data": [
    {
      "Stream": {
        "UserId": "Your_UserId",
        "StreamType": 0
      },
      "ObjectKey": "Your_ObjectKey",
      "Format": 0,
      "Width": 640,
      "Height": 360,
      "Size": 28802,
      "TimeStamp": 1647316896085
    }
  ]
}

json

```

### SnapshotRealTimeData(2022-06-01)
EventType
SnapshotRealTimeData
事件详情
指定视频流截图结果。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 截图任务 ID  
Bucket| String| Your_Bucket| 存储截图的桶名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效。  
VeImageXServiceId| String| /| VeImageX 的服务 ID。当存储平台为 VeImageX 时此字段才会生效。  
Data| array of   
ImageData
参数名| 类型| 示例值| 描述  
---|---|---|---  
Stream| | /| 截图对应的流的信息。  
ObjectKey| String| Your_ObjectKey| 对象键名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效，你可以根据 ObjecetKey 找到对应的图片文件。  
VeImageXUri| String| Your_VeImageXUri| VeImageX 的统一资源标识符。当存储平台为 VeImageX 时此字段才会生效，你可以根据 VeImageXUri找到对应的图片文件。  
Format| Uint32| 0| 截图格式。
  * 0: JPEG
  * 1: PNG

  
Width| Uint32| 640| 截图宽度，单位为像素。  
Height| Uint32| 360| 截图高度，单位为像素。  
Size| Uint64| 28802| 截图大小，单位为 byte。  
TimeStamp| Uint64| 1647316896085| 截图生成时对应的 UNIX 时间戳。  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Bucket": "Your_Bucket",
  "Data": [
    {
      "Stream": {
        "UserId": "Your_UserId",
        "StreamType": 0
      },
      "ObjectKey": "Your_ObjectKey",
      "Format": 0,
      "Width": 640,
      "Height": 360,
      "Size": 28802,
      "TimeStamp": 1647316896085
    }
  ]
}

json

```

### SnapshotRealTimeData(2023-11-01)
EventType
SnapshotRealTimeData
事件详情
指定视频流截图结果。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 截图任务 ID  
Bucket| String| Your_Bucket| 存储截图的桶名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效。  
VeImageXServiceId| String| /| VeImageX 的服务 ID。当存储平台为 VeImageX 时此字段才会生效。  
Data| array of   
ImageData
参数名| 类型| 示例值| 描述  
---|---|---|---  
Stream| | /| 截图对应的流的信息。  
ObjectKey| String| Your_ObjectKey| 对象键名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效，你可以根据 ObjecetKey 找到对应的图片文件。  
VeImageXUri| String| Your_VeImageXUri| VeImageX 的统一资源标识符。当存储平台为 VeImageX 时此字段才会生效，你可以根据 VeImageXUri找到对应的图片文件。  
Format| Uint32| 0| 截图格式。
  * 0: JPEG
  * 1: PNG

  
Width| Uint32| 640| 截图宽度，单位为像素。  
Height| Uint32| 360| 截图高度，单位为像素。  
Size| Uint64| 28802| 截图大小，单位为 byte。  
TimeStamp| Uint64| 1647316896085| 截图生成时对应的 UNIX 时间戳。  
Stream
参数名称| 类型| 必填| 示例值| 描述  
---|---|---|---|---  
Index| Uint32| 否| 0| 在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。  
UserId| String| 是| Your_UserId| 用户Id，表示这个流所属的用户。  
StreamType| Uint32| 否| 0| 流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Bucket": "Your_Bucket",
  "Data": [
    {
      "Stream": {
        "UserId": "Your_UserId",
        "StreamType": 0
      },
      "ObjectKey": "Your_ObjectKey",
      "Format": 0,
      "Width": 640,
      "Height": 360,
      "Size": 28802,
      "TimeStamp": 1647316896085
    }
  ]
}

json

```

## 在线媒体流相关
### RelayStreamStateChanged
EventType
RelayStreamStateChanged
事件详情
输入在线媒体流状态发生变化。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 任务 ID  
UserId| String| Your_UserId| 在线媒体流对应的的 UserId  
StreamUrl| String| rtmp://xxx| 在线流媒体地址  
Status| Int| 1| 任务状态： 1：待机中2：连接中3：运行中4：已停止 5：重试中。  
StartTimeStamp| Int| 0| 任务起始时间戳，用于定时播放，Unix时间，单位为秒。默认为 0，表示立即启动。  
Msg| String| /| 描述信息  
Reason| Int| 1| 任务停止原因：1：空闲超时 2：停止接口调用 3：流播放结束 4：内部错误 5：url地址异常 6：编码格式不支持 7：token 错误 8：没有发布权限 9：被移除房间 仅当 status=4 时，Reason 有值。  
参数示例
```
{
    "AppId": "Your_AppId",
    "EventId": "Your_eventId",
    "EventTime": "2021-08-17T19:22:02+08:00",
    "EventType": "RelayStreamStateChanged",
    "EventData": {
        "RoomId": "Your_RoomId",
        "TaskId": "Your_TaskId", 
        "UserId": "Your_UserId",  
        "StreamUrl": "rtmp://xxx",    
        "Status": 1,
        "StartTimeStamp": 0,    
        "Msg": "",
        "Vid": "xxxxvvv"，
        "Reason": 4
    },
}

json

```

## 云录屏相关
### Webcast
EventType
Webcast
事件详情
云录屏任务状态发生变化
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
TaskId| String| Your_TaskId| 任务 ID  
Status| Int| 1| 任务状态：1：开始转推2：正常结束3：异常结束 4：页面刷新 5：任务重调度（推流用户重新进房）  
Reason| String| /| 异常结束描述信息，当 Status = {3,4,5} 时才有值，枚举值为 {：AudioCaptureModuleError、 WebRenderModuleError、SourceURLInaccessible、StartEventTimeout、PageBlank、PageCrash、PageFreeze} 。  
参数示例
```
{
  "AppId": "Your_AppId",
  "BusinessId": "Your_BusinessId",
  "RoomId": "Your_RoomId",
  "TaskId": "Your_TaskId",
  "Status": 1
}

json

```

## 转推直播相关
### TranscodeStarted
EventType
TranscodeStarted
事件详情
通过调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 发起的转推直播任务开始。
不同版本转推直播功能在转推直播任务开始时均触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时，TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。  
Timestamp| Int64| 1661150097044| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
PushURL| String| rtmp://xxxx| 推流 CDN 地址。该参数当前仅在合流转推下返回。  
Code| Int| 0| 操作结果类型。
  * 0： 创建任务成功。
  * 1：创建任务失败。

  
ErrorMessage| String| 具体错误信息，当 Code为1时，ErrorMessage 会显示具体的错误信息  
参数示例
```
{
 	"AppId": "Your_AppID", 
  "RoomId": "Your_RoomID",
  "TaskId": "Your_TaskID",
  "Timestamp": 1661150097044,
  "PushURL": "rtmp://xxxx",  
  "Code": 0,
  "ErrorMessage": ""
}

json

```

### TranscodeStopped
EventType
TranscodeStopped
事件详情
通过调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 终止了转推直播任务。 不同版本转推直播功能在转推直播任务结束时均触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时， TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。  
Timestamp| Int64| 1661150097044| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
PushURL| String| rtmp://xxxx| 推流 CDN 地址。该参数当前仅在合流转推下返回。  
Code| Int| 0| 操作结果类型。
  * 0： 停止任务成功。
  * 1：停止任务失败。

  
ErrorMessage| String| 具体错误信息，当 Code为1时，ErrorMessage 会显示具体的错误信息  
参数示例
```
{
 	"AppId":"Your_AppID",
  "RoomId":"Your_RoomID",
  "TaskId":"Your_TaskID",
  "Timestamp":1661150097044,
  "PushURL": "rtmp://xxxx",  
  "Code":0,
  "ErrorMessage":""
}

json

```

### TranscodeUpdated
EventType
TranscodeUpdated
事件详情
通过调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 更新了合流转推任务。
不同版本转推直播功能在转推直播任务更新时均触发此回调。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时， TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。  
Timestamp| Int64| 1661150097044| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
PushURL| String| rtmp://xxxx| 推流 CDN 地址。  
Code| Int| 0| 操作结果类型。
  * 0： 更新配置成功。
  * 1：创建配置失败。

  
ErrorMessage| String| 具体错误信息，当 Code为1时，ErrorMessage 会显示具体的错误信息  
参数示例
```
{
  "AppId": "Your_AppID",
  "RoomId": "Your_RoomID", 
  "TaskId": "Your_TaskID", 
  "Timestamp": 1661150055041,  
  "PushURL": "rtmp://xxxx", 
  "Code": 0,
  "ErrorMessage": ""
}

json

```

### TranscodeStateChanged
EventType
TranscodeStateChanged
事件详情
转推直播任务状态变化通知。
不同版本转推直播功能在转推直播任务状态变化时均触发此回调。
状态变化包括用户主动调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 更新了合流转推任务参数。也包括任务执行过程中出现的状态流转。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
RoomId| String| Your_RoomId| 房间 ID，是房间的唯一标志  
TaskId| String| Your_TaskId| 转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时， TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。  
Timestamp| Int64| 1661150097044| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
StartTime| Int64| 1661150054990| 转推直播任务创建的 Unix 时间戳（ms）  
FinishTime| Int64| 0| 转推直播任务结束的 Unix 时间戳（ms）  
PushURL| String| rtmp://xxxx| 推流 CDN 地址。该参数当前仅在合流转推下返回。  
TaskState| Int| 2| 任务状态，枚举值为 {1，2，3，4}。  
Code| Int| 0| 错误码。枚举值为 {0，1，2，3，4，5，9999}。  
ErrorMessage| String| -| 具体错误信息。  
OnStatusCode| String| -| 推流失败时 CDN 返回的错误码。  
OnStatusDescription| String| -| 推流失败时 CDN 返回的错误详情。  
参数示例
```
{
  "AppId": "Your_AppID",
  "RoomId": "Your_RoomID", 
  "TaskId": "Your_TaskID", 
  "Timestamp": 1661150097044, 
  "StartTime": 1661150054990,
  "FinishTime": 0,  
  "PushURL": "rtmp://xxxx", 
  "TaskState": 3,  
  "Code": 0,
  "ErrorMessage": "",
  "OnStatusCode": "",
  "OnStatusDescription": ""
}

json

```

## 公共流相关
### PushPublicStream
EventTypePushPublicStream
事件详情
公共流推送异常或推送结束
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
BusinessId| String| Your_BusinessId| 业务标识  
PublicStreamId| String| Your_PublicStreamId| 公共流对应的流 Id  
Timestamp| Int64| 1661150097044| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)  
Status| Int| 2| 公共流任务状态。枚举值为 {1，2，3}。
  * 1：公共流启动成功
  * 2：公共流推送结束
  * 3：公共流推送异常

  
Msg| String| 具体的错误信息  
Code| Int| 2| 错误码。枚举值为 {1，2，3}。
* 1：空闲超时结束。
* 2：OpenAPI 调用结束。
* 3：内部错误结束。  
参数示例
```
{
  "AppId":"Your_AppID", 
  "BusinessId":"Your_BusinessId",  
  "PublicStreamId":"Your_PublicStreamId", 
  "Timestamp":1661150097044, 
  "Status":2, 
  "Msg":"call api stop",   
  "Code":2
}

json

```

## 歌曲查询相关
### MusicRemoved
EventTypeMusicRemoved
事件详情
曲库下架歌曲列表
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| Your_AppId| 应用的唯一标志  
Timestamp| Int| 1611736812853| 该事件在 RTC 服务器上发生的时间戳，Unix 时间，单位为毫秒  
SongUpdate| Array of | /| 下架歌曲详细信息  
SongList
参数名| 类型| 示例值| 描述  
---|---|---|---  
SongId| String| Your_SongId| 歌曲 ID  
VendorId| Int| 1| 供应商 ID  
UpdateAt| Int| 1611736812853| 歌曲更新时间戳，Unix 时间，单位为毫秒  
参数示例
```
{
  "AppId": "Your_AppId",
  "Timestamp": 1611736812853,
  "SongUpdate": [
    {
      "SongId": "Your_SongId",
      "VendorId": 2,
      "UpdateAt": 1611736812853
    }
  ]
}

json

```

## 互动白板文件转码
### WbTranscodeProgressChanged
EventTypeWbTranscodeProgressChanged
事件详情
文件转码进度。触发时机说明如下：
  * 任务开始时
  * 任务完成时
  * 静态转码任务每完成 10 页时，也会触发一次回调。


EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
TaskId| String| 5f848de8-4258-4c41-964b-80556552ae82| 任务唯一标识  
TranscodeMode| Int| 0| 0：静态转码1：动态转码  
Progress| Int| 0| 进度：当前页数/总页数 *100%。对于动态转码任务，取值为 0 或 100。  
FileName| String| demo| 源文件文件名  
TotalPages| Int| 5| 总页数。对静态转码任务有效。  
### WbTranscodeFinished
EventType
WbTranscodeFinished
事件详情 文件转码任务结束后回调一次，成功或失败。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
TaskId| String| 5f848de8-4258-4c41-964b-80556552ae82| 任务唯一标识  
TranscodeMode| Int| 0| 0：静态转码1：动态转码  
FileName| String| demo| 源文件文件名  
FileId| String| 001| 动态转码文件ID。对动态转码任务有效。  
TotalPages| Int| 5| 总页数。  
Width| Int| 640| 分辨率宽，单位：像素。对静态转码任务有效。  
Height| Int| 360| 分辨率高，单位：像素。对静态转码任务有效。  
ThumbnailWidth| int| 64| 缩略图分辨率宽，单位：像素  
ThumbnailHeight| int| 64| 缩略图分辨率高，单位：像素  
Images| Array of | None| 转码图片结果
>   * 静态转码缩略图 URL。
>   * 动态转码指定缩略图时，返回缩略图 URL。不指定则为空
> 
  
ErrCodeN| Int| 400| 错误码编号，含义见  
ErrCode| String| InvalidParameter| 错误码类型，含义见  
ErrMsg| String| 和错误参数有关的信息| 错误码信息，含义见  
#### Image
参数名| 类型| 示例值| 描述  
---|---|---|---  
PageId| Int| 1| 页数，从1开始编号  
Img| String| https://example.com/demo.png| 转码图片URL  
ThumbnailUrl| String| https://example.com/thumbnail.png| 转码缩略图URL，如没有指定缩略图，则为空  
## 实时对话式 AI
### VoiceChat
EventType
VoiceChat
事件详情
智能体任务状态变化。
EventData
参数名| 类型| 示例值| 描述  
---|---|---|---  
AppId| String| 661e****543cf| 音视频应用的唯一标识。  
BusinessId| String| biz1| 业务标识。  
RoomId| String| room1| 房间 ID，房间的唯一标识。  
TaskId| String| task1| 智能体任务 ID。  
UserID| String| user1| 说话人 UserId。  
RoundID| Int64| 0| 对话轮次。从 0 开始计数。  
EventTime| Int64| 1611736812853| 该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)。  
EventType| Int64| 0| 任务状态类型。
  * 0：智能体任务状态发生变化。
  * 1：智能体任务出现错误。

  
RunStage| String| preParamCheck| 状态详情。  
ErrorInfo| | -| 任务错误详细信息。  
ErrorInfo
参数名| 类型| 示例值| 描述  
---|---|---|---  
Errorcode| Int| 1001| 错误状态码。  
Reason| String| -| 错误详细原因。  
参数示例
```
{
  "AppId": "661e****543cf",
  "BusinessId": "biz1",
  "RoomId": "room1",
  "TaskId": "room1",
  "UserID": "user1",
  "RoundID": 0,
  "EventTime": 1611736812853,
  "EventType": 0,
  "RunStage": "taskStart",
  "ErrorInfo": {
    "Errorcode": 1001,
    "Reason": ""
  }
}

json

```





## None

Source: https://www.volcengine.com/docs/6348/1415216







https://www.volcengine.com/docs/6348/1399966

#接入第三方大模型或 Agent

在实时对话式 AI 场景中，支持集成第三方大模型或 Agent，以满足特定业务需求。为确保集成顺利，你需要提供第三方大模型或 Agent 的服务请求接口，并确保该接口符合火山引擎 RTC 标准规范，否则需要对其进行改造。

接入第三方大模型或 Agent 时，如 Dify Agent，支持通过以下方式传递自定义参数：

直接在第三方大模型或 Agent 的请求 URL 中通过查询参数的方式拼接业务自定义参数，用于传递非敏感信息（如 session_id）。具体实现，参考接入方法。
提供 custom 字段，通过 JSON 格式用于传递业务自定义参数。
接入方法
准备第三方大模型或 Agent 的请求接口 URL，并满足以下要求：

必须使用 HTTPS 域名。
必须支持公网访问。
必须符合火山引擎 接口标准。你可通过 接口验证工具 验证接口是否符合标准。
必须支持 SSE 协议。
调用 StartVoiceChat 接口时，按照规定参数结构配置 LLMConfig。关键配置如下：

Mode：固定设置为 "CustomLLM"。
URL：填写你的第三方大模型或 Agent 的完整 HTTPS URL。
说明

如果需要在每次请求时传递非敏感的业务信息（如 session_id），可以直接将它们作为查询参数拼接到此 URL 中，格式为：https://<第三方模型或Agent的API地址>?<业务自定义参数名>=<参数值>。

custom：可选，可通过该字段传递业务自定义参数。
配置示例：

下述示例中的参数需根据实际进行替换和配置。参数详细说明，请参见 StartVoiceChat。

{
    "LLMConfig": {
        "Mode": "CustomLLM", // 固定取值 CustomLLM
        "URL": "https://api.example.com/v1/custom_chat_agent?source=volc_rtc&version=1.2", // 填写你的第三方大模型或 Agent 的完整 HTTPS URL，此处示例中，source=volc_rtc&version=1.2 为自定义参数
        "ModelName": "my_agent_model_v2", // 可选，自定义第三方大模型或 Agent 的名称
        "APIKey": "sk-xxxxxxxxxxxxxxxxx", // 可选，如果需要 Bearer Token 鉴权
        "custom": {
            "session_id": "123456",
            "user_name": "user1"
        } // 可选，业务自定义参数
        // ... 其他 LLM 通用参数如 Temperature, MaxTokens 等可以按需配置 ...
    }
}
接口标准

在对话期间，将按照以下格式通过 POST 请求访问你配置的第三方大模型或 Agent 的接口地址，获取相应结果。接口返回需支持 SSE 协议。

Request
Method: POST

Headers:

Content-Type: application/json
Authorization: Bearer <your_APIKey> （当需要 Bearer Token 认证方式鉴权大模型时）
Request body:

参数	类型	必填	描述
messages	Array of messages	是	参考 StartVoiceChat.HistoryLength，包含最近 HistoryLength 轮对话内容。当前 ASR 内容在数组末位。
stream	Boolean	否	取值：true。
temperature	Float	否	透传 StartVoiceChat.LLMConfig.temperature。
max_tokens	Int	否	透传 StartVoiceChat.LLMConfig.MaxTokens。
model	String	是	透传 StartVoiceChat.LLMConfig.ModelName。
top_p	Float	否	透传 StartVoiceChat.LLMConfig.TopP。
custom	String	否	自定义 JSON 字符串，透传业务传入的自定义参数。
messages

参数	类型	必填	描述
role	String	是	可取值及含义如下：
user
assistant
content	String	否	对话内容。
Response
注意

协议：必须支持 SSE（Server-Sent Events）协议。
Headers：Content-Type 必须为 text/event-stream。
结束符：必须包含 data: [DONE] 结束符。
StatusCode==200
参数	类型	必填	描述
id	String	是	请求 UUID。不同请求的 ID 需不同，但同一个流式请求的 ID 需相同。
choices	Array of choices	是	流式回复对象。
created	Int	是	UnixTime 时间戳，精确到秒。同一个请求的流式所有块拥有相同的时间戳。
usage	Object of usage	否	最后一个包可带此参数。
object	String	是	取值：chat.completion.chunk。填错将导致接口行为异常。
model	String	是	模型 ID。
stream_options	object of stream_options	是	流接口选项。
Choices

参数	类型	必填	描述
finish_reason	String	是	
null：流式请求未结束。
最后一个流式片段需填入以下值：
stop：正常结束。
length：达到 MaxTokens。
content_filter：内容不合规。
delta	Object of delta	是	流式片段对象。
index	Int	是	数组中位置，从 0 开始。
delta

参数	类型	必填	描述
content	String	是	对话内容。
role	String	是	可取值及含义如下：
user
assistant
usage

参数	类型	必填	描述
completion_tokens	Int	是	generated tokens 长度。
prompt_tokens	Int	是	prompt tokens 长度。
total_tokens	Int	是	prompt+generated tokens 长度。
stream_options

参数	类型	必填	描述
include_usage	Boolean	是	固定为 true，表示流式传输。
成功请求及返回样例如下：

curl -v --location 'https://rtc-cloud-rendering.byted.org/voicechat/test-sse' \
--header 'Content-Type: application/json' \
--data '{
    "messages":[{
        "role":"user",
        "content":"今天适合干什么？"
    }],
    "stream": true,
    "temperature": 0.1,
    "max_tokens": 100,
    "top_p": 0.9,
    "model": "doubao-32k",
    "stream_options": {"include_usage":true}
}'

> POST /voicechat/test-sse HTTP/1.1
> Host: rtc-cloud-rendering.byted.org
> User-Agent: curl/8.4.0
> Accept: */*
> Content-Type: application/json
> Content-Length: 254
>
< HTTP/1.1 200 OK
< Date: Thu, 15 Aug 2024 09:36:02 GMT
< Content-Type: text/event-stream
< Transfer-Encoding: chunked
< Connection: keep-alive
< Access-Control-Allow-Origin: *
< Cache-Control: no-cache

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"role":"assistant"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":1,"delta":{"content":"天"}},{"finish_reason":null,"index":0,"delta":{"content":"从明"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"起，"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":1,"delta":{"content":"幸福"}},{"finish_reason":null,"index":0,"delta":{"content":"做一个"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"的"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"人"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"。"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"喂马，"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"劈柴，"}},{"finish_reason":null,"index":1,"delta":{"content":"周游"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"世界"}},{"finish_reason":null,"index":1,"delta":{"content":"。"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":"stop","index":0,"delta":{}}],"model":"doubao-32k-2024-07-25","created":1723714562,"usage":{"prompt_tokens":1,"completion_tokens":2,"total_tokens":3}}

data: [DONE]
StatusCode==4xx、5xx
请求终止，报错返回。RTC 不做任何处理，仅报错。

参数	类型	必填	描述
Error	Error	是	错误详情。
Error

参数	类型	必填	描述
Code	String	是	错误码。
Message	String	是	错误原因。
StatusCode!==200、4xx、5xx
此时 RTC 不做任何处理，仅报错，直接报错返回 StatusCode。

使用工具验证第三方大模型或 Agent 的请求接口

你可以使用工具验证第三方大模型或 Agent 的请求接口是否符合标准规范。如果符合要求，工具可以返回大模型或 Agent 预期的输出结果，否则将返回错误信息。

注意

目前验证工具仅支持在 Linux 和 macOS 系统上使用。

下载和使用工具
下载验证工具包并解压。

Archive.zip
8.16MB

通过终端，使用 cd 命令进入工具包所在目录。
执行验证命令。
注意

如果你使用 zsh 终端，且 URL 中包含特殊字符（如 ?、=、& 等）时，必须对特殊字符进行转义。

Linux
./app-linux <llmAPIUrl> <llmModel> <llmAPIKey> <LLMQuestion>
macOS
./app-mac <llmAPIUrl> <llmModel> <llmAPIKey> <LLMQuestion>
参数说明如下：
参数名	类型	是否必填	描述	示例
llmAPIUrl	String	是	第三方大模型或 Agent 的完整请求 URL。	https://api.cn/v1/chat/completion
llmModel	String	否	第三方大模型或 Agent 的名称。默认值为空。	model-v1-chat
llmAPIKey	String	否	APIKey。默认值为空。	sk-0aeQbtlX2reL
LLMQuestion	String	否	发送给第三方大模型或 Agent 的测试问题。	你好
验证示例
执行结果类似如下所示，表示验证通过：

openAI

自定义 URL

FAQ
Q1：是否支持在本地环境调用和测试验证对话式 AI 第三方大模型或 Agent 接口？
支持。

Q2：使用第三方大模型时，设置的多轮历史对话 HistoryLength 未生效，每次请求只传递了最新一条对话内容怎么办？
可以尝试以下排查：

检查你的大模型接口是否支持多轮历史对话。
确认第三方大模型的响应中是否包含正确的请求结束标识 data: [DONE]。如果第三方服务没有在响应末尾返回此结束符，系统可能无法正确识别一次完整请求的结束，从而无法正确地存储和使用历史对话记录。

自定义语音播放
最近更新时间：2025.05.08 17:59:13
首次发布时间：2025.03.04 17:21:05
我的收藏
有用
无用
在用户与智能体进行语音互动过程中，你可能需要智能体主动播报自定义文本内容，引导客户互动、提醒用户注意事项、或提供重要信息。你可以使用该功能实现。

应用场景
场景	描述	示例
AI 陪练	用户长时间不说话时，智能体播放内容吸引用户继续互动。	"我注意到你有一会儿没说话了，需要我解释一下刚才的内容吗？"
桌游主持	智能体扮演主持人角色，引导用户完成游戏流程。	“现在轮到玩家 A 发言，请开始。”
安全监管	用户问题或大模型生成内容不合规时，引导用户调整会话内容。	"抱歉，对话内容不符合社区规范。"
延迟安抚	触发 Function Calling 或联网造成智能体回答延迟偏大时，引导用户耐心等待。	"正在处理，请稍等。"
实现方法
1. 构建对话式 AI 场景
参考场景搭建 Web 已实现场景搭建。

2. 传入需要播报的文本内容
建议传入完整分句或整句进行播放，高频送入碎片化文本可能会导致播放顺序错乱。你可以通过以下两种方式传入自定义文本信息：

通过服务端传入
通过客户端传入
调用 UpdateVoiceChat接口，设置以下参数传入播报内容，并控制智能体的播报行为：

Command：填入 ExternalTextToSpeech，表示通过文本转语音（TTS）进行播报。
Message：填入自定义文本内容，长度不超过 200 个字符。
InterruptMode：设置文本内容播报的优先级。
当 InterruptMode 为 1 时：高优先级。智能体会终止当前交互，直接播放传入的文本内容。
当 InterruptMode 为 2 时：中优先级。智能体会在当前交互结束后，播放传入的文本内容。
当 InterruptMode 为 3 时：低优先级。如果此时智能体正在交互，智能体会直接丢弃传入的文本内容。如果未在交互，智能体会播放传入的文本内容。
你可参看以下示例进行传入自定义文本信息进行播放：

POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf", // RTC 应用 AppId
    "RoomId": "Room1", // 会话房间 ID
    "TaskId": "task1", // 会话任务 ID
    "Command": "ExternalTextToSpeech", //控制命令，此处填入 ExternalTextToSpeech
    "Message": "你刚才的故事讲的真棒，能再讲一个吗。" // 自定义文本内容，长度不超过 200 个字符。
    "InterruptMode": 1 //文本内容播报的优先级。
}





FAQ
Q1：如果设置智能体支持语音打断，自定义文本信息不打断智能体输出。本次交互被语音打断时，自定义文本信息何时播放？
A1：此时自定义文本信息会被丢弃。

Q2：播放的自定义文本信息是否会放入历史对话？
A2：会。

Q3：如果设置智能体支持语音打断，自定义文本消息不打断智能体输出。自定义文本播放时被打断，该文本消息是否会放在历史对话中？
A3：已输出的内容会放入历史对话中。


自定义大模型上下文
最近更新时间：2025.06.19 13:27:45
首次发布时间：2025.04.01 17:09:08
我的收藏
有用
无用
在用户与智能体进行语音互动时，你可能需要动态传入自定义信息，以帮助大模型更准确地和真人用户互动，从而提升对话的自然度。自定义信息可以包括用户的实时生理数据、游戏数据、角色设定等。这些信息将根据你设定的优先级决定替代用户输入或增加新一轮对话并记入上下文中。自定义上下文信息作为 UserMessage 传入大模型，并在当前对话轮次中生效。

应用场景
场景	描述	示例
游戏陪玩助手	结合用户实时游戏数据生成合适的回答，为用户提供更贴心的陪伴。	
自定义输入："当前用户战绩 0-14，金币落后，为其提供出装建议"。
智能体输出："我观察到你处于逆风局，建议出防御装。"
健康咨询助手	结合用户实时生理数据生成个性化建议，提升健康指导的精准度。	
自定义输入："当前用户心率 130，面色发白，建议休息"。
智能体输出："监测到您心率持续偏高，建议暂停运动并补充电解质。"
前提条件
你已参考场景搭建 构建一个完整的 AI 应用。

实现方法
通过服务端或客户端均可实现自定义传入大模型上下文信息，你可根据业务请求端的类型选择对应的方式。例如你在开发 AI 应用时，选择服务端响应请求，建议使用服务端实现传入大模型上下文，降低请求延迟。

通过服务端传入大模型上下文
调用 UpdateVoiceChat接口，设置以下参数自定义传入大模型上下文：

参数	类型	描述
AppId	String	RTC 应用 AppId，参看获取 AppId。
RoomId	String	自定义会话房间 ID。
TaskId	String	自定义会话任务 ID。
Command	String	填入ExternalTextToLLM，表示自定义传入大模型上下文。
Message	String	填入自定义传入大模型上下文内容，长度不超过 200 个字符。
InterruptMode	Integer	传入大模型上下文内容处理优先级。
1：高优先级。智能体会终止当前交互，将文本送入大模型进行处理并输出结果。
2：中优先级。智能体会在当前交互结束后，将文本送入大模型进行处理并输出结果。
3：低优先级。如果此时智能体正在交互，智能体会直接丢弃传入的文本内容。如果未在交互，智能体会将文本送入大模型进行处理并输出结果。
你可参考以下示例从服务端实现自定义传入大模型上下文内容：

POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf", 
    "RoomId": "Room1", 
    "TaskId": "task1", 
    "Command": "ExternalTextToLLM",
    "Message": "给一些出装建议。用户当前的背景是：金币落后，法师，输出高，脆皮",
    "InterruptMode": 2
}
通过客户端传入大模型上下文
使用 SendUserBinaryMessage 接口实现自定义传入大模型上下文。该接口的 buffer 参数需要传入特定格式的内容，下图展示了 buffer 参数的格式：

alt

参数名	类型	描述
magic_number	binary	消息格式标识符，当前场景消息格式固定为 ctrl，用于标识该消息为控制消息。
length	binary	传入大模型上下文消息长度，单位为字节，采用大端序（Big-endian）存储方式，用于说明 control_message 字段的字节长度。
control_message	binary	传入大模型上下文配置信息，采用 JSON 格式，具体内容格式参看 control_message 格式。
control_message:

参数名	类型	描述
Command	String	控制命令，此处填入 ExternalTextToLLM。
Message	String	传入大模型上下文内容，长度不超过 200 个字符。
InterruptMode	Int	传入大模型上下文内容处理优先级。
1：高优先级。智能体会终止当前交互，将文本送入大模型进行处理并输出结果。
2：中优先级。智能体会在当前交互结束后，将文本送入大模型进行处理并输出结果。
3：低优先级。如果此时智能体正在交互，智能体会直接丢弃传入的文本内容。如果未在交互，智能体会将文本送入大模型进行处理并输出结果。
你可参看以下示例从客户端实现自定义传入大模型上下文。

C++
Java
TypeScript
// 传入大模型上下文信息
void sendLLMMessage(const std::string &uid, const std::string& content) {
    nlohmann::json json_data;
    json_data["Command"] = "ExternalTextToLLM";
    json_data["Message"] = content;
    json_data["InterruptMode"] = 1; 
    sendUserBinaryMessage(uid, json_data.dump());
}

void buildBinaryMessage(const std::string& magic_number, const std::string& message, size_t& binary_message_length, std::shared_ptr<uint8_t[]>& binary_message) {
    auto magic_number_length = magic_number.size();
    auto message_length = message.size();

    binary_message_length = magic_number_length + 4 + message_length;
    binary_message = std::shared_ptr<uint8_t[]>(new uint8_t[binary_message_length]);
    std::memcpy(binary_message.get(), magic_number.data(), magic_number_length);
    binary_message[magic_number_length] = static_cast<uint8_t>((message_length >> 24) & 0xFF);
    binary_message[magic_number_length+1] = static_cast<uint8_t>((message_length >> 16) & 0xFF);
    binary_message[magic_number_length+2] = static_cast<uint8_t>((message_length >> 8) & 0xFF);
    binary_message[magic_number_length+3] = static_cast<uint8_t>(message_length & 0xFF);
    std::memcpy(binary_message.get()+magic_number_length+4, message.data(), message_length);
}

int sendUserBinaryMessage(const std::string &uid, const std::string& message) {
    if (rtcRoom_ != nullptr)
    {
        size_t length = 0;
        std::shared_ptr<uint8_t[]> binary_message = nullptr;
        buildBinaryMessage("ctrl", message, length, binary_message);
        return rtcRoom_->sendUserBinaryMessage(uid.c_str(), static_cast<int>(length), binary_message.get());
    }
    return -1;
}

打断智能体
最近更新时间：2025.06.09 16:30:06
首次发布时间：2025.04.01 17:09:09
我的收藏
有用
无用
当 AI 智能体讲话时，如果需要打断智能体发言，并开始新一轮对话，可以通过语音自动打断和手动打断两种方式来实现，以提升对话的流畅度和自然度。

语音自动打断：系统根据用户的语音输入自动判断并执行打断。支持三种策略：发声即打断、基于用户持续说话时间打断、通过关键词打断。
手动打断：用户主动发起打断请求，如点击按钮或发送特定命令来打断智能体，可在客户端或服务端实现。
应用场景	描述
客服对话	使用较短的自动打断时长，配合手动打断，实现快速响应和精准控制。
在线教育	采用较长的自动打断时长，避免误打断学生发言，教师可使用手动打断进行必要干预。
多人会议	结合自动打断和手动打断，灵活管理发言顺序和时长。
默认打断策略
若你未进行任何打断相关配置，或保留相关参数为默认值，系统将采用发声即打断策略。

语音自动打断
自动打断由系统根据用户的音频输入，自动判断是否需要中止智能体的输出。


发声即打断
一旦检测到用户发出声音，智能体立刻停止输出。

关键配置：
调用 StartVoiceChat 时，配置 Config 对象下以下参数：

InterruptMode：无需设置，保持默认值（0）。
InterruptConfig：无需设置，保持默认值。
SilenceTime：建议保持默认值（600 ms）或根据具体场景适当调整（例如 800 ms - 1200 ms），以确保用户在触发打断后，有足够的时间完整表达其意图，避免因 VAD（语音检测）过早结束而导致语音输入不完整。
请求示例：

{
    "Config": {
        // InterruptMode 保持默认值 0 (发声即打断总开关开启)
        // ASRConfig.InterruptConfig 使用默认值 
        "ASRConfig": {
            // Provider, ProviderParams 等其他 ASR 必要配置需填写
            "VADConfig": {
                "SilenceTime": 600 // 示例：可按需调整，确保用户打断后能完整表达
            }
            // InterruptConfig 及其子参数使用默认值，实现发声即打断
        }
        // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
    }
    // 其他请求参数...
}
基于用户持续说话时间打断
当检测到用户持续说话的时长达到预设阈值后，智能体才会停止输出。适用于希望避免因短暂语音（语气词），或环境中无意义持续时间较短的人声（如“好的”）而意外打断智能体的场景。

关键配置：
调用 StartVoiceChat 时，配置 Config 对象下以下参数：
InterruptMode：无需设置，保持默认值（ 0）。
InterruptSpeechDuration：设置为一个合适的毫秒数。该参数取值范围为[200，3000]，单位为 ms。
SilenceTime：确保此值显著大于 InterruptSpeechDuration，以便用户在触发打断后有足够时间说完话。例如，如果 InterruptSpeechDuration 是 300ms，SilenceTime 可以是 600ms 或更高。
请求示例：
{
    "Config": {
        "InterruptMode": 0, // 确保自动打断总开关为开启状态 (0 为开启)
        "ASRConfig": {
            // Provider, ProviderParams 等其他 ASR 必要配置需填写
            "InterruptConfig": {
                "InterruptSpeechDuration": 300 // 设置用户持续说话300ms后打断
            },
            "VADConfig": {
                "SilenceTime": 600 // 示例，可按需调整，确保大于 InterruptSpeechDuration
            }
        }
        // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
    }
    // 其他请求参数...
}

通过关键词打断
当用户说出预设的关键词时，智能体会停止输出。适用于需要用户明确表达意图才能打断的场景，如多人会议。

关键配置：
调用 StartVoiceChat 时，配置 Config 对象下以下参数：

InterruptMode：设置为 0。
InterruptKeywords：配置一个字符串数组，如 ["停止", "下一个"]。
说明

支持同时设置 InterruptSpeechDuration，且优先级高于 InterruptKeywords。智能体说话时，若用户说话时长未达到 InterruptSpeechDuration，即使说话中包含关键词，也不会触发打断。

通过热词或替换词功能，提高关键词的识别准确率。仅火山流式语音识别大模型支持。
功能	配置说明
热词	如果关键词（如专有名词、特定术语等）识别率较低时，可将其作为热词传入 ASR 模型，提高这些词汇的识别准确度。可通过以下方式传入：
直接传入（适用临时或少量关键词场景）：
通过 ASRConfig.ProviderParams.context，直接将多个热词作为 JSON 字符串传入。
通过热词词表 ID/名称传入（适用于大量、常用或需要长期维护的热词）：
1. 在火山引擎大模型的自学习平台预先创建热词词表。如何创建？
2. 调用 StartVoiceChat 时，设置参数 boosting_table_id 或 boosting_table_name。
优先级：热词直传优先热词词表。
替换词	将关键词替换为你期望的词汇。可通过步骤传入：
在火山引擎大模型的自学习平台预先创建替换词词表。如何创建？
调用 StartVoiceChat 时，设置参数 correct_table_id 或 correct_table_name。
优先级：替换词优先于热词。即如果一个词同时是热词和替换词的源词，替换词生效。例如，热词里有“苹果”，替换词要求“苹果→Apple”，那最后结果为 “Apple”。
请求示例：

{
    "Config": {
        "InterruptMode": 0, // 确保自动打断总开关为开启状态
        "ASRConfig": {
            "Provider": "volcano", // 使用火山引擎ASR
            "ProviderParams": {
                "Mode": "bigmodel", // 指定使用火山流式语音识别大模型，热词功能（context, boosting_table_id等）仅在 Mode 为 "bigmodel" 时生效
                "AppId": "YOUR_ASR_BIGMODEL_APPID",       // 替换为您的火山大模型ASR AppId
                "AccessToken": "YOUR_ASR_BIGMODEL_TOKEN", // 替换为您的火山大模型ASR AccessToken
                // "ApiResourceId": "volc.bigasr.sauc.duration", // 根据您的购买类型填写
                "context": "{\"hotwords\": [{\"word\":\"停止\"},{\"word\":\"下一个\"},{\"word\":\"我的自定义品牌名\"}]}" // 示例：配置热词直传，提高关键词识别准确率
                // 如果使用热词词表，则配置 boosting_table_id 或 boosting_table_name
                // "boosting_table_id": "YOUR_BOOSTING_TABLE_ID"
            },
            "InterruptConfig": {
                "InterruptKeywords": ["停止", "下一个"] // 设置打断关键词
                // "InterruptSpeechDuration": 500 // 可选：如果同时设置，则时长优先
            },
            "VADConfig": {
                "SilenceTime": 600 //示例：可按需调整，确保用户打断后能完整表达
            }
            // 其他ASRConfig参数，如VolumeGain等，可按需配置
        }
        // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
    }
    // 其他请求参数如 AppId, RoomId, TaskId, AgentConfig 等也需填写
}
关闭语音自动打断
如果需要禁用语音自动打断功能，比如希望智能体完整播报信息，而不被用户的语音输入打断，可执行以下操作：

调用 StartVoiceChat 时，将 Config.InterruptMode 设置 1。设置后，InterruptConfig 内的所有配置无效。

手动打断
手动打断为用户主动发起打断请求，通过点击按钮或快捷键方式，主动打断智能体的回答。
通过服务端或客户端均可实现手动打断操作，你可根据业务请求端种类选择对应的方式。例如你在开发 AI 应用时选择服务端响应请求，则使用服务端实现手动打断，降低请求延迟。

通过服务端实现手动打断
调用 UpdateVoiceChat接口，设置以下参数打断智能体输出：

参数	类型	描述
AppId	String	RTC 应用 AppId，参看获取 AppId。
RoomId	String	替换为调用 StartVoiceChat 时使用的 RoomId。
TaskId	String	替换为调用 StartVoiceChat 时使用的 TaskId。
Command	String	填入interrupt，表示打断智能体。
你可参看以下示例从服务端实现打断操作：

POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf", 
    "RoomId": "Room1", 
    "TaskId": "task1", 
    "Command": "interrupt"
}
通过客户端实现手动打断
使用 SendUserBinaryMessage 接口实现打断操作。该接口的 buffer 参数需要传入特定格式的内容，下图展示了 buffer 参数的格式：

alt

参数名	类型	描述
magic_number	binary	消息格式标识符，当前场景消息格式固定为 ctrl，用于标识该消息为控制消息。
length	binary	打断消息长度，单位为字节，采用大端序（Big-endian）存储方式，用于说明 control_message 字段的字节长度。
control_message	binary	打断行为配置信息，采用 JSON 格式，具体内容格式参看 control_message 格式。
control_message:

参数名	类型	描述
Command	String	控制命令，此处填入 interrupt，表示打断智能体。
你可参看以下示例从客户端实现打断操作：

C++
Java
TypeScript
// 发送打断指令
void sendInterruptMessage(const std::string &uid) {
    nlohmann::json json_data;
    json_data["Command"] = "interrupt";
    sendUserBinaryMessage(uid, json_data.dump());
}
void buildBinaryMessage(const std::string& magic_number, const std::string& message, size_t& binary_message_length, std::shared_ptr<uint8_t[]>& binary_message) { //将字符串包装成 TLV
    auto magic_number_length = magic_number.size();
    auto message_length = message.size();

    binary_message_length = magic_number_length + 4 + message_length;
    binary_message = std::shared_ptr<uint8_t[]>(new uint8_t[binary_message_length]);
    std::memcpy(binary_message.get(), magic_number.data(), magic_number_length);
    binary_message[magic_number_length] = static_cast<uint8_t>((message_length >> 24) & 0xFF);
    binary_message[magic_number_length+1] = static_cast<uint8_t>((message_length >> 16) & 0xFF);
    binary_message[magic_number_length+2] = static_cast<uint8_t>((message_length >> 8) & 0xFF);
    binary_message[magic_number_length+3] = static_cast<uint8_t>(message_length & 0xFF);
    std::memcpy(binary_message.get()+magic_number_length+4, message.data(), message_length);
}

int sendUserBinaryMessage(const std::string &uid, const std::string& message) {
    if (rtcRoom_ != nullptr)
    {
        size_t length = 0;
        std::shared_ptr<uint8_t[]> binary_message = nullptr;
        buildBinaryMessage("ctrl", message, length, binary_message);
        return rtcRoom_->sendUserBinaryMessage(uid.c_str(), static_cast<int>(length), binary_message.get());
    }
    return -1;
接收打断通知
通过状态回调功能，实时获取 AI 智能体在对话过程中被打断的通知，支持通过业务服务器或客户端接收打断通知。

具体实现操作，请参见接收状态变化消息。

FAQ
如何设置打断灵敏度，来避免背景噪音或自己声音打断智能体？
可以尝试以下方法来降低背景噪声干扰：

方法 1：使用关键词打断
只有在检测到指定关键词时才会打断智能体。例如，在会议预约场景中，可设置“取消”/“停止”等关键词，只有当用户说出这些词时，才会打断智能体，有效避免了背景噪音随机触发打断的情况。如何设置关键词打断？

方法 2：降低打断阈值组合策略

设置打断持续时长（InterruptSpeechDuration）：设置为一个大于 0 的值（例如 2000 ms），只有当声音持续达到该时长后才会触发打断，有效过滤短暂噪音。
调整音量增益（VolumeGain）：适当降低 VolumeGain 的值，从而降低送入 ASR 的整体音量，使得较小的背景音可能不被识别为语音活动。需要注意，过度降低可能影响正常语音识别。
请求示例如下：

POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
   "Config": {
       "InterruptMode": 0, // 确保自动打断总开关为开启状态 (0 为开启)
       "ASRConfig": {
           // Provider, ProviderParams 等其他 ASR 必要配置需填写
           "InterruptConfig": {
               "InterruptSpeechDuration": 2000 // 设置用户持续说话2000ms后打断
           },
           "VADConfig": {
               "SilenceTime": 600 // 示例，可按需调整，确保大于 InterruptSpeechDuration
           }
           "VolumeGain": 0.3 
       }
       // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
   }
   // 其他请求参数...
}
方法 3：关闭语音自动打断，采用手动打断
如果场景允许，可关闭语音自动打断功能，让用户通过手动操作（如按键）来打断智能体。这样用户可以根据自己的需求和判断，在合适的时机主动发起打断，完全避免了背景噪音自动触发打断的可能性。具体操作，请参看关闭语音自动打断和手动打断。

VAD 对语音打断持续时长有影响吗？
VAD 配置（SilenceTime）不直接改变 InterruptSpeechDuration 的阈值或行为逻辑。但是，一个设置得过短的 SilenceTime 会导致用户的语音输入被过早地判定为结束。如果用户试图通过持续说话（即依赖 InterruptSpeechDuration > 0）来打断 AI，而他的话还没说到满足 InterruptSpeechDuration 的时长就被过短的 SilenceTime 切断了，那么基于时长的打断就不会发生。用户会感觉“我说了一半，系统就认为我说完了，也没打断 AI”。

如智能体说话时被打断，已经输出的内容会存入上下文吗？
不会存入上下文。


大模型上下文管理
最近更新时间：2025.05.29 15:15:36
首次发布时间：2025.05.29 11:18:34
我的收藏
有用
无用
在对话式 AI 场景下，大模型上下文管理十分重要，它可以让大模型根据聊天历史进行更精准的内容回答。本文将介绍火山引擎支持平台的大模型上下文管理机制，涵盖核心参数说明、关键功能对比及长度限制规则，并补充自定义上下文的动态扩展方法。

上下文管理机制
不同大模型平台下，上下文管理机制和使用的参数不同。你可参看下文了解火山方舟平台、Coze 平台和符合平台要求的第三方大模型的上下文管理机制。

火山方舟平台
使用火山方舟平台时，模型对话上下文生成逻辑由系统提示词（SystemMessages）、用户提示词（UserPrompts 和 UserMessages）和历史问题轮数（HistoryLength）共同控制。具体说明如下：

参数名	类型	说明
SystemMessages	String[]	系统提示词。定义模型的角色、行为准则及输出格式等核心指令。
UserPrompts	Object[]	用户提示词。可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。需包含 Role（仅支持 user/assistant）和 Content 字段，且必须成对出现。推荐使用该参数控制用户提示词，具有存储提示词自动逐出机制，且表现更稳定。关于 UserPrompts 和 UserMessages 差异，参看UserPrompts 与 UserMessages 对比说明。
UserMessages	String[]	用户提示词。可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。该参数为非推荐隐藏参数。
HistoryLength	Integer	模型存储历史问题轮数。
UserPrompts 与 UserMessages 对比说明

相同点
两者均为用户提示词，用于增强模型的回复质量。模型回复时会优先参考此处内容，引导生成特定输出或执行特定任务。

差异点
维度	UserPrompts	UserMessages
数据类型	Object[]（对象数组）。	String[]（字符串数组）。
字段要求	需包含 Role（仅支持 user/assistant）和 Content 字段，且必须成对出现（user 与 assistant 交替）。	无严格字段要求，直接存储对话文本。
存储机制	具有内容自动逐出机制：
若 UserPrompts 预先存储了 2 轮对话，HistoryLength 设置为 3，在智能体任务启动后，UserPrompts中的对话会存储到上下文中，当用户进行第二轮会话时，原 UserPrompts 中的第一轮对话内容会被清空。	内容永久存储在上下文中，不会被自动逐出。
示例对比
UserPrompts 示例（需严格成对）：
"UserPrompts": [
    {
        "Role": "user",
        "Content": "你好"
    },
    {
        "Role": "assistant",
        "Content": "有什么可以帮到你？"
    }
]
UserMessages 示例（直接存储文本）：
"UserMessages": [
    "今天北京今天晴，气温18-28 摄氏度，适合户外活动。",
    "明天北京有小雨，气温16-25 摄氏度，建议带伞。"
]

上下文长度限制
在使用不同的参数控制大模型上下文时，上下文长度限制机制不同，具体如下：

当使用 SystemMessages、UserPrompts 和 HistoryLength 组合时：

在调用该接口时，需要确保 UserPrompts、SystemMessage 与会话内容文本总长度不超过大模型上下文长度。
例如：历史问题轮数为 3，使用 Doubao-lite-8k 大模型，大模型上下文长度限制为 8k，UserPrompts 预先存储了两轮对话，在智能体任务启动后，UserPrompts 中的对话会存储到上下文中，当用户进行第二轮对话时，原 UserPrompts 中的第一轮对话内容会被逐出，需保证 SystemMessage + UserPrompts 中存储的第二轮会话内容 + 第一、二轮会话问题总长度不得超过 8k。

当使用 SystemMessages、UserMessages 和 HistoryLength 组合时：

在调用该接口时，需要确保所有 UserMessage 和 SystemMessage 与会话内容文本总长度不超过大模型上下文长度。
例如：历史问题轮数为 3，使用 Doubao-lite-8k 大模型，预设 SystemMessages 和 UserMessages 各两条，询问第 10 个问题时，需保证预设的两条 SystemMessages、预设的两条 UserMessages 与第八、九、十轮会话的总长度之和不得超过 8k。

Coze 平台
使用 Coze 平台时，你可选择 RTC 管理上下文或 Coze 平台管理上下文，具体由LLMConfig.EnableConversation 控制。不同情况下上下文管理机制不同：

RTC 管理上下文
RTC 管理上下文时，模型对话上下文生成逻辑由历史问题轮数（HistoryLength）和 Coze 平台编排页人设与回复逻辑、插件、知识库共同控制。
除 HistoryLength 参数外，其他参数均在 Coze 平台 bot 页面配置。Coze 编排功能、插件等搭建能力详细介绍参看 Coze 开发智能体功能概述。

Coze 平台管理上下文
Coze 平台管理上下文时，模型对话上下文生成逻辑完全由 Coze 平台编排页人设与回复逻辑、插件、知识库共同控制。
Coze 编排功能、插件等搭建能力详细介绍参看 Coze 开发智能体功能概述。

上下文长度限制
在调用该接口时，需要确保编排页人设与回复逻辑、插件、知识库与会话内容文本总长度不超过大模型上下文长度。历史问题轮数在 RTC 管理上下文时通过HistoryLength 参数控制，在 Coze 平台管理上下文时通过模型设置页面操作。

第三方大模型
使用第三方大模型时，模型对话上下文生成逻辑由系统提示词（SystemMessages）、用户提示词（UserPrompts 和 UserMessages）和历史问题轮数（HistoryLength）共同控制。具体说明如下：

参数名	类型	说明
SystemMessages	String[]	系统提示词。定义模型的角色、行为准则及输出格式等核心指令。
UserPrompts	Object[]	用户提示词。可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。需包含 Role（仅支持 user/assistant）和 Content 字段，且必须成对出现。推荐使用该参数控制用户提示词，具有存储提示词自动逐出机制，且表现更稳定。关于 UserPrompts 和 UserMessages 差异，参看UserPrompts 与 UserMessages 对比说明。
UserMessages	String[]	用户提示词。可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。该参数为非推荐隐藏参数。
HistoryLength	Integer	模型存储历史问题轮数。
UserPrompts 与 UserMessages 对比说明

相同点
两者均为用户提示词，用于增强模型的回复质量。模型回复时会优先参考此处内容，引导生成特定输出或执行特定任务。

差异点
维度	UserPrompts	UserMessages
数据类型	Object[]（对象数组）。	String[]（字符串数组）。
字段要求	需包含 Role（仅支持 user/assistant）和 Content 字段，且必须成对出现（user 与 assistant 交替）。	无严格字段要求，直接存储对话文本。
存储机制	具有内容自动逐出机制：
若 UserPrompts 预先存储了 2 轮对话，HistoryLength 设置为 3，在智能体任务启动后，UserPrompts中的对话会存储到上下文中，当用户进行第二轮会话时，原 UserPrompts 中的第一轮对话内容会被清空。	内容永久存储在上下文中，不会被自动逐出。
示例对比
UserPrompts 示例（需严格成对）：
"UserPrompts": [
    {
        "Role": "user",
        "Content": "你好"
    },
    {
        "Role": "assistant",
        "Content": "有什么可以帮到你？"
    }
]
UserMessages 示例（直接存储文本）：
"UserMessages": [
    "今天北京今天晴，气温18-28 摄氏度，适合户外活动。",
    "明天北京有小雨，气温16-25 摄氏度，建议带伞。"
]

上下文长度限制
在使用不同的参数控制大模型上下文时，上下文长度限制机制不同，具体如下：

当使用 SystemMessages、UserPrompts和 HistoryLength组合时：

在调用该接口时，需要确保 UserPrompts、SystemMessage 与会话内容文本总长度不超过大模型上下文长度。
例如：历史问题轮数为 3，Doubao-lite-8k，大模型上下文长度限制为 8k，UserPrompts 预先存储了两轮对话，在智能体任务启动后，UserPrompts中的对话会存储到上下文中，当用户进行第二轮对话时，原 UserPrompts 中的第一轮对话内容会被逐出，需保证 SystemMessage + UserPrompts 中存储的第二轮会话内容 + 第一、二轮会话问题总长度不得超过8k。

当使用 SystemMessages、UserMessages和 HistoryLength组合时：

在调用该接口时，需要确保所有 UserMessage 和 SystemMessage 与会话内容文本总长度不超过大模型上下文长度。
例如：历史问题轮数为 3，Doubao-lite-8k，预设 SystemMessages 和 UserMessages 各两条，询问第 10 个问题时，需保证预设的两条 SystemMessages、预设的两条 UserMessages 与第八、九、十轮对话的总长度之和不得超过 8k。

自定义上下文
除用户与智能体语音互动的内容作为上下文外，你还可以通过 UpdateVoiceChat 接口动态传入自定义上下文信息，以帮助大模型更准确地理解用户状态，从而提升对话的自然度。自定义上下文信息可以包括用户的实时生理数据、游戏数据、角色设定等。这些信息将根据你设定的优先级决定替代用户输入或增加新一轮对话，自定义上下文信息作为 UserMessage 传入大模型，并在当前对话轮次中生效。具体使用说明参看自定义大模型上下文。