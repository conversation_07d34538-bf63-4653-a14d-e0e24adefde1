@echo off
chcp 65001 >nul

echo Starting XinQiao Agent API...
echo.

REM 保存项目根目录路径
set PROJECT_ROOT=%CD%
echo 项目根目录: %PROJECT_ROOT%

REM 激活conda环境
call conda activate xinqiao-py312

REM 设置PYTHONPATH - 确保模块能被找到
set PYTHONPATH=%PROJECT_ROOT%\apps\agent-api;%PYTHONPATH%
echo PYTHONPATH已设置为: %PYTHONPATH%
echo.

REM 验证Python环境和导入
echo 验证Python环境...
python -c "import sys; print('Python路径:'); [print(f'  {p}') for p in sys.path[:3]]; print('...')"
echo.

REM 切换到agent-api目录并启动
cd apps\agent-api
echo Starting server on http://localhost:8003
echo Health check endpoint: http://localhost:8003/api/v1/health
echo.
uvicorn api.main:app --host 0.0.0.0 --port 8003 --reload --env-file .env

pause 