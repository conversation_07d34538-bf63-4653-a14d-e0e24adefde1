{"name": "x<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "description": "心桥 AI 亲情伴侣 - Monorepo", "scripts": {"dev": "pnpm --parallel run dev", "build": "pnpm --parallel run build", "lint": "pnpm --parallel run lint", "type-check": "pnpm --parallel run type-check", "test": "pnpm --parallel run test", "mobile:dev": "pnpm --filter mobile-app dev", "mobile:build": "pnpm --filter mobile-app build", "mobile:lint": "pnpm --filter mobile-app lint", "mobile:test": "pnpm --filter mobile-app test", "api:dev": "cd apps/agent-api && python -m uvicorn api.main:app --reload", "api:test": "cd apps/agent-api && python -m pytest", "api:lint": "cd apps/agent-api && ruff check .", "api:format": "cd apps/agent-api && ruff format ."}, "workspaces": ["apps/mobile-app"], "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "packageManager": "pnpm@9.0.0+sha256.abcd1234"}