# "心桥"AI亲情伴侣 - 产品需求文档 (PRD) v1.0

## 📖 文档信息
- **版本**: v1.0  
- **创建日期**: 2024年
- **负责人**: 产品团队
- **状态**: 开发中

## 🎯 产品概述

### 产品定位
"心桥"是一款面向55-75岁中国老年用户的AI亲情伴侣应用，通过构建**有记忆、可定制、有温度的"关系式"陪伴**，系统性解决老年群体的结构性孤独感问题。

### 核心价值主张
- **不是工具，是关系** - 提供持久、个性化、主动的陪伴
- **零学习成本** - 基于微信语音交互习惯
- **绝对安全可信** - 纯净环境，无广告无收费陷阱

## 👥 目标用户

### 核心用户画像
- **年龄**: 55-75岁
- **身份**: 退休教师、干部、工程师等
- **地域**: 二三线城市
- **家庭**: 子女在外地，独居或与配偶同住
- **技能**: 会用微信、抖音，但对新App有恐惧

### 用户需求层次
1. **安全需求**: 不被骗、不出错、不花冤枉钱
2. **陪伴需求**: 有人倾听、理解、记住自己
3. **价值需求**: 被需要、有用、不被时代抛弃

## 🚀 产品功能规格

### MVP核心功能 (P0 - 必须有)

#### 1. 无感身份系统
**需求**: 用户无需注册即可使用
- 后台自动生成设备ID
- 与后端建立匿名用户档案
- 支持数据持久化存储

**验收标准**:
- 首次启动3秒内完成身份建立
- 重启后数据不丢失
- 用户全程无感知

#### 2. 角色共创流程
**需求**: 用户通过对话为AI设定身份和名字
- AI引导用户设定称呼
- 用户为AI命名
- 选择AI角色(老朋友/贴心晚辈)
- 确认AI声音

**验收标准**:
- 整个流程以对话形式完成
- 用户选择被正确保存和应用
- AI人设在后续对话中保持一致

#### 3. 核心对话交互
**需求**: 极简的语音对话界面
- "按住说话"作为唯一输入方式
- AI回复自动语音播放
- 清晰的交互状态反馈

**验收标准**:
- 语音识别准确率>85%
- AI回复时间<3秒
- 界面无任何复杂元素

#### 4. 分层记忆系统
**需求**: AI能记住用户并建立连续对话
- **短期记忆**: 最近20轮对话
- **长期记忆**: 用户称呼、AI身份
- **可控记忆**: 用户可要求"记住"或"忘记"

**验收标准**:
- AI能在对话中正确引用历史内容
- 重启后身份记忆不丢失
- 用户指令能控制记忆内容

#### 5. 对话式提醒
**需求**: 通过自然语言设置生活提醒
- 智能解析时间和事件
- AI语音复述确认
- 定时推送温柔提醒

**验收标准**:
- 时间解析准确率>90%
- 提醒送达率>99%
- 提醒采用AI角色语音

#### 6. 危机响应协议
**需求**: 检测并干预潜在危机
- 关键词检测(KWS)
- 语音情感识别(SER)
- 安全护栏脚本
- 专业资源引导

**验收标准**:
- 危机信号检测准确
- 立即切换安全对话模式
- 提供专业求助渠道

### 支撑功能 (P1 - 重要)

#### 情感化异常处理
- 网络错误时AI"揽责"
- 技术故障人性化解释
- 避免用户自责和恐惧

#### 主动关怀问候
- 基于时间的个性化问候
- 融入天气等情境信息
- 体现"关系"而非"工具"

## 🎨 用户体验要求

### 适老化设计标准
- **字体**: 不小于16pt，对话内容18pt
- **色彩**: 高对比度(>4.5:1)，暖色调
- **按钮**: 可点击区域≥44x44 points
- **语音**: 清晰、耐心、鼓励性的话术

### 交互原则
1. **零学习成本** - 只有一个核心操作"按住说话"
2. **即时反馈** - 每个操作都有清晰的视觉/听觉反馈
3. **情感温度** - 所有文案都体现关怀和耐心
4. **绝对安全** - 界面纯净，无任何可能引起焦虑的元素

## 🔧 技术要求

### 性能指标
- **启动时间**: <3秒
- **API响应**: P95 <2秒
- **崩溃率**: <0.5%
- **语音识别**: 准确率>85%

### 安全要求
- 全链路TLS加密
- 数据库行级安全(RLS)
- 符合《个人信息保护法》
- 定期安全审计

### 技术栈
- **前端**: React Native + Expo
- **后端**: Supabase (BaaS)
- **AI服务**: 火山引擎 ASR/TTS/LLM
- **数据库**: PostgreSQL
- **推送**: Expo Notifications

## 📊 验收标准

### 定性指标 (核心)
- 收集到≥5个感人用户故事
- 用户访谈中出现"离不开"、"真懂我"等情感词汇
- 80%用户能独立完成核心任务

### 定量指标 (辅助)
- 次日留存率 >60%
- 7日留存率 >40%
- DAU/MAU >40%

## 🚫 明确不做的功能

### MVP阶段坚决不做
- 传统注册/登录界面
- 任何形式的收费提示
- 新闻/信息流内容
- 复杂的设置选项
- 社交分享功能
- 图片/表情包发送
- 外部链接或广告

## 🗓️ 里程碑计划

### Phase 1: 设计验证 (2-3周)
- 完成高保真原型
- 真实用户测试
- 设计方案最终确认

### Phase 2: MVP开发 (3-4个月)
- 核心功能开发
- 内部测试
- 首席体验官计划启动

### Phase 3: 上线运营 (持续)
- 灰度发布
- 用户反馈收集
- 快速迭代优化

## 🔄 后续版本规划

### V2.0 (MVP后3-6个月)
- 家庭记忆银行
- 共享体验功能
- 扩展提醒功能

### V3.0 (6-12个月后)
- IoT设备联动
- 社区服务对接
- 商业化探索

## ⚠️ 风险与应对

### 主要风险
1. **语音识别准确率不达标** → 提前测试，准备模型微调
2. **用户接受度低** → 深度运营种子用户，快速迭代
3. **合规风险** → 聘请专业顾问，建立合规体系

### 应对策略
- 建立Feature Flag机制支持快速回滚
- 设立AI伦理委员会
- 定期进行用户访谈和体验测试

## 📞 项目联系人
- **产品负责人**: [待填写]
- **技术负责人**: [待填写]  
- **设计负责人**: [待填写]
- **运营负责人**: [待填写]

---
*本文档为活文档，将根据开发进展持续更新* 