当然，完全理解您的需求！这份详尽的 API 测试指南将严格按照您的要求编写，确保您可以在 Windows 11 的普通命令行终端（CMD）中通过复制粘贴来完成所有接口的功能验证。

### **准备工作：配置环境变量和获取令牌**

在开始测试前，请在您的 CMD 终端中设置以下两个变量。这会极大地方便后续的复制粘贴操作。

1.  **设置 `ACCESS_TOKEN`**:
    *   首先，调用匿名登录接口获取一个令牌。
    *   打开一个新的 CMD 终端，运行以下命令（这是一个单行命令，请完整复制）：
        ```cmd
        for /f "tokens=2 delims=:" %a in ('curl -s -X POST "http://localhost:8000/api/v1/auth/anonymous-login" -H "Content-Type: application/json" -d "{\"device_info\": {\"device_id\": \"cmd-test-device-001\", \"platform\": \"windows\", \"app_version\": \"1.0.1\"}}" ^| find "access_token"') do set ACCESS_TOKEN=%a
        ```
    *   这个命令会自动提取 `access_token` 并存入环境变量。**请注意，您可能需要手动去掉令牌前后的引号和逗号**。最稳妥的方式是：
        1.  运行 `curl` 命令本身，复制返回的 `access_token` 值。
        2.  然后手动设置变量：`set ACCESS_TOKEN=<您复制的令牌>`

2.  **设置 `USER_ID`**:
    *   同样，从上一步的登录响应中复制 `user.id` 的值。
    *   手动设置变量：`set USER_ID=<您复制的用户ID>`

**现在，您可以开始测试所有接口了。请确保您的 FastAPI 服务正在运行。**

---

### **GET 请求测试**

#### **1. 健康检查**
*   **请求**: `GET /health`
*   **说明**: 检查 API 服务是否正常运行。
*   **鉴权**: 否
*   **测试命令**:
    ```cmd
    curl -X GET "http://localhost:8000/api/v1/health"
    ```

#### **2. 获取用户画像**
*   **请求**: `GET /user/profile`
*   **说明**: 获取当前登录用户的个人资料。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X GET "http://localhost:8000/api/v1/user/profile" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **3. 获取用户设置**
*   **请求**: `GET /user/settings`
*   **说明**: 获取当前用户的应用设置。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X GET "http://localhost:8000/api/v1/user/settings" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **4. 获取角色列表**
*   **请求**: `GET /characters`
*   **说明**: 获取所有可用的 AI 角色列表。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X GET "http://localhost:8000/api/v1/characters" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```
    *   **提示**: 您可以从返回的 `data` 数组中记下一个 `id`，用于后续的详情和绑定测试。

#### **5. 获取角色详情**
*   **请求**: `GET /characters/{character_id}`
*   **说明**: 获取指定角色的详细信息。
*   **鉴权**: 是
*   **测试命令** (请将 `<CHARACTER_ID>` 替换为上一步获取的ID):
    ```cmd
    set CHARACTER_ID=<CHARACTER_ID>
    curl -X GET "http://localhost:8000/api/v1/characters/%CHARACTER_ID%" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **6. 获取会话列表**
*   **请求**: `GET /chat/sessions`
*   **说明**: 获取当前用户的历史会话列表。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X GET "http://localhost:8000/api/v1/chat/sessions" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **7. 获取指定会话的聊天记录**
*   **请求**: `GET /chat/sessions/{session_id}/messages`
*   **说明**: 获取指定会话的聊天记录。
*   **鉴权**: 是
*   **测试命令** (请将 `<SESSION_ID>` 替换为您通过“创建会话”接口得到的ID):
    ```cmd
    set SESSION_ID=<SESSION_ID>
    curl -X GET "http://localhost:8000/api/v1/chat/sessions/%SESSION_ID%/messages" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **8. 获取 RTC 会话状态**
*   **请求**: `GET /rtc/sessions/{session_id}/status?userId={user_id}`
*   **说明**: 获取一个 RTC 会话的当前状态。
*   **鉴权**: 否 (但通过查询参数校验用户)
*   **测试命令** (请替换 `<SESSION_ID>` 和 `<USER_ID>`):
    ```cmd
    set RTC_SESSION_ID=<RTC_SESSION_ID>
    curl -X GET "http://localhost:8000/api/v1/rtc/sessions/%RTC_SESSION_ID%/status?userId=%USER_ID%"
    ```

#### **9. 获取 RTC 会话配置**
*   **请求**: `GET /rtc/sessions/{session_id}/config?userId={user_id}`
*   **说明**: 获取一个 RTC 会话的配置信息。
*   **鉴权**: 否 (但通过查询参数校验用户)
*   **测试命令** (请替换 `<SESSION_ID>` 和 `<USER_ID>`):
    ```cmd
    set RTC_SESSION_ID=<RTC_SESSION_ID>
    curl -X GET "http://localhost:8000/api/v1/rtc/sessions/%RTC_SESSION_ID%/config?userId=%USER_ID%"
    ```

#### **10. 获取提醒列表**
*   **请求**: `GET /reminders`
*   **说明**: 获取当前用户的所有提醒。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X GET "http://localhost:8000/api/v1/reminders" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **11. 获取提醒详情**
*   **请求**: `GET /reminders/{reminder_id}`
*   **说明**: 获取单个提醒的详细信息。
*   **鉴权**: 是
*   **测试命令** (请将 `<REMINDER_ID>` 替换为创建提醒后获得的ID):
    ```cmd
    set REMINDER_ID=<REMINDER_ID>
    curl -X GET "http://localhost:8000/api/v1/reminders/%REMINDER_ID%" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

---

### **POST 请求测试**

#### **1. 匿名登录 (已在准备工作中完成)**
*   **请求**: `POST /auth/anonymous-login`

#### **2. 刷新令牌**
*   **请求**: `POST /auth/refresh-token`
*   **说明**: 使用 refresh_token 获取新的令牌。
*   **鉴权**: 否
*   **测试命令** (请将 `<YOUR_REFRESH_TOKEN>` 替换为登录时获取的令牌):
    ```cmd
    set REFRESH_TOKEN=<YOUR_REFRESH_TOKEN>
    curl -X POST "http://localhost:8000/api/v1/auth/refresh-token" -H "Content-Type: application/json" -d "{\"refresh_token\": \"%REFRESH_TOKEN%\"}"
    ```

#### **3. 创建新会话**
*   **请求**: `POST /chat/sessions`
*   **说明**: 开始一个新的对话。
*   **鉴权**: 否 (但请求体中需要 userId)
*   **测试命令**:
    ```cmd
    curl -X POST "http://localhost:8000/api/v1/chat/sessions" -H "Content-Type: application/json" -d "{\"userId\": \"%USER_ID%\", \"topic\": \"CMD Test Session\"}"
    ```
    *   **提示**: 请记下返回的会话ID (`id`)，用于后续测试。

#### **4. 文本聊天 (SSE)**
*   **请求**: `POST /chat/text_message`
*   **说明**: 发送文本消息并流式接收回复。
*   **鉴权**: 是
*   **测试命令** (请将 `<SESSION_ID>` 替换为上一步创建的ID):
    ```cmd
    set SESSION_ID=<SESSION_ID>
    curl -N -X POST "http://localhost:8000/api/v1/chat/text_message" -H "Authorization: Bearer %ACCESS_TOKEN%" -H "Content-Type: application/json" -d "{\"message\": \"Hello from CMD\", \"sessionId\": \"%SESSION_ID%\"}"
    ```

#### **5. 绑定角色**
*   **请求**: `POST /user/characters/{character_id}/bind`
*   **说明**: 将指定角色与当前用户绑定。
*   **鉴权**: 是
*   **测试命令** (请将 `<CHARACTER_ID>` 替换为从 `GET /characters` 获取的ID):
    ```cmd
    set CHARACTER_ID=<CHARACTER_ID>
    curl -X POST "http://localhost:8000/api/v1/user/characters/%CHARACTER_ID%/bind" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **6. 准备 RTC 会话**
*   **请求**: `POST /rtc/prepare_session`
*   **说明**: 为实时语音聊天获取连接凭证。
*   **鉴权**: 否
*   **测试命令**:
    ```cmd
    curl -X POST "http://localhost:8000/api/v1/rtc/prepare_session" -H "Content-Type: application/json" -d "{\"userId\": \"%USER_ID%\", \"sessionId\": \"rtc-session-cmd-01\", \"characterId\": \"compassionate_listener\"}"
    ```
    *   **提示**: 记下返回的 `taskId` 和 `sessionId`。

#### **7. 模拟 RTC Webhook**
*   **请求**: `POST /chat/rtc_event_handler`
*   **说明**: 模拟火山引擎发送语音识别结果。
*   **鉴权**: 否 (开发环境下通常会禁用签名验证)
*   **测试命令**:
    ```cmd
    set RTC_SESSION_ID=rtc-session-cmd-01
    curl -X POST "http://localhost:8000/api/v1/chat/rtc_event_handler" -H "Content-Type: application/json" -d "{\"event_type\": \"asr_result\", \"payload\": {\"text\": \"This is a voice test from CMD.\"}, \"custom\": \"{\\\"sessionId\\\": \\\"%RTC_SESSION_ID%\\\", \\\"userId\\\": \\\"%USER_ID%\\\"}\"}"
    ```

#### **8. 结束 RTC 会话**
*   **请求**: `POST /rtc/end_session`
*   **说明**: 结束一个 RTC 会话并触发后台分析。
*   **鉴权**: 否
*   **测试命令** (请将 `<TASK_ID>` 替换为准备会话时返回的ID):
    ```cmd
    set RTC_SESSION_ID=rtc-session-cmd-01
    set TASK_ID=<TASK_ID>
    curl -X POST "http://localhost:8000/api/v1/rtc/end_session" -H "Content-Type: application/json" -d "{\"userId\": \"%USER_ID%\", \"sessionId\": \"%RTC_SESSION_ID%\", \"taskId\": \"%TASK_ID%\"}"
    ```

#### **9. 创建提醒**
*   **请求**: `POST /reminders`
*   **说明**: 创建一个新的提醒。
*   **鉴权**: 是
*   **测试命令**: (这个命令会创建一个明天此时的提醒)
    ```cmd
    set REMINDER_TIME_ISO=
    for /f "tokens=*" %i in ('python -c "from datetime import datetime, timedelta, timezone; print((datetime.now(timezone.utc) + timedelta(days=1)).isoformat())"') do set REMINDER_TIME_ISO=%i
    curl -X POST "http://localhost:8000/api/v1/reminders" -H "Authorization: Bearer %ACCESS_TOKEN%" -H "Content-Type: application/json" -d "{\"content\": \"My CMD Reminder\", \"reminder_time\": \"%REMINDER_TIME_ISO%\"}"
    ```
    *   **提示**: 记下返回的提醒 `id`。

---

### **PUT 请求测试**

#### **1. 更新用户画像**
*   **请求**: `PUT /user/profile`
*   **说明**: 更新当前用户的个人资料。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X PUT "http://localhost:8000/api/v1/user/profile" -H "Authorization: Bearer %ACCESS_TOKEN%" -H "Content-Type: application/json" -d "{\"nickname\": \"CMD Tester\", \"interests\": [\"automation\", \"testing\"]}"
    ```

#### **2. 更新用户设置**
*   **请求**: `PUT /user/settings`
*   **说明**: 更新当前用户的应用设置。
*   **鉴权**: 是
*   **测试命令**:
    ```cmd
    curl -X PUT "http://localhost:8000/api/v1/user/settings" -H "Authorization: Bearer %ACCESS_TOKEN%" -H "Content-Type: application/json" -d "{\"theme\": \"dark\", \"font_size\": \"large\"}"
    ```

#### **3. 结束并分析会话**
*   **请求**: `PUT /chat/sessions/{session_id}/end`
*   **说明**: 结束一个文本会话并触发后台分析。
*   **鉴权**: 是
*   **测试命令** (请将 `<SESSION_ID>` 替换为之前创建的文本会话ID):
    ```cmd
    set SESSION_ID=<SESSION_ID>
    curl -X PUT "http://localhost:8000/api/v1/chat/sessions/%SESSION_ID%/end" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```

#### **4. 更新提醒**
*   **请求**: `PUT /reminders/{reminder_id}`
*   **说明**: 更新一个已存在的提醒。
*   **鉴权**: 是
*   **测试命令** (请将 `<REMINDER_ID>` 替换为创建提醒时获得的ID):
    ```cmd
    set REMINDER_ID=<REMINDER_ID>
    curl -X PUT "http://localhost:8000/api/v1/reminders/%REMINDER_ID%" -H "Authorization: Bearer %ACCESS_TOKEN%" -H "Content-Type: application/json" -d "{\"content\": \"Updated CMD Reminder\", \"status\": \"completed\"}"
    ```

---

### **DELETE 请求测试**

#### **1. 删除提醒**
*   **请求**: `DELETE /reminders/{reminder_id}`
*   **说明**: 删除一个提醒。
*   **鉴权**: 是
*   **测试命令** (请将 `<REMINDER_ID>` 替换为创建提醒时获得的ID):
    ```cmd
    set REMINDER_ID=<REMINDER_ID>
    curl -X DELETE "http://localhost:8000/api/v1/reminders/%REMINDER_ID%" -H "Authorization: Bearer %ACCESS_TOKEN%"
    ```
*   **预期响应**: HTTP 204 No Content (终端可能不会有任何输出)。您可以再调用 `GET /reminders` 确认该提醒已被删除。

---

这份指南覆盖了您项目中定义的所有主要接口，并提供了在 Windows CMD 环境下直接可用的测试命令。希望它能帮助您顺利完成后端的功能验证！