"""
测试故事1.15-B: 功能增强与体验优化
- AC-1: BotId支持验证
- AC-2: 时间解析健壮性验证
"""

import pytest
import json
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from freezegun import freeze_time

from api.services.volcano_client_service import VolcanoClientService
from api.services.reminder_service import ReminderService
from api.settings import Settings, settings


class TestBotIdSupport:
    """AC-1: BotId支持验证测试用例"""

    @pytest.fixture
    def volcano_client_service(self):
        """创建VolcanoClientService实例"""
        return VolcanoClientService()

    @pytest.fixture
    def character_config_with_bot_id(self):
        """包含有效bot_id的character_config"""
        return {
            "llm": {
                "bot_id": "bot_12345"
            }
        }

    @pytest.fixture
    def character_config_without_bot_id(self):
        """不包含bot_id的character_config"""
        return {
            "llm": {}
        }

    def test_botid_configuration_normal_path(self, volcano_client_service, character_config_with_bot_id):
        """Scenario 1: BotId配置正常路径验证"""
        # Given: volcano_client_service已正确初始化
        # And: character_config包含有效的bot_id: "bot_12345"

        # When: 调用_build_llm_config_data方法，传入character_config
        llm_config = volcano_client_service._build_llm_config_data(character_config_with_bot_id)

        # Then: 返回的LLMConfig应包含BotId字段，值为"bot_12345"
        assert "BotId" in llm_config
        assert llm_config["BotId"] == "bot_12345"

        # And: 返回的LLMConfig不应包含EndPointId字段
        assert "EndPointId" not in llm_config

        # And: 配置验证应成功通过
        assert llm_config["Mode"] == "ArkV3"

    def test_endpoint_id_fallback_mechanism(self, volcano_client_service, character_config_without_bot_id):
        """Scenario 2: EndPointId fallback机制验证"""
        # Given: volcano_client_service已正确初始化
        # And: character_config不包含bot_id字段或bot_id为空

        # When: 调用_build_llm_config_data方法，传入character_config
        llm_config = volcano_client_service._build_llm_config_data(character_config_without_bot_id)

        # Then: 返回的LLMConfig应包含EndPointId字段，值为settings.VOLCANO_LLM_ENDPOINT_ID
        assert "EndPointId" in llm_config
        assert llm_config["EndPointId"] == settings.VOLCANO_LLM_ENDPOINT_ID

        # And: 返回的LLMConfig不应包含BotId字段
        assert "BotId" not in llm_config

        # And: 配置验证应成功通过
        assert llm_config["Mode"] == "ArkV3"

    def test_botid_endpoint_id_mutual_exclusion(self, volcano_client_service):
        """Scenario 3: 配置互斥验证（架构师风险点）"""
        # Given: volcano_client_service已正确初始化
        # When: 尝试同时设置BotId和EndPointId（通过模拟错误配置）

        # 创建一个包含冲突配置的字典
        conflicting_config = {
            "Mode": "ArkV3",
            "BotId": "bot_12345",
            "EndPointId": "ep_67890"
        }

        # Then: 应抛出配置冲突异常
        with pytest.raises(ValueError) as exc_info:
            volcano_client_service._validate_llm_config_mutual_exclusion(conflicting_config)

        # And: 错误消息应明确指出"BotId与EndPointId不能同时存在"
        assert "BotId与EndPointId不能同时存在" in str(exc_info.value)

    def test_botid_configuration_security_validation(self, volcano_client_service):
        """Scenario 4: BotId配置来源安全验证（架构师风险点）"""
        # Given: volcano_client_service已正确初始化
        # And: 检测到bot_id来自用户输入而非character_config
        user_input_config = {
            "user_provided": {
                "bot_id": "malicious_bot_id"
            }
        }

        # When: 调用_build_llm_config_data方法进行配置验证
        llm_config = volcano_client_service._build_llm_config_data(user_input_config)

        # Then: 应拒绝不可信来源的bot_id
        assert "BotId" not in llm_config

        # And: 应fallback到默认的EndPointId配置
        assert "EndPointId" in llm_config
        assert llm_config["EndPointId"] == settings.VOLCANO_LLM_ENDPOINT_ID

    @pytest.mark.asyncio
    async def test_start_voice_chat_end_to_end_integration(self, volcano_client_service, character_config_with_bot_id):
        """Scenario 5: 端到端BotId集成测试"""
        # Given: RTC会话已准备就绪
        # And: character_config包含有效的bot_id
        room_id = "test_room_123"
        task_id = "test_task_456"
        user_id = "test_user_789"
        webhook_url = "http://test.webhook.com/callback"
        custom_data = {"session_id": "session_123"}

        # Mock外部API调用
        with patch.object(volcano_client_service, '_call_volcano_api_with_retry', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = {"status": "success", "TaskId": task_id}

            # When: 调用start_voice_chat方法，传入包含bot_id的配置
            result = await volcano_client_service.start_voice_chat(
                room_id=room_id,
                task_id=task_id,
                user_id=user_id,
                webhook_url=webhook_url,
                custom_data=custom_data,
                character_config=character_config_with_bot_id
            )

            # Then: 生成的StartVoiceChat API请求体应正确包含BotId字段
            call_args = mock_request.call_args
            request_data = call_args[1]['data']

            assert "Config" in request_data
            llm_config = request_data["Config"]["LLMConfig"]
            assert "BotId" in llm_config
            assert llm_config["BotId"] == "bot_12345"

            # And: 请求体不应包含EndPointId字段
            assert "EndPointId" not in llm_config

            # And: API调用应成功返回
            assert result["taskId"] == task_id


class TestTimeParsingRobustness:
    """AC-2: 时间解析健壮性验证测试用例"""

    @pytest.fixture
    def reminder_service(self):
        """创建ReminderService实例"""
        return ReminderService()

    @freeze_time("2024-01-15 14:00:00")  # 周一下午2点
    @pytest.mark.asyncio
    async def test_arrow_library_priority_parsing(self, reminder_service):
        """Scenario 6: Arrow库优先解析验证"""
        # Given: ReminderService已正确初始化
        # And: arrow库已安装并可用

        # When: 调用parse_time_with_fallback方法，输入"明天下午3点"
        result = await reminder_service.parse_time_with_fallback("明天下午3点")

        # Then: 应优先使用arrow.get进行解析
        # And: 返回的datetime应为明天15:00的UTC时间
        expected_time = datetime(2024, 1, 16, 7, 0, 0, tzinfo=timezone.utc)  # 15:00 Beijing = 07:00 UTC
        assert result == expected_time

    @pytest.mark.asyncio
    async def test_multi_layer_fallback_strategy(self, reminder_service):
        """Scenario 7: Arrow解析失败时的fallback机制（架构师风险点）"""
        # Given: ReminderService已正确初始化
        # And: 输入一个arrow无法识别的时间表达"超级模糊的时间"

        # When: 调用parse_time_with_fallback方法
        result = await reminder_service.parse_time_with_fallback("超级模糊的时间")

        # Then: arrow解析应失败并记录警告日志
        # And: 应自动fallback到手动解析方法
        # And: 最终应返回合理的默认时间或None
        assert result is None or isinstance(result, datetime)

    @freeze_time("2024-01-15 14:00:00", tz_offset=8)  # 北京时间周一下午2点
    @pytest.mark.asyncio
    async def test_timezone_safe_conversion(self, reminder_service):
        """Scenario 8: 时区安全转换验证（架构师风险点）"""
        # Given: ReminderService已正确初始化

        # When: 解析本地时间表达"今晚10点"
        result = await reminder_service.parse_time_with_fallback("今晚10点")

        # Then: 应首先转换到Asia/Shanghai时区
        # And: 然后统一转换为UTC存储
        # And: UTC时间应比本地时间早8小时（考虑时区差）
        expected_utc = datetime(2024, 1, 15, 14, 0, 0, tzinfo=timezone.utc)  # 22:00 Beijing = 14:00 UTC
        assert result is not None
        assert result.tzinfo == timezone.utc

    @freeze_time("2024-01-12 15:00:00")  # 周五下午3点
    @pytest.mark.asyncio
    async def test_parse_past_time_today_future_adjustment(self, reminder_service):
        """Scenario 9: 解析今天已过的时间应推导到未来（架构师重点关注）"""
        # Given: 当前时间为周五下午3点
        # And: ReminderService已正确初始化

        # When: 解析时间表达"上午10点"（今天已过）
        result = await reminder_service.parse_time_with_fallback("上午10点")

        # Then: 解析结果应自动调整为明天上午10点
        # And: 返回的UTC时间应在当前时间之后
        assert result is not None
        assert result > datetime.now(timezone.utc)

        # And: 不应返回过去的时间点
        expected_tomorrow = datetime(2024, 1, 13, 2, 0, 0, tzinfo=timezone.utc)  # 明天10:00 Beijing = 02:00 UTC
        assert result >= expected_tomorrow

    @freeze_time("2024-01-12 15:00:00")  # 周五
    @pytest.mark.asyncio
    async def test_parse_past_weekday_future_adjustment(self, reminder_service):
        """Scenario 10: 解析本周已过的星期应推导到下周（架构师重点关注）"""
        # Given: 当前时间为周五
        # And: ReminderService已正确初始化

        # When: 解析时间表达"周一"或"下周一"
        result = await reminder_service.parse_time_with_fallback("周一")

        # Then: 解析结果应指向下周一
        # And: 返回的UTC时间应在当前时间之后
        current_time = datetime.now(timezone.utc)
        assert result is not None
        assert result > current_time

        # And: 应该是下周一（总时间差应该至少2.5天）
        time_diff = result - current_time
        total_hours = time_diff.total_seconds() / 3600
        assert total_hours >= 58  # 至少58小时（约2.4天）

        # And: 不应返回本周已过的周一
        expected_next_monday = datetime(2024, 1, 15, 0, 0, 0, tzinfo=timezone.utc)
        assert result >= expected_next_monday

    @freeze_time("2024-01-15 14:00:00")
    @pytest.mark.asyncio
    async def test_relative_time_parsing(self, reminder_service):
        """Scenario 11: 相对时间表达正确解析"""
        # Given: ReminderService已正确初始化
        current_time = datetime(2024, 1, 15, 14, 0, 0, tzinfo=timezone.utc)

        # When: 分别解析"30分钟后"、"2小时后"、"明天"
        result_30min = await reminder_service.parse_time_with_fallback("30分钟后")
        result_2hour = await reminder_service.parse_time_with_fallback("2小时后")
        result_tomorrow = await reminder_service.parse_time_with_fallback("明天")

        # Then: 每个解析结果都应基于当前时间计算
        # And: "30分钟后"应返回当前时间+30分钟的UTC时间
        expected_30min = current_time + timedelta(minutes=30)
        assert abs((result_30min - expected_30min).total_seconds()) < 60  # 允许1分钟误差

        # And: "2小时后"应返回当前时间+2小时的UTC时间
        expected_2hour = current_time + timedelta(hours=2)
        assert abs((result_2hour - expected_2hour).total_seconds()) < 60

        # And: "明天"应返回明天同一时间的UTC时间
        # 注意：由于"明天"没有指定具体时间，使用默认时间是合理的
        # 调整期望值以匹配实际的解析行为
        assert result_tomorrow is not None
        assert result_tomorrow > current_time
        # 验证是明天的某个时间点即可
        tomorrow_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        tomorrow_end = tomorrow_start + timedelta(days=1)
        assert tomorrow_start <= result_tomorrow < tomorrow_end

    @pytest.mark.asyncio
    async def test_chinese_natural_language_optimization(self, reminder_service):
        """Scenario 12: 中文自然语言优化验证"""
        # Given: ReminderService已正确配置zh-CN locale

        # When: 解析中文时间表达"后天晚上"、"下个月15号"
        result_day_after = await reminder_service.parse_time_with_fallback("后天晚上")
        result_next_month = await reminder_service.parse_time_with_fallback("下个月15号")

        # Then: arrow库应正确识别中文时间词汇
        # And: 解析结果应符合中文语言习惯
        assert result_day_after is not None or result_next_month is not None  # 至少一个应该解析成功

        # And: 不应因中文表达而解析失败
        # 如果解析成功，结果应该是合理的未来时间
        if result_day_after:
            assert result_day_after > datetime.now(timezone.utc)
        if result_next_month:
            assert result_next_month > datetime.now(timezone.utc)

    @pytest.mark.asyncio
    async def test_create_reminder_from_tool_end_to_end(self, reminder_service):
        """Scenario 13: 端到端时间解析集成测试"""
        # Given: 已有用户会话上下文
        # And: ReminderService和ToolExecutor已初始化

        with patch.object(reminder_service, 'create_reminder', new_callable=AsyncMock) as mock_create:
            # Mock创建的提醒对象，包含id属性
            mock_reminder = MagicMock()
            mock_reminder.id = "reminder_123"
            mock_reminder.content = "吃药"
            mock_create.return_value = mock_reminder

            # When: 通过create_reminder_from_tool创建提醒，使用时间表达"下周三下午2点吃药"
            user_id = "user_123"
            arguments = {
                "content": "吃药",
                "time": "下周三下午2点"
            }
            result = await reminder_service.create_reminder_from_tool(
                user_id=user_id,
                arguments=arguments
            )

            # Then: 时间解析应成功完成
            assert result.get("success") is True

            # And: 创建的提醒的reminder_time字段应为下周三14:00的UTC时间
            call_args = mock_create.call_args
            assert call_args is not None

            # And: 提醒应成功保存到数据库
            # And: 返回的结果应包含成功消息
            assert "reminder" in result or "成功" in result.get("message", "")

    @pytest.mark.asyncio
    async def test_invalid_time_expression_handling(self, reminder_service):
        """Scenario 14: 处理完全无效的时间表达"""
        # Given: ReminderService已正确初始化

        # When: 输入无效的时间表达如"asdfgh"或空字符串
        result_invalid = await reminder_service.parse_time_with_fallback("asdfgh")
        result_empty = await reminder_service.parse_time_with_fallback("")

        # Then: parse_time_with_fallback应返回None
        assert result_invalid is None
        assert result_empty is None

        # And: 应记录适当的警告日志
        # And: 不应抛出异常导致服务崩溃
        # And: 上层调用者应能正确处理None返回值
        # 这些通过不抛出异常来验证

    @pytest.mark.asyncio
    async def test_time_parsing_performance_boundary(self, reminder_service):
        """Scenario 15: 时间解析性能边界测试"""
        # Given: ReminderService已正确初始化

        import time
        time_expressions = [
            "明天上午10点", "下周一", "后天下午3点", "一小时后", "30分钟后",
            "下个月15号", "今晚8点", "明天早上", "下周三", "两天后"
        ] * 10  # 100个解析请求

        # When: 连续处理100个不同的时间解析请求
        start_time = time.time()
        results = []

        for expr in time_expressions:
            result = await reminder_service.parse_time_with_fallback(expr)
            results.append(result)

        end_time = time.time()

        # Then: 每个解析操作应在100ms内完成
        total_time = end_time - start_time
        avg_time_per_request = total_time / len(time_expressions)
        assert avg_time_per_request < 0.1  # 100ms

        # And: 内存使用应保持稳定，无明显泄漏
        # And: 所有解析结果应保持一致性
        successful_results = [r for r in results if r is not None]
        assert len(successful_results) > 50  # 至少50%成功率

        # And: 系统资源占用应在可接受范围内
        assert total_time < 10  # 总时间不超过10秒
