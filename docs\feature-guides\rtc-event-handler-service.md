# RTC事件处理服务功能说明

## 功能概述

RTC事件处理服务是心桥项目的核心后端服务，负责接收火山引擎RTC服务推送的实时语音事件（如ASR语音转文本），并通过智能对话编排系统生成AI回复。该服务为前端的实时语音对话功能提供核心智能支持，具备深度记忆检索和工具调用能力。

### 核心能力
- **实时事件处理**: 接收并处理火山引擎RTC的ASR等事件
- **智能记忆注入**: 通过外部记忆服务（Zep/Mem0）检索用户历史记忆
- **LLM对话编排**: 自主完成LLM调用、工具执行和回复生成
- **异步消息持久化**: 将对话记录保存到数据库供后续分析
- **完整容错机制**: 记忆服务、LLM服务失败时的降级策略

## 核心API端点

### POST /api/v1/chat/rtc_event_handler

**功能**: 接收火山引擎RTC服务的事件回调，处理用户语音输入并返回AI回复

**认证**: 火山引擎签名验证（HMAC SHA256 + 时间戳验证）

**请求格式**:
```json
{
  "event_type": "asr_result",
  "payload": {
    "text": "用户语音转换的文本内容",
    "timestamp": "2024-01-15T10:30:00Z",
    "confidence": 0.95
  },
  "custom": "{\"sessionId\": \"sess_123\", \"userId\": \"user_456\", \"characterId\": \"char_789\"}"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "AI生成的回复文本，将被火山引擎用于TTS合成",
  "request_id": "req_abc123"
}
```

**错误响应**:
```json
{
  "success": false,
  "error": "错误描述",
  "error_code": "MEMORY_SERVICE_TIMEOUT",
  "request_id": "req_abc123"
}
```

## 数据契约

### RtcWebhookRequest 模型
```typescript
interface RtcWebhookRequest {
  event_type: string;           // 事件类型，如 "asr_result"
  payload: AsrPayload;          // 事件载荷数据
  custom?: string;              // JSON字符串，包含会话上下文
}

interface AsrPayload {
  text: string;                 // ASR识别的文本内容
  timestamp: string;            // 事件时间戳 (ISO 8601)
  confidence: number;           // 识别置信度 (0-1)
}
```

### Custom字段解析
```typescript
interface CustomContext {
  sessionId: string;            // 会话ID，用于记忆检索
  userId: string;               // 用户ID，用于用户画像
  characterId?: string;         // AI角色ID，用于人设配置
}
```

### 响应数据模型
```typescript
interface RtcWebhookResponse {
  success: boolean;             // 处理是否成功
  message: string;              // AI回复文本（成功时）
  request_id: string;           // 请求追踪ID
}

interface RtcWebhookErrorResponse {
  success: boolean;             // 固定为 false
  error: string;                // 错误描述信息
  error_code: string;           // 错误代码
  request_id: string;           // 请求追踪ID
}
```

## 调用示例与注意事项

### 基本调用示例

```bash
# 模拟火山引擎RTC事件回调
curl -X POST https://your-domain.com/api/v1/chat/rtc_event_handler \
  -H "Content-Type: application/json" \
  -H "X-Volcano-Signature: your_signature" \
  -H "X-Volcano-Timestamp: 1642234567" \
  -d '{
    "event_type": "asr_result",
    "payload": {
      "text": "你好，今天天气怎么样？",
      "timestamp": "2024-01-15T10:30:00Z",
      "confidence": 0.95
    },
    "custom": "{\"sessionId\": \"sess_123\", \"userId\": \"user_456\"}"
  }'
```

### 前端集成注意事项

#### 1. 安全验证配置
- **生产环境**: 必须配置正确的火山引擎签名密钥
- **开发环境**: 可通过环境变量跳过签名验证（仅限开发）
- **IP白名单**: 建议配置火山引擎服务器IP白名单

#### 2. 错误处理策略
```typescript
// 推荐的错误处理模式
const handleRtcError = (errorResponse: RtcWebhookErrorResponse) => {
  switch (errorResponse.error_code) {
    case 'MEMORY_SERVICE_TIMEOUT':
      // 记忆服务超时，AI仍会回复但可能缺少个性化
      console.warn('记忆服务超时，使用降级回复');
      break;
    case 'LLM_SERVICE_UNAVAILABLE':
      // LLM服务不可用，显示友好错误信息
      showErrorMessage('AI服务暂时不可用，请稍后重试');
      break;
    case 'INVALID_REQUEST_FORMAT':
      // 请求格式错误，检查数据结构
      console.error('请求格式错误:', errorResponse.error);
      break;
    default:
      // 其他未知错误
      showErrorMessage('服务异常，请稍后重试');
  }
};
```

#### 3. 性能优化建议
- **延迟监控**: 监控从发送请求到收到回复的端到端延迟
- **超时设置**: 建议设置5秒的请求超时时间
- **重试机制**: 对于网络错误可实施指数退避重试
- **并发控制**: 避免同一用户的并发请求，确保对话顺序

#### 4. 会话管理
```typescript
// 会话上下文管理示例
interface SessionContext {
  sessionId: string;      // 全局唯一会话ID
  userId: string;         // 用户ID
  characterId: string;    // 当前AI角色ID
  startTime: Date;        // 会话开始时间
}

// 构建custom字段
const buildCustomContext = (context: SessionContext): string => {
  return JSON.stringify({
    sessionId: context.sessionId,
    userId: context.userId,
    characterId: context.characterId
  });
};
```

#### 5. 监控与日志
- **请求追踪**: 使用返回的`request_id`进行问题追踪
- **性能指标**: 监控记忆检索时间、LLM响应时间
- **错误统计**: 统计各类错误的发生频率，便于问题定位

#### 6. 降级策略理解
服务具备完整的降级机制：
- **记忆服务失败**: AI仍会回复，但可能缺少个性化记忆
- **工具调用失败**: AI会基于失败信息生成合适回复
- **LLM服务异常**: 返回友好的错误提示

#### 7. 开发环境配置
```bash
# 必需的环境变量
VOLCENGINE_API_KEY=your_volcano_api_key
VOLCENGINE_WEBHOOK_SECRET=your_webhook_secret
MEMORY_PROVIDER=zep  # 或 mem0
ZEP_API_KEY=your_zep_api_key
# 其他配置参见 apps/agent-api/.env 文件
```

### 测试建议
- **单元测试**: 测试各种事件类型和错误场景
- **集成测试**: 使用火山引擎官方验证工具验证兼容性
- **压力测试**: 验证并发处理能力（目标: 100+ QPS）
- **延迟测试**: 确保P95延迟 < 1.2秒 