# api/dependencies/auth.py
"""
认证依赖 - 用于验证JWT Token和获取当前用户信息
"""
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from typing import Dict, Any, Optional
import os

from api.settings import logger

# JWT配置 - 使用settings而不是直接os.getenv
from api.settings import get_settings
_settings = get_settings()
JWT_SECRET_KEY = _settings.JWT_SECRET_KEY
JWT_ALGORITHM = _settings.JWT_ALGORITHM

# HTTP Bearer认证方案，auto_error=False让我们手动处理错误
security = HTTPBearer(auto_error=False)

async def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Dict[str, Any]:
    """
    从JWT Token中获取当前用户信息
    """
    try:
        # 检查是否提供了认证凭据
        if credentials is None:
            logger.warning("认证失败 - 缺少认证凭据")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token = credentials.credentials
        logger.debug(f"收到JWT Token - 长度: {len(token)}, 前10字符: {token[:10]}...")

        # 解码JWT Token
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        logger.debug(f"JWT Token解码成功 - Payload: {payload}")

        # 验证必要字段
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.error("JWT Token验证失败 - 缺少sub字段")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.debug(f"JWT认证成功 - UserID: {user_id}")
        return payload

    except JWTError as e:
        logger.warning(f"JWT validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.exception(f"Error in authentication: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """
    可选的用户认证，如果Token无效则返回None
    """
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None


# 为了兼容测试，提供别名
get_current_active_user = get_current_user
