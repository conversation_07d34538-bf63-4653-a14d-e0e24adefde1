"""
RTC关键修复集成测试 - 故事1.12-B
QA补充测试：验证所有修复项的端到端集成
"""
import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from api.services.chat_orchestration_service import ChatOrchestrationService
from api.services.auth_service import AuthService
from api.services.volcano_client_service import VolcanoClientService
from api.utils.volcengine_auth import VolcengineSignatureValidator


class TestEndToEndIntegration:
    """端到端集成测试"""

    @pytest.mark.asyncio
    async def test_complete_crisis_intervention_flow(self):
        """测试完整的危机干预流程（AC5集成验证）"""
        # 模拟完整的危机干预流程：检测 + 文本回复 + 音频禁用

        # 创建真实的服务实例和mock依赖
        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()
        mock_crisis_detection_service = Mock()
        mock_volcano_client = Mock()

        # 设置危机检测为True
        mock_crisis_detection_service.detect.return_value = True
        mock_volcano_client.ban_user_stream = AsyncMock(return_value=True)

        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service,
            crisis_detection_service=mock_crisis_detection_service,
            volcano_client=mock_volcano_client
        )

        # 执行危机场景
        result = await chat_service.handle_message(
            user_message="我想自杀",
            context={
                "userId": "test-user-123",
                "sessionId": "test-session-456",
                "roomId": "test-room-789",
                "requestId": "test-request-001"
            }
        )

        # 验证危机回复包含热线电话
        assert "400-161-9995" in result, "危机回复应包含心理援助热线"

        # 等待异步任务完成
        await asyncio.sleep(0.1)

        # 验证音频禁用被调用
        mock_volcano_client.ban_user_stream.assert_called_once_with(
            room_id="test-room-789",
            user_id="test-user-123",
            ban_audio=True,
            ban_video=False,
            duration_seconds=600
        )

    def test_signature_token_generation_compatibility(self):
        """测试签名验证与token生成的兼容性（AC1+AC2集成）"""
        # 验证签名验证器和token生成器能够正常工作且不冲突

        # 创建签名验证器
        validator = VolcengineSignatureValidator("test-secret")

        # 创建认证服务
        auth_service = AuthService()

        # 生成RTC token
        token = auth_service.generate_rtc_token("test-room", "test-user")

        # 验证token格式正确
        assert token is not None
        assert len(token) > 0
        assert isinstance(token, str)
        assert token.startswith("001"), "Token应以版本号001开头"

        # 验证签名验证器工作正常
        assert validator.webhook_secret == "test-secret"

    def test_function_calling_with_crisis_detection(self):
        """测试Function Calling与危机检测的协同工作（AC4+AC5集成）"""
        # 验证在Function Calling场景下危机检测依然优先工作

        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()
        mock_crisis_detection_service = Mock()
        mock_volcano_client = Mock()

        # 设置危机检测为True（优先级最高）
        mock_crisis_detection_service.detect.return_value = True
        mock_volcano_client.ban_user_stream = AsyncMock(return_value=True)

        # 设置工具定义
        mock_tool_executor_service.get_tool_definitions.return_value = [
            {
                "type": "function",
                "function": {
                    "name": "set_reminder",
                    "description": "设置提醒",
                    "parameters": {"type": "object", "properties": {}}
                }
            }
        ]

        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service,
            crisis_detection_service=mock_crisis_detection_service,
            volcano_client=mock_volcano_client
        )

        # 即使消息看起来像工具调用请求，危机检测也应该优先
        async def test_execution():
            result = await chat_service.handle_message(
                user_message="帮我设置提醒，明天我要自杀",
                context={
                    "userId": "test-user",
                    "sessionId": "test-session",
                    "roomId": "test-room",
                    "requestId": "test-request"
                }
            )

            # 验证返回危机回复而不是工具调用
            assert "400-161-9995" in result

            # 验证LLM没有被调用（危机检测中断了流程）
            mock_llm_proxy_service.call_llm.assert_not_called()

            # 验证工具执行器没有被调用
            mock_tool_executor_service.execute_tool_calls.assert_not_called()

        # 运行异步测试
        asyncio.run(test_execution())

    def test_agent_config_callback_integration(self):
        """测试AgentConfig回调配置的完整性（AC3集成验证）"""
        # 验证StartVoiceChat的AgentConfig包含所有必需的回调字段

        volcano_client = VolcanoClientService()

        config = volcano_client._build_voice_chat_config(
            room_id="test-room-123",
            task_id="test-task-456",
            user_id="test-user-789",
            webhook_url="https://api.example.com/webhook",
            custom_data={"test": "data"}
        )

        agent_config = config.get("AgentConfig", {})

        # 验证所有必需的回调字段
        required_fields = [
            "EnableConversationStateCallback",
            "ServerMessageURLForRTS",
            "ServerMessageSignatureForRTS"
        ]

        for field in required_fields:
            assert field in agent_config, f"AgentConfig缺少必需字段: {field}"
            assert agent_config[field] is not None, f"字段{field}不能为空"

        # 验证字段值的正确性
        assert agent_config["EnableConversationStateCallback"] == True
        assert agent_config["ServerMessageURLForRTS"] == "https://api.example.com/webhook"
        assert isinstance(agent_config["ServerMessageSignatureForRTS"], str)
        assert len(agent_config["ServerMessageSignatureForRTS"]) > 0

    @pytest.mark.asyncio
    async def test_resilience_under_multiple_failures(self):
        """测试在多个组件故障情况下的系统韧性"""
        # 模拟多种故障场景，验证系统的降级机制

        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()
        mock_crisis_detection_service = Mock()
        mock_volcano_client = Mock()

        # 设置非危机场景
        mock_crisis_detection_service.detect.return_value = False

        # 设置记忆服务失败
        mock_memory_service.search_memories = AsyncMock(side_effect=Exception("Memory service failed"))

        # 设置LLM服务降级
        mock_llm_proxy_service.call_llm = AsyncMock(return_value="基础回复")

        # 设置工具服务失败
        mock_tool_executor_service.get_tool_definitions.side_effect = Exception("Tool service failed")

        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service,
            crisis_detection_service=mock_crisis_detection_service,
            volcano_client=mock_volcano_client
        )

        # 即使多个服务失败，系统也应该能返回基础回复
        result = await chat_service.handle_message(
            user_message="你好",
            context={
                "userId": "test-user",
                "sessionId": "test-session",
                "requestId": "test-request"
            }
        )

        # 验证系统没有崩溃，能返回基础回复
        assert result is not None
        assert len(result) > 0
        assert "抱歉" in result or "基础回复" in result


class TestPerformanceAndSecurity:
    """性能和安全测试"""

    def test_signature_verification_performance(self):
        """测试签名验证的性能"""
        import time

        validator = VolcengineSignatureValidator("test-secret")

        # 构造测试数据
        from fastapi import Request
        mock_request = Mock()
        mock_request.headers = {"Signature": "test-signature"}

        test_data = {
            "EventType": "ASRSentenceEnd",
            "EventData": json.dumps({"text": "test" * 100}),  # 较大的数据
            "EventTime": "1234567890",
            "EventId": "test-event-123",
            "Version": "1.0",
            "AppId": "test-app-123",
            "Nonce": "test-nonce-456"
        }

        body = json.dumps(test_data).encode()

        # 测试性能 - 签名验证应该在100ms内完成
        start_time = time.time()
        try:
            validator.verify_signature(mock_request, body)
        except:
            pass  # 预期会失败，我们只关心性能
        end_time = time.time()

        duration = (end_time - start_time) * 1000  # 转换为毫秒
        assert duration < 100, f"签名验证耗时过长: {duration:.2f}ms"

    def test_token_generation_security(self):
        """测试token生成的安全性"""
        auth_service = AuthService()

        # 生成多个token，验证唯一性
        tokens = []
        for i in range(10):
            token = auth_service.generate_rtc_token(f"room-{i}", f"user-{i}")
            tokens.append(token)

        # 验证所有token都不相同
        assert len(set(tokens)) == len(tokens), "生成的token应该都是唯一的"

        # 验证token长度合理（不能太短）
        for token in tokens:
            assert len(token) > 50, f"Token长度过短，可能存在安全风险: {len(token)}"
