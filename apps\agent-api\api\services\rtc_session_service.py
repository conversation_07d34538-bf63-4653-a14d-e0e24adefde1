"""
RTC会话管理服务
负责RTC会话的创建、状态管理、数据库操作和并发控制
"""
import uuid
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, update, delete
from fastapi import HTTPException, status
from api.settings import settings, logger
from api.models.rtc_models import (
    PrepareSessionRequest, PrepareSessionResponse,
    SessionStatusResponse, SessionConfigResponse,
    InternalPrepareSessionRequest  # 添加内部模型
)
from api.services.volcano_client_service import get_volcano_client_service, VolcanoAPIError
from db.session import AsyncSessionLocal
from api.services.auth_service import AuthService


class RTCSessionService:
    """RTC会话管理服务"""

    def __init__(self):
        self.volcano_client = get_volcano_client_service()
        self.auth_service = AuthService()  # 修复: 实例化 AuthService
        self.max_concurrent_sessions_per_user = 3  # 每用户最大并发会话数
        self.session_timeout_minutes = 10  # 会话超时时间（分钟）

    async def prepare_session(
        self,
        request: InternalPrepareSessionRequest  # 使用内部模型
    ) -> PrepareSessionResponse:
        """
        准备RTC会话

        实现核心的会话准备逻辑，包括：
        1. 验证用户和角色
        2. 检查并发限制
        3. 调用火山引擎API
        4. 保存会话状态
        """
        logger.info(f"准备RTC会话 - UserId: {request.userId}, SessionId: {request.sessionId}")

        async with AsyncSessionLocal() as db:
            # 1. 验证用户和角色
            await self._validate_user_and_character(db, request.userId, request.characterId)

            # 2. 检查并发会话限制
            await self._check_concurrent_session_limit(db, request.userId)

            # 3. 生成任务ID和房间ID
            task_id = f"task_{uuid.uuid4().hex[:12]}"
            room_id = f"room_{request.sessionId}"

            # 4. 获取角色配置
            character_config = await self._get_character_config(db, request.characterId)

            # 5. 构建Webhook回调URL
            webhook_url = f"{settings.API_BASE_URL}/api/v1/chat/rtc_event_handler"

            # 6. 准备自定义数据
            custom_data = {
                "sessionId": request.sessionId,
                "userId": request.userId,
                "characterId": request.characterId
            }

            try:
                # 7. 调用火山引擎API启动会话
                volcano_response = await self.volcano_client.start_voice_chat(
                    room_id=room_id,
                    task_id=task_id,
                    user_id=request.userId,
                    webhook_url=webhook_url,
                    custom_data=custom_data,
                    character_config=character_config
                )

                # 修复: 独立生成客户端所需的RTC Token
                client_token = self.auth_service.generate_rtc_token(
                    room_id=room_id,
                    user_id=request.userId
                )

                # 8. 保存会话状态到数据库
                await self._save_session_state(
                    db=db,
                    session_id=request.sessionId,
                    user_id=request.userId,
                    character_id=request.characterId,
                    task_id=task_id,
                    room_id=room_id,
                    volcano_response=volcano_response # 仍然可以保存火山的响应以供调试
                )

                # 9. 返回响应，使用我们自己生成的 token
                response = PrepareSessionResponse(
                    token=client_token, # 使用自己生成的token
                    roomId=room_id,
                    userId=request.userId,
                    taskId=task_id
                )

                logger.info(f"RTC会话准备成功 - SessionId: {request.sessionId}, TaskId: {task_id}")
                return response

            except VolcanoAPIError as e:
                logger.error(f"火山引擎API调用失败 - SessionId: {request.sessionId}, Error: {e}")
                # 根据架构师建议，提供详细的错误分类
                if e.error_code == "VOLCANO_AUTH_ERROR":
                    raise HTTPException(
                        status_code=502,
                        detail="火山引擎认证失败，请联系系统管理员"
                    )
                elif e.error_code == "VOLCANO_SERVICE_UNAVAILABLE":
                    raise HTTPException(
                        status_code=503,
                        detail="火山引擎服务暂时不可用，请稍后重试"
                    )
                else:
                    raise HTTPException(
                        status_code=502,
                        detail=f"火山引擎服务错误: {e.error_message}"
                    )

            except Exception as e:
                logger.error(f"会话准备失败 - SessionId: {request.sessionId}, Error: {e}", exc_info=True)
                # 提供更详细的错误信息以便调试
                error_detail = f"会话准备失败: {str(e)}"
                if "getaddrinfo failed" in str(e):
                    error_detail = "网络连接失败，请检查网络配置"
                elif "timeout" in str(e).lower():
                    error_detail = "请求超时，请稍后重试"
                elif "connection" in str(e).lower():
                    error_detail = "连接失败，请检查服务配置"
                elif "VOLCANO_API_" in str(e):
                    # 火山引擎API错误，保留详细错误信息
                    error_detail = str(e).replace("VOLCANO_API_", "火山引擎API错误: ")
                elif "invalid UserID" in str(e):
                    error_detail = "用户ID格式不正确，请使用字母、数字、@、.、_、-组成的字符串"
                elif "InvalidParameter" in str(e):
                    error_detail = f"参数错误: {str(e)}"
                elif "SignatureDoesNotMatch" in str(e):
                    error_detail = "火山引擎认证失败，请检查访问密钥配置"

                raise HTTPException(
                    status_code=500,
                    detail=error_detail
                )

    async def send_session_command(
        self,
        session_id: str,
        user_id: str,
        command: str,
        message: Optional[str] = None,
        interrupt_mode: Optional[int] = None
    ) -> bool:
        """
        向会话发送一个控制命令
        """
        logger.info(f"向会话发送命令 - SessionId: {session_id}, Command: {command}")
        async with AsyncSessionLocal() as db:
            session_info = await self._get_session_info(db, session_id, user_id)
            if not session_info:
                raise HTTPException(status_code=404, detail="会话不存在或无权访问")

            if session_info.get("status") != "active":
                logger.warning(f"无法向非活动会话发送命令 - SessionId: {session_id}, Status: {session_info.get('status')}")
                return False

            task_id = session_info.get("task_id")
            room_id = session_info.get("room_id")

            if not task_id or not room_id:
                logger.error(f"会话信息不完整，缺少task_id或room_id - SessionId: {session_id}")
                return False

            return await self.volcano_client.update_voice_chat(
                room_id=room_id,
                task_id=task_id,
                command=command,
                message=message,
                interrupt_mode=interrupt_mode
            ).get('success', False)  # 适配新的返回格式

    async def end_session(self, session_id: str, user_id: str, task_id: str) -> bool:
        """
        结束RTC会话
        """
        logger.info(f"结束RTC会话 - SessionId: {session_id}, TaskId: {task_id}")

        async with AsyncSessionLocal() as db:
            try:
                # 1. 验证会话所有权
                session_info = await self._get_session_info(db, session_id, user_id)
                if not session_info:
                    raise HTTPException(
                        status_code=404,
                        detail="会话不存在或无权访问"
                    )

                # 2. 调用火山引擎API停止会话
                success = await self.volcano_client.stop_voice_chat(task_id)

                # 3. 更新数据库状态
                await self._update_session_status(
                    db=db,
                    session_id=session_id,
                    status="ended",
                    ended_at=datetime.now(timezone.utc)
                )

                logger.info(f"RTC会话结束成功 - SessionId: {session_id}")
                return success

            except Exception as e:
                logger.error(f"结束会话失败 - SessionId: {session_id}, Error: {e}")
                return False

    async def get_session_status(self, session_id: str, user_id: str) -> SessionStatusResponse:
        """
        获取会话状态
        """
        try:
            async with AsyncSessionLocal() as db:
                session_info = await self._get_session_info(db, session_id, user_id)
                if not session_info:
                    raise HTTPException(
                        status_code=404,
                        detail="会话不存在或无权访问"
                    )

                return SessionStatusResponse(
                    sessionId=session_id,
                    status=session_info["status"],
                    startTime=session_info.get("started_at"),
                    endTime=session_info.get("ended_at"),
                    taskId=session_info.get("task_id")
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取会话状态时发生数据库错误 - SessionId: {session_id}, Error: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="数据库访问失败，请稍后重试"
            )

    async def get_session_config(self, session_id: str, user_id: str) -> SessionConfigResponse:
        """
        获取会话配置
        """
        try:
            async with AsyncSessionLocal() as db:
                session_info = await self._get_session_info(db, session_id, user_id)
                if not session_info:
                    raise HTTPException(
                        status_code=404,
                        detail="会话不存在或无权访问"
                    )

                # 获取配置信息（脱敏处理）
                config = {
                    "llmConfig": {
                        "model": "doubao-pro-4k",
                        "maxTokens": 2000,
                        "temperature": 0.7
                    },
                    "voiceConfig": session_info.get("voice_config", {}),
                    "characterConfig": {
                        "characterId": session_info.get("character_id"),
                        "name": "AI助手"  # 从角色表获取
                    }
                }

                return SessionConfigResponse(**config)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取会话配置时发生数据库错误 - SessionId: {session_id}, Error: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="数据库访问失败，请稍后重试"
            )

    async def _validate_user_and_character(self, db: AsyncSession, user_id: str, character_id: str):
        """验证用户和角色是否存在"""
        try:
            # 验证用户存在
            user_query = text("SELECT id FROM users WHERE id = :user_id")
            user_result = await db.execute(user_query, {"user_id": user_id})
            user_exists = user_result.fetchone()

            if not user_exists:
                logger.warning(f"用户验证失败 - 用户不存在: {user_id}")
                # 额外调试：检查用户表中有哪些用户
                all_users_query = text("SELECT id FROM users LIMIT 5")
                all_users_result = await db.execute(all_users_query)
                all_users = all_users_result.fetchall()
                logger.debug(f"数据库中的前5个用户ID: {[row[0] for row in all_users]}")

                raise HTTPException(
                    status_code=404,
                    detail=f"用户不存在: {user_id}"
                )

            logger.info(f"用户验证通过 - UserId: {user_id}")

            # 验证角色存在
            character_query = text("SELECT id, name FROM characters WHERE id = :character_id")
            character_result = await db.execute(character_query, {"character_id": character_id})
            character_row = character_result.fetchone()

            if not character_row:
                logger.warning(f"角色验证失败 - 角色不存在: {character_id}")
                # 额外调试：显示可用的角色
                all_chars_query = text("SELECT id, name FROM characters LIMIT 5")
                all_chars_result = await db.execute(all_chars_query)
                all_chars = all_chars_result.fetchall()
                available_chars = [(row[0], row[1]) for row in all_chars]
                logger.debug(f"数据库中的可用角色: {available_chars}")

                raise HTTPException(
                    status_code=404,
                    detail=f"角色不存在: {character_id}。可用角色: {[f'{char[0]}({char[1]})' for char in available_chars[:3]]}"
                )

            logger.info(f"角色验证通过 - CharacterId: {character_id}, Name: {character_row[1]}")

        except HTTPException:
            raise
        except Exception as e:
            logger.warning(f"数据库连接失败，尝试使用Supabase REST API - UserId: {user_id}, CharacterId: {character_id}, Error: {e}")
            # 使用Supabase REST API作为备用方案
            await self._validate_via_supabase_api(user_id, character_id)

    async def _validate_via_supabase_api(self, user_id: str, character_id: str):
        """使用Supabase REST API验证用户和角色"""
        try:
            import httpx
            from api.settings import get_settings

            settings = get_settings()
            headers = {
                "apikey": settings.SUPABASE_SERVICE_ROLE_KEY,
                "Authorization": f"Bearer {settings.SUPABASE_SERVICE_ROLE_KEY}",
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient() as client:
                # 验证用户存在
                user_response = await client.get(
                    f"{settings.SUPABASE_URL}/rest/v1/users?id=eq.{user_id}&select=id",
                    headers=headers
                )
                if user_response.status_code != 200 or not user_response.json():
                    logger.warning(f"用户不存在: {user_id}")
                    raise HTTPException(status_code=404, detail=f"用户不存在: {user_id}")

                # 验证角色存在
                character_response = await client.get(
                    f"{settings.SUPABASE_URL}/rest/v1/characters?id=eq.{character_id}&select=id",
                    headers=headers
                )
                if character_response.status_code != 200 or not character_response.json():
                    logger.warning(f"角色不存在: {character_id}")
                    raise HTTPException(status_code=404, detail=f"角色不存在: {character_id}")

                logger.info(f"Supabase API验证通过 - UserId: {user_id}, CharacterId: {character_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.exception(f"Supabase API验证错误: {e}")
            raise HTTPException(status_code=500, detail="数据库验证错误")

    async def _check_concurrent_session_limit(self, db: AsyncSession, user_id: str):
        """检查用户并发会话限制"""
        try:
            active_sessions_query = text("""
                SELECT COUNT(*) as count
                FROM rtc_sessions
                WHERE user_id = :user_id
                AND status IN ('preparing', 'active', 'in_progress')
            """)

            result = await db.execute(active_sessions_query, {"user_id": user_id})
            active_count = result.fetchone().count

            if active_count >= self.max_concurrent_sessions_per_user:
                raise HTTPException(
                    status_code=429,
                    detail=f"已达到最大并发会话数限制({self.max_concurrent_sessions_per_user})，请结束现有会话后重试"
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"检查并发会话限制时发生数据库错误 - UserId: {user_id}, Error: {e}", exc_info=True)
            # 对于并发检查失败，我们允许继续，但记录警告
            logger.warning(f"无法检查并发会话限制，将跳过检查 - UserId: {user_id}")

    async def _get_character_config(self, db: AsyncSession, character_id: str) -> Dict[str, Any]:
        """获取角色配置"""
        try:
            character_query = text("""
                SELECT name, description, personality, asr_config
                FROM characters
                WHERE id = :character_id
            """)

            result = await db.execute(character_query, {"character_id": character_id})
            character = result.fetchone()

            if not character:
                return {}

            return {
                "character_id": character_id,  # 添加角色ID
                "name": character.name,
                "personality": json.loads(character.personality) if character.personality else {
                    "description": character.description,
                    "greeting": f"你好，我是{character.name}，{character.description}"
                },
                "asr_config": json.loads(character.asr_config) if character.asr_config else {}
            }
        except Exception as e:
            logger.error(f"获取角色配置时发生数据库错误 - CharacterId: {character_id}, Error: {e}", exc_info=True)
            # 返回默认配置而不是抛出异常
            return {
                "name": "AI助手",
                "personality": {
                    "description": "智能AI助手",
                    "greeting": "你好，我是AI助手，很高兴为您服务"
                },
                "voice": {}
            }

    async def _save_session_state(
        self,
        db: AsyncSession,
        session_id: str,
        user_id: str,
        character_id: str,
        task_id: str,
        room_id: str,
        volcano_response: Dict[str, Any]
    ):
        """保存会话状态到数据库"""

        # 🎯 直接插入 - 表已通过迁移预先创建，无需运行时检查
        insert_query = text("""
            INSERT INTO rtc_sessions
            (session_id, user_id, character_id, task_id, room_id, status,
             voice_config, created_at, volcano_response)
            VALUES
            (:session_id, :user_id, :character_id, :task_id, :room_id, :status,
             :voice_config, :created_at, :volcano_response)
        """)

        try:
            await db.execute(insert_query, {
                "session_id": session_id,
                "user_id": user_id,
                "character_id": character_id,
                "task_id": task_id,
                "room_id": room_id,
                "status": "preparing",
                "voice_config": json.dumps({}),
                "created_at": datetime.now(timezone.utc),
                "volcano_response": json.dumps(volcano_response)
            })

            await db.commit()
            logger.info(f"会话状态保存成功 - SessionID: {session_id}, TaskID: {task_id}")

        except Exception as e:
            await db.rollback()
            # 如果表不存在，抛出明确错误而不是尝试创建
            if "relation \"rtc_sessions\" does not exist" in str(e):
                raise RuntimeError(
                    "rtc_sessions表不存在，请检查数据库迁移。"
                    "这通常表示数据库Schema不同步，请联系系统管理员。"
                )
            logger.error(f"保存会话状态失败 - SessionID: {session_id}: {str(e)}")
            raise

    async def _get_session_info(
        self,
        db: AsyncSession,
        session_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        try:
            query = text("""
                SELECT session_id, user_id, character_id, task_id, room_id,
                       status, created_at, started_at, ended_at, voice_config
                FROM rtc_sessions
                WHERE session_id = :session_id AND user_id = :user_id
            """)

            result = await db.execute(query, {
                "session_id": session_id,
                "user_id": user_id
            })

            row = result.fetchone()
            if not row:
                return None

            return {
                "session_id": row.session_id,
                "user_id": row.user_id,
                "character_id": row.character_id,
                "task_id": row.task_id,
                "room_id": row.room_id,
                "status": row.status,
                "created_at": row.created_at,
                "started_at": row.started_at,
                "ended_at": row.ended_at,
                "voice_config": json.loads(row.voice_config) if row.voice_config else {}
            }
        except Exception as e:
            logger.error(f"获取会话信息时发生数据库错误 - SessionId: {session_id}, UserId: {user_id}, Error: {e}", exc_info=True)
            return None

    async def _update_session_status(
        self,
        db: AsyncSession,
        session_id: str,
        status: str,
        error_message: Optional[str] = None
    ):
        """更新会话状态"""

        set_clause = "status = :status"
        params = {"session_id": session_id, "status": status}

        if error_message:
            set_clause += ", error_message = :error_message"
            params["error_message"] = error_message

        query = text(f"""
            UPDATE rtc_sessions
            SET {set_clause}
            WHERE session_id = :session_id
        """)

        await db.execute(query, params)
        await db.commit()


# 依赖注入
from fastapi import HTTPException

_rtc_session_service = None

def get_rtc_session_service() -> RTCSessionService:
    """获取RTC会话服务实例"""
    global _rtc_session_service
    if _rtc_session_service is None:
        _rtc_session_service = RTCSessionService()
    return _rtc_session_service
