# Agent API 技术栈文档

## 项目概述

Agent API 是心桥项目的后端服务，基于现代 Python 技术栈构建的生产级 AI Agent 服务。该服务提供了一套完整的 RESTful API 接口，支持多种 AI Agent 的创建、管理和对话功能。

## 核心技术栈

### 1. Python 运行时
- **Python**: >= 3.11

### 2. Web 框架与服务器
- **FastAPI**: 0.115.12
  - 高性能异步 Web 框架
  - 自动生成 OpenAPI 文档
  - 类型安全和数据验证
- **Uvicorn**: 0.32.1
  - ASGI 服务器
  - 支持异步和 WebSocket
- **Starlette**: 0.46.2
  - FastAPI 底层框架

### 3. AI 框架 (备用)
- **Agno**: 1.7.0
  - **角色定位**: 一个备用的AI框架依赖，**不参与**项目任何核心业务逻辑（如LLM调用、工具调用、记忆管理等）。
  - **使用场景**: 仅为未来可能的技术探索或非生产环境下的实验保留，当前生产路径已完全与其解耦。

### 4. 数据验证与配置
- **Pydantic**: 2.10.3
  - 数据验证和序列化
  - 类型注解支持
- **Pydantic Core**: 2.33.2
  - Pydantic 核心引擎
- **Pydantic Settings**: 2.7.0
  - 基于环境变量的配置管理

### 5. 数据库技术
- **Database**: PostgreSQL 16
- **SQLAlchemy**: 2.0.36
  - ORM 和数据库抽象层
- **Alembic**: 1.14.0
  - 数据库迁移工具，用于版本化管理数据库结构变更
- **pgvector**: 0.4.1 
  - **注**: 不再用于核心对话记忆，但可能用于项目中其他非核心的向量搜索场景。

### 6. 认证与安全
- **python-jose[cryptography]**: 3.3.0
  - 用于处理 JWT Token
- **passlib[bcrypt]**: 1.7.4
  - 用于密码哈希和验证

### 7. AI/ML 相关组件
- **火山方舟大模型服务 (Doubao series)**
  - 项目的核心LLM，例如使用`ep-xxxxxxxxxxxx-xxxxx`等推理接入点。
- **openai**: 1.54.4
  - 作为客户端库，用于调用火山方舟提供的OpenAI兼容API。
- **DuckDuckGo Search**: 8.0.1
  - Web 搜索能力
- **YFinance**: 0.2.59
  - 金融数据获取
- **NumPy**: 2.2.5
  - 数值计算
- **Pandas**: 2.2.3
  - 数据分析和处理

### 7.1 专业记忆服务
- **zep-cloud**: `~=1.4.0`
  - Zep AI Cloud官方Python客户端库
- **mem0ai**: `~=0.1.10`
  - Mem0 AI 官方Python客户端库

### 8. HTTP 和网络组件
- **httpx**: 0.28.1
  - 异步 HTTP 客户端
- **requests**: 2.32.3
  - 同步 HTTP 客户端
- **WebSockets**: 15.0.1
  - 实时通信支持
- **python-multipart**: 0.0.12
  - 文件上传支持

### 9. 开发和调试工具
- **Ruff**: 0.8.4
- **MyPy**: 1.13.0
- **Rich**: 14.0.0
- **Typer**: 0.15.3
  - CLI 应用框架

## 环境变量配置

### 必需环境变量
```bash
# 火山引擎LLM配置
VOLCANO_LLM_APP_KEY=your_volcano_llm_app_key_here
VOLCANO_LLM_ENDPOINT_ID=your_volcano_llm_endpoint_id_here
# 数据库连接字符串
DATABASE_URL=postgresql+psycopg://ai:ai@pgvector:5432/ai
# JWT 签名密钥
SECRET_KEY=your_super_secret_key_for_jwt
```

### 可选环境变量
```bash
WAIT_FOR_DB=True
PRINT_ENV_ON_LOAD=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:19006"]
```

## API 接口规范

- **Base URL**: `/api/v1`
- **Documentation**: `/docs` (Swagger UI)
- **Alternative Docs**: `/redoc` (ReDoc)

### 主要端点
- `GET /api/v1/health` - 健康检查
- `POST /api/v1/chat/stream` - **(新)** 核心对话接口，支持流式响应和原生工具调用。
- `GET /api/v1/sessions` - 获取历史会话列表。
- `POST /api/v1/sessions` - 创建新的对话会话。

## 性能特性

### 1. 异步支持
- 全异步 API 设计
- 非阻塞数据库操作
- 流式响应支持

### 2. 向量搜索
- **pgvector** 扩展，主要用于其他非核心的向量化场景。
- **Zep/Mem0**: 核心对话记忆的向量搜索由外部专业服务提供。

### 3. 内存管理
- **双轨制记忆系统**: 通过`IMemoryService`抽象层，可插拔地使用`Zep AI`或`Mem0 AI`作为专业的外部记忆服务。
- **用户个性化记忆**: 由Zep/Mem0提供支持。
- **对话历史管理**: 由Zep/Mem0提供支持。

## 安全特性

### 1. CORS 配置
- 支持跨域请求
- 可配置的源列表

### 2. 类型安全
- Pydantic 数据验证
- MyPy 静态类型检查

### 3. 环境隔离
- 非 root 用户运行

## 版本信息

- **项目版本**: 0.1.0
- **Python 要求**: >= 3.11
- **构建系统**: setuptools

---

*本文档基于 agent-api v0.1.0 生成，描述了项目所使用的核心技术和版本。* 