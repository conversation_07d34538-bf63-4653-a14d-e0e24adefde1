"""
P0安全漏洞修复测试 - API端点用户ID暴露问题

测试故事1.11-B的AC1：所有会话相关API端点移除userId查询参数，
用户身份完全从JWT Token中安全获取，通过安全审查，
无法通过URL构造访问其他用户数据。
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from jose import jwt
import time
from datetime import datetime, timedelta

from api.main import app
from api.settings import settings


class TestP0SecurityFix:
    """P0安全漏洞修复测试类"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def valid_jwt_token(self):
        """创建有效的JWT Token"""
        payload = {
            "sub": "user123",
            "iat": int(time.time()),
            "exp": int(time.time()) + 3600,  # 1小时后过期
            "user_id": "user123",
            "email": "<EMAIL>"
        }
        # 使用与auth.py相同的密钥
        return jwt.encode(payload, "your-secret-key-at-least-32-characters", algorithm="HS256")

    @pytest.fixture
    def another_user_jwt_token(self):
        """创建另一个用户的JWT Token"""
        payload = {
            "sub": "user456",
            "iat": int(time.time()),
            "exp": int(time.time()) + 3600,
            "user_id": "user456",
            "email": "<EMAIL>"
        }
        # 使用与auth.py相同的密钥
        return jwt.encode(payload, "your-secret-key-at-least-32-characters", algorithm="HS256")

    @pytest.fixture
    def invalid_jwt_token(self):
        """创建无效的JWT Token"""
        return "invalid.jwt.token"

    @pytest.fixture
    def expired_jwt_token(self):
        """创建过期的JWT Token"""
        payload = {
            "sub": "user123",
            "iat": int(time.time()) - 7200,  # 2小时前签发
            "exp": int(time.time()) - 3600,  # 1小时前过期
            "user_id": "user123",
            "email": "<EMAIL>"
        }
        # 使用与auth.py相同的密钥
        return jwt.encode(payload, "your-secret-key-at-least-32-characters", algorithm="HS256")

    # AC1 Scenario 1.1: 正常JWT认证访问会话列表
    def test_get_sessions_with_valid_jwt_no_userid_param(self, client, valid_jwt_token):
        """
        测试场景1.1：正常JWT认证访问会话列表
        Given 用户已登录并持有有效的JWT Token
        When 用户请求GET /api/v1/chat/sessions（不带userId参数）
        Then 系统应从JWT Token的sub字段提取用户ID
        And 返回200状态码和该用户的会话列表
        And 响应中不包含其他用户的会话数据
        """
        headers = {"Authorization": f"Bearer {valid_jwt_token}"}

        # 这个测试现在会失败，因为当前实现仍然需要userId参数
        with patch('api.services.session_service.SessionService.get_user_sessions') as mock_get_sessions:
            test_datetime = datetime(2024, 1, 17, 10, 0, 0)
            mock_get_sessions.return_value = (
                [
                    {
                        "id": "session123",
                        "user_id": "user123",
                        "topic": "Test Topic",
                        "topic_type": "custom",
                        "status": "active",
                        "created_at": test_datetime,
                        "updated_at": test_datetime,
                        "last_message_at": test_datetime,
                        "summary": None,
                        "tags": None,
                        "metadata": {"character_id": "default"}
                    }
                ],
                1  # total count
            )

            response = client.get("/api/v1/chat/sessions", headers=headers)

            # 期望：200状态码，返回用户会话列表
            assert response.status_code == 200
            data = response.json()
            assert "sessions" in data
            assert len(data["sessions"]) == 1
            assert data["sessions"][0]["user_id"] == "user123"

            # 验证服务调用时使用的是JWT Token中的用户ID
            mock_get_sessions.assert_called_once()
            call_args = mock_get_sessions.call_args
            # 检查kwargs中的user_id参数
            args, kwargs = call_args
            assert kwargs["user_id"] == "user123"  # 用户ID应该是从JWT提取的

    # AC1 Scenario 1.2: 无效JWT Token访问会话列表
    def test_get_sessions_with_invalid_jwt(self, client, invalid_jwt_token):
        """
        测试场景1.2：无效JWT Token访问会话列表
        Given 用户提供了无效或过期的JWT Token
        When 用户请求GET /api/v1/chat/sessions
        Then 系统应返回401状态码
        And 响应包含标准错误格式："Authentication failed"
        And 不返回任何会话数据
        """
        headers = {"Authorization": f"Bearer {invalid_jwt_token}"}

        response = client.get("/api/v1/chat/sessions", headers=headers)

        # 期望：401状态码，认证失败
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "authentication" in data["detail"].lower()

    # AC1 Scenario 1.3: 尝试通过URL参数访问其他用户数据
    def test_get_sessions_ignores_userid_url_param(self, client, valid_jwt_token):
        """
        测试场景1.3：尝试通过URL参数访问其他用户数据
        Given 用户持有有效JWT Token（用户ID为user123）
        When 用户尝试请求GET /api/v1/chat/sessions?userId=user456
        Then 系统应忽略URL参数中的userId
        And 仅返回JWT Token中用户ID（user123）的会话数据
        And 不返回user456的任何数据
        """
        headers = {"Authorization": f"Bearer {valid_jwt_token}"}

        with patch('api.services.session_service.SessionService.get_user_sessions') as mock_get_sessions:
            test_datetime = datetime(2024, 1, 17, 10, 0, 0)
            mock_get_sessions.return_value = (
                [
                    {
                        "id": "session123",
                        "user_id": "user123",  # 只返回JWT Token中的用户数据
                        "topic": "Test Topic",
                        "topic_type": "custom",
                        "status": "active",
                        "created_at": test_datetime,
                        "updated_at": test_datetime,
                        "last_message_at": test_datetime,
                        "summary": None,
                        "tags": None,
                        "metadata": {"character_id": "default"}
                    }
                ],
                1  # total count
            )

            # 尝试通过URL参数访问其他用户数据
            response = client.get("/api/v1/chat/sessions?userId=user456", headers=headers)

            # 期望：系统忽略URL参数，只返回JWT Token中用户的数据
            assert response.status_code == 200
            data = response.json()
            assert "sessions" in data
            assert data["sessions"][0]["user_id"] == "user123"  # 不是user456

            # 验证服务调用时使用的是JWT Token中的用户ID，而非URL参数
            mock_get_sessions.assert_called_once()
            call_args = mock_get_sessions.call_args
            args, kwargs = call_args
            assert kwargs["user_id"] == "user123"  # 应该是JWT中的用户ID，不是URL参数的user456

    # AC1 Scenario 1.4: 正常JWT认证访问会话消息
    def test_get_session_messages_with_valid_jwt(self, client, valid_jwt_token):
        """
        测试场景1.4：正常JWT认证访问会话消息
        Given 用户已登录并持有有效的JWT Token
        And 存在属于该用户的会话session123
        When 用户请求GET /api/v1/chat/sessions/session123/messages
        Then 系统应验证JWT Token中的用户ID与会话所有者匹配
        And 返回200状态码和会话消息列表
        """
        headers = {"Authorization": f"Bearer {valid_jwt_token}"}

        with patch('api.services.session_service.SessionService.verify_session_access') as mock_verify_access:
            with patch('api.services.session_service.SessionService.get_session_messages') as mock_get_messages:
                mock_verify_access.return_value = True
                test_datetime = datetime(2024, 1, 17, 10, 0, 0)
                mock_get_messages.return_value = (
                    [
                        {
                            "id": "msg123",
                            "session_id": "session123",
                            "user_id": "user123",
                            "role": "user",
                            "content": "Hello",
                            "created_at": test_datetime,
                            "updated_at": test_datetime,
                            "metadata": {}
                        }
                    ],
                    1  # total count
                )

                response = client.get("/api/v1/chat/sessions/session123/messages", headers=headers)

                # 期望：200状态码，返回会话消息
                assert response.status_code == 200
                data = response.json()
                assert "messages" in data
                assert len(data["messages"]) == 1
                assert data["messages"][0]["session_id"] == "session123"

                # 验证权限检查被调用
                mock_verify_access.assert_called_once_with("session123", "user123")

                # 验证消息获取被调用
                # 验证服务调用参数正确
                mock_get_messages.assert_called_once()
                call_args = mock_get_messages.call_args
                args, kwargs = call_args
                assert kwargs["session_id"] == "session123"  # session_id参数
                # get_session_messages方法不接受user_id参数，权限检查在verify_session_access中完成

    # AC1 Scenario 1.5: 尝试访问其他用户的会话消息
    def test_get_session_messages_access_denied(self, client, valid_jwt_token):
        """
        测试场景1.5：尝试访问其他用户的会话消息
        Given 用户持有有效JWT Token（用户ID为user123）
        And 存在属于其他用户（user456）的会话session789
        When 用户请求GET /api/v1/chat/sessions/session789/messages
        Then 系统应返回403状态码
        And 响应包含"Access denied"错误信息
        """
        headers = {"Authorization": f"Bearer {valid_jwt_token}"}

        with patch('api.services.session_service.SessionService.verify_session_access') as mock_verify_access:
            # 模拟权限检查失败
            mock_verify_access.return_value = False

            response = client.get("/api/v1/chat/sessions/session789/messages", headers=headers)

            # 期望：403状态码，访问被拒绝
            assert response.status_code == 403
            data = response.json()
            assert "detail" in data
            assert "access denied" in data["detail"].lower()

    # AC1 Scenario 1.6: 没有提供JWT Token
    def test_get_sessions_without_jwt(self, client):
        """
        测试场景1.6：没有提供JWT Token
        Given 用户没有提供JWT Token
        When 用户请求GET /api/v1/chat/sessions
        Then 系统应返回401状态码
        And 响应包含认证错误信息
        """
        response = client.get("/api/v1/chat/sessions")

        # 期望：401状态码，缺少认证
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "authentication" in data["detail"].lower()

    # AC1 Scenario 1.7: 过期的JWT Token
    def test_get_sessions_with_expired_jwt(self, client, expired_jwt_token):
        """
        测试场景1.7：过期的JWT Token
        Given 用户提供了过期的JWT Token
        When 用户请求GET /api/v1/chat/sessions
        Then 系统应返回401状态码
        And 响应包含token过期错误信息
        """
        headers = {"Authorization": f"Bearer {expired_jwt_token}"}

        response = client.get("/api/v1/chat/sessions")

        # 期望：401状态码，token过期
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "authentication" in data["detail"].lower()
