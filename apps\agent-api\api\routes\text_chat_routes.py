# api/routes/text_chat_routes.py
"""
文本聊天SSE路由 - 故事1.3-Text
实现基于Server-Sent Events的文本对话接口
"""
import asyncio
import json
import logging
import time
from typing import AsyncGenerator, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse

from api.models.chat_models import TextMessageRequest, SSEData, TextChunkData, StreamEndData, ErrorData
from api.services.chat_orchestration_service import ChatOrchestrationService, get_chat_orchestration_service
from api.dependencies.auth import get_current_user
from api.settings import get_settings

router = APIRouter(tags=["Text Chat"])
logger = logging.getLogger(__name__)
settings = get_settings()

# 全局连接管理 - P1性能调优：放宽连接限制提升用户体验
active_connections: Dict[str, int] = {}
active_connections_lock = asyncio.Lock()  # 添加锁保护
MAX_CONNECTIONS_PER_USER = 5  # P1调优：从3提升到5

@router.post("/text_message")
async def handle_text_message(
    request_data: TextMessageRequest,
    req: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
    orchestrator: ChatOrchestrationService = Depends(get_chat_orchestration_service)
):
    """
    处理文本消息并返回SSE流式响应

    实现故事1.3-Text的核心功能：
    1. JWT认证保护
    2. ChatOrchestrationService集成
    3. SSE流式响应
    4. 错误处理和资源管理
    """
    # 从JWT Token解析用户ID
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID in token"
        )

    # 实现并发连接限制（架构师建议）
    async with active_connections_lock:
        user_connections = active_connections.get(user_id, 0)
        if user_connections >= MAX_CONNECTIONS_PER_USER:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Maximum {MAX_CONNECTIONS_PER_USER} concurrent connections per user"
            )

        # 更新连接计数
        active_connections[user_id] = user_connections + 1

    logger.info(f"Starting text chat for user {user_id}, session {request_data.sessionId}")

    # 构建上下文信息
    context = {
        "userId": user_id,
        "sessionId": request_data.sessionId,
        "characterId": request_data.characterId,
        "requestId": f"text_{int(time.time() * 1000)}"
    }

    # 创建SSE生成器
    async def sse_generator() -> AsyncGenerator[str, None]:
        """
        SSE事件生成器

        实现架构师建议的SSE流内错误处理机制：
        - 异常转换为error事件而不是破坏连接
        - 确保连接正确关闭和资源释放
        """
        try:
            # 客户端断开检测
            if await req.is_disconnected():
                logger.info(f"Client disconnected before streaming started - user: {user_id}")
                return

            # 调用ChatOrchestrationService获取流式响应
            response_stream = orchestrator.handle_message_stream(
                user_message=request_data.message,
                context=context
            )

            # 转换文本流为SSE事件流
            full_response = ""
            async for text_chunk in response_stream:
                # 检查客户端是否断开连接
                if await req.is_disconnected():
                    logger.info(f"Client disconnected during streaming - user: {user_id}")
                    break

                # 检查是否是错误消息
                if text_chunk.startswith("[ERROR:"):
                    # 发送错误事件
                    error_msg = text_chunk[7:-1]  # 移除 [ERROR: 和 ]
                    error_data = ErrorData(
                        type="PROCESSING_ERROR",
                        message=error_msg
                    )
                    sse_event = SSEData(event="error", data=error_data)
                    yield sse_event.to_sse_format()
                    break
                else:
                    # 发送正常文本块
                    full_response += text_chunk
                    chunk_data = TextChunkData(delta=text_chunk)
                    sse_event = SSEData(event="text_chunk", data=chunk_data)
                    yield sse_event.to_sse_format()

            # 如果没有错误，发送流结束事件
            if not full_response.startswith("[ERROR:"):
                stream_end_data = StreamEndData(
                    messageId=None,  # 暂时不实现消息ID
                    status="done",
                    finalContentLength=len(full_response)
                )
                sse_event = SSEData(event="stream_end", data=stream_end_data)
                yield sse_event.to_sse_format()

        except asyncio.CancelledError:
            # 客户端取消连接
            logger.info(f"Stream cancelled by client - user: {user_id}")

        except Exception as e:
            # 捕获所有异常，发送错误事件（架构师重点关注）
            logger.error(f"Error in SSE generator for user {user_id}: {str(e)}", exc_info=True)
            try:
                error_data = ErrorData(
                    type="SSE_GENERATOR_ERROR",
                    message=f"流式响应时发生错误: {str(e)}"
                )
                sse_event = SSEData(event="error", data=error_data)
                yield sse_event.to_sse_format()
            except Exception:
                # 即使错误事件发送失败，也不能抛出异常破坏连接
                logger.error(f"Failed to send error event for user {user_id}")

        finally:
            # 确保连接计数正确更新（资源管理）
            async with active_connections_lock:
                current_connections = active_connections.get(user_id, 0)
                if current_connections > 0:
                    active_connections[user_id] = current_connections - 1
                    if active_connections[user_id] == 0:
                        del active_connections[user_id]

            logger.info(f"SSE stream ended for user {user_id}, remaining connections: {active_connections.get(user_id, 0)}")

    # 返回SSE流式响应
    return StreamingResponse(
        sse_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",  # 在生产环境中应该更严格
            "Access-Control-Allow-Headers": "Authorization",
        }
    )


@router.get("/connections/status")
async def get_connection_status(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取当前用户的连接状态（调试用）
    """
    user_id = current_user.get("sub")
    async with active_connections_lock:
        return {
            "user_id": user_id,
            "active_connections": active_connections.get(user_id, 0),
            "max_connections": MAX_CONNECTIONS_PER_USER,
            "total_active_users": len(active_connections)
        }
