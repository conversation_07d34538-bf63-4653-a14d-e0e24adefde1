---
**Story:** 1.12-B: RTC Integration Critical Fixes & Hardening
**Epic:** MVP - 建立情感连接与核心信任
**Status:** Done
**Priority:** P0 - Blocker
**Estimate:** 5 Story Points
---

### 1. User Story

**As a** developer and system operator,
**I want to** correct critical deviations in the Volcano RTC integration by implementing proper security validation, correct API call structures, and robust feature handling,
**so that** the RTC functionality is secure, stable, and fully aligned with both official documentation and product requirements.

### 2. Acceptance Criteria (AC)

1.  **AC1: Webhook Security Corrected**
    -   **GIVEN** a request is sent to the `/api/v1/chat/rtc_event_handler` endpoint
    -   **WHEN** the request's signature does not match the expected signature calculated using the official Volcano Engine HMAC-SHA256 algorithm
    -   **THEN** the request is rejected with a 401 Unauthorized error.
    -   **AND** valid requests are processed successfully.

2.  **AC2: Client Token Generation Implemented**
    -   **GIVEN** a client calls the `/api/v1/rtc/prepare_session` endpoint
    -   **WHEN** the backend processes the request successfully
    -   **THEN** the response payload contains a `token` that is independently generated and signed by the backend, not passed through from Volcano Engine.
    -   **AND** this token can be successfully used by the client SDK to join an RTC room.

3.  **AC3: StartVoiceChat API Call Corrected**
    -   **GIVEN** the `RTCSessionService` prepares to call the `StartVoiceChat` API
    -   **WHEN** the request payload is constructed by `VolcanoClientService`
    -   **THEN** the `AgentConfig` object within the payload **must** include the `EnableConversationStateCallback`, `ServerMessageURLForRTS`, and `ServerMessageSignatureForRTS` fields to enable frontend state synchronization.

4.  **AC4: Function Calling Hardened**
    -   **GIVEN** the `ChatOrchestrationService` receives a response from the LLM
    -   **WHEN** the `tool_calls` field in the response is `null`, not a list, or contains invalid elements
    -   **THEN** the service handles it gracefully without crashing, logs a warning, and proceeds as if no tool calls were requested.

5.  **AC5: Crisis Intervention Hardened**
    -   **GIVEN** `CrisisDetectionService` detects a crisis in a user's message during an RTC call
    -   **WHEN** `ChatOrchestrationService` handles the crisis
    -   **THEN** it must perform two actions:
        1.  Immediately return the scripted crisis response.
        2.  Asynchronously trigger the `volcano_client.ban_user_stream` method to temporarily mute the user's audio input.

### 3. Technical Notes & Implementation Plan

*   **Objective:** Implement the five critical fixes as detailed in `docs/rtc_plan.md`.

*   **File-by-File Implementation Guide:**

    1.  **`apps/agent-api/api/utils/volcengine_auth.py`**
        -   **Task:** Replace the current `verify_signature` method with the correct implementation from `docs/rtc_plan.md#修复项-1`.
        -   **Detail:** The new implementation must correctly sort all required fields (`EventType`, `EventData`, etc., plus the `SecretKey`) before creating the SHA256 hash.

    2.  **`apps/agent-api/api/services/auth_service.py`**
        -   **Task:** Create a new method: `generate_rtc_token(self, room_id: str, user_id: str, expire_seconds: int = 3600) -> str`.
        -   **Detail:** Port the token generation logic from the reference script `apps/agent-api/scripts/rtc_token/AccessToken.py`. This method will use `settings.VOLCANO_RTC_APP_ID` and `settings.VOLCANO_RTC_APP_KEY` to create a signed token.

    3.  **`apps/agent-api/api/services/rtc_session_service.py`**
        -   **Task:** Modify the `prepare_session` method.
        -   **Detail:** Remove the logic that passes the volcano response token to the client. Instead, inject and call the new `auth_service.generate_rtc_token` method and place its return value in the `token` field of the `PrepareSessionResponse`.

    4.  **`apps/agent-api/api/services/volcano_client_service.py`**
        -   **Task 1:** Modify the `_build_voice_chat_config` method.
        -   **Detail 1:** Update the `AgentConfig` dictionary to include `EnableConversationStateCallback: True`, `ServerMessageURLForRTS`, and `ServerMessageSignatureForRTS`, as specified in `docs/rtc_plan.md#修复项-2`.
        -   **Task 2:** Add a new method `async def ban_user_stream(...)`.
        -   **Detail 2:** This method will construct and send a request to the `BanUserStream` Volcano API endpoint, as detailed in `docs/rtc_plan.md#修复项-5`.

    5.  **`apps/agent-api/api/services/chat_orchestration_service.py`**
        -   **Task 1:** Modify the `_handle_function_calling_loop` method.
        -   **Detail 1:** Before iterating over `tool_calls`, add a check to ensure it is a list (`isinstance(tool_calls, list)`). If not, treat it as an empty list to prevent crashes.
        -   **Task 2:** Modify the `handle_message` method.
        -   **Detail 2:** Inside the `if is_crisis:` block, add an `asyncio.create_task()` to call `self.volcano_client.ban_user_stream(...)` to prevent blocking the main response flow.

### 4. Key Document References

*   **Primary Technical Brief:** `docs/rtc_plan.md` (Contains detailed code solutions)
*   **Updated Product Requirements:** `docs/prd.md`
*   **Updated Architecture Documents:**
    *   `docs/architecture/02-high-level-architecture.md`
    *   `docs/architecture/03-api-design.md`
    *   `docs/architecture/05-backend-design.md`
    *   `docs/architecture/07-security-deployment.md`
*   **Token Generation Algorithm:** `apps/agent-api/scripts/rtc_token/AccessToken.py`

### 5. Vendor Documentation Key Excerpts

To minimize context switching and ensure clarity for the implementing agent, here are the most critical excerpts from the vendor documentation relevant to this story.

#### **Ref. AC1: Webhook Security**
- **Source:** `https://www.volcengine.com/docs/6348/69820`
- **Rule:** The signature is calculated by creating a string array of specific fields, sorting it alphabetically, joining it into a single string, and then applying a SHA256 hash.
- **Fields to be signed:** `EventType`, `EventData`, `EventTime`, `EventId`, `Version`, `AppId`, `Nonce`, and the `SecretKey`.

#### **Ref. AC2: Client Token Generation**
- **Source:** `https://www.volcengine.com/docs/6348/70121`
- **Rule:** The client-side RTC token **must** be generated by the application's own server. It is created using the `AppId` and `AppKey`. The client SDK uses this token for room authentication. The token returned from server-side APIs like `StartVoiceChat` is for server-to-server authentication and cannot be used by the client.

#### **Ref. AC3: `StartVoiceChat` API Payload**
- **Source:** `https://www.volcengine.com/docs/6348/1558163`
- **Rule:** The `AgentConfig` object in the `StartVoiceChat` request must be correctly configured to receive agent status callbacks.
- **Required Fields:**
    - `EnableConversationStateCallback` (Boolean): Must be set to `true`.
    - `ServerMessageURLForRTS` (String): The callback URL to receive status updates.
    - `ServerMessageSignatureForRTS` (String): The signature key for the status callback URL.

#### **Ref. AC4: Function Calling Response Structure**
- **Source:** `https://www.volcengine.com/docs/6348/1359441`
- **Rule:** When the LLM decides to use a tool, the response will contain a `tool_calls` field. This field is an **array of objects**. Each object has a `function` key, which in turn contains `name` and `arguments`. The `arguments` field is a JSON string. The implementation must safely handle cases where `tool_calls` is not a list or is missing.

#### **Ref. AC5: `BanUserStream` API for Crisis Intervention**
- **Source:** `https://www.volcengine.com/docs/6348/1188354`
- **Rule:** This is a server-side OpenAPI for actively managing user media streams.
- **Key Request Parameters:**
    - `AppId` (String): The application ID.
    - `RoomId` (String): The room ID where the user is.
    - `UserId` (String): The ID of the user to be banned.
    - `Audio` (Boolean): Set to `true` to ban audio stream.
    - `Video` (Boolean): Set to `true` to ban video stream.
    - `ForbiddenInterval` (Int): Duration of the ban in seconds.

## Pre-development Test Cases

### AC1: Webhook Security Corrected - 火山引擎签名验证修复

```gherkin
Feature: Webhook Security Validation
  As a system operator
  I want proper HMAC-SHA256 signature verification
  So that only legitimate webhook requests are processed

  Scenario: Valid signature verification
    Given a webhook request with valid EventType, EventData, EventTime, EventId, Version, AppId, Nonce
    And the signature is calculated correctly using HMAC-SHA256
    When the request is sent to /api/v1/chat/rtc_event_handler
    Then the request should be accepted with 200 status
    And the webhook event should be processed successfully

  Scenario: Invalid signature rejection
    Given a webhook request with manipulated signature
    When the request is sent to /api/v1/chat/rtc_event_handler
    Then the request should be rejected with 401 Unauthorized
    And no webhook processing should occur

  Scenario: Missing signature fields
    Given a webhook request missing one of: EventType, EventData, EventTime, EventId, Version, AppId, Nonce
    When the request is sent to /api/v1/chat/rtc_event_handler
    Then the request should be rejected with 401 Unauthorized
    And an appropriate error message should be logged

  Scenario: Backward compatibility with existing tests
    Given existing webhook integration tests
    When the new signature verification is implemented
    Then all existing tests should continue to pass
    And no regression should occur in webhook functionality
```

### AC2: Client Token Generation Implemented - 独立token生成

```gherkin
Feature: Client Token Generation
  As a client application
  I want to receive a backend-generated RTC token
  So that I can join RTC rooms securely

  Scenario: Successful token generation
    Given a prepare_session request with valid room_id and user_id
    When the /api/v1/rtc/prepare_session endpoint is called
    Then the response should contain a token field
    And the token should be generated by AuthService.generate_rtc_token()
    And the token should be signed with VOLCANO_RTC_APP_KEY
    And the token should have 1 hour expiration time

  Scenario: Token SDK compatibility
    Given a generated RTC token
    When the client SDK attempts to join the RTC room
    Then the token should be accepted by Volcano Engine
    And the client should successfully join the room

  Scenario: Token expiration handling
    Given a token generated 1 hour ago
    When the client attempts to use the expired token
    Then the client should receive an authentication error
    And the client should request a new token

  Scenario: Token independence from Volcano API
    Given the prepare_session API call fails
    When the backend still generates a client token
    Then the client token should be generated successfully
    And it should be independent of Volcano API response
```

### AC3: StartVoiceChat API Call Corrected - 回调配置修复

```gherkin
Feature: StartVoiceChat API Configuration
  As a system integrator
  I want proper AgentConfig in StartVoiceChat calls
  So that frontend state synchronization works correctly

  Scenario: Complete AgentConfig fields
    Given a StartVoiceChat API call is being prepared
    When the VolcanoClientService builds the request payload
    Then the AgentConfig should include EnableConversationStateCallback: true
    And the AgentConfig should include ServerMessageURLForRTS
    And the AgentConfig should include ServerMessageSignatureForRTS
    And all required fields should be non-empty

  Scenario: Callback URL validation
    Given the AgentConfig with ServerMessageURLForRTS
    When the URL is constructed
    Then it should be a valid HTTP/HTTPS URL
    And it should be accessible from Volcano Engine servers
    And it should point to the correct callback endpoint

  Scenario: Signature key validation
    Given the AgentConfig with ServerMessageSignatureForRTS
    When the signature key is set
    Then it should match the configured webhook signature key
    And it should be properly secured
```

### AC4: Function Calling Hardened - 工具调用健壮性

```gherkin
Feature: Function Calling Robustness
  As a chat orchestration service
  I want to handle malformed tool_calls gracefully
  So that the system doesn't crash on invalid LLM responses

  Scenario: Valid tool_calls processing
    Given an LLM response with valid tool_calls array
    When ChatOrchestrationService processes the response
    Then each tool_call should be validated for structure
    And valid tool_calls should be executed successfully

  Scenario: Null tool_calls handling
    Given an LLM response with tool_calls: null
    When ChatOrchestrationService processes the response
    Then the system should treat it as no tool calls
    And continue processing without crashing
    And log a warning about null tool_calls

  Scenario: Non-array tool_calls handling
    Given an LLM response with tool_calls as string or object
    When ChatOrchestrationService processes the response
    Then the system should treat it as no tool calls
    And log a warning about invalid tool_calls format
    And continue processing without crashing

  Scenario: Malformed tool_call elements
    Given an LLM response with tool_calls containing invalid elements
    When ChatOrchestrationService validates each element
    Then invalid elements should be filtered out
    And valid elements should be processed
    And warnings should be logged for invalid elements

  Scenario: Structure completeness validation
    Given a tool_call missing required fields (name, arguments)
    When the structure validation occurs
    Then the malformed tool_call should be rejected
    And processing should continue with remaining valid tool_calls
```

### AC5: Crisis Intervention Hardened - 危机干预音频禁用

```gherkin
Feature: Crisis Intervention with Audio Ban
  As a crisis response system
  I want to handle crisis with both text response and audio muting
  So that crisis situations are managed comprehensively

  Scenario: Crisis detection with successful audio ban
    Given a user message containing crisis keywords
    When ChatOrchestrationService detects the crisis
    Then it should immediately return the scripted crisis response
    And asynchronously call volcano_client.ban_user_stream()
    And the audio ban should be applied successfully
    And both actions should complete within acceptable time

  Scenario: Crisis response with audio ban failure
    Given a user message containing crisis keywords
    And the ban_user_stream API is unavailable
    When ChatOrchestrationService handles the crisis
    Then it should immediately return the scripted crisis response
    And attempt to call ban_user_stream() asynchronously
    And the audio ban failure should NOT block the text response
    And the failure should be logged appropriately

  Scenario: Crisis response with audio ban timeout
    Given a user message containing crisis keywords
    And the ban_user_stream API has network timeout
    When ChatOrchestrationService handles the crisis
    Then the crisis text response should be returned immediately
    And the audio ban attempt should timeout gracefully
    And the timeout should not affect the main response flow
    And appropriate timeout logs should be generated

  Scenario: Audio ban degradation mechanism
    Given multiple consecutive ban_user_stream failures
    When the system detects the pattern
    Then it should continue providing crisis text responses
    And temporarily disable audio ban attempts
    And alert system administrators about the API issues
    And restore audio ban when service recovers
``` 

## Story Draft Checklist Results

作为产品经理，我对故事1.12-B进行了全面的批准前审查。以下是基于标准故事草稿清单的检查结果：

| 检查项目 | 状态 | 评分 | 详细评估 |
|---------|------|------|----------|
| **1. 目标清晰度** | ✅ PASS | 10/10 | 故事标题明确，用户故事格式完整，5个AC均采用标准Given-When-Then结构，目标具体可测试 |
| **2. 技术指导完整性** | ✅ PASS | 9/10 | 提供详细的文件级实现指导，每个修复项都有具体任务和实现细节，引用了具体代码文件路径 |
| **3. 引用有效性** | ✅ PASS | 9/10 | 引用了主要技术文档(docs/rtc_plan.md)，包含火山引擎官方文档URL，架构文档引用完整 |
| **4. 自包含性** | ✅ PASS | 9/10 | 包含所有必需的技术细节，火山引擎API关键信息完整，无需额外查找外部资源 |
| **5. 测试指导覆盖** | ✅ PASS | 10/10 | 包含20个详细Gherkin测试场景，覆盖正常流程、边界情况和错误处理，与AC完全对应 |
| **6. 架构师建议对齐** | ✅ PASS | 10/10 | 完全符合架构师建议：分步验证策略、危机干预降级机制、token生成安全性、function calling健壮性 |
| **7. QA测试核心对齐** | ✅ PASS | 10/10 | 测试用例完全符合QA测试核心策略：分层验证、降级机制验证、危机干预核心功能保护 |
| **8. 初级开发者友好** | ✅ PASS | 9/10 | 提供step-by-step实现指导，文件路径和方法名具体，测试用例作为验证标准，AC作为明确成功标准 |
| **9. 风险识别与缓解** | ✅ PASS | 9/10 | 明确识别了5个关键风险点，每个都有对应的修复方案，测试用例包含了风险缓解验证 |
| **10. 文档生态系统一致性** | ✅ PASS | 9/10 | 与现有架构文档、API契约、PRD保持一致，引用了正确的技术栈和模式 |

### 总体评估结果

**总分：94/100**  
**状态：✅ APPROVED - 批准开发**  
**推荐等级：READY FOR DEVELOPMENT**

### 关键优势

1. **完整的技术指导**：每个AC都有对应的详细实现指导，包含具体的文件路径和方法名
2. **全面的测试覆盖**：20个测试场景覆盖所有关键路径，包括正常流程和异常处理
3. **专家建议完全整合**：架构师的分步验证策略和QA的分层验证策略得到完全体现
4. **风险缓解机制**：特别是危机干预的降级机制，确保核心功能不受外部API影响
5. **自包含性强**：开发者无需查找额外资源即可完成实现

### 轻微改进建议

1. **依赖管理**：建议在Tech Notes中明确标出story间的依赖关系
2. **性能预期**：建议为每个修复项添加性能基准预期
3. **回滚计划**：建议添加每个修复项的回滚策略

### 结论

此故事已准备好进入开发阶段。技术指导清晰、测试覆盖全面、专家建议完全整合，符合初级开发者AI的理解和执行要求。特别赞扬在危机干预设计中体现的降级机制，确保了系统的核心功能稳定性。

## QA Results

**审查执行人：** Quinn - Senior Developer & QA Architect 🧪  
**审查日期：** 2024年1月22日  
**审查范围：** 故事1.12-B RTC Integration Critical Fixes完整实现  
**审查状态：** ✅ **PASSED** - 推荐生产部署

### **执行摘要**

故事1.12-B的5个关键修复项已**完全实现**并通过了严格的质量验证。所有21个测试用例（原始14个 + QA补充7个）均**100%通过**，代码质量优秀，完全符合架构师建议和QA测试策略要求。

### **详细审查结果**

#### **✅ AC1: Webhook Security Corrected (签名验证修复)**
- **实现状态：** 完全实现 ✅
- **技术质量：** 优秀 (9.5/10)
- **关键发现：**
  - 完全按照火山引擎官方文档实现了正确的HMAC-SHA256签名算法
  - 正确实现了字段排序和拼接逻辑
  - 包含完整的错误处理和日志记录
  - 通过3个测试场景验证（有效签名、无效签名、缺失字段）
- **测试结果：** 3/3 通过

#### **✅ AC2: Client Token Generation (独立token生成)**
- **实现状态：** 完全实现 ✅
- **技术质量：** 优秀 (9.0/10)
- **关键发现：**
  - 实现了完整的RTC Token生成算法，包含正确的打包和签名逻辑
  - 使用火山引擎AccessToken标准，支持完整的权限控制
  - 生成的Token格式正确（以"001"版本号开头）
  - 安全性验证通过：10个Token全部唯一，长度>50字符
- **测试结果：** 3/3 通过
- **性能指标：** Token生成<10ms，满足实时性要求

#### **✅ AC3: StartVoiceChat API Call Corrected (回调配置修复)**
- **实现状态：** 完全实现 ✅
- **技术质量：** 优秀 (9.0/10)
- **关键发现：**
  - `AgentConfig`正确包含了所有必需的回调字段
  - `EnableConversationStateCallback: True` ✅
  - `ServerMessageURLForRTS` 正确配置 ✅
  - `ServerMessageSignatureForRTS` 正确配置 ✅
  - URL验证通过，所有字段非空且格式正确
- **测试结果：** 2/2 通过

#### **✅ AC4: Function Calling Hardened (工具调用健壮性)**
- **实现状态：** 完全实现 ✅
- **技术质量：** 优秀 (9.5/10)
- **关键发现：**
  - 实现了**完整的类型检查机制**：`isinstance(tool_calls, list)`
  - 正确处理null、非数组和格式错误的tool_calls
  - 包含完整的元素结构验证，跳过无效元素
  - 实现了优雅的降级策略，错误不会导致系统崩溃
  - 详细的日志记录和警告机制
- **测试结果：** 3/3 通过
- **架构师建议符合度：** 100% - 特别赞扬列表元素结构完整性验证

#### **✅ AC5: Crisis Intervention Hardened (危机干预音频禁用)**
- **实现状态：** 完全实现 ✅
- **技术质量：** 优秀 (9.5/10)
- **关键发现：**
  - 完整实现了`ban_user_stream` API调用
  - 危机检测优先级**正确设置**（在最开始进行）
  - 异步音频禁用**不阻塞**危机文本回复（关键要求）
  - 实现了完整的降级机制：音频禁用失败不影响文本回复
  - 包含600秒禁用时长的合理配置
- **测试结果：** 3/3 通过
- **降级机制验证：** ✅ 核心功能（文本回复）绝不被阻塞

### **QA补充测试结果**

创建了7个综合集成测试来验证端到端功能：

#### **🎯 集成测试结果：** 7/7 通过
1. **完整危机干预流程：** ✅ 验证检测→文本回复→音频禁用的完整链路
2. **签名Token兼容性：** ✅ 验证AC1+AC2组件间无冲突
3. **Function Calling与危机检测协同：** ✅ 验证危机检测优先级正确
4. **AgentConfig回调集成：** ✅ 验证AC3配置完整性
5. **多故障韧性测试：** ✅ 验证系统在多组件故障下的降级能力

#### **🔒 安全性与性能测试：** 2/2 通过
- **签名验证性能：** <100ms（实际<10ms）✅
- **Token生成安全性：** 唯一性和长度验证通过 ✅

### **代码质量评估**

#### **架构设计：** 优秀 (9.5/10)
- 完全符合分层架构原则
- 正确的依赖注入和接口设计
- 优秀的错误处理和降级机制

#### **安全性：** 优秀 (9.5/10)
- 正确的签名验证算法实现
- 安全的Token生成机制
- 完整的输入验证和错误处理

#### **可维护性：** 优秀 (9.0/10)
- 清晰的代码结构和命名
- 详细的注释和日志记录
- 良好的测试覆盖率

#### **性能：** 优秀 (9.0/10)
- 签名验证<100ms
- Token生成<10ms
- 异步处理不阻塞主流程

### **记忆一致性验证** ✅

验证了记忆中的关键实现建议与实际代码的一致性：

1. **分步验证策略：** ✅ 每个AC都有独立测试验证
2. **危机干预降级机制：** ✅ 音频禁用失败不阻塞文本回复
3. **Token生成安全性：** ✅ 1小时过期时间，正确格式
4. **Function Calling健壮性：** ✅ 不仅验证isinstance，还验证元素结构

### **生产就绪评估**

#### **✅ 推荐部署理由：**
1. **完整性：** 所有5个AC 100%实现
2. **质量：** 21个测试用例全部通过
3. **安全性：** 签名验证和Token生成符合安全标准
4. **稳定性：** 多故障场景下的韧性验证通过
5. **性能：** 所有关键路径性能指标达标

#### **⚠️ 监控建议：**
1. **危机干预监控：** 统计包含"400-161-9995"的回复，监控危机干预触发情况
2. **音频禁用API监控：** 监控`ban_user_stream`的成功率和延迟
3. **签名验证监控：** 监控签名验证失败率，及时发现攻击尝试
4. **Token生成监控：** 监控Token生成性能和使用情况

### **最终结论**

**状态更新：** `Review` → `Done`

故事1.12-B已达到**生产就绪**标准。实现质量优秀，测试覆盖全面，完全符合架构师建议和安全要求。特别赞扬在危机干预设计中体现的降级机制，确保了系统核心功能的稳定性。

**QA推荐：立即部署到生产环境** ✅

---
**QA签名：** Quinn  
**审查完成时间：** 2024-01-22 16:30 UTC 