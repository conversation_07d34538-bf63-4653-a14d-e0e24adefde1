#!/usr/bin/env python3
"""
用户管理API测试
测试接口:
- GET /api/v1/user/profile
- PUT /api/v1/user/profile
- PATCH /api/v1/user/profile
- POST /api/v1/user/characters/{character_id}/bind
- GET /api/v1/user/settings
- PUT /api/v1/user/settings
"""

import asyncio
import sys
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class UserTester(BaseAPITester):
    """用户管理API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("03_user", base_url)

    async def test_get_user_profile(self):
        """测试获取用户画像API"""
        self.logger.info("👤 测试获取用户画像API")

        response = await self.make_request("GET", "/api/v1/user/profile")

        # 验证响应结构
        expected_fields = ["id", "user_id", "nickname", "onboarding_completed"]
        for field in expected_fields:
            if field in response:
                self.logger.info(f"✅ 响应包含字段: {field}")
            else:
                self.logger.error(f"❌ 响应缺少字段: {field}")

    async def test_update_user_profile(self):
        """测试更新用户画像API"""
        self.logger.info("✏️ 测试更新用户画像API")

        # 优先使用共享配置中的引导数据
        onboarding_data = self.get_shared_config("test_results", {}).get("onboarding", {})

        update_data = {
            "nickname": f"{onboarding_data.get('nickname', self.test_data['nickname'])}_updated",
            "age_range": "55-65",
            "core_needs": onboarding_data.get("core_needs", ["情感陪伴", "健康咨询"]),
            "interests": onboarding_data.get("interests", ["音乐", "阅读"]),
            "communication_style_preference": onboarding_data.get("communication_style_preference", "温和")
        }

        response = await self.make_request(
            "PUT",
            "/api/v1/user/profile",
            data=update_data
        )

        # 验证更新成功
        if response and "error" not in response:
            self.logger.info("✅ 用户画像更新成功")

            # 验证数据是否更新
            if "nickname" in response and response["nickname"] == update_data["nickname"]:
                self.logger.info("✅ 昵称更新成功")
            else:
                self.logger.error("❌ 昵称更新失败")

            # 保存更新后的用户画像数据
            self.save_test_result("user_profile", {
                "profile_updated": True,
                "current_nickname": response.get("nickname"),
                "age_range": update_data["age_range"],
                "core_needs": update_data["core_needs"],
                "interests": update_data["interests"],
                "communication_style_preference": update_data["communication_style_preference"]
            })
        else:
            self.logger.error("❌ 用户画像更新失败")

    async def test_patch_user_profile(self):
        """测试部分更新用户画像API"""
        self.logger.info("🔧 测试部分更新用户画像API")

        patch_data = {"nickname": f"{self.test_data['nickname']}_patched"}

        response = await self.make_request(
            "PATCH",
            "/api/v1/user/profile",
            data=patch_data
        )

        # 验证部分更新成功
        if response and "error" not in response:
            self.logger.info("✅ 用户画像部分更新成功")
        else:
            self.logger.error("❌ 用户画像部分更新失败")

    async def test_bind_character(self):
        """测试绑定角色API"""
        self.logger.info("🎭 测试绑定角色API")

        try:
            # 优先使用共享配置中的角色ID（可能来自引导流程）
            onboarding_data = self.get_shared_config("test_results", {}).get("onboarding", {})
            character_id = onboarding_data.get("character_id") or await self.get_character_id()

            response = await self.make_request(
                "POST",
                f"/api/v1/user/characters/{character_id}/bind"
            )

            # 验证绑定成功
            if response and "error" not in response:
                self.logger.info(f"✅ 角色绑定成功: {character_id}")

                # 保存角色绑定结果
                self.save_test_result("character_binding", {
                    "character_bound": True,
                    "character_id": character_id,
                    "binding_response": response
                })
            else:
                self.logger.error("❌ 角色绑定失败")

        except Exception as e:
            self.logger.error(f"❌ 绑定角色测试失败: {e}")

    async def test_get_user_settings(self):
        """测试获取用户设置API"""
        self.logger.info("⚙️ 测试获取用户设置API")

        response = await self.make_request("GET", "/api/v1/user/settings")

        # 验证响应结构
        expected_fields = ["theme", "font_size", "notification_enabled"]
        for field in expected_fields:
            if field in response:
                self.logger.info(f"✅ 设置包含字段: {field}")
            else:
                self.logger.warning(f"⚠️ 设置可能缺少字段: {field}")

    async def test_update_user_settings(self):
        """测试更新用户设置API"""
        self.logger.info("🔧 测试更新用户设置API")

        settings_data = {
            "theme": "dark",
            "font_size": "large",
            "notification_enabled": True,
            "quiet_hours_start": "22:00",
            "quiet_hours_end": "08:00"
        }

        response = await self.make_request(
            "PUT",
            "/api/v1/user/settings",
            data=settings_data
        )

        # 验证设置更新成功
        if response and "error" not in response:
            self.logger.info("✅ 用户设置更新成功")

            # 验证部分设置是否更新
            if "theme" in response and response["theme"] == settings_data["theme"]:
                self.logger.info("✅ 主题设置更新成功")
            else:
                self.logger.warning("⚠️ 主题设置可能未更新")
        else:
            self.logger.error("❌ 用户设置更新失败")

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始用户管理API测试")

        # 确保用户已认证
        await self.ensure_authenticated()

        tests = [
            ("获取用户画像", self.test_get_user_profile),
            ("更新用户画像", self.test_update_user_profile),
            ("部分更新用户画像", self.test_patch_user_profile),
            ("绑定角色", self.test_bind_character),
            ("获取用户设置", self.test_get_user_settings),
            ("更新用户设置", self.test_update_user_settings),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(0.5)  # 测试间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='用户管理API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with UserTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
