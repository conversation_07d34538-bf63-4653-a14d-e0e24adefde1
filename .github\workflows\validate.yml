name: <PERSON><PERSON><PERSON> (Python)

on:
  push:
    branches:
      - "main"
    paths:
      - "apps/agent-api/**"
      - ".github/workflows/validate.yml"
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
    branches:
      - "main"
    paths:
      - "apps/agent-api/**"
      - ".github/workflows/validate.yml"

jobs:
  validate:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/agent-api
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "apps/agent-api/requirements**.txt"

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Create a virtual environment
        run: uv venv --python ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          uv pip sync requirements.txt
          uv pip install ruff mypy

      - name: Format with ruff
        run: uv run ruff format . --check

      - name: <PERSON><PERSON> with ruff
        run: uv run ruff check .

      - name: Type-check with mypy
        run: uv run mypy .