# 故事 1.6-UI: 提醒功能界面设计

## 基本信息
- **故事编号**: 1.6-UI
- **故事标题**: 提醒功能界面设计
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: UX设计师 + 前端开发者
- **优先级**: 最高（P0）
- **工作量估计**: 4-6 个工作日
- **依赖关系**: 故事 1.1-UI (基础设计系统与组件库)
- **Status**: Approved

## 故事描述

作为UX设计师和前端开发者，我需要设计并实现"对话式提醒设置"的所有界面和交互体验，**以便** 为老年用户提供最自然、最贴心的提醒功能使用体验。

## 验收标准

### AC1: 提醒设置对话界面
- [ ] 设计提醒设置的对话式交互界面
- [ ] 实现自然语言提醒输入的界面布局
- [ ] 创建AI理解确认和澄清的对话框
- [ ] 设计提醒类型选择的友好界面

### AC2: 提醒管理界面
- [ ] 设计现有提醒的列表展示界面
- [ ] 实现提醒卡片的详细信息展示
- [ ] 创建提醒编辑和删除的操作界面
- [ ] 设计提醒状态（激活/暂停/已完成）的视觉区分

### AC3: 提醒通知界面
- [ ] 设计推送通知的内容布局和样式
- [ ] 实现应用内提醒弹窗的界面设计
- [ ] 创建提醒响应操作（完成/推迟/取消）的界面
- [ ] 设计提醒历史和统计的展示界面

### AC4: 时间设置和确认界面
- [ ] 设计时间选择器的适老化界面
- [ ] 实现重复设置的直观选择界面
- [ ] 创建提醒预览和最终确认的界面
- [ ] 设计时间冲突处理的提示界面

## Tasks / Subtasks

### 第一阶段：提醒设置对话界面 (1-2天)
- [ ] **自然语言输入界面** (AC1)
  - 设计提醒输入的对话式界面布局
  - 创建语音/文本输入的切换方案
  - 实现输入提示和示例的展示
  - 设计输入历史和快速选择功能

- [ ] **AI理解确认界面** (AC1)
  - 设计AI解析结果的确认界面
  - 创建时间、内容、类型的分项确认
  - 实现修改和重新输入的操作界面
  - 设计理解歧义的澄清对话界面

### 第二阶段：提醒管理和列表界面 (1-2天)
- [ ] **提醒列表界面** (AC2)
  - 设计提醒卡片的布局和样式
  - 创建不同状态提醒的视觉区分
  - 实现列表的分组和排序功能
  - 设计空状态和加载状态的界面

- [ ] **提醒详情和编辑界面** (AC2)
  - 设计提醒详情页面的信息展示
  - 创建快速编辑操作的界面
  - 实现提醒删除确认的对话框
  - 设计批量操作的界面和交互

### 第三阶段：通知和响应界面 (1天)
- [ ] **推送通知设计** (AC3)
  - 设计推送通知的内容模板
  - 创建不同提醒类型的通知样式
  - 实现通知操作按钮的布局
  - 设计通知展开和详情界面

- [ ] **应用内提醒界面** (AC3)
  - 设计应用内弹窗的界面样式
  - 创建提醒响应操作的按钮布局
  - 实现提醒推迟时间选择的界面
  - 设计提醒完成反馈的界面

### 第四阶段：时间设置和优化 (1天)
- [ ] **时间选择器界面** (AC4)
  - 设计适老化的时间选择界面
  - 创建日期和重复模式的选择器
  - 实现快速时间预设的选择界面
  - 设计时间冲突检测的提示界面

- [ ] **界面优化和集成** (AC4)
  - 优化所有提醒界面的响应式适配
  - 调整界面动画和过渡效果
  - 测试提醒流程的端到端体验
  - 完善界面的无障碍支持

## Dev Notes

CRITICAL: This is a **UI design and implementation story** focused on **reminder feature interfaces**. 
**PREREQUISITE**: Story 1.1-UI (基础设计系统与组件库) must be completed first.

**Design Focus Areas:**
- 对话式提醒设置的自然交互界面
- 提醒管理的直观操作界面  
- 通知和响应的及时反馈界面
- 时间设置的适老化友好界面

### Core Pages to Implement:
基于1.1-UI提供的基础组件进行专门定制：

```typescript
// 提醒功能页面群
app/(reminders)/
├── index.tsx            // 提醒列表页面
├── create.tsx           // 创建提醒页面
├── [id].tsx             // 提醒详情页面
├── edit/[id].tsx        // 编辑提醒页面
└── _layout.tsx          // 提醒模块布局

// 提醒专用组件
src/components/features/reminders/
├── ReminderCard.tsx         // 提醒卡片组件
├── ReminderDialog.tsx       // 提醒设置对话
├── TimePickerModal.tsx      // 时间选择器
├── ReminderNotification.tsx // 应用内通知
├── NaturalLanguageInput.tsx // 自然语言输入
└── ReminderStatusBadge.tsx  // 状态标识组件
```

### Key Design Requirements:
从 `@docs/prd/ux-design.md` 中的提醒功能设计要求：

1. **对话式设置体验**:
   - 自然语言输入：支持"明天上午提醒我吃药"等表达
   - AI理解确认：清晰展示解析结果并支持修正
   - 渐进式信息收集：避免复杂的表单填写
   - 温暖的交互反馈：体现AI的关怀和理解

2. **适老化设计标准**:
   - 大号时间选择器：确保老年用户能够准确选择
   - 清晰的状态指示：不同状态的提醒用色彩和图标区分
   - 简化的操作流程：减少步骤和认知负担
   - 明确的确认机制：重要操作都有二次确认

3. **通知设计原则**:
   - 及时且温和：既不错过重要提醒，又不过度打扰
   - 操作便捷：推迟、完成、取消等操作一键完成
   - 信息完整：通知包含必要的上下文信息
   - 历史可查：用户可以回顾提醒的执行情况

### Technical Architecture:
```typescript
// 自然语言提醒输入的核心实现
const NaturalLanguageReminderInput = () => {
  const [input, setInput] = useState('');
  const [parsedResult, setParsedResult] = useState(null);
  const [confirmationStep, setConfirmationStep] = useState('input');
  
  // 输入阶段 → AI解析 → 确认修正 → 创建成功
  const handleSubmit = async () => {
    const result = await parseNaturalLanguage(input);
    setParsedResult(result);
    setConfirmationStep('confirm');
  };
  
  return (
    <ReminderCreationFlow 
      step={confirmationStep}
      input={input}
      parsedResult={parsedResult}
      onConfirm={createReminder}
      onModify={() => setConfirmationStep('input')}
    />
  );
};
```

// 建议增加：
// AI解析结果(parsedResult)的预期数据结构示例
interface ParsedReminderResult {
  originalInput: string;
  title: string;          // 解析出的提醒事项
  time: string;           // ISO 8601 格式的时间字符串
  repeat?: 'daily' | 'weekly' | 'monthly'; // 重复模式
  type?: 'medicine' | 'appointment' | 'other'; // 提醒类型
}

## Testing

Dev Note: Story Requires the following tests:

- [ ] **Component Unit Tests**: 所有提醒相关组件的单元测试
- [ ] **Dialog Flow Tests**: 提醒设置对话流程的完整测试
- [ ] **Notification Tests**: 推送通知和应用内通知的展示测试
- [ ] **Accessibility Tests**: 提醒界面的无障碍功能验证

Manual Test Steps:
- 测试自然语言输入的各种表达方式
- 验证提醒通知的及时性和准确性
- 测试时间选择器在不同设备上的使用体验
- 验证提醒管理操作的直观性和易用性

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.1-UI（基础设计系统与组件库）已完成
- [ ] 提醒功能的UX规范已明确
- [ ] 自然语言处理的交互模式已设计

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 提醒功能界面设计完成并获得审批
- [ ] 对话式设置体验达到设计要求
- [ ] 通知界面的及时性和准确性验证通过

### 交付物 (Deliverables)
- [ ] **设计文件**: 提醒功能界面的完整Figma设计稿
- [ ] **页面实现**: 提醒相关页面的完整代码
- [ ] **组件库扩展**: 提醒专用的UI组件
- [ ] **通知模板**: 推送通知的内容和样式模板
- [ ] **交互指南**: 对话式设置的用户指导

## 风险与缓解措施

### 主要风险
1. **自然语言理解**: 用户表达方式的多样性难以完全覆盖
2. **时间选择复杂性**: 重复提醒和特殊时间的设置困难
3. **通知干扰**: 推送通知可能对用户造成不必要的打扰
4. **状态同步**: 提醒创建、修改、执行状态的一致性

### 缓解措施
1. **常用表达预设**: 提供常见提醒设置的快速选项
2. **分步设置引导**: 将复杂设置拆分为简单的步骤
3. **智能通知策略**: 根据用户行为调整通知频率和时机
4. **状态管理优化**: 建立可靠的状态同步和错误恢复机制

## 后续依赖关系

### 🔗 此故事完成后解锁的故事：
- **故事1.6**: 对话式提醒功能（功能实现）

### 📋 为故事1.6提供的资源：
- 完整的提醒功能界面实现
- 对话式设置的UI组件
- 提醒管理和通知界面
- 适老化友好的时间选择器

## 相关文档引用
- [UX设计规范: 提醒功能章节](../../prd/ux-design.md#提醒功能设计)
- [基础设计系统](./1.1-UI.story.md)