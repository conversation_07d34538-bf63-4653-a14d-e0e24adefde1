from typing import Generator, AsyncGenerator

from sqlalchemy.engine import Engine, create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import Session, sessionmaker

from db.url import get_db_url

# Create SQLAlchemy Engine using a database URL
db_url: str = get_db_url()
db_engine: Engine = create_engine(db_url, pool_pre_ping=True)

# Create async engine for async operations
async_db_url = db_url.replace("postgresql://", "postgresql+asyncpg://")
async_engine = create_async_engine(async_db_url, pool_pre_ping=True)

# Create a SessionLocal class
SessionLocal: sessionmaker[Session] = sessionmaker(autocommit=False, autoflush=False, bind=db_engine)

# Create async session maker
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False
)


def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get a database session.

    Yields:
        Session: An SQLAlchemy database session.
    """
    db: Session = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get an async database session.

    Yields:
        AsyncSession: An SQLAlchemy async database session.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
