# 提醒服务功能说明

## 功能概述

基于Function Calling的提醒服务为用户提供智能的对话式提醒功能。该服务通过火山引擎的Function Calling能力，实现了用户可以通过自然语言对话设置、管理和查询提醒，无需学习复杂的界面操作。

### 核心特性
- **对话式提醒设置**: 用户可通过自然语言"明天下午3点提醒我吃药"来设置提醒
- **智能时间解析**: 支持多种时间表达格式，自动转换为标准时间格式
- **提醒管理**: 支持提醒的增删改查操作
- **个性化确认**: AI角色会根据设定的角色特性提供个性化的确认回复
- **状态跟踪**: 提醒支持多种状态(待触发、已触发、已完成、已取消)

### 技术架构
- **Function Calling集成**: 通过ChatOrchestrationService处理LLM的工具调用请求
- **循环保护机制**: 最大5次工具调用限制，10秒总时间限制
- **降级策略**: 工具执行失败时提供用户友好的错误回复
- **记忆集成**: 提醒操作自动记录到用户记忆系统

## 核心API端点

### 1. 提醒管理API

#### 创建提醒
```
POST /api/v1/reminders
```
**认证**: JWT Bearer Token (必须)
**请求体**:
```json
{
  "content": "吃药",
  "reminder_time": "2024-12-21T15:00:00Z",
  "status": "pending"
}
```
**响应**:
```json
{
  "id": "rem_123456",
  "content": "吃药", 
  "reminder_time": "2024-12-21T15:00:00Z",
  "status": "pending",
  "created_at": "2024-12-20T10:00:00Z"
}
```

#### 获取提醒列表
```
GET /api/v1/reminders
```
**认证**: JWT Bearer Token (必须)
**查询参数**:
- `status`: 提醒状态筛选 (pending|triggered|completed|cancelled)
- `start_date`: 开始日期筛选 (ISO 8601格式)
- `end_date`: 结束日期筛选 (ISO 8601格式)
- `limit`: 返回数量限制 (默认20)
- `offset`: 分页偏移量 (默认0)

**响应**:
```json
{
  "reminders": [
    {
      "id": "rem_123456",
      "content": "吃药",
      "reminder_time": "2024-12-21T15:00:00Z", 
      "status": "pending",
      "created_at": "2024-12-20T10:00:00Z"
    }
  ],
  "total": 1,
  "has_more": false
}
```

#### 更新提醒
```
PUT /api/v1/reminders/{reminder_id}
```
**认证**: JWT Bearer Token (必须)
**请求体**:
```json
{
  "content": "吃药并测血压",
  "reminder_time": "2024-12-21T16:00:00Z",
  "status": "pending"
}
```

#### 删除提醒
```
DELETE /api/v1/reminders/{reminder_id}
```
**认证**: JWT Bearer Token (必须)
**响应**: 204 No Content

### 2. 对话式提醒设置 (通过Function Calling)

提醒设置主要通过现有的对话API自动触发，无需前端直接调用：

#### 文本对话中的提醒设置
```
POST /api/v1/chat/text_message
```
当用户发送类似"明天下午3点提醒我开会"的消息时，后端会：
1. LLM识别提醒意图
2. 调用set_reminder工具
3. 创建提醒并返回确认回复

#### RTC语音对话中的提醒设置  
```
POST /api/v1/chat/rtc_event_handler
```
语音输入同样支持提醒设置，ASR文本会被LLM分析并触发相应工具调用。

## 数据契约

### 提醒数据模型
```typescript
interface Reminder {
  id: string;                    // 提醒唯一标识
  user_id: string;              // 用户ID
  content: string;              // 提醒内容
  reminder_time: string;        // 提醒时间(ISO 8601 UTC格式)
  status: ReminderStatus;       // 提醒状态
  created_at: string;           // 创建时间
  updated_at: string;           // 更新时间
  metadata?: object;            // 扩展元数据
}

type ReminderStatus = 'pending' | 'triggered' | 'completed' | 'cancelled';
```

### 错误响应格式
```typescript
interface ErrorResponse {
  error: {
    code: string;               // 错误代码
    message: string;            // 错误描述
    details?: object;          // 详细错误信息
  };
  request_id: string;           // 请求追踪ID
}
```

### 常见错误代码
- `REMINDER_NOT_FOUND`: 提醒不存在
- `INVALID_TIME_FORMAT`: 时间格式无效
- `UNAUTHORIZED_ACCESS`: 无权限访问他人提醒
- `TIME_IN_PAST`: 提醒时间不能是过去时间
- `CONTENT_TOO_LONG`: 提醒内容过长(最大500字符)

## 调用示例与注意事项

### 1. 认证管理
```typescript
// 实现token自动刷新机制
const apiClient = axios.create({
  baseURL: process.env.API_BASE_URL,
  timeout: 10000
});

apiClient.interceptors.request.use((config) => {
  const token = getStoredToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 处理401错误自动刷新token
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await refreshTokenAndRetry();
    }
    return Promise.reject(error);
  }
);
```

### 2. 错误处理策略
```typescript
async function createReminder(reminderData: CreateReminderRequest) {
  try {
    const response = await apiClient.post('/api/v1/reminders', reminderData);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      // 处理时间格式等业务错误
      showUserFriendlyError(error.response.data.error.message);
    } else if (error.response?.status === 401) {
      // 认证错误，引导重新登录
      redirectToLogin();
    } else if (error.response?.status >= 500) {
      // 服务器错误，稍后重试
      showRetryOption();
    }
    throw error;
  }
}
```

### 3. 时间处理最佳实践
```typescript
// 用户本地时间转UTC存储
function createReminderWithLocalTime(content: string, localTime: Date) {
  const utcTime = localTime.toISOString(); // 自动转换为UTC
  return createReminder({
    content,
    reminder_time: utcTime
  });
}

// 显示时转换为用户本地时间
function displayReminder(reminder: Reminder) {
  const localTime = new Date(reminder.reminder_time).toLocaleString();
  return `${reminder.content} - ${localTime}`;
}
```

### 4. 分页查询示例
```typescript
async function loadReminders(page = 0, pageSize = 20) {
  const params = {
    limit: pageSize,
    offset: page * pageSize,
    status: 'pending' // 只显示待触发的提醒
  };
  
  const response = await apiClient.get('/api/v1/reminders', { params });
  return {
    reminders: response.data.reminders,
    hasMore: response.data.has_more,
    total: response.data.total
  };
}
```

### 5. 对话式提醒集成
```typescript
// 监听对话响应中的提醒确认
function handleChatResponse(response: ChatResponse) {
  // 检查是否包含提醒确认关键词
  if (response.content.includes('提醒已设置') || 
      response.content.includes('已为您安排提醒')) {
    // 刷新提醒列表
    refreshReminderList();
    // 显示成功提示
    showSuccessToast('提醒设置成功');
  }
}
```

### 6. 性能优化建议
- **缓存策略**: 本地缓存提醒列表，减少API调用频率
- **增量同步**: 只同步指定时间范围内的提醒变更
- **防抖处理**: 用户快速切换状态时，延迟API调用
- **分页加载**: 大量提醒时使用虚拟滚动和分页
- **离线支持**: 关键操作失败时支持离线重试

### 7. 安全注意事项
- **权限验证**: 只能访问当前用户的提醒数据
- **输入验证**: 前端验证提醒内容长度和时间格式
- **敏感数据**: 避免在日志中记录用户提醒内容
- **HTTPS通信**: 生产环境必须使用HTTPS
- **Token安全**: 使用SecureStore存储认证token

### 8. 监控建议
- **API响应时间**: 监控提醒API的响应性能
- **创建成功率**: 跟踪提醒创建的成功率
- **用户使用模式**: 分析用户最常设置的提醒类型和时间
- **错误日志**: 记录并分析API调用失败的原因

### 9. 测试建议
**推荐测试场景**:
- 用户说"明天下午3点提醒我开会"验证提醒创建
- 测试过去时间设置提醒的错误处理
- 验证提醒列表的权限隔离(不显示他人提醒)
- 测试网络中断时的离线重试机制
- 验证token过期时的自动刷新和重试 