"""
后端专用的 Pydantic 模型定义
从 shared/contracts/schema.py 迁移而来，避免跨目录依赖
确保后端可以独立部署运行
"""

from datetime import datetime
from typing import Any, Dict, List, Literal, Optional
from pydantic import BaseModel, Field


class User(BaseModel):
    id: str
    device_fingerprint: str  # 修复：对应数据库字段 device_fingerprint
    created_at: datetime
    updated_at: datetime


class UserProfile(BaseModel):
    user_id: str
    nickname: Optional[str] = None
    age_range: Optional[str] = None  # 修复：对应数据库字段 age_range (varchar)
    core_needs: List[str] = Field(default_factory=list)
    interests: List[str] = Field(default_factory=list)  # 新增：对应数据库字段 interests
    communication_style_preference: Optional[str] = None  # 新增：对应数据库字段
    preferences: Dict[str, Any] = Field(default_factory=dict)
    onboarding_completed: bool = False  # 修复：对应数据库字段 onboarding_completed
    allow_chat_analysis: bool = True  # 新增：对应数据库字段 allow_chat_analysis
    personality_summary_ai: Optional[str] = None  # AI生成的性格总结
    created_at: datetime
    updated_at: datetime


class Character(BaseModel):
    id: str
    name: str
    description: Optional[str] = None  # 修复：数据库中可为空
    voice_id: Optional[str] = None  # 修复：数据库中可为空
    personality: Dict[str, Any] = Field(default_factory=dict)
    is_default: bool = False  # 新增：对应数据库字段 is_default
    created_at: datetime


class ChatSession(BaseModel):
    id: str
    user_id: Optional[str] = None  # 修复：数据库中可为空
    character_id: Optional[str] = None  # 修复：数据库中可为空
    topic: Optional[str] = None
    summary: Optional[str] = None
    status: Literal['active', 'ended', 'error', 'completed', 'archived', 'deleted'] = 'active'  # 修复：完整状态枚举
    rtc_task_id: Optional[str] = None
    analysis_status: Literal['pending', 'processing', 'completed', 'failed', 'timeout', 'sync_failed'] = 'pending'  # 新增：对应数据库字段
    last_message_at: Optional[datetime] = None  # 新增：对应数据库字段
    ended_at: Optional[datetime] = None  # 新增：对应数据库字段
    tags: Optional[List[str]] = None  # 新增：对应数据库字段
    metadata: Optional[Dict[str, Any]] = None  # 新增：对应数据库字段
    topic_type: Optional[str] = 'custom'  # 新增：对应数据库字段
    created_at: datetime
    updated_at: datetime


class ChatMessage(BaseModel):
    id: str
    session_id: Optional[str] = None  # 修复：数据库中可为空
    user_id: Optional[str] = None  # 新增：对应数据库字段
    role: Literal['user', 'assistant', 'system', 'tool']  # 修复：添加 tool 角色
    content: str
    content_type: str = 'text'  # 新增：对应数据库字段
    message_type: str = 'text'  # 新增：对应数据库字段
    emotion_category: Optional[str] = None  # 新增：对应数据库字段
    emotion_intensity: Optional[float] = None  # 新增：对应数据库字段
    tokens_used: Optional[int] = None  # 新增：对应数据库字段
    status: str = 'sent'  # 新增：对应数据库字段
    quoted_message_id: Optional[str] = None  # 新增：对应数据库字段
    reactions: Optional[Dict[str, Any]] = None  # 新增：对应数据库字段
    is_edited: bool = False  # 新增：对应数据库字段
    edit_count: int = 0  # 新增：对应数据库字段
    deleted_at: Optional[datetime] = None  # 新增：对应数据库字段
    is_deleted: bool = False  # 新增：对应数据库字段
    structured_data: Optional[Dict[str, Any]] = None  # 新增：对应数据库字段
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None  # 新增：对应数据库字段


# 新增：危机事件模型
class CrisisEvent(BaseModel):
    id: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    risk_level: str
    triggered_keywords: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    response_template: Optional[str] = None
    event_timestamp: datetime


# 新增：用户角色绑定模型
class UserCharacterBinding(BaseModel):
    id: str
    user_id: str
    character_id: str
    is_active: bool = True
    created_at: datetime
    updated_at: datetime


# 新增：RTC会话模型
class RtcSession(BaseModel):
    session_id: str
    user_id: str
    character_id: Optional[str] = None
    task_id: Optional[str] = None
    room_id: Optional[str] = None
    status: str = 'preparing'
    voice_config: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    error_message: Optional[str] = None
    volcano_response: Optional[Dict[str, Any]] = None


# API 请求和响应模型
class ApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None
    error: Optional[str] = None


class TextMessageRequest(BaseModel):
    """
    对应 POST /api/v1/chat/text_message 接口
    """
    message: str
    sessionId: str
    characterId: Optional[str] = None


class PrepareSessionRequest(BaseModel):
    """
    对应 POST /api/v1/rtc/prepare_session 接口
    用户ID从JWT Token获取，不再在请求体中传递
    """
    sessionId: str
    characterId: str


class RtcCredentials(BaseModel):
    token: str
    roomId: str
    userId: str
    taskId: str


class PrepareSessionResponse(ApiResponse):
    """
    对应 POST /api/v1/rtc/prepare_session 接口的成功响应数据
    """
    data: RtcCredentials


# 火山引擎相关模型
class VolcanoRTCRequest(BaseModel):
    """火山引擎 RTC 请求模型"""
    messages: List[Dict[str, str]]
    stream: bool = True
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    model: str
    top_p: Optional[float] = None
    custom: Optional[str] = None  # JSON 字符串格式的自定义参数


class VolcanoCustomData(BaseModel):
    """解析 custom 字段的数据模型"""
    session_id: str
    user_id: str
    character_id: str


# 提醒相关模型
class Reminder(BaseModel):
    """提醒模型"""
    id: Optional[str] = None
    user_id: Optional[str] = None  # 修复：数据库中可为空
    content: str
    reminder_time: datetime
    status: Literal['pending', 'triggered', 'completed', 'cancelled'] = 'pending'
    pattern_id: Optional[str] = None
    function_call_payload: Optional[Dict[str, Any]] = None  # 新增：对应数据库字段
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ReminderPattern(BaseModel):
    """提醒模式模型 - 支持重复提醒"""
    id: Optional[str] = None
    user_id: str
    name: str
    pattern_type: Literal['daily', 'weekly', 'monthly', 'custom'] = 'daily'
    pattern_config: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class UserReminderSettings(BaseModel):
    """用户提醒偏好设置模型"""
    user_id: str
    notification_enabled: bool = True
    default_reminder_advance: int = 15  # 提前15分钟提醒
    preferred_time_format: Literal['12h', '24h'] = '24h'
    time_zone: str = 'UTC'
    settings: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


# API 请求模型
class CreateReminderRequest(BaseModel):
    """创建提醒请求模型"""
    content: str
    reminder_time: datetime
    pattern_id: Optional[str] = None


class UpdateReminderRequest(BaseModel):
    """更新提醒请求模型"""
    content: Optional[str] = None
    reminder_time: Optional[datetime] = None
    status: Optional[Literal['pending', 'triggered', 'completed', 'cancelled']] = None


class ReminderListResponse(ApiResponse):
    """提醒列表响应模型"""
    data: List[Reminder]


class ReminderResponse(ApiResponse):
    """单个提醒响应模型"""
    data: Reminder


# Function Calling 相关模型
class ToolCall(BaseModel):
    """工具调用模型"""
    id: str
    name: str
    arguments: Dict[str, Any]


class ToolResult(BaseModel):
    """工具执行结果模型"""
    tool_call_id: str
    content: str
    success: bool = True
    error: Optional[str] = None


class FunctionCallingRequest(BaseModel):
    """Function Calling 请求模型"""
    tool_calls: List[ToolCall]


class FunctionCallingResponse(BaseModel):
    """Function Calling 响应模型"""
    results: List[ToolResult]


# 用户设置相关模型
class QuietHoursSettings(BaseModel):
    """安静时间设置"""
    enabled: bool = False
    start: Optional[str] = None  # "HH:MM" 格式
    end: Optional[str] = None    # "HH:MM" 格式


class NotificationSettings(BaseModel):
    """通知设置"""
    enabled: bool = True
    quietHours: QuietHoursSettings = Field(default_factory=QuietHoursSettings)


class UserSettings(BaseModel):
    """用户设置完整模型 - 对应数据库user_settings表"""
    user_id: str
    theme: Literal['auto', 'light', 'dark'] = 'auto'
    font_size: Literal['small', 'medium', 'large', 'extra_large'] = 'large'  # 数据库字段名
    high_contrast: bool = False  # 数据库字段名
    language: Literal['zh-CN', 'en-US'] = 'zh-CN'
    notifications_enabled: bool = True  # 数据库字段名
    quiet_hours_enabled: bool = False  # 数据库字段名
    quiet_hours_start: Optional[str] = None  # "HH:MM" 格式
    quiet_hours_end: Optional[str] = None    # "HH:MM" 格式
    updated_at: Optional[datetime] = None


class UserSettingsUpdate(BaseModel):
    """用户设置更新请求模型 - 支持部分更新(PATCH语义)"""
    userId: Optional[str] = None  # 前端可能传递，但后端会忽略使用JWT中的user_id
    theme: Optional[Literal['auto', 'light', 'dark']] = None
    font_size: Optional[Literal['small', 'medium', 'large', 'extra_large']] = None
    high_contrast: Optional[bool] = None
    language: Optional[Literal['zh-CN', 'en-US']] = None
    notifications_enabled: Optional[bool] = None
    quiet_hours_enabled: Optional[bool] = None
    quiet_hours_start: Optional[str] = None  # "HH:MM" 格式
    quiet_hours_end: Optional[str] = None    # "HH:MM" 格式


class UserSettingsResponse(ApiResponse):
    """用户设置API响应模型"""
    data: UserSettings


# 认证相关模型
class DeviceInfo(BaseModel):
    """设备信息模型"""
    device_id: str
    platform: str
    app_version: str


class AnonymousLoginRequest(BaseModel):
    """匿名登录请求模型"""
    device_info: DeviceInfo


class AuthTokens(BaseModel):
    """认证令牌模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class AuthResponse(ApiResponse):
    """认证响应模型"""
    data: AuthTokens


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str


# 会话创建相关模型
class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    topic: Optional[str] = None
    characterId: Optional[str] = None
    topicType: Optional[str] = 'custom'
    metadata: Optional[Dict[str, Any]] = None


class SessionListResponse(ApiResponse):
    """会话列表响应模型"""
    data: List[ChatSession]


class SessionResponse(ApiResponse):
    """单个会话响应模型"""
    data: ChatSession


# 用户画像相关模型
class OnboardingProfileData(BaseModel):
    """引导流程画像数据模型"""
    core_needs: List[str]
    interests: List[str]
    communication_style_preference: str
    allow_chat_analysis: bool


class UserProfileResponse(ApiResponse):
    """用户画像响应模型"""
    data: UserProfile


# 角色相关模型
class CharacterListResponse(ApiResponse):
    """角色列表响应模型"""
    data: List[Character]


class CharacterResponse(ApiResponse):
    """单个角色响应模型"""
    data: Character


class CharacterBindRequest(BaseModel):
    """角色绑定请求模型"""
    character_id: str
