
基于对您后端API工作流程的深入分析，我将创建详细的流程图来展示RTC、SSE和提醒调度功能。

## 1. RTC (Real-time Communication) API 工作流程
graph TD
    A[Client Request] --> B{Request Type}
    
    %% RTC会话管理流程
    B -->|prepare_session| C[RTC Session Management]
    C --> C1[JWT Token Validation]
    C1 --> C2[Extract user_id from JWT]
    C2 --> C3[Check Concurrent Session Limit]
    C3 --> C4[Call Volcano Engine StartVoiceChat]
    C4 --> C5[Retry Mechanism<br/>3 attempts + exponential backoff]
    C5 --> C6[Store Session in Database]
    C6 --> C7[Return RTC Credentials]
    
    %% RTC会话结束流程
    B -->|end_session| D[End RTC Session]
    D --> D1[JWT Token Validation]
    D1 --> D2[Call Volcano Engine StopVoiceChat]
    D2 --> D3[Update Session Status]
    D3 --> D4[Trigger Background Analysis Task]
    D4 --> D5[SessionAnalysisService.analyze_session_and_sync_memory]
    D5 --> D6[Return Success Response]
    
    %% Webhook回调处理
    B -->|rtc_event_handler| E[RTC Webhook Handler]
    E --> E1[Signature Verification<br/>HMAC SHA256 + Timestamp]
    E1 --> E2[IP Whitelist Check]
    E2 --> E3[Event Type Dispatcher]
    
    E3 -->|asr_result| F[ASR Event Processing]
    F --> F1[Extract Text from Payload]
    F1 --> F2[Parse Context Data<br/>userId, sessionId, characterId]
    F2 --> F3[Background Task: Save User Message]
    F3 --> F4[ChatOrchestrationService.handle_message]
    F4 --> F5[Crisis Detection Check]
    F5 -->|Crisis Detected| F6[Return Scripted Crisis Response]
    F5 -->|No Crisis| F7[Memory Context Retrieval]
    F7 --> F8[LLM Processing with Tool Calling]
    F8 --> F9[Background Task: Save AI Response]
    F9 --> F10[Return AI Response for TTS]
    
    E3 -->|function_call| G[Function Call Event]
    G --> G1[Parse Tool Call Instructions]
    G1 --> G2[Execute Tool via ToolExecutorService]
    G2 --> G3[Update Voice Chat API Call]
    G3 --> G4[Return Empty Response<br/>Await Async Callback]
    
    E3 -->|VoiceChat| H[Voice Chat Status Event]
    H --> H1[Log Status Change]
    H1 --> H2[Return Success Response]
    
    %% 错误处理
    C5 -->|API Failed| C8[Error Classification]
    C8 -->|401/403| C9[Return 502 No Retry]
    C8 -->|503/timeout| C10[Return 503 with Retry]
    C8 -->|429| C11[Return 429 Rate Limited]
    
    style F5 fill:#ff6b6b
    style F6 fill:#feca57
    style C5 fill:#48cae4
    style G4 fill:#a8dadc

## 2. SSE (Server-Sent Events) 流式响应工作流程
graph TD
    A[Client SSE Request] --> B{SSE Endpoint}
    
    %% 文本聊天SSE流程
    B -->|/chat/text_message| C[Text Chat SSE Handler]
    C --> C1[JWT Token Validation]
    C1 --> C2[Extract user_id from JWT]
    C2 --> C3[Check Concurrent Connection Limit<br/>Max 3 per user]
    C3 -->|Limit Exceeded| C4[Return 429 Too Many Requests]
    C3 -->|Under Limit| C5[Increment Connection Counter]
    C5 --> C6[Create SSE Generator]
    
    C6 --> C7[Client Disconnection Check]
    C7 -->|Disconnected| C8[Early Return]
    C7 -->|Connected| C9[ChatOrchestrationService.handle_message_stream]
    
    %% ChatOrchestrationService流式处理
    C9 --> D[Crisis Detection First]
    D -->|Crisis Detected| D1[Async Ban User Audio Stream]
    D1 --> D2[Generate Scripted Crisis Response Stream]
    D2 --> D3[Send Crisis Response Chunks]
    D3 --> D4[End Stream]
    
    D -->|No Crisis| E[Memory Context Retrieval<br/>3 second timeout]
    E --> E1[Build Messages Array]
    E1 --> E2[LLM Stream API Call]
    E2 --> E3[Real-time Chunk Processing]
    
    E3 --> F[SSE Event Generation Loop]
    F --> F1[Check Client Disconnection]
    F1 -->|Disconnected| F2[Break Loop]
    F1 -->|Connected| F3[Process LLM Chunk]
    F3 -->|Error Chunk| F4[Send Error Event]
    F3 -->|Text Chunk| F5[Send Text Chunk Event]
    F5 --> F6[Accumulate Full Response]
    F6 --> F1
    
    F2 --> G[Stream End Processing]
    G --> G1[Send Stream End Event]
    G1 --> G2[Decrement Connection Counter]
    G2 --> G3[Clean Up Resources]
    
    %% 聊天SSE流程（另一个实现）
    B -->|/chat/message| H[Chat SSE Handler]
    H --> H1[JWT Token Validation]
    H1 --> H2[Background Task: Save User Message]
    H2 --> H3[Crisis Detection]
    H3 --> H4[Memory Processing Task]
    H4 --> H5[AI Response Generation]
    H5 --> H6[Background Task: Save AI Message]
    H6 --> H7[Send Events: text_chunk, crisis_alert, stream_end]
    
    %% 错误处理机制
    E3 -->|LLM Error| I[Error Handling]
    I --> I1[Log Error Details]
    I1 --> I2[Send Error Event in Stream]
    I2 --> I3[Ensure Stream Proper End]
    I3 --> G2
    
    %% 超时处理
    E2 -->|30s Timeout| J[Timeout Handling]
    J --> J1[Send Timeout Error Event]
    J1 --> G2
    
    %% 性能监控
    E2 --> K[Performance Metrics]
    K --> K1[Track First Chunk Latency]
    K1 --> K2[Monitor Total Duration]
    K2 --> K3[Log Performance Stats]
    
    style D fill:#ff6b6b
    style D2 fill:#feca57
    style F4 fill:#ff6b6b
    style J1 fill:#f1c40f
    style K1 fill:#74b9ff

## 3. 提醒调度功能 (Reminder Scheduling) 工作流程
graph TD
    A[Reminder Request] --> B{Request Type}
    
    %% CRUD API操作
    B -->|GET /reminders| C[Get Reminders List]
    C --> C1[JWT Token Validation]
    C1 --> C2[Extract user_id from JWT]
    C2 --> C3[Apply Filters<br/>status, date_range, pagination]
    C3 --> C4[RLS Security Check]
    C4 --> C5[Query User Reminders from Database]
    C5 --> C6[Return Paginated Results]
    
    B -->|POST /reminders| D[Create Reminder]
    D --> D1[JWT Token Validation]
    D1 --> D2[Validate Request Data]
    D2 --> D3[Time Parsing with Arrow Library]
    D3 --> D4[UTC Time Conversion]
    D4 --> D5[Store in Database]
    D5 --> D6[Memory Service Integration]
    D6 --> D7[Return Created Reminder]
    
    B -->|PUT /reminders/id| E[Update Reminder]
    E --> E1[JWT Token Validation]
    E1 --> E2[Ownership Verification]
    E2 --> E3[Update Database Record]
    E3 --> E4[Return Updated Reminder]
    
    B -->|DELETE /reminders/id| F[Delete Reminder]
    F --> F1[JWT Token Validation]
    F1 --> F2[Ownership Verification]
    F2 --> F3[Soft Delete or Hard Delete]
    F3 --> F4[Return 204 No Content]
    
    %% Function Calling工具调用流程
    B -->|Function Calling| G[Tool-based Reminder Creation]
    G --> G1[LLM Detects Reminder Intent]
    G1 --> G2[Generate Tool Call: set_reminder]
    G2 --> G3[ToolExecutorService.execute_tool_calls]
    
    G3 --> H[Tool Execution Process]
    H --> H1[Extract Arguments<br/>content, time]
    H1 --> H2[Validate Required Fields]
    H2 --> H3[Time Parsing with Fallback]
    H3 -->|Parse Success| H4[ReminderService.create_reminder_from_tool]
    H3 -->|Parse Failed| H5[Return User-friendly Error]
    
    H4 --> I[Advanced Time Parsing]
    I --> I1[Arrow Library Primary Parser]
    I1 -->|Success| I2[UTC Conversion]
    I1 -->|Failed| I3[Natural Language Fallback]
    I3 --> I4[Chinese Locale Support<br/>zh_CN, Asia/Shanghai]
    I4 --> I5[Relative Time Processing]
    I5 -->|Success| I2
    I5 -->|Failed| I6[Return Parsing Error]
    
    I2 --> J[Database & Memory Integration]
    J --> J1[Create Reminder Record]
    J1 --> J2[Memory Service Recording]
    J2 --> J3[User Behavior Pattern Analysis]
    J3 --> J4[Return Success Message]
    
    %% 工具调用流程中的错误处理
    H2 -->|Validation Failed| K[Error Handling]
    K --> K1[Generate User-friendly Message]
    K1 --> K2[Log Error Details]
    K2 --> K3[Return Tool Result with Error]
    
    %% 记忆服务集成
    J2 --> L[Memory Service Tasks]
    L --> L1[Record Reminder Creation Event]
    L1 --> L2[Update User Preference Patterns]
    L2 --> L3[Context for Future Conversations]
    L3 --> L4[Graceful Degradation if Memory Fails]
    
    %% 时间解析示例流程
    I4 --> M[Time Expression Examples]
    M --> M1["明天下午3点" → Next Day 15:00 UTC]
    M1 --> M2["1小时后" → Current Time + 1 hour]
    M2 --> M3["下周一上午9点" → Next Monday 09:00 UTC]
    M3 --> I2
    
    %% 性能和监控
    G3 --> N[Performance Monitoring]
    N --> N1[Track Tool Execution Time]
    N1 --> N2[Monitor Memory Service Latency]
    N2 --> N3[Log Successful/Failed Reminders]
    
    style H3 fill:#a8dadc
    style I1 fill:#48cae4
    style I3 fill:#f1c40f
    style K1 fill:#ff6b6b
    style L4 fill:#feca57
    style M1 fill:#74b9ff


## 4. 核心编排服务 (ChatOrchestrationService) 工作流程
graph TD
    A[Message Input] --> B[ChatOrchestrationService.handle_message]
    
    %% 危机检测优先级
    B --> C[STAGE 0: Crisis Detection]
    C --> C1[CrisisDetectionService.detect]
    C1 -->|Crisis Detected| C2[Log CRISIS Event]
    C2 --> C3[Ban User Audio Stream<br/>Volcano RTC API]
    C3 --> C4[Return Scripted Crisis Response<br/>Include Hotline: 400-161-9995]
    C1 -->|No Crisis| D[STAGE 1: Memory Context Retrieval]
    
    %% 记忆上下文检索
    D --> D1[MemoryService.retrieve_context]
    D1 -->|Success| D2[Extract User History & Patterns]
    D1 -->|Failed| D3[Graceful Degradation<br/>Continue with Empty Context]
    D2 --> E[STAGE 2: Prompt Building]
    D3 --> E
    
    %% 提示词构建
    E --> E1[PromptBuilderService.build_prompt]
    E1 --> E2[Combine System Prompt + Memory Context + User Message]
    E2 --> E3[Add Character Personality & Constraints]
    E3 --> F[STAGE 3: LLM Processing with Tool Calling]
    
    %% LLM处理和工具调用
    F --> F1[ToolExecutorService.get_tool_definitions]
    F1 --> F2[LLMProxyService.call_llm<br/>Include Tool Definitions]
    F2 --> F3[Circuit Breaker Pattern<br/>3 Retry + Exponential Backoff]
    F3 -->|Tool Calls Detected| G[Function Calling Workflow]
    F3 -->|No Tool Calls| H[Direct LLM Response]
    
    %% Function Calling异步工作流
    G --> G1[Parse Tool Call Instructions]
    G1 --> G2[Validate Tool Call Format & Parameters]
    G2 --> G3[Set User Context<br/>Thread-safe with ContextVar]
    G3 --> G4[Execute Tools via ToolExecutorService]
    
    G4 --> G5[Tool Execution Results]
    G5 -->|RTC Context Available| G6[Async Mode: Send to Volcano RTC<br/>UpdateVoiceChat API]
    G5 -->|No RTC Context| G7[Sync Mode: Return Tool Results]
    G6 --> G8[Return Empty Response<br/>Await Webhook Callback]
    G7 --> G9[Continue LLM Conversation Loop]
    
    %% 工具执行细节
    G4 --> I[Tool Execution Details]
    I -->|set_reminder| I1[ReminderService.create_reminder_from_tool]
    I1 --> I2[Arrow Time Parsing + UTC Conversion]
    I2 --> I3[Database Transaction + Memory Recording]
    I -->|get_user_profile| I4[UserProfileService.get_profile]
    I -->|search_memory| I5[MemoryService.search_memories]
    
    %% 循环保护机制
    G9 --> J[Function Calling Loop Protection]
    J --> J1[Check Max Tool Calls: 5]
    J1 --> J2[Check Max Time Limit: 10s]
    J2 -->|Under Limits| F2
    J2 -->|Exceeded Limits| J3[Return Loop Protection Message]
    
    %% 错误处理和降级
    F3 -->|LLM Service Failed| K[Service Degradation]
    K --> K1[Circuit Breaker Open<br/>After 5 Consecutive Failures]
    K1 --> K2[Return Emergency Fallback Response]
    
    G4 -->|Tool Execution Failed| L[Tool Error Handling]
    L --> L1[Log Tool Execution Error]
    L1 --> L2[Return User-friendly Error Message]
    L2 --> L3[Continue Conversation Flow]
    
    %% 性能监控
    B --> M[Performance Monitoring]
    M --> M1[Track Stage Durations<br/>Memory: <5s, LLM: <30s, Tools: <10s]
    M1 --> M2[Log Detailed Performance Metrics]
    M2 --> M3[Monitor Service Health Status]
    
    %% 异步任务
    H --> N[Background Tasks]
    N --> N1[Update Memory Service<br/>Record Conversation Context]
    N1 --> N2[Save Message to Database<br/>With Retry Mechanism]
    N2 --> N3[Analytics & User Behavior Tracking]
    
    %% 流式响应变体
    B -->|Stream Mode| O[handle_message_stream]
    O --> O1[Same Crisis Detection Priority]
    O1 --> O2[Real-time LLM Streaming]
    O2 --> O3[SSE Chunk Processing]
    O3 --> O4[30s Timeout Protection]
    O4 --> O5[Client Disconnection Detection]
    
    style C fill:#ff6b6b
    style C4 fill:#feca57
    style D3 fill:#a8dadc
    style K2 fill:#f1c40f
    style G6 fill:#74b9ff
    style J3 fill:#ff9ff3
    style M1 fill:#00b894

## 5. 后端API系统架构概览
graph TB
    %% 客户端层
    subgraph "Client Layer"
        A1[React Native App]
        A2[Web Browser SSE]
        A3[Volcano RTC Client]
    end
    
    %% API网关层
    subgraph "API Gateway Layer"
        B1[FastAPI Main Router]
        B2[v1_router.py]
        B3[JWT Auth Middleware]
        B4[Rate Limiting]
    end
    
    %% 路由层
    subgraph "Route Layer"
        C1[auth_routes.py<br/>Authentication APIs]
        C2[rtc_routes.py<br/>RTC Session Management]
        C3[rtc_webhook_routes.py<br/>Volcano RTC Callbacks]
        C4[text_chat_routes.py<br/>SSE Text Chat]
        C5[chat_sse_routes.py<br/>SSE Chat Alternative]
        C6[reminder_routes.py<br/>CRUD Reminders]
        C7[sessions_routes.py<br/>Chat Session Management]
        C8[user_routes.py<br/>User Profile Management]
    end
    
    %% 核心服务层
    subgraph "Core Service Layer"
        D1[ChatOrchestrationService<br/>Central Message Coordinator]
        D2[CrisisDetectionService<br/>Safety First Priority]
        D3[LLMProxyService<br/>Volcano LLM Integration]
        D4[MemoryService<br/>Zep/Mem0 Integration]
        D5[ToolExecutorService<br/>Function Calling Handler]
        D6[ReminderService<br/>Scheduling Logic]
        D7[SessionAnalysisService<br/>Post-Chat Analysis]
        D8[VolcanoClientService<br/>RTC API Client]
    end
    
    %% 外部服务层
    subgraph "External Services"
        E1[Volcano Engine RTC<br/>Real-time Communication]
        E2[Volcano Engine LLM<br/>Doubao AI Model]
        E3[Supabase Database<br/>PostgreSQL + RLS]
        E4[Zep Memory Service<br/>Long-term Memory]
        E5[Mem0 Memory Service<br/>Alternative Memory]
    end
    
    %% 主要工作流程连接
    A1 -->|HTTP/JWT| B1
    A2 -->|SSE Connection| B1
    A3 -->|Webhook Callback| B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
    B4 --> C5
    B4 --> C6
    B4 --> C7
    B4 --> C8
    
    %% 核心编排流程
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    
    D1 -->|Crisis Check| D2
    D1 -->|Memory Context| D4
    D1 -->|LLM Processing| D3
    D1 -->|Tool Execution| D5
    D1 -->|RTC Control| D8
    
    %% 工具服务集成
    D5 -->|Reminder Creation| D6
    C6 -->|CRUD Operations| D6
    
    %% 会话后分析
    C2 -->|End Session| D7
    C7 -->|End Session| D7
    
    %% 外部服务集成
    D3 -->|API Calls| E2
    D4 -->|Memory Operations| E4
    D4 -->|Memory Operations| E5
    D6 -->|Data Storage| E3
    D7 -->|Data Storage| E3
    D8 -->|RTC Operations| E1
    
    %% 关键工作流程标识
    subgraph "Key Workflows"
        F1[🔄 RTC Workflow<br/>Session → Webhook → Response]
        F2[📡 SSE Workflow<br/>Request → Stream → Chunks]
        F3[⏰ Reminder Workflow<br/>Intent → Parse → Schedule]
        F4[🧠 Core Orchestration<br/>Crisis → Memory → LLM → Tools]
    end
    
    %% 数据流方向
    style D1 fill:#ff6b6b,stroke:#333,stroke-width:3px
    style D2 fill:#feca57,stroke:#333,stroke-width:2px
    style E1 fill:#74b9ff,stroke:#333,stroke-width:2px
    style E2 fill:#74b9ff,stroke:#333,stroke-width:2px
    style E3 fill:#00b894,stroke:#333,stroke-width:2px
    style B3 fill:#fdcb6e,stroke:#333,stroke-width:2px

## 后端API工作流程综合分析

基于对您的 `@/agent-api` 目录的深入分析，我已经创建了五个详细的流程图，展示了系统的核心工作流程：

### 📊 **系统架构特点**

**核心设计模式：**
- **安全优先**：JWT认证 + RLS数据隔离 + 火山引擎签名验证
- **危机检测优先**：所有消息处理前的强制安全检查
- **优雅降级**：外部服务失败时的完整容错机制
- **异步处理**：Function Calling、会话分析、记忆更新的后台任务
- **性能监控**：详细的响应时间跟踪和资源管理

### 🔄 **RTC工作流程核心特性**

1. **会话管理三重保护**：并发限制、重试机制、状态跟踪
2. **Webhook事件分发**：ASR、Function Calling、状态变化的统一处理
3. **异步回调机制**：工具调用结果通过UpdateVoiceChat API返回
4. **完整的错误分类**：401/403不重试，503重试，429智能延迟

### 📡 **SSE流式响应特色**

1. **双重实现**：`text_chat_routes.py` 和 `chat_sse_routes.py` 两套方案
2. **连接管理**：每用户最多3个并发连接，断开检测，超时保护
3. **流内错误处理**：异常转换为error事件，确保连接稳定
4. **真实流式响应**：首字延迟<1秒，直接传递LLM数据块

### ⏰ **提醒调度系统亮点**

1. **智能时间解析**：Arrow库 + 中文自然语言 + 相对时间支持
2. **Function Calling集成**：LLM意图识别自动触发提醒创建
3. **记忆服务联动**：用户行为模式记录和偏好学习
4. **完整CRUD**：REST API + 分页 + 筛选 + 权限验证

### 🧠 **ChatOrchestrationService编排策略**

1. **分阶段处理**：危机检测 → 记忆检索 → 提示构建 → LLM+工具调用
2. **工具调用保护**：最大5次调用，10秒超时，循环检测
3. **线程安全设计**：ContextVar用户上下文，避免并发竞态
4. **性能边界控制**：记忆检索5秒，LLM处理30秒，工具执行10秒

### 🔧 **关键技术实现**

**认证授权架构：**
```
JWT Token (sub字段) → 用户ID提取 → RLS数据隔离 → 权限验证
```

**危机干预机制：**
```
消息输入 → 关键词检测 → 音频流禁用 → 脚本化回复 → 热线提供
```

**Function Calling流程：**
```
LLM识别意图 → 工具调用生成 → 参数验证 → 执行业务逻辑 → 结果返回
```

**记忆服务集成：**
```
Zep/Mem0双重支持 → 上下文检索 → 对话增强 → 异步更新 → 优雅降级
```

### 🎯 **系统优势总结**

1. **高可靠性**：多层容错，优雅降级，完整监控
2. **高安全性**：多重身份验证，数据隔离，危机检测
3. **高性能**：异步处理，连接复用，智能缓存
4. **高扩展性**：插件化工具系统，统一编排服务
5. **用户友好**：自然语言处理，智能提醒，实时交互

这套后端API系统展现了企业级的设计水准，特别是在安全性、可靠性和用户体验方面的深度考虑。所有核心功能都围绕ChatOrchestrationService这个中央编排器构建，形成了一个高度协调的分布式系统架构。