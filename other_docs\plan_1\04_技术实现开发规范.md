
---

### **《“心桥”AI亲情伴侣：技术实现与交付详细方案》**

---

### 🌐 1️⃣ 功能模块清单

**实现原理：** 此清单将产品功能（What）与技术目标（Why）强绑定，确保开发团队理解每个模块对于构筑“关系式”陪伴的核心价值。

| 功能模块 | 📌 功能目标 | 📌 核心逻辑 | 📌 与用户交互的点 | 📖 可替代方案 |
| :--- | :--- | :--- | :--- | :--- |
| **无感身份系统** | 在用户无感知的情况下，通过后台匿名设备ID自动创建并识别用户身份。 | 1. 客户端首次启动生成唯一、持久的设备ID。<br>2. 调用后端接口，用此ID注册匿名用户并换取JWT会话令牌。 | 无直接交互，对用户完全透明。 | **手机号/微信一键登录：** 虽然便捷，但仍会增加用户的初始操作和心理门槛，违背了“零学习成本”原则，故MVP阶段不采用。 |
| **角色共创流程** | 让用户通过对话为AI设定身份、命名，并最终确认其声音。 | 1. 通过一系列引导式对话收集用户偏好。<br>2. 将用户的选择（名字、角色、声音）持久化到数据库，与用户ID关联。<br>3. AI后续所有交互都必须加载并遵循此配置。 | 对话式引导、选项点击（角色图标、声音确认按钮）。 | **传统的设置向导页面：** 会破坏沉浸式体验，增加认知负荷，让用户感觉在“配置软件”而非“结识朋友”，故不采用。 |
| **核心对话交互** | 提供一个以“按住说话”为唯一核心输入方式的、极致简洁的对话界面，并提供清晰的交互反馈。 | 1. 客户端捕获用户语音，上传至后端。<br>2. 后端编排ASR->LLM->TTS流程，返回文本和音频URL。<br>3. 客户端渲染对话气泡并自动播放音频。 | “按住说话”按钮的按下、松开操作。 | **文本输入框：** 会给不擅长打字的老年用户带来巨大障碍，违背“语音优先”原则，MVP阶段不作为主要交互。 |
| **分层记忆系统** | 实现短期对话记忆、长期身份记忆和用户可控的事实记忆。 | 1. **短期：** 在每次请求LLM时，附带上最近N轮的对话历史。<br>2. **长期：** 在对话开始前，从数据库加载用户的身份记忆，注入到Prompt中。<br>3. **可控：** 设计特定的NLU意图，识别用户的“记住/忘记”指令，并对数据库进行操作。 | 无直接交互，体现在AI的对话内容和行为中。 | **将会话历史完全存储在客户端：** 这样做虽然能降低后端成本，但无法实现跨设备漫游，也无法支持更复杂的长期记忆和分析，不符合长期战略。 |
| **对话式提醒** | 用户可通过自然语言设置提醒，AI进行语音复述确认，并在指定时间以角色语音进行温柔提醒。 | 1. NLU模块识别对话中的提醒意图（时间、事件）。<br>2. 任务被存储到数据库，并由Cron Job定时轮询。<br>3. 触发时，通过系统推送服务将提醒内容送达客户端。 | 对话式输入提醒需求。 | **传统的闹钟/日历界面：** 增加了学习成本，破坏了产品的“陪伴”心流，将“关怀”降级为“工具”。 |
| **危机响应协议(V1)** | 内置基础的危机信号识别与脚本化干预机制。 | 1. KWS和SER模型实时监测用户输入。<br>2. 检测到危机信号时，立即中断自由对话，强制切换到基于RAG的、安全的干预脚本。<br>3. 记录危机事件，并为未来的人工上报预留接口。 | 无直接交互，被动触发。用户感知到的是AI对话风格和内容的突然变化。 | **无此功能：** 将带来巨大的伦理和安全风险，对于情感支持类产品是不可接受的。 |

---

### 🌐 2️⃣ 技术实现要点

**实现原理：** 技术选型与实现方式服务于**“快速验证、稳健运行、未来可扩展”**三大目标。我们采用成熟的BaaS平台加速开发，同时通过架构解耦，为未来的技术演进和供应商替换预留空间。

| 功能模块 | 📌 推荐实现方式 | 📌 涉及的框架/库 | 📌 数据结构/API | 📖 为什么推荐这样做 | 📌 可扩展性方案 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **无感身份系统** | 1. 客户端使用 `expo-application` 获取唯一标识符。<br>2. 在Supabase中创建自定义的 `rpc` 函数，如 `create_anonymous_user`，该函数接收设备ID，创建`auth.users`和`public.users`记录，并返回JWT。 | `expo-application`, `@supabase/supabase-js` | **Data:** `users`表。<br>**API:** `POST /rpc/create_anonymous_user` | **安全与便捷的平衡。** 利用Supabase强大的Auth体系，同时为用户提供无缝的首次体验。RPC函数将复杂逻辑封装在后端，保证了安全性。 | 未来可增加`link_wechat`等RPC函数，将匿名账户与社交账户绑定。 |
| **角色共创流程** | 将用户的选择（名字、角色、声音）作为JSON对象，存储在`ai_profiles`表中。 | `@supabase/supabase-js` | **Data:** `ai_profiles`表，`user_id`为外键。 | **数据持久化。** 将配置与用户强绑定，便于后续所有后端函数加载并应用这个人设。 | - 声音库可以从静态列表扩展为独立的`voices`表。<br>- 角色可以增加更多维度，如“性格”、“幽默感”等字段。 |
| **核心对话交互** | 后端使用Supabase Edge Function (`/functions/chat/index.ts`)作为核心中间件，编排对火山引擎ASR/LLM/TTS的调用。 | **Client:** `expo-av` (录音), `axios` (请求)<br>**Server:** `Deno`, `fetch` (调用外部API) | **API:** `POST /chat`, `Authorization: Bearer <JWT>` | **逻辑集中与解耦。** 将复杂的AI服务调用逻辑封装在单一的云函数中，客户端只需进行一次简单的API请求，极大地简化了客户端逻辑并保护了第三方API密钥。 | - **流式响应：** 升级为WebSocket或HTTP流，实现TTS语音的流式传输，降低首包时间。<br>- **模型抽象层：** 在中间件中增加一个模型抽象层，未来可以轻松切换或混合使用不同供应商的LLM。 |
| **分层记忆系统** | 1. **短期：** 在调用`/chat`函数时，从`messages`表查询最近20条记录，拼接成上下文。<br>2. **长期：** 在函数开始时，根据JWT中的`user_id`，从`ai_profiles`表加载人设信息。<br>3. **可控：** 在`/chat`函数中增加NLU意图识别，若识别为“记忆操作”，则调用`rpc`函数（如`add_fact`, `delete_fact`）操作新的`facts`表。 | **Server:** `Pgtle` (数据库客户端), `Supabase NLU` (⚠️**TODO:** 需调研Supabase是否有内置或推荐的NLU方案，否则需集成第三方) | **Data:** `messages`, `ai_profiles`, `facts` (新表) | **性能与功能的平衡。** 短期记忆通过简单的SQL查询实现，性能高。长期身份记忆与核心人设绑定。事实记忆通过独立的表和RPC实现，逻辑清晰，易于管理。 | 增加向量数据库（如Supabase的`pg_vector`扩展），将事实记忆和长期对话历史向量化，实现更智能、更模糊的记忆检索。 |
| **对话式提醒** | 使用Supabase Cron Jobs (`supabase/config.toml`中定义)定时触发一个专门的Edge Function（如`/functions/trigger-reminders/`）。 | **Server:** `Supabase Cron Jobs`, `expo-server-sdk` (推送) | **Data:** `reminders`表。<br>**API:** Supabase Cron Job的内部调用。 | **可靠与解耦。** 将耗时的、周期的任务与核心的、实时的对话任务分离，避免相互影响。使用`expo-server-sdk`能可靠地向Expo应用发送推送通知。 | 对接短信或电话API，作为App推送失败时的备用高可靠性提醒通道。 |
| **危机响应协议(V1)** | 在`/chat`函数中，增加一个前置的“安全中间件层”。该层首先对ASR识别出的文本进行KWS（关键词）匹配和SER（情感）分析。 | **Server:** ⚠️**TODO:** 需要引入轻量级的中文关键词匹配库和预训练的语音情感识别模型（或API）。 | **API:** 无对外API，是`/chat`函数的内部逻辑。 | **安全前置。** 在进入LLM自由生成之前进行拦截，是保证危机干预能够被可靠触发、且不被LLM“绕过”的关键。 | **集成专业平台。** 将上报接口直接对接到成熟的、拥有专业人工坐席的第三方心理健康或危机干预平台。 |

---

### 🌐 3️⃣ 预期的开发难点

**实现原理：** 提前识别并暴露开发中最可能遇到的“硬骨头”，能让团队在规划Sprint时，为这些任务预留足够的**研究（Spike）**和**缓冲时间**，避免因过度乐观的排期导致项目延期。

| 模块 | 📌 技术难点 | 📌 潜在Bug场景 | 📖 应对思路 |
| :--- | :--- | :--- | :--- |
| **核心对话交互** | **1. ASR准确率：** 火山引擎ASR在真实老年用户方言、口音、嘈杂环境下的表现未知。<br>**2. LLM人设稳定性：** LLM在多轮对话后可能会“忘记”或偏离预设的角色（Persona）。 | - 用户说“吃药”，被识别成“吃叫”。<br>- “贴心晚辈”角色的AI突然用非常书面的、冷冰冰的语言回复。 | 1. **专项测试与微调：** 在项目早期，投入资源进行专项的ASR模型测试，如果准确率不达标，必须考虑进行模型微调（Fine-tuning）。<br>2. **精密的Prompt Engineering：** 设计一个强大的、包含角色定义、对话历史、禁止项的系统级Prompt，并在每次调用LLM时都强制注入。 |
| **分层记忆系统** | **1. 意图识别的模糊性：** 如何区分用户的闲聊和需要被“记住”的关键事实？<br>**2. “遗忘”的平衡：** 短期记忆窗口（20轮）的设定，可能会在稍长的对话中导致上下文丢失。 | - AI错误地将用户的随口一提（“我邻居也姓王”）记为关键事实。<br>- 用户在第21轮对话问及第1轮的内容时，AI完全失忆。 | 1. **引入“确认”机制：** 当NLU不确定是否需要记忆时，让AI反问一句：“您希望我记住这件事吗？”<br>2. **滑动窗口+摘要：** 探索更高级的短期记忆机制，如保留最近10轮的完整对话，并将更早的对话（11-30轮）通过LLM生成一个摘要，一并作为上下文。 |
| **对话式提醒** | **1. 推送通知的“必达性”：** 国内安卓厂商对App后台活动的限制非常严格，可能导致推送通知被系统“杀死”而无法送达。<br>**2. 时间解析的复杂性：** NLU需要准确理解“后天下午”、“下周三晚上”等模糊的中文时间表述。 | - 用户设置了吃药提醒，但从未收到过通知。<br>- 用户说“明天早上”，被错误地解析为当天的早上。 | 1. **多渠道冗余：** **（关键）** 提醒功能不能只依赖App推送。应在V2.0尽快集成**短信**或**电话语音**作为高可靠性的备用提醒通道。<br>2. **集成成熟的NLU服务：** 时间解析是一个成熟的NLP问题，应优先考虑集成第三方成熟的NLU服务，而非自研。 |
| **危机响应协议** | **1. 误报与漏报的平衡：** 过于敏感的关键词或情感模型会导致大量误报，打扰用户；过于迟钝则可能错过真正的危机。<br>**2. 伦理与法律边界：** AI的干预行为（即使是善意的）是否会带来法律责任？ | - 用户只是在看一部悲情电视剧并复述台词，却触发了危机响应。<br>- 用户用非常平静的语气表达了危险的想法，系统未能识别。 | 1. **人机协同审核：** 所有被系统标记为“潜在危机”的对话，都应进入一个后台队列，由受过培训的运营人员进行二次审核，以降低误报率。<br>2. **明确的免责与告知：** ⚠️**TODO:** 必须与法律顾问合作，在《用户协议》中加入关于危机干预的免责条款，并清晰告知用户AI的局限性。 |

---

### 🌐 4️⃣ 性能保障与可观测性

**实现原理：** 我们无法优化我们无法衡量的东西。建立一个全面的、覆盖前端、后端和第三方服务的可观测性体系，是确保产品稳定、快速响应用户问题、并做出数据驱动决策的基础。

| 层面 | 📌 性能指标 | 📌 监控点 (日志/告警) | 📌 性能异常预案 | 📖 为什么这样监控 | 📌 建议工具 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **前端应用** | - **App启动时间：** < 3秒<br>- **UI渲染帧率：** > 45 FPS<br>- **Crash率：** < 0.5% | - **日志：** 记录所有未捕获的JS异常和关键的用户操作流程。<br>- **告警：** 当Crash率在1小时内突增超过20%时，自动告警。 | - 对于严重Crash，可通过后台开关，紧急禁用导致问题的新功能。<br>- 通过分析错误日志，快速定位并发布修复版本。 | **保障基础用户体验。** 卡顿和闪退是用户流失的最主要原因。 | **Sentry** (错误监控), **Datadog RUM** (真实用户体验监控) |
| **后端中间件** | - **API P95响应时间：** < 2秒<br>- **API成功率：** > 99.9%<br>- **函数冷启动时间：** < 500毫秒 | - **日志：** 记录每一条API请求的完整生命周期，包括其对下游服务的调用耗时。<br>- **告警：** 当API成功率低于99.5%持续5分钟，或P95响应时间超过3秒时，自动告警。 | - 通过日志快速定位是哪个下游服务（LLM/TTS/DB）导致了延迟。<br>- 若某个函数持续失败，触发熔断机制，并向客户端返回预设的“降级”回复。 | **快速定位问题根源。** 后端是流程的中枢，详尽的日志能帮助我们快速判断问题是出在自身逻辑还是外部依赖。 | **Supabase Logs** (内置), **Better Stack / Logtail** (高级日志聚合与告警) |
| **第三方服务** | - **火山引擎API的P95响应时间和成功率** | - **日志：** 记录每一次对火山引擎API调用的请求参数、响应状态码和耗时。<br>- **告警：** 当对任一火山引擎API的调用失败率在5分钟内超过5%时，自动告警。 | - 立即触发对该API的**熔断器**，在一段时间内不再调用，直接返回降级回复。<br>- 检查火山引擎的官方服务状态页面，并准备联系其技术支持。 | **管理外部依赖风险。** 我们的核心体验依赖于第三方，必须对其健康状况有实时的、精确的掌握。 | ⚠️**TODO:** 需调研如何将Supabase函数的日志与外部监控平台（如Datadog）集成。 |
| **业务指标** | - **每日提醒任务成功送达率：** > 99.5% | - **日志：** 记录每一个提醒任务从创建、调度到推送的全过程。<br>- **告警：** 当每日提醒成功送达率低于99%时，自动告警。 | - 立即排查是Cron Job调度问题、函数执行问题还是推送服务问题。<br>- 对于失败的提醒，建立一个“重试队列”进行补偿性推送。 | **守护核心功能承诺。** 提醒功能的可靠性直接关系到用户的信任，是必须死守的质量红线。 | 自定义Dashboard (在Supabase数据库上构建) |

---

### 🌐 5️⃣ 安全和隐私

**实现原理：** 在中国日益严格的监管环境下，安全与隐私合规不是“加分项”，而是产品的“生命线”。我们的策略是：**以最高的、符合伦理的标准进行自我约束，并让用户拥有绝对的、可感知的控制权**。

| 领域 | 📌 安全防护方案 | 📌 敏感数据处理 | 📌 访问控制 | 📖 为什么这样做 |
| :--- | :--- | :--- | :--- | :--- |
| **数据传输** | 全链路强制使用**TLS 1.3**加密。 | 用户的语音文件在上传过程中全程加密。 | - | **防止窃听。** 确保用户与服务器之间的所有通信内容都无法被中间人截获。 |
| **数据存储** | 1. **数据库静态加密：** Supabase默认提供静态加密。<br>2. **应用层加密：** 对于`messages`和`facts`表中极其敏感的对话内容，在存入数据库前，进行一次应用层的二次加密。 | **对话内容、事实记忆。** | - **密钥管理：** 应用层加密的密钥，必须存储在专门的密钥管理服务（KMS）中，如Supabase Vault或AWS KMS。后端函数通过安全的IAM角色获取临时访问权限。 | **纵深防御。** 即使数据库被攻破，攻击者也只能拿到一堆无法解密的密文，这是保护用户最私密对话的最后一道防线。 |
| **身份认证** | 严格使用**Supabase Auth**提供的标准流程，客户端只负责调用SDK，绝不自行处理密码或令牌刷新。 | **用户JWT令牌。** | - **短时效JWT：** JWT的有效期应设置为较短的时间（如15分钟），并配合长时效的Refresh Token进行无感刷新。 | **遵循最佳实践。** 认证是安全领域最容易出错的地方，使用成熟的、经过安全审计的第三方服务是最佳选择。短时效令牌能有效降低令牌泄露后的风险。 |
| **访问控制** | **强制开启并默认拒绝所有访问的行级别安全（RLS）策略。** | 所有与用户ID关联的数据表 (`users`, `ai_profiles`, `messages`, `reminders`, `facts`)。 | **RLS策略示例 (messages表)：**<br>`CREATE POLICY "Users can manage their own messages" ON messages FOR ALL USING (auth.uid() = user_id);` | **最小权限原则。** RLS确保了即使用户的JWT被盗用，攻击者也无法通过API访问到不属于该用户的任何数据。这是防止水平越权攻击的关键。 |
| **法律合规** | 1. **数据境内存储：** 必须选择Supabase或其他云服务商位于**中国大陆**的数据中心。<br>2. **清晰的《隐私政策》：** 提供大字、口语化的隐私政策，明确告知用户我们收集了什么数据、为什么收集、如何使用以及存储多久。<br>3. **独立的同意机制：** 对麦克风权限、对话数据处理等，获取用户单独、主动的勾选同意。 | 用户的语音特征、对话内容、健康相关信息。 | - **数据导出与删除：** 必须为用户提供清晰、便捷的途径，让他们可以随时导出自己的所有数据，并“一键”永久删除账户和所有相关信息。 | **满足中国《个人信息保护法》(PIPL)的严格要求。** 这是在中国市场运营的法律底线。透明和可控是赢得用户信任的基础。 |

---

### 🌐 6️⃣ 部署与上线

**实现原理：** 部署流程的设计目标是**“自动化、可重复、风险可控”**。通过CI/CD流水线，将人工操作降到最低，确保每次上线都是一个标准、可靠的过程。

| 环节 | 📌 构建流程 | 📌 CI/CD建议 | 📌 灰度发布策略 | 📌 回滚方案 | 📖 为什么这样设计 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **后端 (Supabase)** | 1. 在本地使用`supabase db diff`生成数据库迁移SQL。<br>2. `supabase functions deploy`命令用于部署云函数。 | **GitHub Actions:**<br>1. **触发：** 当代码合并到`main`分支时自动触发。<br>2. **流程：** 安装依赖 -> 运行Lint和测试 -> **自动将`supabase/migrations`下的SQL应用到生产数据库** -> **自动部署`supabase/functions`**。 | 不适用（后端功能通常整体发布）。采用**功能开关（Feature Flag）**在代码逻辑层面进行灰度。 | **数据库回滚：** ⚠️**TODO:** 手动执行一个与迁移SQL相反的“down”脚本。<br>**函数回滚：** 重新部署上一个稳定的Git提交版本。 | **基础设施即代码 (IaC)。** 将数据库变更和函数代码都纳入版本控制，确保生产环境与代码库的绝对一致，避免了手动操作带来的风险。 |
| **前端 (Mobile App)** | 使用**Expo Application Services (EAS) Build**进行云端构建，生成`.ipa`和`.aab`文件。 | **GitHub Actions:**<br>1. **触发：** 手动触发或当Git tag被创建时触发。<br>2. **流程：** 安装依赖 -> 运行Lint和测试 -> 调用`eas build`命令启动云端构建。 | **邀请制内测 + 分阶段推送。**<br>1. **内测：** 通过**TestFlight**和**Google Play内部测试轨道**，首先将新版本推送给“首席体验官”群组。<br>2. **分阶段：** 在应用商店后台，将新版本以1% -> 10% -> 50% -> 100%的比例，逐步推送给所有用户。 | **应用商店机制。** 暂停分阶段推送，或向应用商店提交一个修复了问题的更高版本号的新版本。 | **控制风险暴露面。** 移动端发布不可逆，必须通过灰度发布，将新版本的潜在风险（如在特定机型上的Crash）控制在小部分用户范围内，发现问题可立即暂停。 |

---

### 🌐 7️⃣ 常见故障与排查

**实现原理：** 建立一份**“故障应急手册（Playbook）”**。对于最高频、影响最大的潜在故障，提前定义好清晰的排查路径、修复方案和负责人，确保在问题发生时，团队能冷静、高效地应对，而非惊慌失措。

| 潜在故障 | 📌 如何排查 | 📌 如何修复 | 📖 建议监控指标 |
| :--- | :--- | :--- | :--- |
| **语音识别出错** | 1. **定位问题范围：** 是个别用户还是所有用户？是个别词语还是所有语音？<br>2. **检查日志：** 查看后端中间件日志，确认是否成功调用了火山引擎ASR API，以及API返回的原始识别结果和置信度。 | 1. **临时修复：** 若是特定词语问题，可在中间件中增加一个“同义词替换”或“纠错”的逻辑。<br>2. **根本修复：** 将出错的语音样本和正确的文本作为标注数据，用于ASR模型的下一轮微调。 | - ASR API调用成功率<br>- ASR API平均响应时间<br>- **（关键）** 用户“再说一遍”或“你说的不对”的反馈事件发生率 |
| **用户数据丢失** | 1. **确认场景：** 是新用户无法创建账户，还是老用户历史数据丢失？<br>2. **检查数据库：** 登录Supabase后台，直接查询`auth.users`和`public.users`等相关表，确认数据是否存在。<br>3. **检查RLS策略：** 确认该用户的访问权限没有因为错误的RLS策略而被阻断。 | 1. **若数据确实丢失：** 立即使用**Supabase的PITR（时间点恢复）功能**，将该用户的数据从最近的备份中恢复出来。<br>2. **若数据存在但无法访问：** 修正错误的RLS策略。 | - 数据库连接错误率<br>- `create_anonymous_user` RPC调用失败率 |
| **用户授权失败** | 1. **检查客户端日志：** 确认客户端是否成功获取到设备ID。<br>2. **检查后端日志：** 查看`create_anonymous_user`函数的调用日志，确认失败原因（如数据库写入失败）。<br>3. **检查Supabase Auth状态：** 查看Supabase后台，确认认证服务是否正常。 | - 修复导致设备ID获取失败的客户端代码。<br>- 修复导致数据库写入失败的后端逻辑。 | - 客户端获取设备ID失败事件数<br>- 后端JWT签发失败率 |
| **灰度放量后Crash率飙升** | 1. **立即暂停灰度：** 在应用商店后台，将分阶段推送的比例暂停在当前水平，不再扩大。<br>2. **分析错误报告：** 登录Sentry等错误监控平台，查看新增Crash的堆栈信息，定位导致崩溃的代码行和设备型号。 | 1. **紧急修复：** 开发者快速修复问题，并提交一个更高版本号的Hotfix版本。<br>2. **发布修复版：** 将Hotfix版本通过内测渠道验证后，向所有用户（包括已收到问题版本的用户）进行100%推送。 | - Crash-Free Users Rate (无崩溃用户比例)<br>- 特定错误事件的发生频率 |

---

### 🌐 8️⃣ 测试与验收

**实现原理：** 我们的测试策略是**“价值驱动”**的。测试资源的投入，必须与该功能在用户体验和产品信任中的重要性成正比。

| 测试类型 | 📌 核心测试内容 | 📌 预期通过标准 | 📖 为什么需要 |
| :--- | :--- | :--- | :--- |
| **单元测试** | - 对所有独立的工具函数、算法逻辑进行测试。<br>- 对“记忆中间件”中的核心逻辑（如Prompt拼接、人设应用）进行mock测试。 | **代码覆盖率 > 80%。** 所有逻辑分支（if/else, try/catch）都必须被覆盖。 | **保证基础逻辑的正确性。** 这是构建上层功能的基石，能以最低成本发现和修复底层错误。 |
| **集成测试** | - 测试后端中间件与Supabase数据库的连接和读写。<br>- 测试中间件与火山引擎API的真实调用和响应处理。<br>- 测试客户端与后端中间件的完整API请求/响应流程。 | **核心流程100%通过。** 对话、提醒设置等核心业务流程必须能端到端跑通。 | **验证模块间的“握手”。** 单元测试只保证单个模块正确，集成测试确保它们组合在一起时能按预期工作。 |
| **可用性/适老化测试** | **（手动，邀请真实老年用户）**<br>- 观察用户能否在无指导下完成“温暖的初见”流程。<br>- 观察用户在有背景噪音的环境下使用“按住说话”的成功率。<br>- 访谈用户对字体大小、颜色对比度、按钮大小的感受。 | **80%的测试用户能独立完成核心任务。** 访谈中美誉度（“很简单”、“不费劲”）远高于抱怨。 | **产品的“生死线”。** 无论功能多强大，如果目标用户觉得“难用”或“看不清”，产品就是失败的。 |
| **压力测试** | ⚠️**TODO:** MVP阶段可简化，但需规划。<br>- 模拟大量用户同时请求`/chat`接口。<br>- 模拟高并发的提醒任务触发。 | - P99响应时间在压力下增长不超过50%。<br>- 系统在达到吞吐量上限时能优雅地降级（如返回503），而不是崩溃。 | **评估系统的承载能力。** 确保在未来用户量增长或遭遇恶意流量攻击时，系统不会瘫痪。 |
| **安全合规测试** | - **权限测试：** 尝试用一个用户的JWT去访问另一个用户的数据，预期失败。<br>- **注入测试：** 尝试在API输入中包含SQL或脚本，预期被正确处理或拒绝。<br>- **隐私政策审查：** 由法务顾问审查《隐私政策》是否符合《个保法》要求。 | **100%的安全漏洞被修复。** 合规性完全满足法务要求。 | **保护用户和公司的生命线。** 安全漏洞和合规风险都可能导致灾难性后果。 |

---

### 🌐 9️⃣ 交付前验证清单

**实现原理：** 这是产品上线前的**“起飞检查单”**。团队核心成员（产品、技术、设计、运营）必须共同逐项确认，确保没有任何一个关键环节被遗漏。

| 类别 | 检查项 | 状态 (待检查/通过/失败) |
| :--- | :--- | :--- |
| **核心功能** | [ ] 用户可以成功完成完整的“温暖的初见”流程。 | ⚠️TODO |
| | [ ] AI能正确记忆并使用用户的称呼和自己的角色。 | ⚠️TODO |
| | [ ] 核心对话功能流畅，ASR/TTS体验可接受。 | ⚠️TODO |
| | [ ] 对话式提醒能被正确设置、复述确认并准时送达。 | ⚠️TODO |
| **边缘情况** | [ ] 网络中断时，App有清晰的、情感化的提示。 | ⚠️TODO |
| | [ ] 语音识别失败时，AI有礼貌的重试引导。 | ⚠️TODO |
| | [ ] 危机响应协议在输入特定关键词时能被正确触发。 | ⚠️TODO |
| **灾难恢复** | [ ] 已验证Supabase的PITR备份可以成功恢复单个用户的数据。 | ⚠️TODO |
| | [ ] 已演练后端函数版本回滚流程。 | ⚠️TODO |
| **灰度与上线** | [ ] TestFlight和Google Play内部测试渠道已配置完成。 | ⚠️TODO |
| | [ ] 应用商店的截图、描述、隐私政策链接已准备就绪。 | ⚠️TODO |
| | [ ] 上线后的核心指标监控Dashboard已配置完成。 | ⚠️TODO |
| **数据可追溯** | [ ] 已确认所有涉及敏感数据的后台操作都有可审计的日志。 | ⚠️TODO |
| | [ ] 已确认用户数据的导出和删除功能符合《个保法》要求。 | ⚠️TODO |

---

### 🌐 10️⃣ 团队对齐 & 知识共享

**实现原理：** 产品的质量是团队协作质量的直接反映。建立清晰、高效的协作规范，是减少内耗、提升交付速度的关键。

| 事项 | 📌 具体方案 | 📖 为什么这些制度关键 |
| :--- | :--- | :--- |
| **多人协作注意事项** | 1. **代码分支管理：** 严格遵循`Git Flow`或简化的`GitHub Flow`，所有代码变更都必须通过Pull Request (PR)并经过至少一位其他成员的Code Review。<br>2. **每日站会：** 每天进行15分钟的线上站会，同步进度、暴露问题、寻求帮助。 | **保证代码质量和信息同步。** Code Review是发现潜在Bug和知识传递的最佳方式。每日站会能确保团队节奏一致，问题被及时解决。 |
| **需要特别解释的名词** | **“关系式”陪伴、“情感化”异常处理、“自我效能感”、“关怀责任(Duty of Care)”**等。 | **统一语言。** 这些词汇是产品的灵魂。团队的每个人都必须对它们的内涵有统一、深刻的理解，才能在各自的工作中贯彻这些理念。 |
| **需要专门培训的知识点** | 1. **老年用户心理学基础**<br>2. **中国《个人信息保护法》核心要点**<br>3. **Prompt Engineering基础** | **提升团队认知。** 让工程师理解用户，让产品理解法规，能从根本上提升决策和执行的质量。 |
| **如何沉淀文档** | **使用Notion或飞书作为“唯一事实来源”的知识库。**<br>- 所有PRD、架构文档、会议纪要、决策记录都沉淀于此。<br>- 每个重要的技术组件或业务模块，都必须有对应的设计文档。 | **让知识独立于个人而存在。** 这是对抗人员流失风险、降低新成员上手成本的唯一方法。 |
| **如何写代码注释** | **“Why” over “What”。**<br>- 注释不应解释“这段代码做了什么”（代码本身应该自解释），而应解释“**为什么**要这么写”（如，这里的这个特殊处理是为了兼容某个旧版API）。 | **提升代码的可维护性。** 半年后，即便是原作者也可能忘记当初的上下文。解释“为什么”能让未来的维护者快速理解代码背后的商业逻辑和技术权衡。 |