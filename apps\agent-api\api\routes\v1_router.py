from fastapi import APIRouter
from api.routes.health import health_router
from api.routes.chat_sse_routes import router as chat_sse_router
from api.routes.text_chat_routes import router as text_chat_router  # 新增文本聊天路由
from api.routes.rtc_webhook_routes import router as rtc_webhook_router
from api.routes.rtc_routes import router as rtc_router  # 新增RTC会话管理路由 - 故事1.4-B
from api.settings import get_settings
from api.routes.sessions_routes import router as sessions_router
# 移除不安全的IDOR端点 - 故事1.13-B安全加固
from api.routes.auth_routes import router as auth_api_router
from api.routes.user_routes import router as user_router
from api.routes.character_routes import router as characters_router
from api.routes.reminder_routes import router as reminder_router  # 新增提醒管理路由 - 故事1.6-B

settings = get_settings()

# 创建v1版本的主路由，所有端点将以/v1为前缀
api_v1_router = APIRouter()

# 添加各种路由
api_v1_router.include_router(health_router)
api_v1_router.include_router(chat_sse_router, prefix="/chat")  # 添加聊天SSE路由
api_v1_router.include_router(text_chat_router, prefix="/chat")  # 添加文本聊天SSE路由 - 故事1.3-Text
api_v1_router.include_router(rtc_webhook_router, prefix="/chat")  # 添加RTC Webhook路由
api_v1_router.include_router(rtc_router)  # 添加RTC会话管理路由 - 故事1.4-B
api_v1_router.include_router(sessions_router, prefix="/chat/sessions")
# 移除不安全的IDOR端点注册 - 故事1.13-B安全加固
api_v1_router.include_router(auth_api_router)
api_v1_router.include_router(user_router)  # 新增用户管理路由
api_v1_router.include_router(characters_router)  # 新增角色管理路由
api_v1_router.include_router(reminder_router)  # 新增提醒管理路由 - 故事1.6-B

# Potentially, a generic health check for v1 if not already covered by app.include_router(health_router)
# @api_v1_router.get("/v1-health", tags=["Health"])
# async def v1_health_check():
# return {"status": "V1 API is healthy"}

