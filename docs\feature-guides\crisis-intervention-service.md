# 危机干预服务 - 前端集成指南

## 功能概述

危机干预服务是一个后端安全机制，当检测到用户输入包含危机信号（如自杀倾向、自伤表达等）时，系统会自动切换到预设的、安全的、脚本化的干预流程。

### 核心特性

- **自动检测**: 系统在后端自动检测29种危机关键词，包含自杀、自伤、绝望等表达。
- **透明集成**: 对前端完全透明，无需修改现有的API调用逻辑。
- **响应一致**: 危机干预回复与正常AI回复使用相同的响应格式和渠道。
- **安全保障**: 危机模式下绝不调用AI生成内容，并会尝试**主动中止用户的音频输入**，确保干预的有效性和安全性。

### 支持场景

- 文本对话中的危机检测和干预
- 语音对话（RTC）中的危机检测和干预
- SSE流式响应中的危机处理
- Webhook回调中的危机处理

### 新增功能：主动干预

除了返回脚本化回复，危机干预服务现在还集成了**主动干预**机制，以更好地保护处于危机状态的用户，这符合我们产品FR7（有效干预危机）的需求。

- **音频流封禁**: 在检测到危机信号后，后端会异步调用火山引擎的 `BanUserStream` API，尝试临时禁止用户的音频流上行。
- **目的**: 这样做是为了给用户一个冷静期，并防止在危机状态下说出可能加剧其风险的话语。
- **前端影响**: 此操作对前端是透明的，前端不需要做任何额外的处理。用户的麦克风可能会在短时间内被静音。

## 核心API端点

危机干预服务集成在现有的对话API中，**无需新增API端点**：

### 1. 文本对话接口
- **端点**: `POST /api/v1/chat/text_message`
- **功能**: 发送文本消息并接收AI的流式回复，自动包含危机检测。
- **认证**: JWT Bearer Token（必须）
- **响应**: SSE流式格式，危机回复与正常回复格式完全一致。

### 2. RTC语音对话接口  
- **端点**: `POST /api/v1/chat/rtc_event_handler`
- **功能**: 接收火山引擎RTC事件回调，处理语音输入并返回AI回复，包含危机检测。**当检测到危机时，会触发主动干预机制。**
- **认证**: 火山引擎签名验证。
- **响应**: JSON格式，包含AI回复文本。

## 主动干预机制 (FR7)

为了更有效地进行危机干预，满足产品需求文档(PRD)中的**FR7**要求，系统在语音对话场景下增加了主动干预能力：

- **触发时机**: 当 `ChatOrchestrationService` 检测到用户的ASR识别结果包含危机信号时。
- **执行动作**: 后端服务会立即**异步调用**火山引擎的 `BanUserStream` API。
- **目标效果**: 尝试暂时禁止用户的麦克风音频流上行，从而阻止用户继续说出可能加剧其风险的话语，为用户提供一个冷静和接收帮助信息的机会。
- **前端影响**: 此操作对前端是**完全透明**的，不会改变API的响应格式或客户端的连接状态。

## 数据契约

### 请求格式
危机干预服务使用现有的API请求格式，无需额外字段：

```typescript
// 文本对话请求
interface TextMessageRequest {
  message: string;          // 用户消息文本（会自动进行危机检测）
  sessionId: string;        // 会话ID
  characterId: string;      // AI角色ID
}

// RTC事件请求  
interface RTCEventRequest {
  event_type: string;       // 事件类型
  payload: {
    text: string;           // ASR识别的文本（会自动进行危机检测）
    timestamp: number;      // 时间戳
    confidence: number;     // 置信度
  };
  custom: {
    sessionId: string;      // 会话ID
    userId: string;         // 用户ID
    characterId: string;    // AI角色ID
  };
}
```

### 响应格式

#### 正常AI回复
```typescript
// SSE流式响应事件
interface NormalSSEEvent {
  event: "text_chunk";
  data: string;             // AI生成的文本片段
}

interface StreamEndEvent {
  event: "stream_end";
  data: "";
}
```

#### 危机干预回复
```typescript
// 危机干预的SSE响应（格式与正常回复完全一致）
interface CrisisSSEEvent {
  event: "text_chunk";
  data: string;             // 预设的危机干预文本片段
}

// 危机回复内容示例
interface CrisisResponse {
  content: string;          // 包含关怀内容和心理援助热线的脚本化回复
  hotline: "400-161-9995";  // 国家心理危机干预热线
  isScripted: true;         // 标识这是脚本化回复（前端不可见，仅用于说明）
}
```

## 调用示例与注意事项

### 文本对话调用示例

```typescript
// 前端调用代码（无需修改）
const eventSource = new EventSource('/api/v1/chat/text_message', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + accessToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: userInput,        // 可能包含危机信号
    sessionId: currentSessionId,
    characterId: selectedCharacterId
  })
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  if (data.event === 'text_chunk') {
    // 处理回复文本片段
    // 注意：可能是正常AI回复，也可能是危机干预回复
    // 前端处理逻辑完全相同，无需区分
    appendToConversation(data.data);
  } else if (data.event === 'stream_end') {
    // 流式响应结束
    finishMessage();
  }
};
```

### RTC语音对话集成

```typescript
// RTC回调处理（后端自动处理，前端无需额外操作）
// 火山引擎会自动将ASR识别的文本发送到后端
// 后端会自动进行危机检测并返回相应回复，同时触发主动干预
```

### 关键注意事项

#### 1. 透明集成 🔄
- **无需修改现有代码**: 危机干预功能对前端完全透明。
- **响应格式一致**: 危机回复与正常AI回复使用相同的SSE/JSON格式。
- **处理逻辑统一**: 前端无需区分是否为危机回复，使用相同的处理逻辑。

#### 2. 用户体验考虑 💭
- **回复内容变化**: 当用户输入危机信号时，回复将变为关怀性的脚本化内容。
- **热线信息显示**: 危机回复会包含"400-161-9995"心理援助热线，前端可考虑特殊样式显示。
- **响应时间**: 危机回复通常更快（<1ms），因为不需要调用AI模型。

#### 3. 错误处理 ⚠️
```typescript
eventSource.onerror = function(event) {
  // 危机干预模式下错误处理与正常模式相同
  handleConnectionError(event);
};
```

#### 4. 监控和日志 📊
- **后端日志**: 危机事件会在后端产生CRISIS级别的高优日志，并记录是否触发了主动干预。
- **前端监控**: 建议监控包含"400-161-9995"的回复，用于统计危机干预触发情况。
- **用户隐私**: 不要在前端日志中记录用户的危机相关输入内容。

#### 5. 测试建议 🧪
```typescript
// 测试危机干预功能
const crisisTestCases = [
  "我想自杀",           // 应触发危机回复
  "不想活了",           // 应触发危机回复  
  "今天心情不好"        // 应返回正常AI回复
];

// 验证回复是否包含心理援助热线
function isCrisisResponse(response: string): boolean {
  return response.includes("400-161-9995");
}
```

#### 6. 性能优化 ⚡
- **更快响应**: 危机回复不需要AI处理，响应更快。
- **连接复用**: 使用相同的SSE连接，无额外网络开销。
- **缓存策略**: 危机回复内容可以考虑前端缓存（可选）。

#### 7. 安全注意事项 🔒
- **内容安全**: 危机回复内容经过专业审核，安全可靠。
- **隐私保护**: 危机检测在后端进行，前端无需处理敏感关键词。
- **日志安全**: 避免在前端日志中记录用户的危机相关输入。

### 故障排除

#### 常见问题
1. **Q: 如何判断回复是否为危机干预？**
   A: 检查回复内容是否包含"400-161-9995"热线号码。

2. **Q: 危机回复是否支持流式响应？**
   A: 是的，危机回复同样使用流式响应，格式与正常回复完全一致。

3. **Q: 需要为危机回复添加特殊UI吗？**
   A: 不是必须的，但建议为包含热线号码的回复添加醒目样式。

4. **Q: 如何测试危机干预功能？**
   A: 在测试环境中输入关键词如"想死"、"自杀"等，验证是否返回包含热线的回复。

### 集成检查清单

- [ ] 确认现有文本对话接口正常工作。
- [ ] 确认SSE事件处理逻辑完整。
- [ ] 测试危机关键词触发情况。
- [ ] 验证危机回复格式与正常回复一致。
- [ ] 检查错误处理机制。
- [ ] 配置前端监控（可选）。
- [ ] 为热线号码添加特殊样式（可选）。