# 🔗 Links:
# Source file: https://github.com/obytes/react-native-template-obytes/blob/master/.github/workflows/test.yml

# ✍️ Description:
# This action is used to run unit tests for the mobile app
# Runs on pull requests and pushes to  the main/master branches
# Based on the event type:
#   - If it's a pull request, it will run the tests and post a comment with coverage details.
#   - If it's a push to main/master, it will run the tests and add the check to the commit.

# 🚨 GITHUB SECRETS REQUIRED: NONE

name: Test Frontend (Jest)

on:
  push:
    branches: [main, master]
    paths:
      - "apps/mobile-app/**"
      - ".github/workflows/test.yml"
      - "shared/**"
  pull_request:
    branches: [main, master]
    paths:
      - "apps/mobile-app/**"
      - ".github/workflows/test.yml"
      - "shared/**"

jobs:
  test:
    name: Test Frontend (Jest)
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/mobile-app

    steps:
      - name: 📦 Checkout project repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node + PNPM + install deps
        uses: ./.github/actions/setup-node-pnpm-install

      - name: 🏃‍♂️ Run Tests
        run: pnpm run test:ci

      - name: Jest Coverage Comment
        uses: MishaKav/jest-coverage-comment@main
        if: (success() || failure()) && github.event_name == 'pull_request'
        with:
          coverage-summary-path: apps/mobile-app/coverage/coverage-summary.json
          summary-title: '💯 Mobile App Test Coverage'
          badge-title: Coverage
          create-new-comment: false
          junitxml-title: 😎 Mobile App Test Results
          junitxml-path: apps/mobile-app/coverage/jest-junit.xml
          coverage-title: 👀 Mobile App Test Details
          coverage-path: apps/mobile-app/coverage/coverage.txt
          report-only-changed-files: true
