[project]
name = "agent-api"
version = "0.1.0"
requires-python = ">=3.11"
readme = "README.md"
authors = [{ name = "Agno", email = "<EMAIL>" }]

dependencies = [
  "agno==1.7.0",
  "duckduckgo-search",
  "fastapi[standard]",
  "openai",
  "pgvector",
  "psycopg[binary]",
  "sqlalchemy",
  "yfinance",
  "baidusearch",
  "litellm",
  "pycountry==24.6.1", # Pinned to latest version
  "supabase",
  "sse-starlette",
  "python-jose[cryptography]",
  "cachetools",
  "uvicorn[standard]",
  "pydantic-settings",
  "pydantic",
  "asyncpg",
  "python-dotenv",
  "uuid",
  "ecdsa",
  "gotrue",
  "PyJWT>=2.0.0",
  "cryptography",
  "postgrest",
  "storage3",
  "realtime",
  "websockets<15",
  "deprecation",
  "supafunc",
  "tenacity",
  "pydash",
  "backoff",
  "passlib",
  "httpx[http2]",
  "h2",
  "hyperframe",
  "hpack",
  "pytest",
  "pytest-asyncio",
  "pluggy",
  "freezegun",
  "uuid-extensions",
  "lancedb",
  "pyarrow",
  "arrow"
]

[project.optional-dependencies]
dev = ["mypy", "ruff"]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
# find all packages in the project
# you could exclude the test folders by uncommenting this
# exclude = ["test*", "*.tests.*", "*.tests"]

[tool.mypy]
python_version = "3.11"
disallow_untyped_defs = false
disallow_incomplete_defs = false
disallow_untyped_decorators = false
disallow_untyped_calls = false
warn_redundant_casts = true
warn_return_any = false
warn_unused_ignores = true
disallow_any_generics = false
disallow_subclassing_any = false
check_untyped_defs = true
allow_redefinition = true
warn_no_return = true
show_error_codes = true
pretty = true
exclude = []
explicit_package_bases = true

[[tool.mypy.overrides]]
module = [
      "agno.*",
      "psycopg.*",
      "pgvector.*",
      "yfinance.*",
      "baidusearch.*",
      "litellm.*",
      "pycountry.*",
      "duckduckgo_search.*",
      "httpx.*",
      "cachetools.*",
      "jose.*",
      "postgrest.*",
      "storage3.*",
      "realtime.*",
      "supafunc.*",
      "tenacity.*",
      "pydash.*",
      "backoff.*",
      "lancedb.*",
      "pyarrow.*"
]
ignore_missing_imports = true

[tool.ruff]
line-length = 120
exclude = [".venv*"]
[tool.ruff.lint.per-file-ignores]
# Ignore `F401` (import violations) in all `__init__.py` files
"__init__.py" = ["F401", "F403"]

[tool.uv.pip]
no-annotate = true

[tool.pytest.ini_options]
cache_dir = "/tmp/.pytest_cache"
