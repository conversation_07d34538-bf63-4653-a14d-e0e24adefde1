"""
火山引擎签名验证工具类
实现基于HMAC SHA256的签名验证机制，确保Webhook请求的安全性
"""
import hmac
import hashlib
import time
import logging
from typing import Optional, Dict, Any, List, Union, TYPE_CHECKING
from fastapi import HTTPException, Request
import json

if TYPE_CHECKING:
    from api.models.rtc_models import RtcWebhookRequest

logger = logging.getLogger(__name__)


class VolcengineSignatureValidator:
    """火山引擎签名验证器"""

    def __init__(self, webhook_secret: str, signature_tolerance: int = 300):
        """
        初始化签名验证器

        Args:
            webhook_secret: Webhook签名密钥
            signature_tolerance: 签名时间容忍度（秒），默认5分钟
        """
        if not webhook_secret:
            raise ValueError("Webhook secret不能为空")
        self.webhook_secret = webhook_secret
        self.signature_tolerance = signature_tolerance

    def verify_signature(self, request: Request, body: bytes):
        """
        验证火山引擎Webhook签名

        根据火山引擎官方文档，签名算法需要：
        1. 将 EventType, EventData, EventTime, EventId, Version, AppId, Nonce, SecretKey 组成数组
        2. 对数组进行字母序排序
        3. 将排序后的数组元素直接拼接成字符串
        4. 对拼接后的字符串进行SHA256哈希计算并转为十六进制
        5. 验证时间戳是否在容忍范围内
        """
        try:
            signature_header = request.headers.get("Signature")
            if not signature_header:
                logger.warning("Webhook请求缺少Signature头")
                raise HTTPException(status_code=401, detail="缺少签名头")

            request_data = json.loads(body.decode('utf-8'))

            # 步骤 5: 验证时间戳
            self._verify_timestamp(request_data)

            expected_signature = self._calculate_expected_signature(request_data)

            if not hmac.compare_digest(signature_header, expected_signature):
                logger.warning(f"签名验证失败 - 期望: {expected_signature}, 实际: {signature_header}")
                raise HTTPException(status_code=401, detail="签名验证失败")

            logger.info("Webhook签名验证成功")

        except json.JSONDecodeError:
            logger.error("无法解析请求体为JSON")
            raise HTTPException(status_code=400, detail="无效的JSON格式")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"签名验证过程中发生未知错误: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="内部错误导致签名验证失败")

    def _verify_timestamp(self, request_data: Dict[str, Any]):
        """验证请求时间戳是否在容忍范围内，防止重放攻击

        根据官方文档，EventTime字段格式为ISO-8601标准
        """
        event_time_str = request_data.get("EventTime")
        if not event_time_str:
            logger.warning("请求体中缺少EventTime字段，无法验证时效性")
            raise HTTPException(status_code=400, detail="请求体中缺少EventTime字段")

        try:
            # 根据官方文档，时间格式遵守ISO-8601标准
            from datetime import datetime, timezone
            import re

            # 标准化时间字符串处理
            normalized_time_str = event_time_str.strip()

            # 处理不同的ISO-8601格式变体
            if normalized_time_str.endswith('Z'):
                # UTC时间：2023-03-21T15:32:04Z
                event_time = datetime.fromisoformat(normalized_time_str.replace('Z', '+00:00'))
            elif re.search(r'[+-]\d{2}:\d{2}$', normalized_time_str):
                # 带时区信息：2023-03-21T15:32:04+08:00
                event_time = datetime.fromisoformat(normalized_time_str)
            elif re.search(r'[+-]\d{4}$', normalized_time_str):
                # 紧凑时区格式：2023-03-21T15:32:04+0800
                # 插入冒号
                normalized_time_str = re.sub(r'([+-]\d{2})(\d{2})$', r'\1:\2', normalized_time_str)
                event_time = datetime.fromisoformat(normalized_time_str)
            else:
                # 无时区信息，假定为UTC
                event_time = datetime.fromisoformat(normalized_time_str)
                if event_time.tzinfo is None:
                    event_time = event_time.replace(tzinfo=timezone.utc)

            # 当前UTC时间
            current_time = datetime.now(timezone.utc)

            # 计算时间差
            time_difference = abs((current_time - event_time).total_seconds())

            if time_difference > self.signature_tolerance:
                logger.warning(
                    f"时间戳验证失败. "
                    f"当前时间: {current_time.isoformat()}, "
                    f"事件时间: {event_time.isoformat()}, "
                    f"差距: {time_difference}s, "
                    f"容忍度: {self.signature_tolerance}s"
                )
                raise HTTPException(status_code=403, detail="请求时间戳超出容忍范围")

            logger.debug(f"时间戳验证通过，时间差: {time_difference}s")

        except HTTPException:
            # 已经是HTTPException，直接抛出
            raise
        except ValueError as e:
            logger.error(f"无法解析EventTime格式: {event_time_str}, 错误: {e}")
            raise HTTPException(status_code=400, detail=f"无效的EventTime格式: {event_time_str}")
        except Exception as e:
            logger.error(f"时间戳验证时发生错误: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="时间戳验证失败")

    def verify_ip_whitelist(self, request: Request, allowed_ips: List[str]):
        """
        验证请求来源IP是否在白名单内

        Args:
            request: FastAPI请求对象
            allowed_ips: 允许的IP地址列表

        Raises:
            HTTPException: 如果IP不在白名单内
        """
        client_ip = request.client.host
        if client_ip not in allowed_ips:
            logger.warning(f"IP白名单验证失败. 拒绝来自 {client_ip} 的请求. 允许的IP: {allowed_ips}")
            raise HTTPException(status_code=403, detail="IP地址不允许访问")
        logger.debug(f"IP白名单验证通过. 请求来自: {client_ip}")


    def _calculate_expected_signature(self, request_data) -> str:
        """
        根据火山引擎官方文档计算预期的回调签名

        官方文档要求：
        1. 将 EventType, EventData, EventTime, EventId, Version, AppId, Nonce, SecretKey 组成数组
        2. 对数组进行字母序排序
        3. 将排序后的数组元素直接拼接成字符串
        4. 对拼接后的字符串进行SHA256哈希计算并转为十六进制

        注意：SecretKey应该使用服务端配置的webhook_secret，而不是从请求数据中获取

        Args:
            request_data: 从请求体JSON解析出的字典或RtcWebhookRequest对象

        Returns:
            计算出的十六进制签名字符串
        """
        # 处理RtcWebhookRequest对象
        if hasattr(request_data, 'EventType'):
            # 使用服务端配置的webhook_secret作为SecretKey
            fields_to_sign = [
                request_data.EventType,
                request_data.EventData,
                request_data.EventTime,
                request_data.EventId,
                request_data.Version,
                request_data.AppId,
                request_data.Nonce,
                self.webhook_secret,  # 修正：使用服务端配置的密钥
            ]
        else:
            # 处理字典格式，使用服务端配置的webhook_secret
            fields_to_sign = [
                request_data.get("EventType") or request_data.get("event_type"),
                request_data.get("EventData") or request_data.get("event_data"),
                request_data.get("EventTime") or request_data.get("event_time"),
                request_data.get("EventId") or request_data.get("event_id"),
                request_data.get("Version") or request_data.get("version"),
                request_data.get("AppId") or request_data.get("app_id"),
                request_data.get("Nonce") or request_data.get("nonce"),
                self.webhook_secret,  # 修正：使用服务端配置的密钥
            ]

        # 检查是否有缺失的字段（除了SecretKey，因为我们使用服务端配置的）
        if any(f is None for f in fields_to_sign[:-1]):  # 排除最后一个SecretKey字段
            missing = []
            field_names = ["EventType", "EventData", "EventTime", "EventId", "Version", "AppId", "Nonce"]
            for name, val in zip(field_names, fields_to_sign[:-1]):
                if val is None:
                    missing.append(name)

            logger.error(f"签名计算失败，缺少必要字段: {missing}")
            raise HTTPException(status_code=400, detail=f"缺少必要的签名字段: {missing}")

        # 检查webhook_secret是否配置
        if not self.webhook_secret:
            logger.error("签名计算失败，webhook_secret未配置")
            raise HTTPException(status_code=500, detail="服务端webhook密钥未配置")

        # 按字母序排序并拼接 - 严格按照官方文档
        str_fields = [str(f) for f in fields_to_sign]
        str_fields.sort()  # 字母序排序
        payload_to_sign = "".join(str_fields)  # 直接拼接，无分隔符

        logger.debug(f"签名计算 - 排序后字段: {[f for f in str_fields if f != self.webhook_secret]}")  # 不记录密钥
        logger.debug(f"签名计算 - 拼接字符串长度: {len(payload_to_sign)}")

        # SHA256 + 十六进制编码
        return hashlib.sha256(payload_to_sign.encode('utf-8')).hexdigest()

# 单例工厂函数
_validator_instance: Optional[VolcengineSignatureValidator] = None

def create_volcengine_validator(webhook_secret: str, signature_tolerance: int) -> VolcengineSignatureValidator:
    """
    创建或获取一个火山引擎签名验证器的单例

    Args:
        webhook_secret: Webhook签名密钥
        signature_tolerance: 签名时间容忍度（秒）

    Returns:
        VolcengineSignatureValidator: 验证器实例
    """
    global _validator_instance
    if _validator_instance is None:
        _validator_instance = VolcengineSignatureValidator(webhook_secret, signature_tolerance)
    return _validator_instance
