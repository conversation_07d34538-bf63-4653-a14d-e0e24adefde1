---
# MCP Filesystem 索引文件
# File Path: docs/help_txt.md
# Access Method: mcp-filesystem
# Document Type: 技术文档 - 火山引擎Webhook集成指南
# Keywords: webhook, 火山引擎, 实时对话AI, function calling, 回调机制
# Last Updated: 2024
# Description: 火山引擎实时对话式AI的Webhook集成核心文档，包含回调机制、Function Calling、API调用等关键内容
---

# 火山引擎 Webhook 集成指南

> **🔍 MCP工具访问提示**: 此文档已建立MCP filesystem索引，AI助手可使用 `mcp-filesystem` 工具直接访问文件内容
> 
> **文件路径**: `docs/help_txt.md`
> 
> **快速访问命令**: 使用MCP filesystem工具的 `read_file` 功能

### 一、核心回调（Webhook）机制

这一部分是您需要接收火山引擎服务端事件通知（如ASR识别结果、Function Calling指令、智能体状态等）的基础，是实现Webhook的核心。

*   **开通消息通知服务** (📁 `d:\mcp\volcengine_docs\回调格式参考.txt`):
    *   **内容**: 指导如何在火山引擎控制台启用回调服务，并设置接收回调的URL和密钥。这是接收任何事件的第一步。
    *   **核心要点**: 开通消息通知服务后，当指定的事件发生时，你的应用的业务服务端会收到来自RTC服务端的消息通知。接收回调的URL必须以域名开头，HTTPS域名需确保SSL证书合法且完整。
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\回调格式参考.txt` 文件

*   **接收消息通知回调** (📁 `d:\mcp\volcengine_docs\接收消息通知回调.txt`):
    *   **内容**: 详细说明了如何验证从火山引擎收到的Webhook请求的签名，确保请求的安全性和真实性，并提供了Go和Java的验签示例代码。这是保障Webhook安全的关键。
    *   **核心要点**: 
        - HTTP状态码为200时回调成功，否则最多重试2次
        - RTC使用回调密钥对回调签名，需要验证EventType、EventData、EventTime等字段
        - 建议使用EventId对事件回调进行去重
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\接收消息通知回调.txt` 文件

*   **回调格式参考** (📁 `d:\mcp\volcengine_docs\回调格式参考.txt`):
    *   **内容**: 描述了所有Webhook回调请求的通用JSON结构，包括`EventType`, `EventData`, `EventTime`, `Signature`等核心字段。
    *   **核心要点**: RTC服务端会向指定URL发起HTTP POST请求，具体回调信息包含在request Body中，支持域名形式的回调地址。
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\回调格式参考.txt` 文件

*   **消息事件参考** (📁 `d:\mcp\volcengine_docs\消息事件参考.txt`):
    *   **内容**: 这是最重要的参考文档之一，它详细列出了所有可能收到的事件类型（`EventType`）及其对应的 `EventData` JSON结构，包括房间、流、录制、以及**实时对话式AI**的各类事件。
    *   **核心要点**: 
        - 支持音频流、视频流、屏幕共享流的开始/结束事件
        - 云端录制相关事件（RecordStarted、RecordStopped、RecordUploadDone等）
        - VoiceChat相关的智能体任务状态和错误事件
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\消息事件参考.txt` 文件

### 二、实时对话式 AI（核心Webhook交互场景）

您的实时音频聊天项目最核心的Webhook交互都发生在这个场景下。

#### 1. 启动与配置

*   **启动智能体 StartVoiceChat** (📁 `d:\mcp\volcengine_docs\启动智能体 StartVoiceChat.txt`):
    *   **内容**: 这是启动AI对话的核心API。在调用此API时，您需要在请求体中配置`FunctionCallingConfig`或`AgentConfig`里的`ServerMessageUrl`，这就是您用来接收火山引擎Webhook的后端接口地址。
    *   **核心要点**: 
        - RTC提供ASR、TTS、LLM一站式接入，支持第三方大模型/Agent接入
        - QPS限制：单账号不超过60
        - 用户退出房间180s后智能体任务自动停止，建议及时调用StopVoiceChat
        - 请求地址：https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\启动智能体 StartVoiceChat.txt` 文件

*   **大模型配置** (📁 `d:\mcp\volcengine_docs\大模型配置.txt`):
    *   **内容**: 详细介绍了如何配置不同的大模型平台（火山方舟、Coze、第三方），这决定了后续Webhook交互的具体能力（如是否支持Function Calling）。
    *   **核心要点**: 
        - 支持三种平台：火山方舟（支持Function Calling等高级能力）、Coze平台、第三方大模型
        - 在StartVoiceChat接口配置LLMConfig完成大模型接入
        - 建议使用非深度思考大模型保证对话流畅性
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\大模型配置.txt` 文件

*   **接入第三方大模型或 Agent** (📁 `d:\mcp\volcengine_docs\接入第三方大模型或 Agent.txt`):
    *   **内容**: 详细说明如何集成第三方大模型或Agent（如Dify Agent），满足特定业务需求的定制化场景。
    *   **核心要点**: 
        - 支持通过查询参数或custom字段传递自定义参数
        - 必须使用HTTPS域名，支持公网访问和SSE协议
        - 需要符合火山引擎接口标准规范
        - 配置Mode为"CustomLLM"，提供完整的HTTPS URL
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\接入第三方大模型或 Agent.txt` 文件

*   **语音识别配置** (📁 `d:\mcp\volcengine_docs\语音识别配置.txt`):
    *   **内容**: 详细介绍如何配置ASR能力，将用户语音实时转换为文本。提供两种方案：流式语音识别大模型和流式语音识别。
    *   **核心要点**: 
        - 流式语音识别大模型：识别准确率更高，适用于会议记录、智能客服等场景
        - 流式语音识别：识别速度更快，适用于语音控制场景
        - 在StartVoiceChat接口配置ASRConfig完成语音识别接入
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\语音识别配置.txt` 文件

*   **语音合成配置** (📁 `d:\mcp\volcengine_docs\语音合成配置.txt`):
    *   **内容**: 详细介绍如何配置TTS能力，将大模型生成的文本转换为自然流畅的语音输出。提供6种语音合成方案。
    *   **核心要点**: 
        - 火山引擎语音合成：生成速度快，适合短语播报
        - 语音合成大模型：支持SSML标记，更自然，支持流式输入输出
        - 声音复刻大模型：支持复刻真人音色
        - MiniMax语音合成：支持多语言、混合音色
        - 在StartVoiceChat接口配置TTSConfig完成语音合成接入
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\语音合成配置.txt` 文件

#### 2. Function Calling（双向Webhook交互）

当您的AI需要调用您后端定义的工具时，会触发此流程。

*   **Function Calling（非流式返回结果）** (📁 `d:\mcp\volcengine_docs\Function Calling.txt`):
    *   **内容**: 详细描述了Function Calling的完整时序图。**关键点**：火山引擎会通过Webhook向您的`ServerMessageUrl`发送工具调用指令，您的后端在执行完工具后，需要调用`UpdateVoiceChat`接口将结果再"Webhook"回火山引擎。
    *   **核心要点**: 
        - 允许大模型识别用户对话中的特定需求，智能调用外部函数、API等工具
        - 适用于天气查询、股票行情查询、数学计算等场景
        - 仅在使用火山方舟平台模型时生效，且只有doubao非1.5代系模型按非流式返回
        - 包含三个步骤：开启功能→接收工具调用指令→执行工具并返回结果
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\Function Calling.txt` 文件

*   **更新智能体 UpdateVoiceChat** (📁 `d:\mcp\volcengine_docs\更新智能体.txt`):
    *   **内容**: 这是Function Calling流程的下半部分。当您的Webhook接收到工具调用指令并执行完毕后，需要调用此API，将`Command`设置为`function`，并在`Message`中携带工具执行结果返回给火山引擎。
    *   **核心要点**: 
        - 用于对智能体进行操作，如打断智能体语音输出
        - QPS限制：单账号不超过60
        - 请求地址：https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
        - 支持多种Command类型，包括function、ExternalTextToSpeech、ExternalTextToLLM等
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\更新智能体.txt` 文件

#### 3. 高级交互与控制

这些功能允许您的后端主动向火山引擎服务发送指令或接收状态更新。

*   **接收状态变化消息** (📁 `d:\mcp\volcengine_docs\接收状态变化消息.txt`):
    *   **内容**: 解释了如何通过配置`StartVoiceChat`中的`ServerMessageURLForRTS`来接收智能体在对话过程中的实时状态（如聆听中、思考中、说话中等）的Webhook回调。
    *   **核心要点**: 
        - 可接收两类状态：智能体任务状态（任务生命周期事件）和智能体状态（对话交互中的实时状态）
        - 需要在RTC控制台配置回调设置，选择VoiceChat回调事件
        - 智能体状态包括：聆听中、思考中、说话中、被打断等
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\接收状态变化消息.txt` 文件

*   **自定义语音播放** (📁 `d:\mcp\volcengine_docs\自定义语音播放.txt`):
    *   **内容**: 指导如何通过调用`UpdateVoiceChat`接口并设置`Command`为`ExternalTextToSpeech`，让您的后端可以主动命令AI播报指定的文本内容。
    *   **核心要点**: 
        - 适用场景：AI陪练、桌游主持、安全监管、延迟安抚等
        - 可以主动播报自定义文本内容，引导客户互动、提醒用户注意事项
        - 示例应用：用户长时间不说话时的互动引导、游戏流程引导、不合规内容提醒
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\自定义语音播放.txt` 文件

*   **自定义大模型上下文** (📁 `d:\mcp\volcengine_docs\自定义大模型上下文.txt`):
    *   **内容**: 指导如何通过调用`UpdateVoiceChat`接口并设置`Command`为`ExternalTextToLLM`，从后端动态地为AI注入上下文信息，影响其回复。
    *   **核心要点**: 
        - 可动态传入自定义信息帮助大模型更准确地和用户互动
        - 适用场景：游戏陪玩助手（结合实时游戏数据）、健康咨询助手（结合生理数据）
        - 自定义信息作为UserMessage传入大模型，可根据优先级决定替代或增加对话轮次
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\自定义大模型上下文.txt` 文件

*   **打断智能体** (📁 `d:\mcp\volcengine_docs\打断智能体.txt`):
    *   **内容**: 详细说明当AI智能体讲话时如何打断智能体发言，提供语音自动打断和手动打断两种方式。
    *   **核心要点**: 
        - 语音自动打断：发声即打断、基于持续说话时间打断、通过关键词打断三种策略
        - 手动打断：用户主动发起打断请求，可在客户端或服务端实现
        - 适用场景：客服对话、在线教育、多人会议等不同场景的打断策略
        - 默认策略：未配置时采用发声即打断策略
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\打断智能体.txt` 文件

*   **实时对话式 AI 字幕** (📁 `d:\mcp\volcengine_docs\实时对话式 AI 字幕.txt`):
    *   **内容**: 详细介绍字幕功能，实时收到真人用户和智能体语音对话对应的文本内容。
    *   **核心要点**: 
        - 适用场景：实时显示字幕、业务存储分析、根据字幕返回时机手动触发新一轮对话
        - 可通过服务端和客户端实现，建议根据使用场景选择实现方式
        - 支持真人用户和智能体的语音实时转文字，用于应用终端展示、业务数据分析等
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\实时对话式 AI 字幕.txt` 文件

*   **配置对话触发模式** (📁 `d:\mcp\volcengine_docs\配置对话触发模式.txt`):
    *   **内容**: 详细说明如何配置新一轮对话的触发时机，提供手动触发和自动触发两种方式。
    *   **核心要点**: 
        - 自动触发：RTC检测到用户输入完整一句话后自动触发新一轮会话，反应迅速
        - 手动触发：用户通过按钮或特定操作控制对话开始和结束
        - 适用场景：咨询陪练、在线教育等不同场景的触发策略选择
        - 配置方法：通过StartVoiceChat接口的ASRConfig.TurnDetectionMode参数控制
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\配置对话触发模式.txt` 文件

*   **音频音量** (📁 `d:\mcp\volcengine_docs\音频音量.txt`):
    *   **内容**: 介绍音视频通话中如何获取和调整通话音量，实现多种实用功能。
    *   **核心要点**: 
        - 功能应用：动态展示通话音量条、识别活跃发言用户、提醒用户麦克风状态
        - 音量调节：通过SDK接口分别设置采集、播放或混音音量
        - 前提条件：需要实现实时音视频通信功能，使用RTC SDK内置的音频采集功能
        - 支持获取用户音量和调整音量设置
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\音频音量.txt` 文件

### 三、服务端API调用基础

这部分是您后端与火山引擎进行任何API交互（包括调用`StartVoiceChat`等）都必须参考的基础文档。

*   **请求结构** (📁 `d:\mcp\volcengine_docs\请求结构.txt`):
    *   **内容**: 描述了API的接入地址、通信协议、请求方法以及如何构造URI。
    *   **核心要点**: 
        - 服务接入地址：华北cn-north-1使用rtc.volcengineapi.com，亚太东南使用open-ap-singapore-1.volcengineapi.com
        - 支持HTTP和HTTPS协议，推荐使用HTTPS
        - 字符编码：UTF-8
        - 包含接口限制和地域部署信息
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\请求结构.txt` 文件

*   **公共参数** (📁 `d:\mcp\volcengine_docs\公共参数.txt`):
    *   **内容**: 列出了所有API请求都必须包含的公共参数，如`Action`, `Version`等。
    *   **核心要点**: 
        - 必须包含的header参数：Host、Content-Type、X-Date、X-Content-Sha256、Authorization
        - 必须包含的Query String参数：Action（接口名称）、Version（接口版本）
        - Host固定为rtc.volcengineapi.com，X-Date为UTC时间格式
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\公共参数.txt` 文件

*   **签名方法** (📁 `d:\mcp\volcengine_docs\签名方法.txt`):
    *   **内容**: 详细解释了火山引擎服务端API的V4签名机制，这是保证您的后端发往火山引擎的请求安全合法的关键。
    *   **核心要点**: 
        - API签名用于验证请求者身份并防止请求被篡改
        - 推荐使用火山引擎SDK免去签名过程，或使用API Explorer了解签名生成过程
        - 需要Access Key ID、Secret Access Key等敏感信息
        - 包含完整的签名机制说明和实现示例
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\签名方法.txt` 文件

*   **返回结构** (📁 `d:\mcp\volcengine_docs\返回结构.txt`):
    *   **内容**: 描述了调用API后，火山引擎返回的JSON数据结构，便于您解析响应。
    *   **核心要点**: 
        - 返回结果使用UTF-8字符集编码
        - 包含标准的HTTP响应格式和JSON结构
        - 不同版本API的返回结构可能有差异
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\返回结构.txt` 文件

*   **公共错误码** (📁 `d:\mcp\volcengine_docs\公共错误码.txt`):
    *   **内容**: 列出了通用的API错误码，帮助您在API调用失败时进行问题排查。
    *   **核心要点**: 
        - 当OpenAPI调用失败时，响应结果中会包含错误码和错误信息
        - 提供完整的错误码列表及对应的说明和处理方法
        - 帮助开发者快速定位和解决API调用问题
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\公共错误码.txt` 文件

### 四、优化与故障排查

这部分帮助您优化系统性能和解决常见问题，确保智能体稳定运行。

*   **如何提升语音识别准确性** (📁 `d:\mcp\volcengine_docs\如何提升语音识别准确性.txt`):
    *   **内容**: 详细介绍如何提高RTC实时对话式AI场景中语音识别的准确性，提供具体的优化策略。
    *   **核心要点**: 
        - 选择合适的ASR模型：流式语音识别大模型（准确性更高）vs 流式语音识别（速度更快）
        - 调整音量增益：在嘈杂环境下调低VolumeGain减少背景噪音干扰
        - 通过StartVoiceChat接口的ASRConfig.VolumeGain参数进行配置
        - 需要平衡识别准确性和响应速度的需求
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\如何提升语音识别准确性.txt` 文件

*   **智能体未进房或未正常工作** (📁 `d:\mcp\volcengine_docs\智能体未进房或未正常工作.txt`):
    *   **内容**: 详细说明当StartVoiceChat返回200但智能体出现异常时的故障排查方法。
    *   **核心要点**: 
        - 常见异常：智能体未加入房间、无响应、无欢迎语、语音沟通无反应、听不到声音
        - 可能原因：音频设备问题、服务配置问题（ASR/TTS/LLM未开通）、接口参数设置问题
        - 排查重点：麦克风扬声器配置、服务开通状态、StartVoiceChat接口参数检查
        - 适用于首次接入遇到问题的场景
    *   **MCP访问**: 使用 `mcp-filesystem` 工具读取 `d:\mcp\volcengine_docs\智能体未进房或未正常工作.txt` 文件

---

## 📖 MCP工具使用指南

当AI助手需要查看具体的火山引擎文档内容时，建议使用以下MCP命令：

```bash
# 读取特定文档文件
mcp-filesystem read_file "d:\mcp\volcengine_docs\启动智能体 StartVoiceChat.txt"

# 搜索特定主题（例如搜索Function Calling相关内容）
mcp-filesystem grep_files "d:\mcp\volcengine_docs" --pattern "Function Calling"

# 读取文档的前几行了解概要
mcp-filesystem head_file "d:\mcp\volcengine_docs\消息事件参考.txt" --lines 50

# 列出所有可用的火山引擎文档
mcp-filesystem list_directory "d:\mcp\volcengine_docs"
```

### 🎯 快速查找指南

#### 🔧 核心功能
- **Webhook回调相关**: 查看 `接收消息通知回调.txt` 和 `回调格式参考.txt`
- **Function Calling**: 查看 `Function Calling.txt` 和 `更新智能体.txt`
- **智能体控制**: 查看 `启动智能体 StartVoiceChat.txt` 和 `接收状态变化消息.txt`

#### ⚙️ 配置与设置
- **大模型配置**: 查看 `大模型配置.txt` 和 `接入第三方大模型或 Agent.txt`
- **语音配置**: 查看 `语音识别配置.txt` 和 `语音合成配置.txt`
- **对话控制**: 查看 `配置对话触发模式.txt` 和 `打断智能体.txt`

#### 🎛️ 高级功能
- **字幕功能**: 查看 `实时对话式 AI 字幕.txt`
- **自定义控制**: 查看 `自定义语音播放.txt` 和 `自定义大模型上下文.txt`
- **音频控制**: 查看 `音频音量.txt`

#### 🛠️ API基础
- **API调用**: 查看 `请求结构.txt`、`公共参数.txt`、`签名方法.txt`
- **错误处理**: 查看 `公共错误码.txt` 和 `返回结构.txt`

#### 🔍 优化与排查
- **性能优化**: 查看 `如何提升语音识别准确性.txt`
- **故障排查**: 查看 `智能体未进房或未正常工作.txt`