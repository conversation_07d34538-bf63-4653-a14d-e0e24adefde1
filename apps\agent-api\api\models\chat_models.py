from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid
import json

class ChatMessageInput(BaseModel):
    role: str = Field("user", pattern="^(user)$") # Enforce role is user for input
    content: str = Field(..., min_length=1, max_length=8000) # Max length from PRD
    timestamp: str # ISO 8601 format from client

    @field_validator('timestamp')
    @classmethod
    def validate_timestamp(cls, value: str) -> str:
        try:
            # Python 3.11+ handles 'Z' natively, for older versions:
            if isinstance(value, str) and value.endswith('Z'):
                dt_obj = datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                dt_obj = datetime.fromisoformat(value) # type: ignore
            # Optionally, convert to UTC string to standardize, though client already sends ISO
            # return dt_obj.astimezone(timezone.utc).isoformat()
        except ValueError:
            raise ValueError("Invalid ISO 8601 timestamp format")
        return value

class ChatRequest(BaseModel):
    # userId字段已移除 - 现在从JWT Token中安全获取用户ID
    sessionId: str = Field(..., min_length=1, description="Current session ID (UUID)")
    message: ChatMessageInput
    characterId: Optional[str] = Field("default", description="Selected AI character ID (UUID)")
    stream: bool = Field(True, description="Indicates if a streaming response is requested (always true for this endpoint)")
    promptParams: Optional[Dict[str, Any]] = Field(None, description="Optional parameters to influence prompt generation, e.g., activityType")


class TextChunkData(BaseModel):
    delta: str

class SuggestedQuestionsData(BaseModel):
    questions: List[str]

class CrisisAlertData(BaseModel):
    is_crisis: bool
    type: Optional[str] = None
    level: Optional[float] = None
    hotline_info: Optional[Dict[str, str]] = None
    message: Optional[str] = None

class StreamEndData(BaseModel):
    messageId: Optional[str] = None # ID of the completed AI message
    status: str # e.g., "done", "done_with_errors", "error"
    finalContentLength: Optional[int] = None # Length of the full AI response

    @field_validator('status')
    @classmethod
    def validate_status(cls, value: str) -> str:
        # Extended allowed statuses
        allowed_statuses = {"done", "done_with_errors", "error", "error_user_crisis", "error_ai_crisis", "error_stream_unexpectedly_terminated", "error_llm_http_status", "error_llm_connection", "error_stream_unexpected"}
        if value not in allowed_statuses:
            raise ValueError(f"Status must be one of {allowed_statuses}")
        return value

class ErrorData(BaseModel):
    type: str
    message: str

class SSEData(BaseModel):
    event: str
    data: Union[TextChunkData, SuggestedQuestionsData, CrisisAlertData, StreamEndData, ErrorData, Dict[str, Any]]
    id: Optional[str] = None # SSE event ID

    def to_sse_format(self) -> str:
        lines = []
        if self.id:
            lines.append(f"id: {self.id}")
        lines.append(f"event: {self.event}")
        # Ensure data is JSON encoded string if it's a model or dict
        if isinstance(self.data, BaseModel):
            lines.append(f"data: {json.dumps(self.data.model_dump(), ensure_ascii=False)}")
        elif isinstance(self.data, dict):
            lines.append(f"data: {json.dumps(self.data, ensure_ascii=False)}")
        else: # Assume data is already a string
            lines.append(f"data: {self.data}")
        # Use actual newlines for SSE compliance
        return "\n".join(lines) + "\n\n"


# 故事1.3-Text: 文本聊天SSE接口专用模型
class TextMessageRequest(BaseModel):
    """
    文本聊天消息请求模型

    用于POST /api/v1/chat/text_message接口
    比ChatRequest更简单，专注于纯文本对话
    """
    message: str = Field(..., min_length=1, max_length=8000, description="用户消息文本")
    sessionId: str = Field(..., min_length=1, description="会话ID")
    characterId: Optional[str] = Field("default_character", description="AI角色ID")

    model_config = {
        # 提供示例数据用于API文档
        "json_schema_extra": {
            "example": {
                "message": "你好，今天天气怎么样？",
                "sessionId": "session-uuid-123",
                "characterId": "default_character"
            }
        }
    }
