import json
from typing import List, Dict, AsyncIterable, Optional, Union, Any, AsyncGenerator
from openai import AsyncOpenAI, AsyncStream
from openai.types.chat import ChatCompletion, ChatCompletionChunk
import asyncio
import httpx
from pydantic import BaseModel, SecretStr, ConfigDict
from api.settings import settings, logger
from api.services.volcano_client_service import VolcanoClientService


class LLMConfig(BaseModel):
    """LLM服务配置模型 - 符合架构师建议的安全配置管理"""
    model_config = ConfigDict(
        # 保护敏感信息不在序列化中暴露
        str_strip_whitespace=True,
        validate_assignment=True
    )

    endpoint_id: str
    app_key: SecretStr  # 使用SecretStr保护敏感信息
    base_url: str = "https://ark.cn-beijing.volces.com"
    timeout: float = 30.0
    max_retries: int = 3


class LLMProxyService:
    """
    A service to proxy requests to an LLM provider, such as Volcano Engine.
    """

    def __init__(self):
        """初始化LLM代理服务 - 符合架构师建议的配置安全化"""
        self.volcano_client = VolcanoClientService()

        # 使用安全配置模型
        self._config = LLMConfig(
            endpoint_id=settings.VOLCANO_LLM_ENDPOINT_ID,
            app_key=settings.VOLCANO_LLM_APP_KEY,
            base_url="https://ark.cn-beijing.volces.com",
            timeout=30.0,
            max_retries=3
        )

        # 为向后兼容保留旧属性
        self.endpoint_id = self._config.endpoint_id
        self.app_key = self._config.app_key.get_secret_value()
        self.base_url = self._config.base_url
        self.timeout = self._config.timeout
        self.max_retries = self._config.max_retries

        # 断路器状态 - 符合架构师建议的断路器模式
        self._circuit_breaker_failures = 0
        self._circuit_breaker_open = False
        self._circuit_breaker_last_failure = 0
        # 暴露断路器对象供测试验证
        self._circuit_breaker = {
            "failures": 0,
            "is_open": False,
            "last_failure": 0,
            "failure_threshold": 5,
            "reset_timeout": 300  # 5分钟
        }

        # 验证配置 - 提供更详细的错误信息
        if not self.endpoint_id:
            raise ValueError("Missing VOLCANO_LLM_ENDPOINT_ID configuration - please check environment variables")
        if not self._config.app_key.get_secret_value():
            raise ValueError("Missing VOLCANO_LLM_APP_KEY configuration - please check environment variables")

    async def generate_text_async(
        self,
        prompt: str,
        max_tokens: int = 300,
        temperature: float = 0.7
    ) -> str:
        """
        使用火山引擎LLM生成文本（异步）

        Args:
            prompt: 输入提示
            max_tokens: 最大令牌数
            temperature: 温度参数

        Returns:
            生成的文本
        """
        try:
            # 检查断路器状态
            if self._is_circuit_breaker_open():
                return "服务暂时不可用，请稍后重试"

            messages = [{"role": "user", "content": prompt}]

            # 构建请求数据
            request_data = {
                "model": self.endpoint_id,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }

            # 执行带重试的API调用
            response = await self._call_volcano_llm_with_retry(request_data)

            # 重置断路器
            self._reset_circuit_breaker()

            # 提取文本内容
            if "choices" in response and len(response["choices"]) > 0:
                return response["choices"][0]["message"]["content"]
            else:
                return "抱歉，我无法为您生成回复"

        except Exception as e:
            logger.error(f"LLM文本生成失败: {e}")
            self._record_circuit_breaker_failure()
            return f"抱歉，服务暂时不可用: {str(e)}"

    async def call_llm_with_tools(
        self,
        messages: List[Dict[str, str]],
        tools: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        调用支持工具的LLM API

        Args:
            messages: 消息列表
            tools: 工具定义列表

        Returns:
            LLM响应，包含可能的工具调用
        """
        try:
            # 检查断路器状态
            if self._is_circuit_breaker_open():
                return {"content": "服务暂时不可用，请稍后重试", "tool_calls": []}

            # 构建请求数据
            request_data = {
                "model": self.endpoint_id,
                "messages": messages,
                "tools": tools,
                "tool_choice": "auto",
                "stream": False
            }

            # 执行带重试的API调用
            response = await self._call_volcano_llm_with_retry(request_data)

            # 重置断路器
            self._reset_circuit_breaker()

            # 处理响应
            if "choices" in response and len(response["choices"]) > 0:
                choice = response["choices"][0]
                message = choice["message"]

                result = {
                    "content": message.get("content"),
                    "tool_calls": []
                }

                # 处理工具调用
                if "tool_calls" in message and message["tool_calls"]:
                    result["tool_calls"] = message["tool_calls"]

                return result
            else:
                return {"content": "抱歉，我无法为您生成回复", "tool_calls": []}

        except Exception as e:
            logger.error(f"LLM工具调用失败: {e}")
            self._record_circuit_breaker_failure()
            return {"content": f"抱歉，服务暂时不可用: {str(e)}", "tool_calls": []}

    async def _call_volcano_llm_with_retry(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        带重试机制的火山引擎LLM API调用
        """
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    # 指数退避
                    delay = 2 ** attempt
                    logger.info(f"LLM API调用第{attempt}次重试，延迟{delay}秒")
                    await asyncio.sleep(delay)

                return await self._call_volcano_llm_api(request_data)

            except httpx.TimeoutException as e:
                last_exception = e
                logger.warning(f"LLM API调用第{attempt + 1}次尝试超时")
                if attempt == self.max_retries:
                    break
                continue

            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 401:
                    # 认证失败，不重试
                    logger.error(f"LLM API认证失败: {e}")
                    raise Exception("LLM API认证失败，请检查密钥配置")
                elif e.response.status_code == 429:
                    # 限流，需要重试
                    if attempt < self.max_retries:
                        delay = max(2 ** attempt, 5)
                        logger.warning(f"LLM API限流，延迟{delay}秒后重试")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        break
                else:
                    # 其他HTTP错误
                    logger.error(f"LLM API HTTP错误: {e}")
                    break

            except Exception as e:
                last_exception = e
                logger.error(f"LLM API调用未知错误: {e}")
                if attempt == self.max_retries:
                    break
                continue

        # 所有重试失败
        if isinstance(last_exception, httpx.TimeoutException):
            error_message = f"LLM服务响应超时，请稍后重试 (尝试了{self.max_retries + 1}次)"
        elif isinstance(last_exception, (httpx.ConnectError, httpx.NetworkError)):
            error_message = f"暂时无法连接LLM服务，请检查网络 (尝试了{self.max_retries + 1}次)"
        else:
            error_message = f"LLM服务暂时不可用，请稍后重试 (尝试了{self.max_retries + 1}次)"
        logger.error(f"LLM API调用最终失败: {error_message}")
        raise Exception(error_message)

    async def _call_volcano_llm_api(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        实际调用火山引擎LLM API
        """
        # 准备请求
        path = f"/api/v3/chat/completions"

        # 修复：火山引擎LLM API使用Bearer Token认证，不是V4签名
        headers = {
            "Authorization": f"Bearer {self._config.app_key.get_secret_value()}",
            "Content-Type": "application/json"
        }

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            logger.debug(f"调用火山引擎LLM API")
            response = await client.post(
                f"{self.base_url}{path}",
                json=request_data,
                headers=headers
            )
            response.raise_for_status()

            result = response.json()
            logger.debug(f"火山引擎LLM API响应成功")

            return result

    async def _call_volcano_llm_api_stream(self, request_data: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """
        实际调用火山引擎LLM API (流式版本)

        Args:
            request_data: 请求数据，必须包含stream=True

        Yields:
            从火山引擎API流式返回的文本片段
        """
        # 确保启用流式模式
        request_data = request_data.copy()
        request_data["stream"] = True

        # 准备请求
        path = f"/api/v3/chat/completions"

        # 修复：火山引擎LLM API使用Bearer Token认证，不是V4签名
        headers = {
            "Authorization": f"Bearer {self._config.app_key.get_secret_value()}",
            "Content-Type": "application/json",
            "Accept": "text/event-stream"
        }
        headers['Cache-Control'] = 'no-cache'

        # 使用try-except-finally结构确保连接正确关闭（架构师建议）
        client = None
        try:
            client = httpx.AsyncClient(timeout=self.timeout)
            logger.debug(f"调用火山引擎LLM API (流式)")

            async with client.stream(
                "POST",
                f"{self.base_url}{path}",
                json=request_data,
                headers=headers
            ) as response:
                response.raise_for_status()

                async for line in response.aiter_lines():
                    if line.strip():
                        # 解析SSE格式
                        if line.startswith("data: "):
                            data_content = line[6:]  # 移除"data: "前缀

                            # 处理结束信号
                            if data_content.strip() == "[DONE]":
                                logger.debug("火山引擎LLM API流式响应完成")
                                break

                            try:
                                # 解析JSON数据
                                chunk_data = json.loads(data_content)

                                # 提取文本内容
                                if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                    choice = chunk_data["choices"][0]
                                    if "delta" in choice and "content" in choice["delta"]:
                                        content = choice["delta"]["content"]
                                        if content:  # 过滤空内容
                                            yield content

                            except json.JSONDecodeError as e:
                                logger.warning(f"解析SSE JSON数据失败: {e}, 原始内容: {data_content}")
                                continue

        except httpx.RequestError as e:
            logger.error(f"火山引擎LLM API流式请求失败: {e}")
            yield f"[ERROR: 网络请求失败 - {str(e)}]"
        except httpx.HTTPStatusError as e:
            logger.error(f"火山引擎LLM API流式响应错误: {e.response.status_code}")
            yield f"[ERROR: API响应错误 - {e.response.status_code}]"
        except Exception as e:
            logger.error(f"火山引擎LLM API流式调用异常: {e}", exc_info=True)
            yield f"[ERROR: 服务异常 - {str(e)}]"
        finally:
            # 确保httpx客户端连接正确关闭（架构师建议）
            if client:
                await client.aclose()

    def _is_circuit_breaker_open(self) -> bool:
        """检查断路器是否打开"""
        if not self._circuit_breaker_open:
            return False

        # 检查是否应该重置断路器（5分钟后重试）
        import time
        if time.time() - self._circuit_breaker_last_failure > 300:
            self._circuit_breaker_open = False
            self._circuit_breaker_failures = 0
            logger.info("断路器已重置，恢复LLM服务调用")
            return False

        return True

    def _record_circuit_breaker_failure(self):
        """记录断路器失败 - 符合架构师建议的断路器模式"""
        import time
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = time.time()

        # 同步更新断路器状态对象
        self._circuit_breaker["failures"] = self._circuit_breaker_failures
        self._circuit_breaker["last_failure"] = self._circuit_breaker_last_failure

        # 失败超过5次，打开断路器
        if self._circuit_breaker_failures >= 5:
            self._circuit_breaker_open = True
            self._circuit_breaker["is_open"] = True
            logger.warning("LLM服务连续失败，断路器已打开")

    def _reset_circuit_breaker(self):
        """重置断路器 - 符合架构师建议的断路器模式"""
        if self._circuit_breaker_failures > 0:
            self._circuit_breaker_failures = 0
            self._circuit_breaker_open = False
            # 同步更新断路器状态对象
            self._circuit_breaker["failures"] = 0
            self._circuit_breaker["is_open"] = False

    async def create_chat_completion(
        self, messages: List[Dict[str, Any]], stream: bool = False, **kwargs: Any
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        创建聊天完成请求 - 支持流式和非流式响应

        Args:
            messages: 消息对象列表
            stream: 是否流式响应
            **kwargs: LLM提供者的额外参数

        Yields:
            聊天完成响应的数据块
        """
        try:
            if stream:
                # 流式响应实现
                request_data = {
                    "model": self.endpoint_id,
                    "messages": messages,
                    "stream": True,
                    **kwargs
                }

                # 这里应该实现真正的流式API调用
                # 目前作为占位符，生成模拟的流式响应
                response_text = await self.generate_text_async(
                    prompt=messages[-1].get("content", "") if messages else "",
                    max_tokens=kwargs.get("max_tokens", 300),
                    temperature=kwargs.get("temperature", 0.7)
                )

                # 将完整响应分块模拟流式输出
                chunk_size = 10
                for i in range(0, len(response_text), chunk_size):
                    chunk = response_text[i:i+chunk_size]
                    yield {
                        "delta": {
                            "content": chunk
                        },
                        "finish_reason": "length" if i + chunk_size >= len(response_text) else None
                    }
            else:
                # 非流式响应
                response_text = await self.generate_text_async(
                    prompt=messages[-1].get("content", "") if messages else "",
                    max_tokens=kwargs.get("max_tokens", 300),
                    temperature=kwargs.get("temperature", 0.7)
                )
                yield {
                    "content": response_text,
                    "finish_reason": "stop"
                }

        except Exception as e:
            logger.error(f"create_chat_completion失败: {e}")
            yield {
                "error": str(e),
                "content": "抱歉，无法生成回复"
            }

    async def call_llm(
        self,
        messages: List[Dict[str, str]],
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> Union[str, Dict[str, Any]]:
        """
        Call LLM with messages and optional tools using real Volcano API.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            tools: Optional list of tool definitions for function calling

        Returns:
            LLM response as string or dictionary (with potential tool_calls)
        """
        try:
            # 获取最新的用户消息用于fallback
            user_message = ""
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break

            # 准备API请求数据
            request_data = {
                "model": self._config.endpoint_id,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000
            }

            # 如果提供了工具定义，添加到请求中
            if tools:
                request_data["tools"] = tools
                request_data["tool_choice"] = "auto"

            # 调用真实的火山引擎API
            response = await self._call_volcano_llm_with_retry(request_data)

            # 处理API响应
            if "choices" in response and len(response["choices"]) > 0:
                choice = response["choices"][0]
                message = choice.get("message", {})

                # 检查是否有工具调用
                if "tool_calls" in message and message["tool_calls"]:
                    return {
                        "content": message.get("content"),
                        "tool_calls": message["tool_calls"]
                    }
                else:
                    # 普通文本响应
                    content = message.get("content", "")
                    if tools:
                        return {
                            "content": content,
                            "tool_calls": []
                        }
                    else:
                        return content

            # API响应格式异常，使用fallback
            logger.warning("火山引擎API响应格式异常，使用emergency fallback")
            return self._generate_emergency_fallback(user_message)

        except Exception as e:
            # API调用失败，使用emergency fallback
            logger.error(f"火山引擎LLM API调用失败: {e}，使用emergency fallback")
            fallback_response = self._generate_emergency_fallback(user_message)

            if tools:
                return {
                    "content": fallback_response,
                    "tool_calls": []
                }
            else:
                return fallback_response

    def _should_use_tools(self, user_message: str, tools: List[Dict[str, Any]]) -> bool:
        """
        分析用户消息，判断是否需要使用工具

        Args:
            user_message: 用户消息
            tools: 可用工具列表

        Returns:
            是否应该使用工具
        """
        if not user_message or not tools:
            return False

        user_message_lower = user_message.lower()

        # 检查每个工具是否适用
        for tool in tools:
            function_info = tool.get("function", {})
            function_name = function_info.get("name", "")
            function_description = function_info.get("description", "")

            # 根据工具名称和描述判断是否适用
            if function_name == "set_reminder":
                # 提醒相关关键词
                reminder_keywords = [
                    "提醒", "remind", "记住", "别忘了", "alarm", "闹钟",
                    "明天", "下午", "晚上", "早上", "周", "月", "日",
                    "开会", "会议", "appointment", "meeting", "deadline"
                ]
                if any(keyword in user_message_lower for keyword in reminder_keywords):
                    return True

            elif function_name == "get_weather":
                # 天气相关关键词
                weather_keywords = ["天气", "weather", "温度", "下雨", "晴天", "阴天"]
                if any(keyword in user_message_lower for keyword in weather_keywords):
                    return True

            elif function_name == "search_memory":
                # 记忆搜索相关关键词
                memory_keywords = ["记得", "之前", "上次", "以前", "回忆", "记录"]
                if any(keyword in user_message_lower for keyword in memory_keywords):
                    return True

        return False



    def _generate_emergency_fallback(self, user_message: str) -> str:
        """
        Emergency fallback响应 - 仅在API完全不可用时使用

        Args:
            user_message: 用户消息

        Returns:
            基础回复文本
        """
        return "抱歉，我的服务暂时不可用，请稍后再试。如果您需要紧急帮助，请联系相关专业机构。"

    async def generate_summary(self, conversation_text: str) -> str:
        """
        Generate a summary of the conversation.

        Args:
            conversation_text: The text content of the conversation to summarize

        Returns:
            A summary string
        """
        # Placeholder implementation for testing
        # In a real implementation, this would call the LLM to generate a proper summary
        if len(conversation_text) > 1000:
            return "这是一个深度对话会话，用户分享了多个主题，AI提供了相应的支持和建议。"
        elif "压力" in conversation_text or "工作" in conversation_text:
            return "用户分享了工作压力，AI提供了情感支持和建议。"
        elif "天气" in conversation_text:
            return "用户询问了关于天气的信息，AI提供了相应回应。"
        else:
            return "这是一次友好的对话，用户和AI进行了互动交流。"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 300,
        temperature: float = 0.3
    ) -> str:
        """
        基于提示生成文本 - 调用真实LLM API

        实现架构师要求的LLM服务统一化：
        1. 调用真实fire volcanon引擎API
        2. 移除硬编码占位符
        3. 实现优雅的降级机制

        Args:
            prompt: The input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature for generation

        Returns:
            Generated text from real LLM API
        """
        logger.debug(f"生成文本请求: '{prompt[:50]}...'")

        # 构建消息格式
        messages = [{"role": "user", "content": prompt}]

        try:
            # 使用核心call_llm方法调用真实API
            response = await self.call_llm(
                messages=messages,
                tools=None  # 文本生成不需要工具
            )

            # call_llm对于非工具调用返回字符串
            if isinstance(response, str):
                logger.debug(f"LLM API调用成功，生成文本长度: {len(response)}")
                return response

            # 处理字典格式响应
            if isinstance(response, dict) and "content" in response:
                content = response.get("content") or ""
                logger.debug(f"LLM API调用成功，生成文本长度: {len(content)}")
                return content

            logger.warning(f"意外的响应类型: {type(response)}")
            return self._generate_emergency_fallback(prompt)

        except Exception as e:
            logger.error(f"文本生成调用失败: {e}", exc_info=True)
            return self._generate_emergency_fallback(prompt)

    def _generate_emergency_fallback(self, prompt: str) -> str:
        """
        紧急降级策略 - 提供基础但有意义的回复

        实现架构师要求的降级机制：
        - 用户友好的消息
        - 不暴露技术错误信息
        - 根据提示类型提供相应回复
        """
        if "摘要" in prompt:
            return "抱歉，无法生成详细摘要，请稍后重试。"
        elif "分析" in prompt:
            return "系统暂时无法完成分析，请稍后再试。"
        elif "总结" in prompt:
            return "抱歉，总结功能暂时不可用，请稍后重试。"
        elif "建议" in prompt or "推荐" in prompt:
            return "暂时无法提供建议，请稍后再试或咨询相关专家。"
        else:
            return "抱歉，服务暂时不可用，请稍后重试。"


# Singleton instance of the service
llm_proxy_service = LLMProxyService()

async def get_llm_proxy_service() -> LLMProxyService:
    """Get LLM proxy service instance."""
    return llm_proxy_service

# Alias for test compatibility
LLMService = LLMProxyService
