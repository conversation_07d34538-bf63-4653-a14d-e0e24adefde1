@ECHO OFF
SETLOCAL
ECHO ------------------------------------------------------------
ECHO -*- Development Setup for Windows
ECHO ------------------------------------------------------------
ECHO.

REM ############################################################################
REM # Development Setup for Windows (CMD)
REM # - This script creates a virtual environment and installs libraries.
REM # - Please install uv before running this script.
REM # - Please deactivate the existing virtual environment before running.
REM # Usage: .\scripts\dev_setup.bat
REM ############################################################################

REM Set the project root directory, which is one level above the 'scripts' directory.
SET "REPO_ROOT=%~dp0.."
FOR %%i IN ("%REPO_ROOT%") DO SET "REPO_ROOT=%%~fi"
SET "VENV_DIR=%REPO_ROOT%\.venv"

ECHO ------------------------------------------------------------
ECHO -*- Removing existing virtual env
ECHO ------------------------------------------------------------
ECHO -*- rmdir /s /q "%VENV_DIR%"
IF EXIST "%VENV_DIR%" (
    RMDIR /S /Q "%VENV_DIR%"
)
ECHO.

ECHO ------------------------------------------------------------
ECHO -*- Creating virtual env with Python 3.12
ECHO ------------------------------------------------------------
ECHO -*- uv venv --python 3.12 -p 3.12 "%VENV_DIR%"
uv venv --python 3.12 "%VENV_DIR%"
IF %ERRORLEVEL% NEQ 0 (
    ECHO.
    ECHO ERROR: Failed to create virtual environment.
    ECHO Please ensure 'uv' is installed and in your PATH.
    GOTO :EOF
)
ECHO.

ECHO ------------------------------------------------------------
ECHO -*- Installing requirements
ECHO ------------------------------------------------------------
ECHO -*- uv pip install -r "%REPO_ROOT%\requirements.txt"
uv pip install -r "%REPO_ROOT%\requirements.txt"
IF %ERRORLEVEL% NEQ 0 (
    ECHO.
    ECHO ERROR: Failed to install requirements from requirements.txt.
    GOTO :EOF
)
ECHO.

ECHO ------------------------------------------------------------
ECHO -*- Installing workspace in editable mode with dev dependencies
ECHO ------------------------------------------------------------
ECHO -*- uv pip install -e "%REPO_ROOT%[dev]"
uv pip install -e "%REPO_ROOT%[dev]"
IF %ERRORLEVEL% NEQ 0 (
    ECHO.
    ECHO ERROR: Failed to install workspace in editable mode.
    GOTO :EOF
)
ECHO.

ECHO ------------------------------------------------------------
ECHO -*- Development setup complete
ECHO ------------------------------------------------------------
ECHO -*- Activate venv using: .\.venv\Scripts\activate
ECHO.

ENDLOCAL
