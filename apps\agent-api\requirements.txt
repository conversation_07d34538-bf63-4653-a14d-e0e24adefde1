# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --no-cache -o requirements.txt
agno==1.7.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asyncpg==0.30.0
attrs==25.3.0
backoff==2.2.1
baidusearch==1.0.3
beautifulsoup4==4.13.4
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.0
colorama==0.4.6
cryptography==45.0.2
curl-cffi==0.10.0
deprecation==2.1.0
distro==1.9.0
dnspython==2.7.0
docstring-parser==0.16
duckduckgo-search==8.0.1
ecdsa==0.19.1
email-validator==2.2.0
fastapi==0.115.12
fastapi-cli==0.0.7
filelock==3.18.0
frozendict==2.4.6
frozenlist==1.6.0
fsspec==2025.3.2
gitdb==4.0.12
gitpython==3.1.44
gotrue==2.12.0
greenlet==3.2.2
h11==0.16.0
h2==4.1.0
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.27.0
huggingface-hub==0.31.1
hyperframe==6.1.0
idna==3.10
importlib-metadata==8.7.0
iniconfig==2.1.0
jinja2==3.1.6
jiter==0.9.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
lancedb==0.24.0
litellm
lxml==5.4.0
markdown-it-py==3.0.0
markupsafe==3.0.2
mdurl==0.1.2
multidict==6.4.3
multitasking==0.0.11
numpy==2.2.5
openai==1.54.5
overrides==7.7.0
packaging==25.0
pandas==2.2.3
passlib==1.7.4
peewee==3.18.1
pgvector==0.4.1
platformdirs==4.3.8
pluggy==1.6.0
postgrest==1.0.1
primp==0.15.0
propcache==0.3.1
protobuf==6.30.2
psycopg==3.2.8
psycopg-binary==3.2.8
pyarrow==20.0.0
pyasn1==0.4.8
pycountry==24.6.1
pycparser==2.22
pydantic==2.11.4
pydantic-core==2.33.2
pydantic-settings==2.9.1
pydash==8.0.5
pygments==2.19.1
pyjwt==2.10.1
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-jose==3.4.0
python-multipart==0.0.20
pytz==2025.2
pyyaml==6.0.2
realtime==2.4.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==14.0.0
rich-toolkit==0.14.6
rpds-py==0.24.0
rsa==4.9.1
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.7
sqlalchemy==2.0.40
sse-starlette==2.3.5
starlette==0.46.2
storage3==0.11.3
strenum==0.4.15
supabase==2.15.1
supafunc==0.9.4
tenacity==9.1.2
tiktoken==0.9.0
tokenizers==0.21.1
tomli==2.2.1
tqdm==4.67.1
typer==0.15.3
typing-extensions==4.13.2
typing-inspection==0.4.0
tzdata==2025.2
urllib3==2.4.0
uuid==1.30
uvicorn==0.34.2
watchfiles==1.0.5
websockets==14.2
yarl==1.20.0
yfinance==0.2.61
zep-cloud
zipp==3.21.0
mypy==1.16.1
ruff==0.12.2
mem0ai
aiofiles
arrow
asyncio-throttle
freezegun
psycopg2
