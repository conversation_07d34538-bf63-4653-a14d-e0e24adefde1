#!/usr/bin/env python3
"""
运行所有API测试的主脚本

提供多种运行模式：
1. 运行所有测试
2. 运行指定测试
3. 批量运行测试并生成汇总报告

使用示例：
1. 运行所有测试：
   python run_all_tests.py

2. 运行指定测试：
   python run_all_tests.py --tests health auth user

3. 指定服务器URL：
   python run_all_tests.py --url http://localhost:8003

4. 生成详细报告：
   python run_all_tests.py --detailed-report
"""

import asyncio
import argparse
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 导入所有测试模块
from base_tester import BaseAPITester

# 测试模块映射
TEST_MODULES = {
    "health": "01_health_test",
    "auth": "02_auth_test",
    "user": "03_user_test",
    "character": "04_character_test",
    "session": "05_session_test",
    "chat": "06_chat_test",
    "reminder": "07_reminder_test",
    "rtc": "08_rtc_test",
    "rtc_webhook": "09_rtc_webhook_test",
}

class TestRunner:
    """API测试运行器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        self.base_url = base_url
        self.results = {}
        self.start_time = None
        self.end_time = None

    async def run_single_test(self, test_name: str, module_name: str) -> Dict[str, Any]:
        """运行单个测试"""
        print(f"\n{'='*60}")
        print(f"🚀 运行测试: {test_name.upper()}")
        print(f"{'='*60}")

        start_time = time.time()

        try:
            # 动态导入测试模块
            module = __import__(module_name)

            # 获取测试类名 - 修复类名匹配问题
            if test_name == "rtc":
                test_class_name = "RTCTester"
            elif test_name == "rtc_webhook":
                test_class_name = "RTCWebhookTester"
            else:
                test_class_name = f"{test_name.title()}Tester"

            if hasattr(module, test_class_name):
                test_class = getattr(module, test_class_name)

                # 运行测试
                async with test_class(self.base_url) as tester:
                    await tester.run_tests()

                    # 收集结果
                    duration = time.time() - start_time
                    result = {
                        "test_name": test_name,
                        "status": "completed",
                        "duration": round(duration, 2),
                        "total": tester.test_results["total"],
                        "passed": tester.test_results["passed"],
                        "failed": tester.test_results["failed"],
                        "errors": tester.test_results["errors"],
                        "success_rate": (tester.test_results["passed"] / tester.test_results["total"] * 100) if tester.test_results["total"] > 0 else 0
                    }

                    print(f"\n✅ {test_name.upper()} 测试完成")
                    print(f"   耗时: {duration:.2f}秒")
                    print(f"   成功率: {result['success_rate']:.1f}%")

                    return result
            else:
                raise Exception(f"找不到测试类: {test_class_name}")

        except Exception as e:
            duration = time.time() - start_time
            print(f"\n❌ {test_name.upper()} 测试失败: {e}")

            return {
                "test_name": test_name,
                "status": "failed",
                "duration": round(duration, 2),
                "total": 0,
                "passed": 0,
                "failed": 1,
                "errors": [{"error": str(e)}],
                "success_rate": 0
            }

    async def run_tests(self, test_names: List[str]) -> Dict[str, Any]:
        """运行指定的测试列表"""
        self.start_time = time.time()

        print(f"🎯 开始运行API测试")
        print(f"📍 目标服务器: {self.base_url}")
        print(f"📋 测试列表: {', '.join(test_names)}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 运行所有测试
        for test_name in test_names:
            if test_name in TEST_MODULES:
                module_name = TEST_MODULES[test_name]
                result = await self.run_single_test(test_name, module_name)
                self.results[test_name] = result

                # 测试间隔
                await asyncio.sleep(1.0)
            else:
                print(f"⚠️ 未知测试: {test_name}")
                self.results[test_name] = {
                    "test_name": test_name,
                    "status": "skipped",
                    "duration": 0,
                    "total": 0,
                    "passed": 0,
                    "failed": 0,
                    "errors": [{"error": "未知测试"}],
                    "success_rate": 0
                }

        self.end_time = time.time()

        # 生成汇总报告
        return self.generate_summary()

    def generate_summary(self) -> Dict[str, Any]:
        """生成测试汇总报告"""
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0

        summary = {
            "metadata": {
                "start_time": datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else None,
                "end_time": datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None,
                "total_duration": round(total_duration, 2),
                "base_url": self.base_url,
                "test_count": len(self.results)
            },
            "overall": {
                "total_tests": sum(r["total"] for r in self.results.values()),
                "total_passed": sum(r["passed"] for r in self.results.values()),
                "total_failed": sum(r["failed"] for r in self.results.values()),
                "completed_suites": len([r for r in self.results.values() if r["status"] == "completed"]),
                "failed_suites": len([r for r in self.results.values() if r["status"] == "failed"]),
                "skipped_suites": len([r for r in self.results.values() if r["status"] == "skipped"])
            },
            "details": self.results
        }

        # 计算总体成功率
        if summary["overall"]["total_tests"] > 0:
            summary["overall"]["success_rate"] = round(
                summary["overall"]["total_passed"] / summary["overall"]["total_tests"] * 100, 1
            )
        else:
            summary["overall"]["success_rate"] = 0

        return summary

    def print_summary(self, summary: Dict[str, Any]):
        """打印测试汇总"""
        print(f"\n{'='*80}")
        print(f"📊 API测试汇总报告")
        print(f"{'='*80}")

        metadata = summary["metadata"]
        overall = summary["overall"]

        print(f"🕒 测试时间: {metadata['start_time']} -> {metadata['end_time']}")
        print(f"⏱️  总耗时: {metadata['total_duration']}秒")
        print(f"🎯 目标服务器: {metadata['base_url']}")
        print(f"📋 测试套件数: {metadata['test_count']}")

        print(f"\n📈 总体统计:")
        print(f"   测试总数: {overall['total_tests']}")
        print(f"   成功: {overall['total_passed']}")
        print(f"   失败: {overall['total_failed']}")
        print(f"   成功率: {overall['success_rate']}%")

        print(f"\n📋 套件统计:")
        print(f"   完成: {overall['completed_suites']}")
        print(f"   失败: {overall['failed_suites']}")
        print(f"   跳过: {overall['skipped_suites']}")

        print(f"\n📝 详细结果:")
        for test_name, result in summary["details"].items():
            status_emoji = "✅" if result["status"] == "completed" else "❌" if result["status"] == "failed" else "⏭️"
            print(f"   {status_emoji} {test_name.upper()}: {result['passed']}/{result['total']} ({result['success_rate']:.1f}%) - {result['duration']}s")

        # 显示错误详情
        error_count = 0
        for test_name, result in summary["details"].items():
            if result["errors"]:
                if error_count == 0:
                    print(f"\n❌ 错误详情:")
                error_count += 1
                print(f"   {test_name.upper()}:")
                for i, error in enumerate(result["errors"][:3], 1):  # 只显示前3个错误
                    error_msg = error.get("error", str(error))
                    print(f"     {i}. {error_msg}")
                if len(result["errors"]) > 3:
                    print(f"     ... 和 {len(result['errors']) - 3} 个其他错误")

        print(f"{'='*80}")

    def save_report(self, summary: Dict[str, Any], detailed: bool = False):
        """保存测试报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存JSON报告
        json_report_path = Path(f"e2e_tests/logs/all_tests_report_{timestamp}.json")
        json_report_path.parent.mkdir(parents=True, exist_ok=True)

        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"📄 详细报告已保存: {json_report_path}")

        if detailed:
            # 保存可读报告
            text_report_path = Path(f"e2e_tests/logs/all_tests_summary_{timestamp}.txt")

            with open(text_report_path, 'w', encoding='utf-8') as f:
                f.write(f"API测试汇总报告 - {timestamp}\n")
                f.write("=" * 80 + "\n\n")

                metadata = summary["metadata"]
                overall = summary["overall"]

                f.write(f"测试基本信息:\n")
                f.write(f"  开始时间: {metadata['start_time']}\n")
                f.write(f"  结束时间: {metadata['end_time']}\n")
                f.write(f"  总耗时: {metadata['total_duration']}秒\n")
                f.write(f"  目标服务器: {metadata['base_url']}\n")
                f.write(f"  测试套件数: {metadata['test_count']}\n\n")

                f.write(f"总体统计:\n")
                f.write(f"  测试总数: {overall['total_tests']}\n")
                f.write(f"  成功: {overall['total_passed']}\n")
                f.write(f"  失败: {overall['total_failed']}\n")
                f.write(f"  成功率: {overall['success_rate']}%\n\n")

                f.write(f"套件统计:\n")
                f.write(f"  完成: {overall['completed_suites']}\n")
                f.write(f"  失败: {overall['failed_suites']}\n")
                f.write(f"  跳过: {overall['skipped_suites']}\n\n")

                f.write(f"详细结果:\n")
                for test_name, result in summary["details"].items():
                    status = "完成" if result["status"] == "completed" else "失败" if result["status"] == "failed" else "跳过"
                    f.write(f"  {test_name.upper()}: {status} - {result['passed']}/{result['total']} ({result['success_rate']:.1f}%) - {result['duration']}s\n")

                # 错误详情
                error_count = 0
                for test_name, result in summary["details"].items():
                    if result["errors"]:
                        if error_count == 0:
                            f.write(f"\n错误详情:\n")
                        error_count += 1
                        f.write(f"  {test_name.upper()}:\n")
                        for i, error in enumerate(result["errors"], 1):
                            error_msg = error.get("error", str(error))
                            f.write(f"    {i}. {error_msg}\n")

            print(f"📄 可读报告已保存: {text_report_path}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='API测试运行器')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')
    parser.add_argument('--tests', '-t', nargs='+',
                        choices=list(TEST_MODULES.keys()) + ['all'],
                        default=['all'],
                        help='要运行的测试 (默认: all)')
    parser.add_argument('--detailed-report', '-d', action='store_true',
                        help='生成详细报告文件')
    parser.add_argument('--list-tests', '-l', action='store_true',
                        help='列出所有可用的测试')

    args = parser.parse_args()

    # 列出可用测试
    if args.list_tests:
        print("可用的测试列表:")
        for test_name, module_name in TEST_MODULES.items():
            print(f"  {test_name}: {module_name}")
        return

    # 确定要运行的测试
    if 'all' in args.tests:
        test_names = list(TEST_MODULES.keys())
    else:
        test_names = args.tests

    # 运行测试
    runner = TestRunner(args.url)
    summary = await runner.run_tests(test_names)

    # 打印汇总
    runner.print_summary(summary)

    # 保存报告
    runner.save_report(summary, args.detailed_report)

if __name__ == "__main__":
    asyncio.run(main())
