"""
外部服务连接测试 - 对应AC3: 云服务账号配置
"""
import pytest
from unittest.mock import patch, MagicMock
import subprocess
import os

class TestExternalServiceConnections:
    """外部服务连接测试"""

    def test_test_connections_script_exists(self):
        """测试test_connections.py脚本是否存在"""
        script_path = os.path.join("scripts", "test_connections.py")

        # 这个测试会失败，因为test_connections.py脚本还不存在
        assert os.path.exists(script_path), f"scripts/test_connections.py脚本应该存在"

    def test_volcano_llm_connection_success(self):
        """场景9: 火山方舟LLM服务连接成功"""
        # 模拟运行test_connections.py --test-llm
        script_path = "scripts/test_connections.py"

        # 这个测试会失败，因为脚本还不存在
        if not os.path.exists(script_path):
            pytest.fail("test_connections.py脚本不存在，需要先创建")

        # 如果脚本存在，测试其LLM连接功能
        try:
            result = subprocess.run(
                ["python", script_path, "--test-llm"],
                capture_output=True,
                text=True,
                timeout=30
            )

            # 预期连接成功
            assert result.returncode == 0, f"LLM连接测试应该成功，但返回: {result.stderr}"
            # 检查输出信息 (日志现在输出到stdout)
            output_text = result.stdout + result.stderr
            assert "连接成功" in output_text or "success" in output_text.lower()

        except subprocess.TimeoutExpired:
            pytest.fail("LLM连接测试超时")
        except FileNotFoundError:
            pytest.fail("无法找到test_connections.py脚本")

    def test_volcano_rtc_connection_success(self):
        """场景10: 火山RTC服务连接成功"""
        script_path = "scripts/test_connections.py"

        if not os.path.exists(script_path):
            pytest.fail("test_connections.py脚本不存在，需要先创建")

        try:
            result = subprocess.run(
                ["python", script_path, "--test-volcano"],
                capture_output=True,
                text=True,
                timeout=30
            )

            assert result.returncode == 0, f"RTC连接测试应该成功，但返回: {result.stderr}"
            output_text = result.stdout + result.stderr
            assert "RTC服务可用" in output_text or "rtc" in output_text.lower()

        except subprocess.TimeoutExpired:
            pytest.fail("RTC连接测试超时")
        except FileNotFoundError:
            pytest.fail("无法找到test_connections.py脚本")

    def test_supabase_connection_success(self):
        """场景11: Supabase服务连接成功"""
        script_path = "scripts/test_connections.py"

        if not os.path.exists(script_path):
            pytest.fail("test_connections.py脚本不存在，需要先创建")

        try:
            result = subprocess.run(
                ["python", script_path, "--test-supabase"],
                capture_output=True,
                text=True,
                timeout=30
            )

            assert result.returncode == 0, f"Supabase连接测试应该成功，但返回: {result.stderr}"
            output_text = result.stdout + result.stderr
            assert "supabase" in output_text.lower() and "成功" in output_text

        except subprocess.TimeoutExpired:
            pytest.fail("Supabase连接测试超时")
        except FileNotFoundError:
            pytest.fail("无法找到test_connections.py脚本")

class TestAPIKeyValidation:
    """API密钥验证测试"""

    def test_invalid_volcano_llm_key_handling(self):
        """场景12: API密钥认证失败处理"""
        # 这个测试验证当API密钥无效时的错误处理

        # 模拟无效的API密钥
        with patch.dict(os.environ, {"VOLCANO_LLM_APP_KEY": "invalid_key"}):
            script_path = "scripts/test_connections.py"

            if not os.path.exists(script_path):
                pytest.skip("test_connections.py脚本不存在，跳过此测试")

            try:
                result = subprocess.run(
                    ["python", script_path, "--test-llm"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                # 应该返回失败状态码
                assert result.returncode != 0, "无效API密钥应该导致连接失败"

                # 错误信息应该指出认证问题
                error_output = result.stderr.lower()
                assert "401" in error_output or "403" in error_output or "auth" in error_output

            except subprocess.TimeoutExpired:
                pytest.fail("API密钥验证测试超时")

    def test_invalid_rtc_app_id_handling(self):
        """场景13: 服务配置错误检测"""
        with patch.dict(os.environ, {"VOLCANO_RTC_APP_ID": "invalid_app_id"}):
            script_path = "scripts/test_connections.py"

            if not os.path.exists(script_path):
                pytest.skip("test_connections.py脚本不存在，跳过此测试")

            try:
                result = subprocess.run(
                    ["python", script_path, "--test-volcano"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                # 应该返回失败状态码并指出配置错误
                assert result.returncode != 0, "无效应用ID应该导致连接失败"
                assert "app" in result.stderr.lower() or "id" in result.stderr.lower()

            except subprocess.TimeoutExpired:
                pytest.fail("RTC配置错误测试超时")

class TestEnvironmentVariableValidation:
    """环境变量验证测试"""

    def test_required_volcano_environment_variables(self):
        """测试火山引擎相关环境变量是否设置"""
        from api.settings import settings

        # 这些环境变量应该从.env或1.env文件中加载
        required_vars = [
            "VOLCANO_LLM_APP_KEY",
            # 注意: 根据当前settings.py，一些RTC变量可能还没有定义
        ]

        # 至少VOLCANO_LLM_APP_KEY应该存在
        assert settings.VOLCANO_LLM_APP_KEY is not None, "VOLCANO_LLM_APP_KEY应该被设置"

    def test_required_supabase_environment_variables(self):
        """测试Supabase相关环境变量是否设置"""
        from api.settings import settings

        assert settings.SUPABASE_URL is not None, "SUPABASE_URL应该被设置"
        assert settings.SUPABASE_SERVICE_ROLE_KEY is not None, "SUPABASE_SERVICE_ROLE_KEY应该被设置"
