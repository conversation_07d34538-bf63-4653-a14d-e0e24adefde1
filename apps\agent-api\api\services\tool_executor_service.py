"""
工具执行服务 - 故事1.6-B Function Calling核心实现

处理LLM的工具调用请求，执行相应的工具函数。
实现架构师要求的工具定义精确性和降级策略。

关键特性：
1. 工具定义精确性 - 详细的description和examples字段
2. 工具调用降级策略 - 错误处理和用户友好回复
3. 可扩展的工具注册机制
4. 线程安全的用户上下文管理 - 使用contextvars避免竞态条件
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from contextvars import ContextVar

from api.services.user_profile_service import user_profile_service
from api.services.reminder_service import ReminderService, get_reminder_service
from api.models.schema_models import ToolCall, ToolResult
from api.settings import logger

# 使用contextvars进行线程安全的用户上下文管理
current_user_id: ContextVar[Optional[str]] = ContextVar('current_user_id', default=None)


class ToolExecutorService:
    """
    工具执行服务 - 处理Function Calling工具调用

    实现架构师建议：
    1. 工具定义精确性 - 明确的schema和examples
    2. 工具调用降级策略 - 错误处理和友好回复
    3. 扩展性设计 - 支持多种工具类型
    """

    def __init__(self, reminder_service: Optional[ReminderService] = None):
        self.reminder_service = reminder_service or get_reminder_service()
        self.logger = logging.getLogger(__name__)

    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """
        获取所有可用工具的定义

        实现架构师要求的工具定义精确性：
        1. 详细的description字段引导LLM
        2. 明确的参数类型和格式要求
        3. examples字段提供示例格式

        Returns:
            工具定义列表，符合OpenAI Function Calling规范
        """
        return [
            {
                "type": "function",
                "function": {
                    "name": "set_reminder",
                    "description": (
                        "当用户想要设置一个提醒、待办事项或闹钟时调用此函数。"
                        "支持各种时间表达方式，包括具体日期时间、相对时间和自然语言时间。"
                        "系统会自动解析时间并创建提醒，然后生成确认回复。"
                        "适用场景：用户说'提醒我...', '明天...要...', '设个闹钟', '别忘了...'等。"
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string",
                                "description": (
                                    "提醒的具体内容，应该简洁明确地描述用户要被提醒的事情。"
                                    "例如：'吃药'、'开会'、'买菜'、'给妈妈打电话'。"
                                )
                            },
                            "time": {
                                "type": "string",
                                "format": "date-time",
                                "description": (
                                    "提醒的时间，支持多种格式：\n"
                                    "1. ISO 8601格式：'2024-12-20T15:00:00Z'\n"
                                    "2. 简化格式：'2024-12-20 15:00', '12-20 15:00'\n"
                                    "3. 自然语言：'明天下午3点', '后天上午9点', '今晚8点'\n"
                                    "4. 相对时间：'1小时后', '30分钟后', '明天'\n"
                                    "系统会自动解析并转换为准确的时间。"
                                )
                            }
                        },
                        "required": ["content", "time"]
                    },
                    "examples": [
                        {
                            "description": "设置明天的提醒",
                            "arguments": {
                                "content": "吃药",
                                "time": "明天下午3点"
                            }
                        },
                        {
                            "description": "设置具体日期时间的提醒",
                            "arguments": {
                                "content": "参加会议",
                                "time": "2024-12-20T14:30:00Z"
                            }
                        },
                        {
                            "description": "设置相对时间的提醒",
                            "arguments": {
                                "content": "休息一下",
                                "time": "1小时后"
                            }
                        }
                    ]
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_user_profile",
                    "description": "获取当前用户的个人资料信息，包括昵称、年龄、核心需求等。仅能查看当前登录用户的资料。",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        ]

    async def execute_tool_calls(self, tool_calls: List[ToolCall], context: Dict[str, Any] = None) -> List[ToolResult]:
        """
        执行多个工具调用

        实现架构师要求的工具调用降级策略：
        1. 完整的异常处理机制
        2. 用户友好的错误信息
        3. 确保单个工具失败不影响其他工具
        4. 线程安全的用户上下文管理

        Args:
            tool_calls: 工具调用列表
            context: 执行上下文，包含用户ID等信息

        Returns:
            工具执行结果列表
        """
        # 从context中提取并设置用户ID到上下文变量中
        if context and context.get('userId'):
            self.set_user_context(context['userId'])

        results = []

        for tool_call in tool_calls:
            try:
                result = await self._execute_single_tool(tool_call)
                results.append(result)

            except Exception as e:
                # 架构师要求：工具调用降级策略 - 错误转换为友好回复
                self.logger.error(f"工具调用执行失败: {tool_call.name}, 错误: {str(e)}", exc_info=True)

                error_result = ToolResult(
                    tool_call_id=tool_call.id,
                    content=f"工具执行失败: {str(e)}",
                    success=False,
                    error=str(e)
                )
                results.append(error_result)

        return results

    async def _execute_single_tool(self, tool_call: ToolCall) -> ToolResult:
        """
        执行单个工具调用

        Args:
            tool_call: 工具调用对象

        Returns:
            工具执行结果
        """
        self.logger.info(f"执行工具调用: {tool_call.name}, ID: {tool_call.id}")

        if tool_call.name == "set_reminder":
            return await self._execute_set_reminder(tool_call)
        elif tool_call.name == "get_user_profile":
            return await self._execute_get_user_profile(tool_call)
        else:
            return ToolResult(
                tool_call_id=tool_call.id,
                content=f"未知的工具: {tool_call.name}",
                success=False,
                error=f"unsupported_tool: {tool_call.name}"
            )

    async def _execute_set_reminder(self, tool_call: ToolCall) -> ToolResult:
        """
        执行set_reminder工具调用

        实现与ReminderService的集成，处理提醒创建逻辑
        使用线程安全的上下文变量获取用户ID

        Args:
            tool_call: set_reminder工具调用

        Returns:
            工具执行结果
        """
        try:
            # 从上下文变量获取用户ID，线程安全
            user_id = current_user_id.get()
            if not user_id:
                # 如果没有设置用户上下文，返回错误
                return ToolResult(
                    tool_call_id=tool_call.id,
                    content="用户身份验证失败，请重新登录",
                    success=False,
                    error="missing_user_context"
                )

            # 调用ReminderService创建提醒
            result = await self.reminder_service.create_reminder_from_tool(
                user_id=user_id,
                arguments=tool_call.arguments
            )

            # 转换结果格式
            if result.get("success"):
                return ToolResult(
                    tool_call_id=tool_call.id,
                    content=result["message"],
                    success=True
                )
            else:
                return ToolResult(
                    tool_call_id=tool_call.id,
                    content=result["message"],
                    success=False,
                    error=result.get("error", "unknown_error")
                )

        except Exception as e:
            self.logger.error(f"set_reminder工具执行异常: {str(e)}", exc_info=True)
            return ToolResult(
                tool_call_id=tool_call.id,
                content="创建提醒时遇到了技术问题，请稍后再试",
                success=False,
                error=str(e)
            )

    async def _execute_get_user_profile(self, tool_call: ToolCall) -> ToolResult:
        """
        执行get_user_profile工具调用

        安全修复：只允许查看当前认证用户的资料，防止未授权访问其他用户数据

        Args:
            tool_call: get_user_profile工具调用

        Returns:
            工具执行结果
        """
        try:
            # 从上下文变量获取当前用户ID，线程安全且防止未授权访问
            user_id = current_user_id.get()
            if not user_id:
                return ToolResult(
                    tool_call_id=tool_call.id,
                    content="用户身份验证失败，请重新登录",
                    success=False,
                    error="missing_user_context"
                )

            # 调用用户画像服务，只查看当前用户的资料
            profile = await user_profile_service.get_user_profile(user_id)

            if profile:
                profile_info = f"用户昵称: {profile.get('nickname', '未设置')}, " \
                             f"年龄: {profile.get('age', '未知')}"
                return ToolResult(
                    tool_call_id=tool_call.id,
                    content=profile_info,
                    success=True
                )
            else:
                return ToolResult(
                    tool_call_id=tool_call.id,
                    content="未找到用户资料",
                    success=False,
                    error="user_not_found"
                )

        except Exception as e:
            self.logger.error(f"get_user_profile工具执行异常: {str(e)}", exc_info=True)
            return ToolResult(
                tool_call_id=tool_call.id,
                content="获取用户资料时遇到了技术问题",
                success=False,
                error=str(e)
            )

    # 向后兼容的方法，保持原有接口
    def execute(self, tool_name: str, **kwargs: Any) -> Any:
        """
        同步执行工具（向后兼容接口）

        Args:
            tool_name: 工具名称
            **kwargs: 工具参数

        Returns:
            工具执行结果
        """
        # 该方法保持原有设计，但不建议使用
        # 推荐使用新的execute_tool_calls异步接口
        raise NotImplementedError("请使用execute_tool_calls方法进行异步工具调用")

    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个工具（向后兼容接口）

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            工具执行结果字典
        """
        # 创建ToolCall对象
        tool_call = ToolCall(
            id=f"compat_{int(datetime.now().timestamp())}",
            name=tool_name,
            arguments=arguments
        )

        # 执行工具调用
        result = await self._execute_single_tool(tool_call)

        # 转换返回格式
        return {
            "success": result.success,
            "content": result.content,
            "error": result.error
        }

    def set_user_context(self, user_id: str) -> None:
        """
        设置用户上下文 - 使用上下文变量进行线程安全的用户ID管理

        修复竞态条件：使用contextvars替代实例变量，确保在并发环境中
        每个异步任务都有独立的用户上下文，避免用户数据混淆的安全漏洞。

        Args:
            user_id: 当前用户ID
        """
        current_user_id.set(user_id)
        self.logger.debug(f"用户上下文已设置: {user_id}")


async def get_tool_executor_service() -> ToolExecutorService:
    """获取工具执行服务实例"""
    from api.services.reminder_service import get_reminder_service

    # 获取ReminderService实例
    reminder_service = await get_reminder_service()

    # 创建ToolExecutorService实例并传入依赖
    return ToolExecutorService(reminder_service=reminder_service)
