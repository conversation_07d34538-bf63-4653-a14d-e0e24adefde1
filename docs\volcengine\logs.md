
### 错误 1: 数据库函数未找到

*   **程序路径/链接:** 数据库函数 `public.get_table_info`
*   **错误信息:**
    ```
    获取表信息失败: {'code': 'PGRST202', 'details': 'Searched for the function public.get_table_info with parameter table_name or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': 'Perhaps you meant to call the function public.get_all_tables', 'message': 'Could not find the function public.get_table_info(table_name) in the schema cache'}
    ```
*   **参数信息:** 试图调用的函数是 `get_table_info`，传入的参数是 `table_name`。
*   **上下文信息:** 这个警告发生在 `2025-07-18 14:55:20`。当时系统正在尝试结束一个聊天会话（Session ID: `d4182fbe-0da9-4fb8-adb2-3a1fc16b72b6`）。日志提示可能应该调用 `public.get_all_tables` 函数。

---

### 错误 2: 会话结束时更新状态失败

*   **程序路径/链接:** `D:\pys\xinqiao\apps\agent-api\api\services\session_service.py`, 第 628 行, 在 `end_session` 函数中。
*   **错误信息:**
    ```
    AttributeError: 'APIResponse[TypeVar]' object has no attribute 'error'
    ```
    完整的 Traceback 指出错误发生在 `if response_main.error:` 这一行。
*   **参数信息:**
    *   请求: `PUT /api/v1/chat/sessions/d4182fbe-0da9-4fb8-adb2-3a1fc16b72b6/end`
    *   更新载荷: `{'status': 'completed', 'summary': '...', 'topic': None, 'tags': [], 'ended_at': '...', 'updated_at': '...'}`
*   **上下文信息:** 这个错误发生在 `2025-07-18 14:55:22`，紧接着上面的数据库函数警告。代码试图检查 Supabase API 响应中是否存在 `.error` 属性，但该对象没有这个属性，导致程序崩溃并返回 `500 Internal Server Error`。

---

### 错误 3: 获取提醒列表时参数不匹配

*   **程序路径/链接:** `D:\pys\xinqiao\apps\agent-api\api\routes\reminder_routes.py`, 第 71 行, 在 `get_reminders` 函数中。
*   **错误信息:**
    ```
    TypeError: ReminderService.get_user_reminders() got an unexpected keyword argument 'limit'
    ```
*   **参数信息:**
    *   请求: `GET /api/v1/reminders/?limit=10&offset=0`
    *   传入的参数: `limit=10`
*   **上下文信息:** 这个错误发生在 `2025-07-18 14:55:23`。API 路由层（`reminder_routes.py`）接收了 `limit` 和 `offset` 作为查询参数，但在调用服务层的方法 `ReminderService.get_user_reminders()` 时，传递了一个该方法不支持的 `limit` 关键字参数，导致了 `TypeError` 和 `500 Internal Server Error`。

---

### 错误 4: 创建提醒时参数不匹配

*   **程序路径/链接:** `D:\pys\xinqiao\apps\agent-api\api\routes\reminder_routes.py`, 第 124 行, 在 `create_reminder` 函数中。
*   **错误信息:**
    ```
    TypeError: ReminderService.create_reminder() got an unexpected keyword argument 'content'
    ```
*   **参数信息:**
    *   请求: `POST /api/v1/reminders/`
    *   传入的参数: `content='测试提醒...'`
*   **上下文信息:** 这个错误发生在 `2025-07-18 14:55:23`。与上一个错误类似，API 路由层在调用服务层的方法 `ReminderService.create_reminder()` 时，传递了一个该方法不支持的 `content` 关键字参数，导致了 `TypeError` 和 `500 Internal Server Error`。

---

### 错误 5: 数据库连接失败 (DNS解析错误)

这是一个根本性的配置或网络问题，在多个地方出现。

*   **程序路径/链接:** 数据库连接层 (`psycopg` 和 `sqlalchemy`)。
*   **错误信息:**
    ```
    sqlalchemy.exc.OperationalError: (psycopg.OperationalError) [Errno 11001] getaddrinfo failed
    (Background on this error at: https://sqlalche.me/e/20/e3q8)
    ```
    这个错误表示应用程序无法解析数据库服务器的主机名。这通常是由于 DNS 问题、网络配置错误或环境变量中数据库主机名（`DB_HOST`）不正确导致的。
*   **上下文信息:** 此错误在 `2025-07-18 14:55:26` 之后多次出现，导致所有需要访问数据库的API请求都失败并返回 `500 Internal Server Error`。具体影响的请求包括：
    1.  **准备RTC会话:** `POST /api/v1/rtc/prepare_session` (14:55:26)
    2.  **结束RTC会话:** `POST /api/v1/rtc/end_session` (14:55:28)
    3.  **获取RTC会话状态:** `GET /api/v1/rtc/sessions/test-rtc-50f4ad4d-a051-4006-b383-d709aa8897c6/status` (14:55:31)
    4.  **获取RTC会话配置:** `GET /api/v1/rtc/sessions/test-rtc-50f4ad4d-a051-4006-b383-d709aa8897c6/config` (14:55:34)

---

### 错误 6: 火山引擎LLM流式API返回404

*   **程序路径/链接:** 外部API调用 `火山引擎LLM API (流式)`
*   **错误信息:**
    ```
    火山引擎LLM API流式响应错误: 404
    ```
*   **参数信息:** 无明确参数记录，但与 `text_chat_routes` 相关。
*   **上下文信息:** 这个错误发生在 `2025-07-18 14:55:42`。在处理文本聊天请求 `POST /api/v1/chat/text_message` 时，后端服务尝试调用火山引擎的流式LLM API，但收到了 `404 Not Found` 错误。这表明请求的API端点地址不正确或已失效。

---

### 错误 7: Webhook安全验证失败

*   **程序路径/链接:** `api/routes/rtc_webhook_routes.py`
*   **错误信息:**
    ```
    WARNING - Webhook请求缺少Signature头
    ERROR - 安全验证失败: 缺少签名头
    ```
*   **参数信息:**
    *   请求: `POST /api/v1/chat/rtc_event_handler`
*   **上下文信息:** 这个错误发生在 `2025-07-18 14:55:42`。端到端测试脚本在调用RTC事件回调接口时，没有在请求头中提供 `Signature` 字段，导致安全验证失败，服务器返回 `401 Unauthorized`。

---

### 错误 8: 应用程序关闭时关闭Supabase客户端失败

*   **程序路径/链接:** `D:\pys\xinqiao\apps\agent-api\db\supabase_init.py`, 第 100 行, 在 `aclose` 函数中。
*   **错误信息:**
    ```
    AttributeError: 'AsyncClient' object has no attribute 'aclose'
    ```
*   **上下文信息:** 这个错误在 `15:04:43` 和 `15:06:22` 多次出现。当FastAPI应用因为代码变动（`WatchFiles detected changes...`）而重新加载时，应用会执行关闭逻辑。在关闭逻辑中，代码尝试调用 `supabase.AsyncClient` 对象的 `aclose()` 方法来关闭连接，但该对象没有这个方法，导致 `AttributeError`。日志中很贴心地列出了该对象的所有可用属性和方法，证实了 `aclose` 的缺失。
