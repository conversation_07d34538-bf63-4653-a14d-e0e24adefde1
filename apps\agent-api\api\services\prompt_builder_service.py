from typing import Optional, Dict, Any, List
from api.settings import logger

# 临时定义UserMemory类，替代缺失的agno_memory_service
# TODO: 在后续故事中集成真正的记忆服务实现
class AgnoUserMemory:
    """临时的用户记忆类，用于占位"""
    def __init__(self, memory: str):
        self.memory = memory

# Simplified Python versions of your templates
BASE_CHARACTER_TEMPLATE_PY = """
# AI 伙伴角色定义: {{character_name}}
你是一个名为"{{character_name}}"的AI伙伴。
{{character_personality_section}}
{{character_intro_section}}
你的核心目标是与用户进行友好、支持性的对话。请使用简体中文回答。
"""

USER_PROFILE_TEMPLATE_PY = """
# 关于我正在对话的用户
{{nickname_part}}
{{core_needs_part}}
{{interests_part}}
{{communication_style_part}}
{{ai_summary_part}}
"""

SERVER_MEMORY_TEMPLATE_PY = """
# 关于这位用户，你还需要记住以下重要事情 (来自你的长期记忆):
{{server_memory}}
"""

ACTIVITY_TEMPLATES_PY = {
    "breathing": "当前你正在引导用户进行呼吸练习。请根据用户的反馈和练习阶段提供合适的引导和鼓励。",
    "mood_tracking": "当前用户正在记录或反思自己的心情。请以理解和支持的态度回应，并可以根据用户分享的内容提供一些积极的反馈或小建议。",
    "chat": "你正在与用户进行日常对话，请参考以下信息以提供更个性化的回应。" # Default for chat
    # ... other activity templates from your frontend
}


class PromptBuilderService:
    @staticmethod
    def _get_user_profile_section(
        user_profile_data: Optional[Dict[str, Any]], # Combined data from users & user_profiles
        prompt_params: Optional[Dict[str, Any]] = None
    ) -> str:
        if not user_profile_data:
            # Ensure even if user_profile_data is None, we return a valid default string.
            return USER_PROFILE_TEMPLATE_PY.replace(
                "{{nickname_part}}", "你正在和'朋友'聊天。\n"
            ).replace(
                "{{core_needs_part}}", "Ta的核心需求：我会尝试在对话中了解Ta更具体的期望。\n"
            ).replace(
                "{{interests_part}}", "Ta的兴趣包括: '待了解'。\n"
            ).replace(
                "{{communication_style_part}}", "Ta偏好的沟通风格是'balanced'。\n"
            ).replace(
                "{{ai_summary_part}}", ""
            )

        prompt_params = prompt_params or {}
        replacements: Dict[str, str] = {}

        nickname = prompt_params.get("userNickname") or user_profile_data.get("nickname", "朋友")
        replacements["nickname_part"] = f"你正在和'{nickname}'聊天。\n"

        core_needs = user_profile_data.get("core_needs")
        if core_needs and isinstance(core_needs, list) and core_needs: # Check if list and not empty
            needs_texts = []
            for need_item in core_needs: # core_needs is now guaranteed to be a list
                if isinstance(need_item, str):
                    if need_item.startswith("custom:"): needs_texts.append(need_item.replace("custom:", "").strip())
                    else: needs_texts.append({"stress_relief":"缓解压力","emotional_support":"情感支持"}.get(need_item, need_item))
            if needs_texts:
                replacements["core_needs_part"] = f"Ta的核心需求可能包括：'{', '.join(needs_texts)}'。\n"
            else: replacements["core_needs_part"] = "Ta希望获得通用的陪伴和支持。\n"
        else:
            replacements["core_needs_part"] = "Ta的核心需求：我会尝试在对话中了解Ta更具体的期望。\n"

        interests_data = user_profile_data.get("interests") # Get data, could be None
        if interests_data and isinstance(interests_data, list) and interests_data: # Check if list and not empty
            replacements["interests_part"] = f"Ta的兴趣包括: '{', '.join(interests_data)}'。\n"
        else:
            replacements["interests_part"] = "Ta的兴趣包括: '待了解'。\n"

        comm_style = user_profile_data.get("communication_style_preference", "balanced")
        replacements["communication_style_part"] = f"Ta偏好的沟通风格是'{comm_style}'。\n" # Add mapping if needed

        ai_summary = user_profile_data.get("personality_summary_ai")
        replacements["ai_summary_part"] = f"\nAI对Ta的画像洞察：{ai_summary}\n" if ai_summary else ""

        profile_section = USER_PROFILE_TEMPLATE_PY
        for key, value in replacements.items():
            profile_section = profile_section.replace(f"{{{{{key}}}}}", value)
        return profile_section

    @staticmethod
    async def build_dynamic_system_prompt(
        user_profile_data: Optional[Dict[str, Any]], # Combined from users & user_profiles
        ai_character_data: Optional[Dict[str, Any]],
        agno_memories_data: List[AgnoUserMemory],
        short_term_history_data: List[Dict[str, Any]], # List of {"role": ..., "content": ...}
        user_mood_info_str: str,
        prompt_params: Optional[Dict[str, Any]] = None
    ) -> str:
        final_prompt_parts = []
        prompt_params = prompt_params or {}
        char_name = "心桥"

        # 1. AI Character Base Prompt
        if ai_character_data:
            char_name = ai_character_data.get("name", "心桥")
            base_template = ai_character_data.get("system_prompt") or BASE_CHARACTER_TEMPLATE_PY

            personality_section = f"你的个性是：{ai_character_data.get('personality', '友好、乐于助人')}。"
            intro_section = f"你的开场白或介绍可以是：{ai_character_data.get('intro', '你好！')}"

            processed_char_prompt = base_template.replace("{{character_name}}", char_name)
            processed_char_prompt = processed_char_prompt.replace("{{character_personality_section}}", personality_section)
            processed_char_prompt = processed_char_prompt.replace("{{character_intro_section}}", intro_section)
            final_prompt_parts.append(processed_char_prompt)
        else:
            # Fallback default character if none found/specified
            final_prompt_parts.append(BASE_CHARACTER_TEMPLATE_PY.replace("{{character_name}}", char_name).replace("{{character_personality_section}}","").replace("{{character_intro_section}}",""))

        # 2. User Profile Section
        profile_section = PromptBuilderService._get_user_profile_section(user_profile_data, prompt_params)
        final_prompt_parts.append(profile_section)

        # 3. Agno Semantic Memories
        if agno_memories_data:
            memory_texts = [mem.memory for mem in agno_memories_data if mem.memory]
            if memory_texts:
                formatted_memories = "\n- ".join(memory_texts)
                server_memory_section = SERVER_MEMORY_TEMPLATE_PY.replace("{{server_memory}}", f"- {formatted_memories}")
                final_prompt_parts.append(server_memory_section)
        else:
            final_prompt_parts.append(SERVER_MEMORY_TEMPLATE_PY.replace("{{server_memory}}", "我们之间还没有太多专属记忆，期待与你创造更多！"))

        # 4. Current Mood Info
        if user_mood_info_str:
            final_prompt_parts.append(f"\n# 用户当前情况参考\n{user_mood_info_str}\n")

        # 5. Activity Specific Instructions from promptParams
        activity_type = prompt_params.get("activityType")
        if activity_type and isinstance(activity_type, str) and activity_type in ACTIVITY_TEMPLATES_PY:
            final_prompt_parts.append(f"\n# 当前活动：{activity_type}\n{ACTIVITY_TEMPLATES_PY[activity_type]}\n")
        elif not activity_type: # Default chat instruction if no specific activity
             final_prompt_parts.append(f"\n# 当前活动：chat\n{ACTIVITY_TEMPLATES_PY['chat']}\n")


        # Add short_term_history placeholder (LLM service usually handles this as message list)
        # final_prompt_parts.append("\n# 对话历史 (最近):\n{HISTORY_PLACEHOLDER}\n")

        final_prompt_parts.append("\n请综合以上所有信息，结合当前的对话历史，给出你的回复。")
        final_prompt_parts.append("确保你的回复既体现了AI角色的特点，也考虑了用户的个性和当前情境。")

        final_system_prompt = "\n".join(final_prompt_parts)
        logger.debug(f"Generated System Prompt (length: {len(final_system_prompt)}). Preview: {final_system_prompt[:300]}...")
        return final_system_prompt

    async def build_messages(
        self,
        user_message: str,
        memory_context: Dict[str, Any],
        character_id: str = "default"
    ) -> List[Dict[str, str]]:
        """
        Build messages list for LLM call.

        Args:
            user_message: User's message
            memory_context: Memory context from memory service
            character_id: Character ID for AI personality

        Returns:
            List of message dictionaries for LLM
        """
        messages = []

        # 1. 获取角色的完整system prompt
        system_content = await self._get_character_system_prompt(character_id)

        # 2. 如果有记忆上下文，添加到系统消息中
        if memory_context.get("memories"):
            memories_text = "\n".join(memory_context["memories"])
            system_content += f"\n\n## 关于用户的记忆\n{memories_text}"
        elif memory_context.get("context"):
            system_content += f"\n\n## 关于用户的记忆\n{memory_context['context']}"

        messages.append({
            "role": "system",
            "content": system_content
        })

        # 3. 用户消息
        messages.append({
            "role": "user",
            "content": user_message
        })

        return messages

    async def _get_character_system_prompt(self, character_id: str) -> str:
        """
        从数据库获取角色的完整system prompt

        Args:
            character_id: 角色ID

        Returns:
            完整的system prompt文本
        """
        try:
            # 导入CharacterService来获取角色数据
            from api.services.character_service import character_service

            # 处理default角色的查询
            if character_id == "default":
                # 查询默认角色
                characters_list = await character_service.get_characters_list(limit=100)
                default_character = None
                for char in characters_list.data:
                    if char.is_default:
                        default_character = char
                        break

                if default_character:
                    character_data = default_character
                else:
                    logger.warning("未找到默认角色，使用fallback system prompt")
                    return self._get_fallback_system_prompt()
            else:
                # 查询指定角色
                character_data = await character_service.get_character_by_id(character_id)

            if not character_data:
                logger.warning(f"未找到角色 {character_id}，使用fallback system prompt")
                return self._get_fallback_system_prompt()

            # 从personality字段中获取完整的system_prompt
            if character_data.personality and isinstance(character_data.personality, dict):
                system_prompt = character_data.personality.get("system_prompt")
                if system_prompt and isinstance(system_prompt, str):
                    logger.info(f"成功获取角色 {character_data.name} 的完整system prompt (长度: {len(system_prompt)})")
                    return system_prompt

            # 如果没有完整的system_prompt，构建基础的
            logger.warning(f"角色 {character_data.name} 没有完整的system_prompt，使用基础描述")
            return f"你是 {character_data.name}。{character_data.description or '你是一个友善的AI助手。'}请用中文回复用户。"

        except Exception as e:
            logger.error(f"获取角色system prompt失败: {e}")
            return self._get_fallback_system_prompt()

    def _get_fallback_system_prompt(self) -> str:
        """
        获取fallback system prompt

        Returns:
            默认的system prompt
        """
        return "你是一个友善的AI助手，名叫心桥。请用中文回复用户。"

prompt_builder_service = PromptBuilderService()

async def get_prompt_builder_service() -> PromptBuilderService:
    """Get prompt builder service instance."""
    return prompt_builder_service
