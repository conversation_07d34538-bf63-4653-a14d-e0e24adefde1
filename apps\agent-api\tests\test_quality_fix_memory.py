"""
P0功能缺陷修复测试 - 记忆系统用户ID混淆问题

测试故事1.11-B的AC2：IMemoryService接口正确处理user_id和session_id参数，
Mem0MemoryServiceImpl调用使用正确的user_id，记忆搜索在用户级别而非会话级别进行。
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import asyncio
from typing import Dict, Any, List

from api.services.memory_service import IMemoryService, Mem0MemoryServiceImpl, ZepMemoryServiceImpl
from api.services.memory_service import Message


class TestP0MemoryFix:
    """P0功能缺陷修复测试类 - 记忆系统用户ID混淆问题"""

    @pytest.fixture
    def mem0_service(self):
        """创建Mem0记忆服务实例"""
        return Mem0MemoryServiceImpl()

    @pytest.fixture
    def zep_service(self):
        """创建Zep记忆服务实例"""
        return ZepMemoryServiceImpl()

    @pytest.fixture
    def mock_mem0_client(self):
        """模拟Mem0客户端"""
        mock_client = MagicMock()
        mock_client.search.return_value = [
            {
                "memory": "用户喜欢咖啡",
                "score": 0.95,
                "metadata": {"user_id": "user123", "session_id": "session_a"}
            }
        ]
        mock_client.get_all.return_value = [
            {
                "memory": "用户喜欢咖啡",
                "metadata": {"user_id": "user123", "session_id": "session_a"}
            }
        ]
        return mock_client

    # AC2 Scenario 2.1: 记忆服务正确使用用户ID搜索
    @pytest.mark.asyncio
    async def test_memory_service_search_uses_user_id(self, mem0_service):
        """
        测试场景2.1：记忆服务正确使用用户ID搜索
        Given 用户user123在多个会话中提及了"喜欢咖啡"
        And 记忆服务已正确存储用户级别的记忆
        When 系统调用memory_service.search_memory(user_id="user123", query="咖啡")
        Then 应返回所有与"咖啡"相关的用户记忆
        And 记忆来源应跨越该用户的多个会话
        """
        # 模拟_search_memory_sync方法
        with patch.object(mem0_service, '_search_memory_sync') as mock_search:
            mock_search.return_value = [
                {"role": "system", "content": "用户喜欢咖啡"},
                {"role": "system", "content": "用户偏爱拿铁"}
            ]

            # 调用搜索方法
            result = await mem0_service.search_memory(user_id="user123", query="咖啡")

            # 期望：返回用户级别的记忆
            assert isinstance(result, list)
            assert len(result) == 2
            assert any("咖啡" in msg["content"] for msg in result)

            # 验证调用时使用了正确的user_id
            mock_search.assert_called_once_with("user123", "咖啡", 5)

    # AC2 Scenario 2.2: 不同用户的记忆隔离
    @pytest.mark.asyncio
    async def test_memory_service_user_isolation(self, mem0_service):
        """
        测试场景2.2：不同用户的记忆隔离
        Given 用户user123和user456都在各自会话中提及了"喜欢咖啡"
        When 系统调用memory_service.search_memory_sync(user_id="user123", query="咖啡")
        Then 应仅返回user123的记忆
        And 不应返回user456的任何记忆数据
        """
        with patch('api.services.memory_service.MemoryClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client

            # 模拟只返回user123的记忆
            mock_client.search.return_value = [
                {
                    "memory": "user123喜欢咖啡",
                    "score": 0.95,
                    "metadata": {"user_id": "user123", "session_id": "session_a"}
                }
            ]

            service = Mem0MemoryServiceImpl()
            result = await service.search_memory(user_id="user123", query="咖啡")

            # 期望：只返回user123的记忆
            assert isinstance(result, list)
            assert len(result) > 0

            # 验证调用时使用了正确的user_id
            mock_client.search.assert_called_once()
            call_args = mock_client.search.call_args
            assert call_args[1]["user_id"] == "user123"

    # AC2 Scenario 2.3: 记忆服务接口参数正确传递
    @pytest.mark.asyncio
    async def test_memory_service_interface_parameters(self, mem0_service):
        """
        测试场景2.3：记忆服务接口参数正确传递
        Given 用户在会话session123中添加了新的记忆
        When 系统调用memory_service.add_memory(user_id="user123", session_id="session123", content="新记忆")
        Then Mem0MemoryServiceImpl应使用user_id="user123"调用外部API
        And 不应使用session_id作为用户标识
        And 记忆应正确关联到用户user123
        """
        with patch('api.services.memory_service.MemoryClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client

            service = Mem0MemoryServiceImpl()

            # 调用add_memory方法
            await service.add_memory(
                user_id="user123",
                session_id="session123",
                human_message="我喜欢咖啡",
                assistant_message="好的，我记住了您喜欢咖啡",
                metadata={"topic": "preferences"}
            )

            # 验证Mem0客户端被正确调用
            mock_client.add.assert_called_once()
            call_args = mock_client.add.call_args

            # 检查调用参数 - 应该使用user_id而不是session_id
            messages = call_args[0][0]  # messages参数
            user_id = call_args[1]["user_id"]  # user_id参数

            assert user_id == "user123"  # 应该使用user_id
            assert isinstance(messages, list)
            assert len(messages) == 2  # human + assistant messages

    # AC2 Scenario 2.4: 记忆服务降级机制
    @pytest.mark.asyncio
    async def test_memory_service_fallback_mechanism(self, mem0_service):
        """
        测试场景2.4：记忆服务降级机制
        Given 外部记忆服务（Mem0）暂时不可用
        When 系统尝试搜索用户记忆
        Then 应返回空的记忆上下文
        And 不应抛出异常或阻塞对话流程
        And 记录适当的警告日志
        """
        with patch('api.services.memory_service.MemoryClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client

            # 模拟外部服务不可用
            mock_client.search.side_effect = Exception("External service unavailable")

            service = Mem0MemoryServiceImpl()

            # 调用搜索方法 - 应该降级处理，不抛出异常
            result = await service.search_memory(user_id="user123", query="咖啡")

            # 期望：返回空列表，不抛出异常
            assert isinstance(result, list)
            assert len(result) == 0

    # AC2 Scenario 2.5: IMemoryService接口一致性
    @pytest.mark.asyncio
    async def test_memory_service_interface_consistency(self):
        """
        测试场景2.5：IMemoryService接口一致性
        Given 系统使用IMemoryService接口
        When 调用接口方法时
        Then 所有实现类都应该接受user_id和session_id参数
        And 接口签名应该一致
        """
        # 检查接口方法签名
        import inspect

        # 检查add_memory方法签名
        add_memory_sig = inspect.signature(IMemoryService.add_memory)
        params = list(add_memory_sig.parameters.keys())

        # 期望：接口应该包含user_id和session_id参数
        assert "user_id" in params
        assert "session_id" in params
        assert "human_message" in params
        assert "assistant_message" in params

    # AC2 Scenario 2.6: 记忆上下文获取使用用户ID
    @pytest.mark.asyncio
    async def test_get_memory_context_uses_user_id(self, mem0_service):
        """
        测试场景2.6：记忆上下文获取使用用户ID
        Given 用户user123有多个会话的记忆
        When 系统调用get_memory_context(user_id="user123", query="咖啡")
        Then 应该使用user_id搜索记忆
        And 返回跨会话的记忆上下文
        """
        with patch('api.services.memory_service.MemoryClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client

            mock_client.search.return_value = [
                {
                    "memory": "用户喜欢咖啡",
                    "score": 0.95,
                    "metadata": {"user_id": "user123", "session_id": "session_a"}
                }
            ]

            service = Mem0MemoryServiceImpl()
            result = await service.get_memory_context(user_id="user123", query="咖啡")

            # 期望：返回记忆上下文
            assert isinstance(result, dict)
            assert "memories" in result

            # 验证调用时使用了正确的user_id
            mock_client.search.assert_called_once()
            call_args = mock_client.search.call_args
            assert call_args[1]["user_id"] == "user123"

    # AC2 Scenario 2.7: Zep服务也应该遵循相同的接口
    @pytest.mark.asyncio
    async def test_zep_service_interface_compliance(self, zep_service):
        """
        测试场景2.7：Zep服务接口遵循性
        Given 系统使用ZepMemoryServiceImpl
        When 调用记忆服务方法时
        Then 应该接受user_id和session_id参数
        And 使用user_id进行用户级别的记忆管理
        """
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value.json.return_value = {"status": "success"}

            # 调用add_memory方法
            await zep_service.add_memory(
                user_id="user123",
                session_id="session123",
                human_message="我喜欢咖啡",
                assistant_message="好的，我记住了"
            )

            # 验证HTTP请求中使用了正确的user_id
            mock_client.post.assert_called_once()
            call_args = mock_client.post.call_args

            # 检查请求数据
            request_data = call_args[1]["json"]
            assert "user_id" in str(request_data) or "user_id" in str(call_args)

    # AC2 Scenario 2.8: 记忆服务错误处理
    @pytest.mark.asyncio
    async def test_memory_service_error_handling(self, mem0_service):
        """
        测试场景2.8：记忆服务错误处理
        Given 记忆服务调用时发生各种错误
        When 系统处理这些错误时
        Then 应该优雅地处理错误
        And 不应该影响主要的对话流程
        """
        with patch('api.services.memory_service.MemoryClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client

            # 测试网络错误
            mock_client.search.side_effect = ConnectionError("Network error")

            service = Mem0MemoryServiceImpl()
            result = await service.search_memory(user_id="user123", query="咖啡")

            # 期望：返回空结果，不抛出异常
            assert isinstance(result, list)
            assert len(result) == 0

            # 测试超时错误
            mock_client.search.side_effect = asyncio.TimeoutError("Request timeout")
            result = await service.search_memory(user_id="user123", query="咖啡")

            # 期望：返回空结果，不抛出异常
            assert isinstance(result, list)
            assert len(result) == 0
