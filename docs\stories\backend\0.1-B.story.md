### 故事: 0.1 核心服务脚手架搭建 (Foundation Scaffolding)

**作为** 开发者，
**我需要** 创建`LLMProxyService`, `MemoryService`, 和`ToolExecutorService`的核心骨架文件和接口定义，
**以便** 为后续所有功能开发提供一个统一、可导入、类型安全的服务基础。

## 基本信息
- **Status**: Approved

---

### 验收标准 (Acceptance Criteria)

1.  **文件创建**: 在`apps/agent-api/api/services/`目录下，成功创建`llm_proxy_service.py`, `memory_service.py`, 和 `tool_executor_service.py`三个文件。
2.  **MemoryService**: `memory_service.py`中必须包含`IMemoryService`抽象基类，以及继承自它的`ZepMemoryServiceImpl`和`Mem0MemoryServiceImpl`两个空实现类，并且包含`get_memory_service`工厂函数。
3.  **LLMProxyService**: `llm_proxy_service.py`中必须包含`LLMProxyService`类，并定义好与火山引擎交互的核心方法（如`create_chat_completion`），但方法体可以为空或抛出`NotImplementedError`。
4.  **ToolExecutorService**: `tool_executor_service.py`中必须包含`ToolExecutorService`类，并定义好`execute(tool_name, **kwargs)`方法。
5.  **类型注解**: 所有创建的类和方法必须有完整、准确的Python类型注解。
6.  **代码质量**: 整个项目在创建这些文件后，必须能通过`ruff`和`mypy`的检查，确保没有引入语法或类型错误。
7.  **无业务逻辑**: 【严格要求】所有方法的实现体不应包含任何具体的业务逻辑，只能是`pass`或`raise NotImplementedError()`。

---

### 开发笔记 (Dev Notes)

### 重要开发环境说明 (CRITICAL: Development Environment)
- **环境变量**: 项目所需的所有环境变量均已在 `apps/agent-api/.env` 文件中配置完毕。如果无法读取，可参考内容完全相同的 `apps/agent-api/1.env` 文件。
- **Python依赖**: `requirements.txt` 中列出的所有依赖库均已安装。
- **虚拟环境**: **必须**在执行任何脚本或启动服务前，激活指定的Conda虚拟环境: `conda activate xinqiao-py312`，当前环境为Windows系统。
- **服务端口**: FastAPI服务应在 **8003** 端口启动，例如: `uvicorn api.main:app --reload --host 0.0.0.0 --port 8003`。

*   技术指导完全遵循`docs/architecture/`下的最新架构文档。
*   此故事的目标是创建“接口”和“结构”，而非“实现”。 