# 故事 1.1-UI: 应用核心界面设计实现

## 基本信息
- **故事编号**: 1.1-UI
- **故事标题**: 基础设计系统与组件库实现
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: UX设计师 + 前端开发者
- **优先级**: 最高（P0 - 所有前端故事的前置依赖）
- **工作量估计**: 8-12 个工作日
- **依赖关系**: 故事 1.1 (项目基础设置)
- **Status**: Approved

## 故事描述

作为UX设计师和前端开发者，我需要设计并实现心桥应用的所有核心界面布局和视觉元素，**以便** 为后续的功能开发提供完整的UI基础，确保用户体验的一致性和适老化设计标准的实现。

## 验收标准

### AC1: 设计系统和视觉规范建立
- [ ] 建立完整的设计系统（色彩、字体、间距、组件规范）
- [ ] 所有设计元素符合适老化标准（字体≥16pt，触摸区域≥44x44pt，高对比度）
- [ ] 创建品牌视觉识别（Logo、色彩方案、图标库）
- [ ] 建立响应式设计规范，适配不同屏幕尺寸

### AC2: 核心界面设计完成
- [ ] 完成3个核心界面的高保真设计稿（启动页、引导流程、主对话界面）
- [ ] 所有界面符合UX设计文档中的交互原则和用户流程
- [ ] 界面设计体现"温暖、简洁、无负担"的品牌特质
- [ ] 完成界面间的过渡动画和交互效果设计

### AC3: 基础UI组件库实现
- [ ] 实现完整的原子级UI组件库（Button、Text、Input、Card等）
- [ ] 所有组件支持主题变换和适老化配置
- [ ] 组件库具有完整的TypeScript类型定义和文档
- [ ] 实现组件的无障碍功能和测试用例
- [ ] 配置NativeWind自定义design tokens和主题系统
- [ ] 设置必要的环境变量支持主题和无障碍模式切换
- [ ] 部署Storybook交互式组件文档

### AC4: 界面代码实现和集成
- [ ] 完成所有核心页面的基础布局实现
- [ ] 界面在不同设备上展示一致且适配良好
- [ ] 实现设计稿中的动画效果和交互反馈
- [ ] 完成界面的性能优化和加载体验
- [ ] 主对话界面正确集成FlashList并优化长列表性能
- [ ] 支持减少动画模式和其他无障碍功能
- [ ] 所有组件符合适老化设计标准（字体≥16pt，触摸区域≥44pt）

## Tasks / Subtasks

### 第一阶段：设计系统建立 (2-3天)
- [ ] **品牌视觉设计** (AC1)
  - 设计应用Logo和品牌标识
  - 建立色彩系统（主色、辅色、语义色）
  - 选择和配置字体系统（中文、英文、数字）
  - 创建图标库和插画风格指南

- [ ] **设计规范制定** (AC1)
  - 制定间距系统（4px、8px、16px基础网格）
  - 建立组件尺寸规范（按钮、输入框、卡片等）
  - 定义阴影、圆角、边框等视觉样式
  - 创建适老化设计检查清单

- [ ] **响应式设计系统** (AC1)
  - 定义不同屏幕尺寸的适配规则
  - 建立布局断点和响应式组件规范
  - 设计横屏和竖屏的适配方案
  - 考虑不同设备的安全区域适配

### 第二阶段：核心界面设计 (3-4天)
- [ ] **启动和欢迎界面设计** (AC2)
  - 设计应用启动页（Splash Screen）布局
  - 创建首次使用欢迎页面设计
  - 设计加载状态和进度指示器
  - 实现启动动画和品牌展示效果

- [ ] **引导流程界面设计** (AC2)
  - 设计角色共创流程的整体视觉框架
  - 创建步骤指示器和导航设计
  - 设计对话式交互的界面布局
  - 实现进度展示和返回/继续操作设计

- [ ] **主对话界面设计** (AC2)
  - 设计主对话页面的整体布局结构
  - 创建双模输入切换的视觉设计
  - 设计对话气泡和消息状态的视觉样式
  - 实现语音/文本模式的界面差异化设计

- [ ] **提醒功能界面设计** (AC2)
  - 设计提醒确认卡片的视觉样式
  - 创建提醒列表和管理界面设计
  - 设计提醒编辑和操作的交互界面
  - 实现提醒状态的视觉区分方案

### 第三阶段：UI组件库开发 (2-3天)
- [ ] **基础原子组件** (AC3)
  - 实现`Button`组件（多种状态和尺寸）
  - 实现`Text`组件（多种层级和样式）
  - 实现`Input`组件（支持不同类型和验证）
  - 实现`Card`组件（多种布局和内容类型）

- [ ] **组合分子组件** (AC3)
  - 实现`ChatBubble`组件（用户和AI消息）
  - 实现`ChatInputController`组件（双模输入切换）
  - 实现`ReminderCard`组件（提醒信息展示）
  - 实现`ProgressIndicator`组件（引导流程进度）

- [ ] **组件主题和配置** (AC3)
  - 建立组件主题系统（颜色、尺寸、字体变体）
  - 实现适老化模式的组件配置
  - 添加组件的无障碍属性和标签
  - 创建组件使用文档和示例代码
  - 配置NativeWind自定义design tokens
  - 设置环境变量和主题切换机制

### 第四阶段：界面实现和集成 (2-3天)
- [ ] **页面布局实现** (AC4)
  - 实现`app/splash.tsx` - 启动页面完整布局
  - 实现`app/welcome.tsx` - 欢迎页面布局
  - 实现`app/(onboarding)/` - 引导流程页面群
  - 实现`app/(chat)/index.tsx` - 主对话页面布局（集成FlashList）

- [ ] **FlashList对话界面优化** (AC4)
  - 配置FlashList的消息渲染和性能优化
  - 实现消息类型区分（getItemType）
  - 设置自动滚动和内容位置维护
  - 优化长对话列表的内存使用

- [ ] **动画效果实现** (AC4)
  - 实现页面切换的过渡动画
  - 添加组件的微交互和反馈动效
  - 实现双模切换的流畅动画
  - 优化动画性能和流畅度
  - 支持减少动画模式（accessibility）

- [ ] **响应式适配验证** (AC4)
  - 测试不同屏幕尺寸的界面适配
  - 验证横屏和竖屏的布局适应性
  - 检查不同设备的安全区域适配
  - 优化界面在低端设备上的性能

- [ ] **设计质量验证** (AC4)
  - 与设计稿进行像素级对比验证
  - 检查适老化设计标准的实现
  - 验证无障碍功能的正确实现
  - 进行跨平台一致性测试
  - 部署Storybook组件文档

## Dev Notes

CRITICAL: This is a **UI design and implementation story**. This story is a **prerequisite for all frontend functionality stories** (1.2, 1.4, 1.6).

**Design Resources to Create:**
- High-fidelity design mockups for all core screens
- Complete design system and component library
- Interactive prototypes for key user flows
- Design specification documents with measurements and assets

**Technical Implementation Standards:**
- Use Figma for high-fidelity design creation
- Implement components using NativeWind styling system
- Follow React Native and TypeScript best practices
- Ensure all designs are responsive and accessible

**Environment Variables Required:**
- [ ] `DESIGN_SYSTEM_THEME` - 主题配置 (light/dark/auto)
- [ ] `ACCESSIBILITY_MODE` - 适老化模式开关 (standard/enhanced)
- [ ] `STORYBOOK_ENABLED` - 组件文档开发模式 (true/false)
- [ ] `DESIGN_TOKENS_VERSION` - 设计令牌版本控制
- [ ] `ANIMATION_REDUCED_MOTION` - 减少动画模式 (true/false)

**NativeWind Configuration Requirements:**
```javascript
// tailwind.config.js
module.exports = {
  content: ["./App.{js,jsx,ts,tsx}", "./src/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontSize: {
        'accessibility-sm': '16px',  // 适老化最小字体
        'accessibility-base': '18px', // 适老化基础字体
        'accessibility-lg': '24px',   // 适老化大字体
      },
      spacing: {
        'touch-target': '44px',       // 最小触摸区域
      },
      colors: {
        // 高对比度色彩方案 (4.5:1 contrast ratio)
        'primary-accessible': '#0066CC',
        'text-accessible': '#1a1a1a',
        'bg-accessible': '#ffffff',
      }
    }
  },
  plugins: [
    // 适老化设计插件
    require('./plugins/accessibility-utilities')
  ]
}
```

**FlashList Configuration for Chat Interface:**
```typescript
// 主对话界面的FlashList配置
<FlashList
  data={messages}
  renderItem={({ item }) => (
    <ChatBubble message={item} isUser={item.isUser} />
  )}
  estimatedItemSize={80}
  getItemType={(item) => item.isUser ? 'user' : 'ai'}
  maintainVisibleContentPosition={{
    autoscrollToBottomThreshold: 0.2,
    startRenderingFromBottom: true
  }}
  keyExtractor={(item) => item.id}
  inverted={true}
  showsVerticalScrollIndicator={false}
/>
```

**Design Principles from UX Documentation:**
从 `@docs/prd/ux-design.md#核心设计原则` 中的核心设计原则：

### Visual Design Requirements:
1. **适老化设计标准**:
   - 字体大小 ≥ 16pt（核心内容 ≥ 18pt）
   - 触摸区域 ≥ 44x44pt
   - 高对比度色彩方案（对比度 > 4.5:1）
   - 柔和的暖色调，避免刺眼颜色

2. **情感化设计**:
   - 温暖、友好的视觉语言
   - 避免冷硬的技术感
   - 体现陪伴和关怀的主题
   - 简洁、不分散注意力的布局

3. **交互设计原则**:
   - 语音优先，文本辅助的双模交互
   - 对话驱动的功能设置
   - 零学习成本的熟悉交互模式
   - 即时、温暖的反馈机制

### Core Screen Design Requirements:
1. **启动和欢迎界面**:
   - 品牌Logo的温暖展示
   - 无感认证的背景处理提示
   - 平滑过渡到引导流程

2. **引导流程界面**:
   - 对话式的渐进信息收集
   - 清晰的进度指示和导航
   - AI角色创建的可视化预览
   - 温暖的庆祝和确认体验

3. **主对话界面**:
   - 大型"按住说话"按钮为视觉中心
   - 清晰的双模切换指示和动画
   - 用户/AI消息的明显视觉区分
   - FlashList优化的对话历史展示

### Component Architecture:
从 `@docs/architecture/06-frontend-architecture.md#组件设计模式` 中的组件设计：
- **原子级组件** (`src/components/ui/`): Button, Text, Input, Card, Icon
- **分子级组件** (`src/components/features/`): ChatBubble, ReminderCard, ProgressIndicator
- **页面级组件** (`app/`): 完整的页面布局和路由

### Design Tools and Workflow:
1. **设计工具**: Figma（主要）+ Adobe Illustrator（图标）
2. **原型工具**: Figma Interactive Prototypes
3. **设计交付**: Figma Dev Mode + Design Tokens
4. **协作流程**: 设计评审 → 开发实现 → 设计验收

## Testing

Dev Note: Story Requires the following tests:

- [ ] **Visual Regression Tests**: 使用Storybook截图对比测试
  ```javascript
  // .storybook/test-runner.js
  module.exports = {
    setup() {
      require('@storybook/test-runner/playwright/setup');
    },
    async postRender(page, context) {
      await page.screenshot({
        path: `screenshots/${context.title}.png`,
        fullPage: true
      });
    }
  };
  ```

- [ ] **Component Unit Tests**: Jest + React Native Testing Library，覆盖率≥90%
  ```javascript
  // 组件测试示例
  import { render, screen } from '@testing-library/react-native';
  import { Button } from '../components/ui/Button';
  
  test('renders button with accessibility features', () => {
    render(
      <Button 
        onPress={jest.fn()} 
        accessibilityLabel="主要操作按钮"
        className="accessibility-touch-target"
      >
        确认
      </Button>
    );
    expect(screen.getByRole('button')).toHaveAccessibilityState({
      disabled: false
    });
  });
  ```

- [ ] **Accessibility Tests**: 无障碍功能自动化检测
  ```javascript
  // 无障碍测试配置
  import { render } from '@testing-library/react-native';
  import { axe, toHaveNoViolations } from 'jest-axe';
  
  expect.extend(toHaveNoViolations);
  
  test('design system components have no accessibility violations', async () => {
    const { container } = render(<DesignSystemComponents />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  ```

- [ ] **Design System Tests**: 设计令牌和主题一致性验证

Manual Test Steps:
- 在不同设备尺寸上验证界面适配
- 使用无障碍功能验证界面可用性
- 对比设计稿进行像素级精度检查
- 验证适老化设计标准的实现
- 测试动画效果的流畅性和性能

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.1（项目基础设置）已完成
- [ ] UX设计文档已确认和批准
- [ ] 设计工具（Figma）账号和协作权限已配置
- [ ] 品牌设计指导原则已明确
- [ ] NativeWind和相关依赖已安装配置

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 高保真设计稿已完成并获得产品/UX团队批准
- [ ] UI组件库实现完成并通过质量检查
- [ ] 所有核心页面布局已实现并符合设计稿
- [ ] 设计系统文档已完成
- [ ] 无障碍和适老化标准验证通过
- [ ] Storybook组件文档已部署

### 交付物 (Deliverables)
- [ ] **设计文件**: Figma设计文件和交互原型
- [ ] **组件库**: 完整的UI组件库代码和文档
- [ ] **页面模板**: 所有核心页面的基础布局实现
- [ ] **设计规范**: 设计系统指南和使用文档
- [ ] **设计资源**: 图标、插画、品牌资源包
- [ ] **Storybook文档**: 交互式组件文档站点

## 风险与缓解措施

### 主要风险
1. **设计和开发脱节**: 设计稿与实际实现能力不匹配
2. **适老化标准验证**: 缺乏老年用户的真实反馈
3. **跨平台一致性**: iOS和Android平台的视觉差异
4. **性能影响**: 复杂动画和视觉效果影响低端设备性能
5. **NativeWind配置复杂**: 自定义主题和适老化令牌配置错误

### 缓解措施
1. **设计开发协同**: 设计师和开发者全程协作，技术可行性早期验证
2. **用户测试验证**: 邀请目标用户群体进行界面可用性测试
3. **平台适配测试**: 在真实设备上进行跨平台UI一致性验证
4. **性能监控**: 建立UI性能基准线，持续监控和优化
5. **配置标准化**: 建立NativeWind配置模板和验证脚本

## 🧐 后续依赖关系
- **🔗 此故事完成后解锁的故事**:
  - `故事1.2`: 首次使用的引导流程
  - `故事1.4`: 实时语音会话流程
  - `故事1.6`: AI主动发起对话与提醒

### 📚 相关文档引用

- **UX设计规范** (`@docs/prd/ux-design.md#适老化设计标准`)
  *包含字体大小≥16pt、触摸区域≥44x44pt、高对比度要求(4.5:1)等核心适老化设计标准和实施细节*

- **前端架构设计** (`@docs/architecture/06-frontend-architecture.md#组件设计模式`)
  *定义了原子级、分子级、页面级组件的架构层次、命名规范和文件组织结构*

- **移动端编码规范** (`@docs/architecture/mobile-app-coding-standards.md#UI组件开发`)
  *规定了React Native组件开发的最佳实践、代码风格和质量标准*

- **WCAG 2.1 AA级无障碍标准** (https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)
  *具体说明了对比度4.5:1的计算方法、测试工具和验证流程，确保界面符合国际无障碍标准*

## 成功指标

### 设计质量指标
- 设计稿完成度：100%
- 适老化标准符合率：100%
- 用户可用性测试评分：≥4.5/5.0
- 无障碍功能检测通过率：100%
- Figma设计系统一致性：≥95%

### 技术实现指标
- 组件库测试覆盖率：≥90%
- 页面渲染性能：首屏时间<3秒
- 动画流畅度：≥45fps
- 跨平台一致性：像素级相似度≥95%
- Storybook组件文档覆盖率：100%
