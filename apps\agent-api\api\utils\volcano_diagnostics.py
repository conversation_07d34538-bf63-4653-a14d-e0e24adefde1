"""
火山引擎配置诊断工具
用于检查和验证火山引擎相关配置的正确性
"""

import re
import json
from typing import Dict, List, Any, Optional
from ..services.volcano_client_service import get_volcano_client_service


class VolcanoDiagnostics:
    """火山引擎配置诊断工具"""
    
    @staticmethod
    def validate_user_id(user_id: str) -> Dict[str, Any]:
        """验证UserId格式"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        if not user_id:
            result["valid"] = False
            result["errors"].append("UserId不能为空")
            return result
        
        # 检查长度
        if len(user_id) > 128:
            result["valid"] = False
            result["errors"].append(f"UserId长度超过128字符，当前长度: {len(user_id)}")
        
        # 检查字符格式
        valid_pattern = re.compile(r'^[a-zA-Z0-9@._-]+$')
        if not valid_pattern.match(user_id):
            result["valid"] = False
            result["errors"].append("UserId包含不允许的字符，只能包含字母、数字、@、.、_、-")
        
        # 检查是否包含中文字符
        if re.search(r'[\u4e00-\u9fff]', user_id):
            result["valid"] = False
            result["errors"].append("UserId不能包含中文字符")
        
        return result
    
    @staticmethod
    def validate_agent_config(agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证AgentConfig配置"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查必需字段
        if "TargetUserId" not in agent_config:
            result["valid"] = False
            result["errors"].append("AgentConfig缺少必需字段: TargetUserId")
        else:
            target_user_ids = agent_config["TargetUserId"]
            if not isinstance(target_user_ids, list) or len(target_user_ids) == 0:
                result["valid"] = False
                result["errors"].append("TargetUserId必须是非空数组")
            elif len(target_user_ids) > 1:
                result["warnings"].append("TargetUserId数组包含多个用户，但当前只支持一对一通话")
            
            # 验证每个TargetUserId
            for target_user_id in target_user_ids:
                target_validation = VolcanoDiagnostics.validate_user_id(target_user_id)
                if not target_validation["valid"]:
                    result["valid"] = False
                    for error in target_validation["errors"]:
                        result["errors"].append(f"TargetUserId '{target_user_id}': {error}")
        
        # 检查UserId
        if "UserId" in agent_config:
            user_id = agent_config["UserId"]
            user_validation = VolcanoDiagnostics.validate_user_id(user_id)
            if not user_validation["valid"]:
                result["valid"] = False
                for error in user_validation["errors"]:
                    result["errors"].append(f"UserId '{user_id}': {error}")
            
            # 检查UserId与TargetUserId是否重复
            if "TargetUserId" in agent_config:
                target_user_ids = agent_config["TargetUserId"]
                if user_id in target_user_ids:
                    result["valid"] = False
                    result["errors"].append(f"UserId '{user_id}' 与 TargetUserId 重复")
        else:
            result["warnings"].append("未设置UserId，将使用系统默认值")
        
        return result
    
    @staticmethod
    def validate_rtc_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """验证完整的RTC配置"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证AgentConfig
        if "AgentConfig" in config:
            agent_validation = VolcanoDiagnostics.validate_agent_config(config["AgentConfig"])
            if not agent_validation["valid"]:
                result["valid"] = False
            result["errors"].extend(agent_validation["errors"])
            result["warnings"].extend(agent_validation["warnings"])
        else:
            result["valid"] = False
            result["errors"].append("配置缺少必需的AgentConfig")
        
        # 验证ASR配置
        if "ASRConfig" in config:
            asr_config = config["ASRConfig"]
            if asr_config.get("Provider") == "volcano":
                params = asr_config.get("ProviderParams", {})
                if params.get("Mode") == "bigmodel":
                    required_fields = ["AppId", "AccessToken", "ApiResourceId"]
                    for field in required_fields:
                        if not params.get(field):
                            result["valid"] = False
                            result["errors"].append(f"ASR配置缺少必需字段: {field}")
        
        # 验证TTS配置
        if "TTSConfig" in config:
            tts_config = config["TTSConfig"]
            if tts_config.get("Provider") == "volcano_bidirection":
                params = tts_config.get("ProviderParams", {})
                app_config = params.get("app", {})
                if not app_config.get("appid") or not app_config.get("token"):
                    result["valid"] = False
                    result["errors"].append("TTS配置缺少必需的appid或token")
        
        # 验证LLM配置
        if "LLMConfig" in config:
            llm_config = config["LLMConfig"]
            mode = llm_config.get("Mode")
            if mode == "ArkV3":
                if not llm_config.get("EndPointId") and not llm_config.get("BotId"):
                    result["valid"] = False
                    result["errors"].append("LLM配置：ArkV3模式需要EndPointId或BotId")
        
        return result
    
    @staticmethod
    async def test_volcano_connection() -> Dict[str, Any]:
        """测试火山引擎连接"""
        result = {
            "success": False,
            "message": "",
            "details": {}
        }
        
        try:
            service = get_volcano_client_service()
            
            # 检查基本配置
            if not service.app_id:
                result["message"] = "缺少AppId配置"
                return result
            
            if not service.access_key or not service.secret_key:
                result["message"] = "缺少访问密钥配置"
                return result
            
            # 尝试一个简单的API调用来测试连接
            test_data = {
                'AppId': service.app_id,
                'RoomId': 'diagnostic-test-room',
                'TaskId': 'diagnostic-test-task',
                'Config': {
                    'ASRConfig': {
                        'Provider': 'volcano',
                        'ProviderParams': {
                            'Mode': 'bigmodel',
                            'AppId': '**********',
                            'AccessToken': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox',
                            'ApiResourceId': 'volc.bigasr.sauc.duration',
                            'StreamMode': 0
                        }
                    },
                    'TTSConfig': {
                        'Provider': 'volcano_bidirection',
                        'ProviderParams': {
                            'app': {
                                'appid': '**********',
                                'token': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox'
                            },
                            'audio': {
                                'voice_type': 'zh_male_qingshuangnanda_mars_bigtts',
                                'speech_rate': 0,
                                'pitch_rate': 0
                            },
                            'ResourceId': 'volc.service_type.10029'
                        }
                    },
                    'LLMConfig': {
                        'Mode': 'ArkV3',
                        'EndPointId': 'ep-20250704092428-tl9sc',
                        'MaxTokens': 1024,
                        'Temperature': 0.7,
                        'SystemMessages': ['你是一个AI助手。']
                    },
                    'AgentConfig': {
                        'UserId': 'diagnostic-bot',
                        'TargetUserId': ['diagnostic-user']
                    }
                }
            }
            
            await service._call_volcano_api('StartVoiceChat', test_data)
            result["success"] = True
            result["message"] = "火山引擎连接测试成功"
            
        except Exception as e:
            result["message"] = f"火山引擎连接测试失败: {str(e)}"
            result["details"]["error"] = str(e)
        
        return result
    
    @staticmethod
    def generate_diagnostic_report(config: Dict[str, Any]) -> str:
        """生成诊断报告"""
        validation_result = VolcanoDiagnostics.validate_rtc_config(config)
        
        report = ["=== 火山引擎配置诊断报告 ===\n"]
        
        if validation_result["valid"]:
            report.append("✅ 配置验证通过")
        else:
            report.append("❌ 配置验证失败")
        
        if validation_result["errors"]:
            report.append("\n🚨 错误:")
            for error in validation_result["errors"]:
                report.append(f"  - {error}")
        
        if validation_result["warnings"]:
            report.append("\n⚠️ 警告:")
            for warning in validation_result["warnings"]:
                report.append(f"  - {warning}")
        
        report.append("\n=== 建议 ===")
        if not validation_result["valid"]:
            report.append("1. 请根据上述错误信息修复配置")
            report.append("2. 确保所有ID字段符合格式要求（字母、数字、@、.、_、-，最大128字符）")
            report.append("3. 确保UserId与TargetUserId不重复")
            report.append("4. 检查火山引擎账号权限和服务开通状态")
        else:
            report.append("配置看起来正确，如果仍有问题，请检查网络连接和账号权限")
        
        return "\n".join(report)
