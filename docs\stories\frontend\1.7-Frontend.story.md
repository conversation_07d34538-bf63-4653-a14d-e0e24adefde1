# 故事 1.7-Frontend: 危机干预功能实现

## 基本信息
- **故事编号**: 1.7-Frontend  
- **故事标题**: 危机干预功能实现
- **Epic**: MVP - 安全保障与风险防范
- **用户角色**: 前端开发者
- **优先级**: 高（P0 - 安全合规要求）
- **工作量估计**: 3-4 个工作日
- **依赖关系**: 1.1-Frontend（项目基础设置）, 1.7-UI（危机干预界面设计）, 1.1-Frontend-ErrorHandling（错误处理）
- **Status**: Approved

## 故事描述

作为前端开发者，我需要实现完整的危机干预功能模块，**以便** 当系统检测到用户可能处于危机状态时，能够及时、准确、有效地提供专业帮助资源和安全脚本，确保用户安全。

## 验收标准

### AC1: 危机检测和响应
- [ ] 实现危机状态检测的前端逻辑
- [ ] 建立危机级别分类和响应策略
- [ ] 集成后端危机检测API
- [ ] 实现危机状态的本地存储和上报

### AC2: 安全脚本执行
- [ ] 实现呼吸练习引导功能
- [ ] 集成接地技巧步骤展示
- [ ] 支持语音播放安全脚本
- [ ] 提供练习进度跟踪和记录

### AC3: 紧急资源调用
- [ ] 实现一键拨打紧急热线功能
- [ ] 集成地理定位获取就近心理健康服务
- [ ] 支持紧急联系人快速呼叫
- [ ] 提供专业咨询资源推荐

### AC4: 后续关怀和跟进
- [ ] 实现危机后关怀提醒设置
- [ ] 支持情绪状态跟踪记录
- [ ] 提供专业咨询预约引导
- [ ] 集成用户安全状态报告

## Tasks / Subtasks

### 第一阶段：危机检测基础设施 (1天)
- [ ] **危机状态管理** (AC1)
  - 创建危机状态Store和类型定义
  - 实现危机检测触发逻辑
  - 集成后端危机检测API
  - 建立危机事件日志记录

### 第二阶段：安全脚本模块 (1-2天)
- [ ] **呼吸练习实现** (AC2)
  - 创建呼吸练习引导组件
  - 实现练习计时器和视觉提示
  - 集成音频播放和控制
  - 建立练习进度跟踪

- [ ] **接地技巧模块** (AC2)
  - 实现5-4-3-2-1接地技巧引导
  - 创建感官体验引导界面
  - 支持用户自定义安全技巧
  - 实现技巧效果评估

### 第三阶段：紧急资源集成 (1天)
- [ ] **紧急联系功能** (AC3)
  - 实现热线拨打功能
  - 集成地理定位服务
  - 创建紧急联系人管理
  - 实现资源推荐算法

### 第四阶段：关怀跟进系统 (1天)
- [ ] **后续关怀模块** (AC4)
  - 实现关怀提醒调度
  - 创建情绪状态记录
  - 集成咨询预约系统
  - 建立安全状态报告

## Dev Notes

### 技术实现要求

#### API集成：危机检测 (AC1)

本故事需要与后端危机检测API集成。前端将向此端点发送用户相关上下文（在严格遵守隐私协议的前提下），并接收风险等级评估。

**Endpoint:** `POST /api/v1/crisis/detect`

**Request Body:**
```json
{
  "sessionId": "user_session_id_abc123",
  "source": "in_app_text_analysis",
  "context": {
    "recentMessages": [
      "我感到非常绝望",
      "一切都失去了意义"
    ]
  }
}
```

**Success Response (200 OK):**
```json
{
  "level": "high",
  "confidence": 0.92,
  "recommendations": ["breathing_exercise", "grounding_technique"]
}
```

**Error Response (4xx/5xx):**
```json
{
  "error": "Invalid context provided"
}
```

#### 环境变量
为了保证API地址的灵活性，请使用以下环境变量：
```
EXPO_PUBLIC_API_URL=https://api.example.com
```

#### UI组件参考 (来自故事 1.7-UI)
- **呼吸练习界面**: 包含一个中央的、根据呼吸阶段（吸气、屏息、呼气）缩放和变色的动画圆圈。下方有文字提示当前阶段和操作指令。
- **紧急联系人列表**: 一个可滚动的列表，每一项都清晰地展示了机构名称、联系电话和简要描述，并有一个显眼的"拨打"按钮。

基于安全和隐私的危机干预系统：

#### 危机状态管理
```typescript
// store/crisis.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export enum CrisisLevel {
  NONE = 'none',
  LOW = 'low',        // 轻微情绪波动
  MEDIUM = 'medium',   // 明显情绪困扰
  HIGH = 'high',       // 危机风险较高
  CRITICAL = 'critical' // 紧急危机状态
}

export enum SafetyTechnique {
  BREATHING = 'breathing',
  GROUNDING = 'grounding',
  DISTRACTION = 'distraction',
  SUPPORT = 'support'
}

interface CrisisState {
  // 当前状态
  currentLevel: CrisisLevel;
  isInCrisis: boolean;
  lastAssessment: Date | null;
  
  // 危机历史
  crisisHistory: CrisisEvent[];
  safetyPlan: SafetyPlan;
  
  // 安全技巧进度
  breathingProgress: BreathingSession[];
  groundingProgress: GroundingSession[];
  
  // Actions
  updateCrisisLevel: (level: CrisisLevel, context?: string) => void;
  startSafetyTechnique: (technique: SafetyTechnique) => void;
  completeSafetyTechnique: (technique: SafetyTechnique, effectiveness: number) => void;
  scheduleFollowUp: (hours: number) => void;
  reportSafetyStatus: (status: 'safe' | 'need_help' | 'emergency') => void;
}

interface CrisisEvent {
  id: string;
  timestamp: Date;
  level: CrisisLevel;
  context?: string;
  interventions: InterventionUsed[];
  outcome: 'resolved' | 'ongoing' | 'escalated';
}

interface SafetyPlan {
  emergencyContacts: EmergencyContact[];
  copingStrategies: string[];
  warningSignsPersonal: string[];
  reasonsForLiving: string[];
  professionalSupports: ProfessionalSupport[];
}
```

#### 呼吸练习组件
```typescript
// components/crisis/breathing-exercise.tsx
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Pressable, Alert } from 'react-native';
import { Audio } from 'expo-av';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  cancelAnimation,
} from 'react-native-reanimated';

interface BreathingExerciseProps {
  onComplete: (effectiveness: number) => void;
  onExit: () => void;
}

export const BreathingExercise: React.FC<BreathingExerciseProps> = ({
  onComplete,
  onExit,
}) => {
  const [phase, setPhase] = useState<'inhale' | 'hold' | 'exhale' | 'pause' | 'idle'>('idle');
  const [cycle, setCycle] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [sound, setSound] = useState<Audio.Sound>();
  
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0.6);
  const timerRef = useRef<NodeJS.Timeout[]>([]);

  // 4-7-8呼吸法：吸气4秒，屏息7秒，呼气8秒
  const BREATHING_PATTERN = {
    inhale: 4000,
    hold: 7000,
    exhale: 8000,
    pause: 1000,
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));
  
  // 清理所有定时器
  const clearTimers = () => {
    timerRef.current.forEach(clearTimeout);
    timerRef.current = [];
    cancelAnimation(scale);
    cancelAnimation(opacity);
  };

  useEffect(() => {
    loadSoundEffect();
    return () => {
      clearTimers();
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);

  const loadSoundEffect = async () => {
    try {
      const { sound: audioSound } = await Audio.Sound.createAsync(
        require('@/assets/audio/breathing-guide.mp3'),
      );
      setSound(audioSound);
    } catch (error) {
      console.warn('Could not load breathing audio:', error);
    }
  };

  const startBreathing = () => {
    if (isActive) return;
    setIsActive(true);
    // 如果是从暂停状态恢复，则继续当前周期，否则从0开始
    runBreathingCycle(cycle);
  };
  
  const pauseBreathing = () => {
    setIsActive(false);
    clearTimers();
  };
  
  const resetBreathing = () => {
    pauseBreathing();
    setCycle(0);
    setPhase('idle');
    scale.value = withTiming(0.8, { duration: 300 });
    opacity.value = withTiming(0.6, { duration: 300 });
  };

  const runBreathingCycle = (currentCycle: number) => {
    clearTimers();

    // 吸气阶段
    setPhase('inhale');
    scale.value = withTiming(1.2, {
      duration: BREATHING_PATTERN.inhale,
      easing: Easing.bezier(0.4, 0, 0.6, 1),
    });
    opacity.value = withTiming(1, { duration: BREATHING_PATTERN.inhale });

    const t1 = setTimeout(() => {
      // 屏息阶段
      setPhase('hold');

      const t2 = setTimeout(() => {
        // 呼气阶段
        setPhase('exhale');
        scale.value = withTiming(0.8, {
          duration: BREATHING_PATTERN.exhale,
          easing: Easing.bezier(0.4, 0, 0.6, 1),
        });
        opacity.value = withTiming(0.6, { duration: BREATHING_PATTERN.exhale });

        const t3 = setTimeout(() => {
          // 暂停阶段
          setPhase('pause');
          const nextCycle = currentCycle + 1;
          setCycle(nextCycle);

          const t4 = setTimeout(() => {
            if (nextCycle < 5) { // 5个完整周期
              runBreathingCycle(nextCycle);
            } else {
              setIsActive(false);
              showEffectivenessDialog();
            }
          }, BREATHING_PATTERN.pause);
          timerRef.current.push(t4);
        }, BREATHING_PATTERN.exhale);
        timerRef.current.push(t3);
      }, BREATHING_PATTERN.hold);
      timerRef.current.push(t2);
    }, BREATHING_PATTERN.inhale);
    timerRef.current.push(t1);
  };

  const showEffectivenessDialog = () => {
    Alert.alert(
      '练习完成',
      '这次呼吸练习对您有帮助吗？\n(1分表示完全没用, 5分表示非常有帮助)',
      [
        { text: '1', onPress: () => { onComplete(1); resetBreathing(); } },
        { text: '2', onPress: () => { onComplete(2); resetBreathing(); } },
        { text: '3', onPress: () => { onComplete(3); resetBreathing(); } },
        { text: '4', onPress: () => { onComplete(4); resetBreathing(); } },
        { text: '5', onPress: () => { onComplete(5); resetBreathing(); } },
      ],
      { cancelable: false }
    );
  };

  const handleExit = () => {
    resetBreathing();
    onExit();
  };

  const getPhaseInstruction = () => {
    switch (phase) {
      case 'inhale':
        return '深深吸气...';
      case 'hold':
        return '屏住呼吸...';
      case 'exhale':
        return '慢慢呼气...';
      case 'pause':
        return '稍作休息...';
      default:
        return '准备开始';
    }
  };

  const getPhaseColor = () => {
    switch (phase) {
      case 'inhale':
        return '#3B82F6'; // 蓝色
      case 'hold':
        return '#8B5CF6'; // 紫色
      case 'exhale':
        return '#10B981'; // 绿色
      case 'pause':
        return '#6B7280'; // 灰色
    }
  };

  return (
    <View className="flex-1 bg-white items-center justify-center p-8">
      <View className="items-center space-y-8">
        
        {/* 呼吸引导圆圈 */}
        <Animated.View
          style={[
            animatedStyle,
            {
              width: 200,
              height: 200,
              borderRadius: 100,
              backgroundColor: getPhaseColor(),
            }
          ]}
          className="items-center justify-center"
        >
          <Text className="text-white text-xl font-medium">
            {cycle + 1}/5
          </Text>
        </Animated.View>

        {/* 阶段提示 */}
        <View className="items-center space-y-4">
          <Text className="text-2xl font-medium text-gray-900">
            {getPhaseInstruction()}
          </Text>
          
          {phase === 'inhale' && (
            <Text className="text-lg text-gray-600">
              通过鼻子慢慢吸气，数到4
            </Text>
          )}
          {phase === 'hold' && (
            <Text className="text-lg text-gray-600">
              保持呼吸，数到7
            </Text>
          )}
          {phase === 'exhale' && (
            <Text className="text-lg text-gray-600">
              通过嘴巴慢慢呼气，数到8
            </Text>
          )}
        </View>

        {/* 控制按钮 */}
        <View className="flex-row space-x-4 mt-8">
          {!isActive ? (
            <Pressable
              onPress={startBreathing}
              className="bg-blue-500 px-8 py-4 rounded-xl"
            >
              <Text className="text-white text-lg font-medium">
                {cycle > 0 && cycle < 5 ? '继续' : '开始练习'}
              </Text>
            </Pressable>
          ) : (
            <Pressable
              onPress={pauseBreathing}
              className="bg-gray-500 px-8 py-4 rounded-xl"
            >
              <Text className="text-white text-lg font-medium">
                暂停
              </Text>
            </Pressable>
          )}
          
          <Pressable
            onPress={handleExit}
            className="bg-gray-200 px-8 py-4 rounded-xl"
          >
            <Text className="text-gray-700 text-lg font-medium">
              退出
            </Text>
          </Pressable>
        </View>

      </View>
    </View>
  );
};
```

#### 紧急联系功能
```typescript
// components/crisis/emergency-contacts.tsx
import React from 'react';
import { View, Text, Pressable, Linking, Alert } from 'react-native';
import * as Location from 'expo-location';

const EMERGENCY_HOTLINES = [
  {
    name: '心理危机干预热线',
    number: '400-161-9995',
    description: '24小时专业心理危机干预',
    type: 'crisis'
  },
  {
    name: '全国心理援助热线',
    number: '400-6769-525',
    description: '24小时心理援助服务',
    type: 'support'
  },
  {
    name: '青少年心理热线',
    number: '12355',
    description: '针对青少年的心理咨询',
    type: 'youth'
  }
];

export const EmergencyContacts: React.FC = () => {
  const callHotline = (number: string, name: string) => {
    Alert.alert(
      '拨打求助热线',
      `确定要拨打${name}吗？\n号码：${number}`,
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '拨打', 
          onPress: () => Linking.openURL(`tel:${number}`)
        }
      ]
    );
  };

  const findNearbyServices = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('权限提示', '需要位置权限来查找附近的心理健康服务');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      // 使用地理位置查找附近的心理健康服务
      // 这里可以集成地图API或预设的服务机构数据
    } catch (error) {
      console.error('Location error:', error);
    }
  };

  return (
    <View className="space-y-6">
      <Text className="text-xl font-semibold text-gray-900">
        紧急求助资源
      </Text>
      
      {/* 热线电话 */}
      <View className="space-y-3">
        {EMERGENCY_HOTLINES.map((hotline, index) => (
          <Pressable
            key={index}
            onPress={() => callHotline(hotline.number, hotline.name)}
            className="bg-white p-4 rounded-lg border border-red-200 shadow-sm"
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="text-lg font-medium text-gray-900">
                  📞 {hotline.name}
                </Text>
                <Text className="text-red-600 font-mono text-lg">
                  {hotline.number}
                </Text>
                <Text className="text-sm text-gray-600 mt-1">
                  {hotline.description}
                </Text>
              </View>
              <View className="bg-red-500 px-4 py-2 rounded-lg">
                <Text className="text-white font-medium">拨打</Text>
              </View>
            </View>
          </Pressable>
        ))}
      </View>

      {/* 附近服务 */}
      <Pressable
        onPress={findNearbyServices}
        className="bg-blue-50 p-4 rounded-lg border border-blue-200"
      >
        <View className="flex-row items-center space-x-3">
          <Text className="text-2xl">🏥</Text>
          <View className="flex-1">
            <Text className="text-lg font-medium text-blue-900">
              查找附近的心理健康服务
            </Text>
            <Text className="text-sm text-blue-600">
              获取就近的心理咨询机构和医疗服务
            </Text>
          </View>
        </View>
      </Pressable>

      {/* 免责声明 */}
      <View className="bg-amber-50 p-4 rounded-lg">
        <Text className="text-amber-800 text-sm">
          ⚠️ 如果您感到生命受到威胁，请立即拨打110或就近前往医院急诊科。
          本应用提供的资源仅为支持性质，不能替代专业医疗建议。
        </Text>
      </View>
    </View>
  );
};
```

## Testing

### 功能测试
- [ ] **危机检测准确性**: 测试危机状态识别的准确性
- [ ] **安全脚本有效性**: 验证呼吸练习和接地技巧的执行
- [ ] **紧急联系功能**: 测试热线拨打和地理定位功能
- [ ] **数据安全性**: 验证危机数据的加密存储和传输

### 安全性测试
- [ ] **隐私保护**: 确保危机数据不会被滥用或泄露
- [ ] **错误处理**: 测试关键功能失败时的降级机制
- [ ] **网络异常**: 验证离线状态下的基本功能可用性
- [ ] **权限检查**: 确保权限被拒绝时的功能降级

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 前端项目基础设置已完成（故事1.1-Frontend）
- [ ] 危机干预界面设计已完成（故事1.7-UI）
- [ ] 错误处理机制已实现（故事1.1-Frontend-ErrorHandling）
- [ ] 心理健康专家已审核安全脚本内容
- [ ] 法律合规要求已明确

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过功能测试
- [ ] 危机干预功能已通过心理健康专家审核
- [ ] 安全性和隐私保护测试通过
- [ ] 用户测试结果为正面

### 交付物 (Deliverables)
- [ ] **完整的危机干预模块**：包含检测、干预、跟进功能
- [ ] **安全脚本库**：呼吸练习、接地技巧等组件
- [ ] **紧急资源系统**：热线联系和地理服务功能
- [ ] **危机处理文档**：功能使用指南和安全协议

## 风险与缓解措施

### 主要风险
1. **医疗责任风险**：应用可能被视为提供医疗建议
2. **隐私泄露风险**：危机数据属于高度敏感信息
3. **技术故障风险**：关键时刻功能失效可能造成严重后果
4. **误判风险**：错误的危机检测可能引起不必要的恐慌

### 缓解措施
1. **清晰免责声明**：明确应用仅提供支持资源，不替代专业医疗
2. **数据加密保护**：使用最高级别的加密存储和传输
3. **离线功能保障**：确保基本功能在网络异常时仍可用
4. **人工审核机制**：重要决策涉及人工确认

## 后续故事依赖关系

### 🔗 此故事与以下功能协同：
- **对话功能**：集成危机检测到对话分析中
- **提醒功能**：设置危机后关怀提醒
- **设置功能**：允许用户管理危机干预选项

### 📋 为产品提供的核心价值：
- **用户安全保障**：确保高风险用户得到及时帮助
- **法律合规要求**：满足心理健康应用的安全标准
- **专业可信度**：提升产品的专业性和可信度

## 相关文档引用
- [产品需求 - 基础危机干预](../../prd/requirements.md#FR7)
- [UX设计指南](../../prd/ux-design.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md)
- [用户故事 - 安全保障](../../prd/user-stories.md) 