import asyncio
import os
from openai import AsyncOpenAI
from mem0 import AsyncMemoryClient
from dotenv import load_dotenv

# --- 1. 配置 ---
# 加载 1.env 文件中的环境变量
# 请确保已安装 python-dotenv: pip install python-dotenv
load_dotenv(dotenv_path="../1.env")

# Mem0 配置
# 从环境变量中读取 MEM0_API_KEY
MEM0_API_KEY = os.environ.get("MEM0_API_KEY")

# 火山引擎 (方舟) LLM 配置
# 从环境变量中读取 VOLCENGINE_API_KEY
ARK_API_KEY = os.environ.get("VOLCENGINE_API_KEY")
VOLCANO_LLM_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
# 从环境变量中读取模型 Endpoint ID
VOLCANO_LLM_ENDPOINT_ID = os.environ.get("VOLCANO_LLM_ENDPOINT_ID")

# 用于演示的唯一用户标识符
USER_ID = "demo_user_volcano_mem0_chat_001"

# 为聊天机器人设计的系统提示
SYSTEM_PROMPT = """你是一个拥有长期记忆的 AI 助手。
你可以访问用户的记忆，包括过往对话、偏好和重要信息。
请利用这些记忆信息提供更个性化、连贯的回应。"""


async def chat_with_ai():
    """
    主聊天循环函数，集成了 Mem0 记忆和火山引擎 LLM。
    """
    # --- 检查 API Keys 是否已配置 ---
    if not MEM0_API_KEY or not ARK_API_KEY:
        print("错误：请在运行脚本前设置 MEM0_API_KEY 和 ARK_API_KEY 环境变量。")
        print("脚本会尝试从 1.env 文件加载，请检查该文件是否存在且包含正确的值。")
        print("获取 MEM0_API_KEY: https://app.mem0.ai/")
        print("获取 ARK_API_KEY: https://www.volcengine.com/product/ark")
        return

    if not VOLCANO_LLM_ENDPOINT_ID:
        print("错误：未设置 VOLCANO_LLM_ENDPOINT_ID")
        print("请在 1.env 文件中设置火山引擎的模型 Endpoint ID")
        return

    # --- 初始化 Mem0 和 LLM 的异步客户端 ---
    print("--- 正在初始化 Mem0 和火山引擎 LLM 客户端 ---")
    mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)
    llm_client = AsyncOpenAI(
        base_url=VOLCANO_LLM_BASE_URL,
        api_key=ARK_API_KEY,
    )

    print(f"--- Mem0 客户端初始化成功，用户 ID: {USER_ID} ---")

    # --- 显示现有记忆状态 ---
    try:
        existing_memories = await mem0_client.get_all(user_id=USER_ID, limit=100)
        if existing_memories and hasattr(existing_memories, 'results') and existing_memories.results:
            memory_count = len(existing_memories.results)
            print(f"📚 发现 {memory_count} 条历史记忆，我会根据这些记忆与你对话")
        else:
            print("📝 这是我们的第一次对话，我会开始学习和记住你的信息")
    except Exception as e:
        print(f"📋 检查历史记忆时出现问题: {e}")

    print("\n--- 聊天开始 ---")
    print(f"你好！我是你的 AI 助手，拥有持久记忆功能。所有对话都将为用户 '{USER_ID}' 保存。")
    print("输入 'quit' 或 'exit' 来结束对话。")

    while True:
        try:
            # 在异步函数中使用 input() 的推荐方式
            user_input = await asyncio.to_thread(input, f"\n你 ({USER_ID}): ")

            if user_input.lower() in ["quit", "exit"]:
                print("--- 聊天结束，下次再见！ ---")
                break

            # --- 1. 从 Mem0 搜索相关记忆 ---
            print(">>> 正在从 Mem0 搜索相关记忆...")
            try:
                # 多策略搜索：结合用户输入和通用身份查询
                search_queries = [user_input]

                # 添加身份相关的搜索词
                identity_keywords = ["姓名", "名字", "身份", "我是", "叫", "name", "identity"]
                if any(keyword in user_input.lower() for keyword in ["谁", "who", "我是", "name", "身份", "姓名"]):
                    search_queries.extend(["用户姓名", "用户身份", "name", "identity"])

                relevant_memories = []
                all_search_results = []

                # 执行多个搜索查询
                for query in search_queries:
                    try:
                        memories = await mem0_client.search(
                            query=query,
                            user_id=USER_ID,
                            limit=10  # 增加搜索数量
                        )

                        if memories and hasattr(memories, 'results') and memories.results:
                            all_search_results.extend(memories.results)
                        elif memories and isinstance(memories, list):
                            all_search_results.extend(memories)

                    except Exception as search_error:
                        print(f">>> 搜索查询 '{query}' 出现错误: {search_error}")
                        continue

                # 去重并按相关性排序
                seen_memories = set()
                for memory_obj in all_search_results:
                    if isinstance(memory_obj, dict):
                        memory_text = memory_obj.get('memory', '')
                        score = memory_obj.get('score', 0)
                        if memory_text and memory_text not in seen_memories:
                            seen_memories.add(memory_text)
                            relevant_memories.append({
                                'memory': memory_text,
                                'score': score
                            })

                # 按分数排序，取前5个
                relevant_memories.sort(key=lambda x: x.get('score', 0), reverse=True)
                relevant_memories = relevant_memories[:5]

                if relevant_memories:
                    print(f">>> 找到 {len(relevant_memories)} 条相关记忆:")
                    for i, mem_obj in enumerate(relevant_memories, 1):
                        memory_text = mem_obj['memory']
                        score = mem_obj.get('score', 0)
                        print(f">>> {i}. [相关性: {score:.3f}] {memory_text[:100]}...")
                else:
                    print(">>> 基于语义搜索未找到相关记忆")
                    # 如果没找到，尝试获取最近的几条记忆作为上下文
                    try:
                        recent_memories = await mem0_client.get_all(
                            user_id=USER_ID,
                            limit=5
                        )
                        if recent_memories and hasattr(recent_memories, 'results') and recent_memories.results:
                            print(">>> 获取最近的记忆作为上下文:")
                            # 按创建时间排序，取最新的几条
                            sorted_memories = sorted(
                                recent_memories.results,
                                key=lambda x: x.get('created_at', ''),
                                reverse=True
                            )
                            for memory in sorted_memories[:3]:  # 只取最近3条
                                memory_text = memory.get('memory', '')
                                if memory_text:
                                    relevant_memories.append({'memory': memory_text, 'score': 0.3})
                                    created_at = memory.get('created_at', '')[:16]  # 只显示日期和时间
                                    print(f">>> 最近记忆 [{created_at}]: {memory_text[:80]}...")
                        else:
                            print(">>> 这是我们的第一次对话，我还没有关于你的记忆")
                    except Exception as recent_error:
                        print(f">>> 获取最近记忆时出现错误: {recent_error}")

            except Exception as e:
                print(f">>> 搜索记忆时出现错误: {e}")
                relevant_memories = []

            # --- 2. 为 LLM 构建提示 ---
            llm_messages = [{"role": "system", "content": SYSTEM_PROMPT}]

            # 如果有相关记忆，添加到上下文中
            if relevant_memories:
                memory_texts = []
                for mem_obj in relevant_memories:
                    if isinstance(mem_obj, dict):
                        memory_texts.append(mem_obj['memory'])
                    else:
                        memory_texts.append(str(mem_obj))

                memory_context = "以下是相关的记忆信息：\n" + "\n".join([f"- {memory}" for memory in memory_texts])
                llm_messages.append(
                    {
                        "role": "system",
                        "content": f"记忆上下文:\n{memory_context}\n\n请根据这些记忆信息来回应用户。"
                    }
                )

            # 添加用户当前输入
            llm_messages.append({"role": "user", "content": user_input})

            # --- 3. 调用火山引擎 LLM ---
            print(">>> 正在调用火山引擎 LLM 生成回复...")
            response = await llm_client.chat.completions.create(
                model=VOLCANO_LLM_ENDPOINT_ID,
                messages=llm_messages,
                max_tokens=1024,
                temperature=0.7,
            )
            ai_response_content = response.choices[0].message.content
            print(f"\nAI: {ai_response_content}")

            # --- 4. 将新的对话存入 Mem0 记忆 ---
            print(">>> 正在将新对话存入 Mem0 记忆库...")
            try:
                # 构建对话消息格式
                conversation_messages = [
                    {"role": "user", "content": user_input},
                    {"role": "assistant", "content": ai_response_content}
                ]

                print(f">>> 准备存储的消息: {conversation_messages}")

                # 添加到 Mem0，让它自动提取重要信息
                add_result = await mem0_client.add(
                    messages=conversation_messages,
                    user_id=USER_ID,
                    metadata={
                        "timestamp": int(asyncio.get_event_loop().time()),
                        "session": "demo_chat",
                        "conversation_turn": f"turn_{len(user_input)}"
                    },
                    output_format="v1.1"  # 使用新的API格式消除警告
                )

                print(f">>> 存储API返回结果: {add_result}")

                # 显示存储结果的详细信息
                if add_result:
                    if hasattr(add_result, 'results') and add_result.results:
                        print(f">>> ✓ 记忆存储成功，新增 {len(add_result.results)} 条记忆:")
                        for i, memory in enumerate(add_result.results, 1):
                            if isinstance(memory, dict):
                                memory_text = memory.get('memory', '')
                                memory_id = memory.get('id', 'N/A')
                                print(f">>> {i}. [ID: {memory_id}] {memory_text[:100]}...")
                    elif isinstance(add_result, dict):
                        print(f">>> ✓ 记忆存储成功: {add_result}")
                    else:
                        print(f">>> ✓ 记忆存储成功，返回类型: {type(add_result)}")
                else:
                    print(">>> ⚠️ 记忆存储返回结果为空")

                # 验证存储：立即尝试搜索刚存储的内容
                print(">>> 验证记忆存储...")
                await asyncio.sleep(2)  # 等待2秒让记忆索引完成

                verification_result = await mem0_client.search(
                    query=user_input[:50],  # 使用用户输入的前50个字符搜索
                    user_id=USER_ID,
                    limit=1
                )

                if verification_result and (
                    (hasattr(verification_result, 'results') and verification_result.results) or
                    (isinstance(verification_result, list) and verification_result)
                ):
                    print(">>> ✓ 验证成功：记忆已可搜索")
                else:
                    print(">>> ⚠️ 验证失败：刚存储的记忆无法立即搜索到")
                    print(f">>> 验证搜索结果: {verification_result}")

            except Exception as e:
                print(f">>> ❌ 存储记忆时出现错误: {e}")
                print(f">>> 错误详情: {type(e).__name__}: {str(e)}")
                import traceback
                print(f">>> 完整错误栈: {traceback.format_exc()}")

        except Exception as e:
            print(f"\n在聊天过程中发生错误: {e}")
            print("请检查你的 API Keys、网络连接和模型 Endpoint ID 是否正确。")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断。")
            break


async def test_mem0_connection():
    """
    测试 Mem0 连接和基本功能的辅助函数
    """
    print("--- 🔧 Mem0 连接和功能测试 ---")
    try:
        if not MEM0_API_KEY:
            print("❌ 错误：未找到 MEM0_API_KEY")
            return False

        mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)

        # 1. 测试基本连接
        print(">>> 测试1: 基本连接...")
        try:
            memories = await mem0_client.get_all(user_id=USER_ID, limit=1)
            print("✓ 基本连接成功")
        except Exception as e:
            print(f"❌ 基本连接失败: {e}")
            return False

        # 2. 测试添加记忆功能
        print(">>> 测试2: 添加记忆功能...")
        test_user_id = f"test_user_{int(asyncio.get_event_loop().time())}"
        test_messages = [
            {"role": "user", "content": "测试用户: 我的名字是测试用户"},
            {"role": "assistant", "content": "好的，我记住了你的名字是测试用户"}
        ]

        try:
            add_result = await mem0_client.add(
                messages=test_messages,
                user_id=test_user_id,
                metadata={"test": True, "timestamp": int(asyncio.get_event_loop().time())}
            )
            print(f"✓ 添加记忆成功: {add_result}")
        except Exception as e:
            print(f"❌ 添加记忆失败: {e}")
            return False

        # 3. 等待索引完成
        print(">>> 等待3秒让记忆索引完成...")
        await asyncio.sleep(3)

        # 4. 测试搜索功能
        print(">>> 测试3: 搜索记忆功能...")
        try:
            search_result = await mem0_client.search(
                query="测试用户",
                user_id=test_user_id,
                limit=5
            )

            if search_result and (
                (hasattr(search_result, 'results') and search_result.results) or
                (isinstance(search_result, list) and search_result)
            ):
                print("✓ 搜索记忆成功")
                print(f"搜索结果: {search_result}")
            else:
                print("⚠️ 搜索记忆无结果")
                print(f"搜索返回: {search_result}")

        except Exception as e:
            print(f"❌ 搜索记忆失败: {e}")
            return False

        # 5. 测试获取所有记忆
        print(">>> 测试4: 获取所有记忆...")
        try:
            all_memories = await mem0_client.get_all(user_id=test_user_id, limit=10)

            if all_memories and (
                (hasattr(all_memories, 'results') and all_memories.results) or
                (isinstance(all_memories, list) and all_memories)
            ):
                print("✓ 获取所有记忆成功")
                print(f"获取结果: {all_memories}")
            else:
                print("⚠️ 获取所有记忆无结果")
                print(f"获取返回: {all_memories}")

        except Exception as e:
            print(f"❌ 获取所有记忆失败: {e}")
            return False

        # 6. 清理测试数据
        print(">>> 测试5: 清理测试数据...")
        try:
            await mem0_client.delete_all(user_id=test_user_id)
            print("✓ 清理测试数据成功")
        except Exception as e:
            print(f"⚠️ 清理测试数据失败: {e}")

        print("\n🎉 所有测试通过！Mem0 API 工作正常")
        return True

    except Exception as e:
        print(f"❌ 整体测试失败: {e}")
        import traceback
        print(f"完整错误栈: {traceback.format_exc()}")
        return False


async def show_user_memories():
    """
    显示用户的所有记忆（用于调试）
    """
    try:
        if not MEM0_API_KEY:
            print("错误：未找到 MEM0_API_KEY")
            return

        mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)

        print(f"\n--- 📋 用户 {USER_ID} 的记忆诊断 ---")

        # 尝试不同的API格式获取记忆
        print(">>> 尝试使用 v1.1 格式获取记忆...")
        try:
            memories_v11 = await mem0_client.get_all(user_id=USER_ID, limit=50, output_format="v1.1")
            print(f">>> v1.1 API 返回结果: {type(memories_v11)}")
            print(f">>> v1.1 结果内容: {memories_v11}")
        except Exception as e:
            print(f">>> v1.1 格式获取失败: {e}")
            memories_v11 = None

        print(">>> 尝试使用默认格式获取记忆...")
        try:
            memories_default = await mem0_client.get_all(user_id=USER_ID, limit=50)
            print(f">>> 默认 API 返回结果: {type(memories_default)}")
            print(f">>> 默认结果内容: {memories_default}")
        except Exception as e:
            print(f">>> 默认格式获取失败: {e}")
            memories_default = None

        # 处理结果
        memories = memories_v11 or memories_default

        if memories and hasattr(memories, 'results') and memories.results:
            print(f"\n✅ 总共找到 {len(memories.results)} 条记忆:")
            for i, memory in enumerate(memories.results, 1):
                memory_text = memory.get('memory', 'N/A')
                memory_id = memory.get('id', 'N/A')
                created_at = memory.get('created_at', 'N/A')
                updated_at = memory.get('updated_at', 'N/A')
                categories = memory.get('categories', [])
                metadata = memory.get('metadata', {})

                print(f"\n{i}. 记忆 ID: {memory_id}")
                print(f"   内容: {memory_text}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                if categories:
                    print(f"   分类: {', '.join(categories)}")
                if metadata:
                    print(f"   元数据: {metadata}")

        elif memories and isinstance(memories, list):
            print(f"\n✅ 总共找到 {len(memories)} 条记忆:")
            for i, memory in enumerate(memories, 1):
                print(f"{i}. {memory}")

        else:
            print("\n❌ 该用户暂无记忆记录")
            print("🔍 可能的原因:")
            print("   1. 记忆确实没有存储成功")
            print("   2. 记忆正在处理中，需要等待几分钟")
            print("   3. API Key 权限不足")
            print("   4. 用户ID不匹配")

        # 尝试搜索测试
        print(f"\n--- 🔍 搜索功能测试 ---")
        test_queries = ["alex", "hello", "姓名", "name", "用户"]
        for query in test_queries:
            try:
                search_result = await mem0_client.search(query=query, user_id=USER_ID, limit=5)
                if search_result and (
                    (hasattr(search_result, 'results') and search_result.results) or
                    (isinstance(search_result, list) and search_result)
                ):
                    print(f">>> 搜索 '{query}': 找到结果")
                    if hasattr(search_result, 'results'):
                        for res in search_result.results[:2]:
                            print(f"    - {res.get('memory', str(res))[:50]}...")
                    else:
                        for res in search_result[:2]:
                            print(f"    - {res.get('memory', str(res))[:50]}...")
                else:
                    print(f">>> 搜索 '{query}': 无结果")
            except Exception as e:
                print(f">>> 搜索 '{query}' 失败: {e}")

    except Exception as e:
        print(f"❌ 获取记忆时出现错误: {e}")
        import traceback
        print(f"完整错误栈: {traceback.format_exc()}")


async def clear_user_memories():
    """
    清除用户的所有记忆（用于调试和重新开始）
    """
    try:
        if not MEM0_API_KEY:
            print("错误：未找到 MEM0_API_KEY")
            return

        print(f"⚠️  警告：即将删除用户 {USER_ID} 的所有记忆！")
        confirm = input("请输入 'DELETE' 来确认删除所有记忆: ").strip()

        if confirm != "DELETE":
            print("操作已取消。")
            return

        mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)

        # 删除用户的所有记忆
        result = await mem0_client.delete_all(user_id=USER_ID)
        print(f"✓ 已成功删除用户 {USER_ID} 的所有记忆")
        print(f"删除结果: {result}")

    except Exception as e:
        print(f"删除记忆时出现错误: {e}")


async def debug_config():
    """
    显示当前配置和环境信息
    """
    print("\n--- 🔍 配置和环境信息诊断 ---")

    print(f">>> 用户ID: {USER_ID}")
    print(f">>> MEM0_API_KEY 状态: {'✓ 已设置' if MEM0_API_KEY else '❌ 未设置'}")
    if MEM0_API_KEY:
        print(f">>> MEM0_API_KEY 长度: {len(MEM0_API_KEY)} 字符")
        print(f">>> MEM0_API_KEY 前缀: {MEM0_API_KEY[:10]}..." if len(MEM0_API_KEY) > 10 else f">>> MEM0_API_KEY: {MEM0_API_KEY}")

    print(f">>> ARK_API_KEY 状态: {'✓ 已设置' if ARK_API_KEY else '❌ 未设置'}")
    print(f">>> VOLCANO_LLM_ENDPOINT_ID 状态: {'✓ 已设置' if VOLCANO_LLM_ENDPOINT_ID else '❌ 未设置'}")

    if VOLCANO_LLM_ENDPOINT_ID:
        print(f">>> VOLCANO_LLM_ENDPOINT_ID: {VOLCANO_LLM_ENDPOINT_ID}")

    # 检查环境文件
    import os
    env_file_path = "1.env"
    if os.path.exists(env_file_path):
        print(f">>> 环境文件 {env_file_path}: ✓ 存在")
        try:
            with open(env_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f">>> 环境文件内容长度: {len(content)} 字符")
                lines = content.strip().split('\n')
                for line in lines:
                    if line.strip() and not line.strip().startswith('#') and '=' in line:
                        key = line.split('=')[0].strip()
                        print(f"    - {key}: {'已设置' if '=' in line and line.split('=')[1].strip() else '未设置'}")
        except Exception as e:
            print(f">>> 读取环境文件失败: {e}")
    else:
        print(f">>> 环境文件 {env_file_path}: ❌ 不存在")

    # Python包版本检查
    try:
        import mem0
        print(f">>> mem0ai 版本: {getattr(mem0, '__version__', '未知')}")
    except ImportError:
        print(">>> mem0ai: ❌ 未安装")

    try:
        import openai
        print(f">>> openai 版本: {getattr(openai, '__version__', '未知')}")
    except ImportError:
        print(">>> openai: ❌ 未安装")

    try:
        import dotenv
        print(f">>> python-dotenv: ✓ 已安装")
    except ImportError:
        print(">>> python-dotenv: ❌ 未安装")


if __name__ == "__main__":
    print("=== Mem0 + 火山引擎 LLM 带记忆的 AI 助手 ===")
    print("选择操作:")
    print("1. 开始聊天")
    print("2. 测试 Mem0 API 功能")
    print("3. 查看用户记忆 (详细诊断)")
    print("4. 清除所有记忆 (⚠️ 危险操作)")
    print("5. 显示配置信息")

    try:
        choice = input("请输入选择 (1-5): ").strip()

        if choice == "1":
            asyncio.run(chat_with_ai())
        elif choice == "2":
            asyncio.run(test_mem0_connection())
        elif choice == "3":
            asyncio.run(show_user_memories())
        elif choice == "4":
            asyncio.run(clear_user_memories())
        elif choice == "5":
            asyncio.run(debug_config())
        else:
            print("无效选择，启动默认聊天模式...")
            asyncio.run(chat_with_ai())

    except KeyboardInterrupt:
        print("\n程序被用户中断。")
