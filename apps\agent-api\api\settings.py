from typing import List, Optional, Any # Add Any
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator
import os
import json  # 添加json模块导入
import logging # <--- 添加此行
from datetime import datetime, timezone # Import timezone

class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', extra='ignore')

    PROJECT_NAME: str = "Agent API"
    API_V1_STR: str = "/api/v1"

    # --- Database for AgnoMemoryDb (Standard PostgreSQL Connection String) ---
    # Agno's PostgresMemoryDb uses SQLAlchemy, which needs a standard DSN.
    # This should point to your Supabase PostgreSQL database.
    # Example: postgresql://postgres:[YOUR-PASSWORD]@[AWS-REGION].pooler.supabase.com:5432/postgres
    # Database Configuration - 修复：优先使用环境变量中的DATABASE_URL
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL")  # 直接从环境变量读取完整DATABASE_URL

    # 如果没有完整的DATABASE_URL，才从单独的组件构建
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASS: str = os.getenv("DB_PASS", "your_supabase_db_password") # Get from Supabase dashboard
    DB_HOST: str = os.getenv("DB_HOST", "db.your-project-ref.supabase.co") # Get from Supabase dashboard
    DB_PORT: str = os.getenv("DB_PORT", "5432")

    # --- Supabase SDK Configuration (for direct table access) ---
    SUPABASE_URL: Optional[str] = os.getenv("SUPABASE_URL") # e.g., https://your-project-ref.supabase.co
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = os.getenv("SUPABASE_SERVICE_ROLE_KEY") # Backend Service Role Key

    # LLM Configuration
    DEFAULT_CHAT_MODEL: str = os.getenv("DEFAULT_CHAT_MODEL", "gemini/gemini-1.5-flash-latest")
    DEFAULT_MEMORY_LLM_MODEL: str = os.getenv("DEFAULT_MEMORY_LLM_MODEL", "gemini/gemini-pro")
    DEFAULT_SUGGESTION_LLM_MODEL: str = os.getenv("DEFAULT_SUGGESTION_LLM_MODEL", "gemini/gemini-1.5-flash-latest")
    DEFAULT_CRISIS_LLM_MODEL: str = os.getenv("DEFAULT_CRISIS_LLM_MODEL", "gemini/gemini-1.5-flash-latest")

    # Agno Memory Configuration
    AGNO_MEMORY_TABLE_NAME: str = os.getenv("AGNO_MEMORY_TABLE_NAME", "agno_memory_storage")
    AGNO_MEMORY_DB_SCHEMA: str = os.getenv("AGNO_MEMORY_DB_SCHEMA", "public")
    AGNO_MEMORY_SEARCH_LIMIT: int = int(os.getenv("AGNO_MEMORY_SEARCH_LIMIT", "3"))

    # JWT Authentication - 这些配置现在可以安全地注释掉或移除
    # SUPABASE_JWT_SECRET: str = os.getenv("SUPABASE_JWT_SECRET", "your-very-strong-secret-key-please-change")
    # JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    # ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "1440")) # 24 hours

    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO").upper()

    CRISIS_KEYWORDS_LIST: List[str] = [
        '自杀', '结束生命', '不想活了', '想死', '厌倦生活',
        '自残', '伤害自己' # Simplified list, add more as needed
    ]
    DEFAULT_CRISIS_HOTLINE_NAME: str = "全国心理援助热线"
    DEFAULT_CRISIS_HOTLINE_NUMBER: str = "400-161-9995"

    # === 故事1.5：会话后分析生产环境配置 ===
    # 大型会话处理策略配置
    SESSION_ANALYSIS_MAX_TOKENS: int = int(os.getenv("SESSION_ANALYSIS_MAX_TOKENS", "4000"))
    SESSION_ANALYSIS_CHUNK_OVERLAP: int = int(os.getenv("SESSION_ANALYSIS_CHUNK_OVERLAP", "200"))

    # 异步任务可靠性保障配置
    SESSION_ANALYSIS_TIMEOUT_SECONDS: int = int(os.getenv("SESSION_ANALYSIS_TIMEOUT_SECONDS", "300"))  # 5分钟
    SESSION_ANALYSIS_MAX_RETRIES: int = int(os.getenv("SESSION_ANALYSIS_MAX_RETRIES", "3"))
    SESSION_ANALYSIS_RETRY_BASE_DELAY: float = float(os.getenv("SESSION_ANALYSIS_RETRY_BASE_DELAY", "2.0"))  # 指数退避基础延迟

    # 记忆服务同步配置
    MEMORY_SYNC_TIMEOUT_SECONDS: int = int(os.getenv("MEMORY_SYNC_TIMEOUT_SECONDS", "30"))
    MEMORY_SYNC_MAX_RETRIES: int = int(os.getenv("MEMORY_SYNC_MAX_RETRIES", "2"))
    MEMORY_SYNC_ENABLE_FALLBACK: bool = os.getenv("MEMORY_SYNC_ENABLE_FALLBACK", "true").lower() == "true"

    # 性能和监控配置
    SESSION_ANALYSIS_ENABLE_MONITORING: bool = os.getenv("SESSION_ANALYSIS_ENABLE_MONITORING", "true").lower() == "true"
    SESSION_ANALYSIS_LOG_PERFORMANCE: bool = os.getenv("SESSION_ANALYSIS_LOG_PERFORMANCE", "true").lower() == "true"
    SESSION_ANALYSIS_CONCURRENT_LIMIT: int = int(os.getenv("SESSION_ANALYSIS_CONCURRENT_LIMIT", "5"))  # 同时处理的会话分析任务数量

    # 不使用List[str]类型定义，改用str类型，避免Pydantic自动尝试JSON解析
    CORS_ORIGINS: str = os.getenv("CORS_ORIGINS", "http://localhost:3000")

    # === 故事1.13-B：JWT认证安全配置 ===
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "1440"))  # 24 hours

    @field_validator('JWT_SECRET_KEY')
    @classmethod
    def validate_jwt_secret_key(cls, v):
        if not v or v == "your-secret-key-at-least-32-characters":
            raise ValueError("JWT_SECRET_KEY must be set to a strong, secret value in the environment.")
        if len(v) < 32:
            raise ValueError("JWT_SECRET_KEY must be at least 32 characters long.")
        return v

    VOLCANO_LLM_APP_KEY: Optional[str] = os.getenv("VOLCANO_LLM_APP_KEY")
    VOLCANO_LLM_ENDPOINT_ID: Optional[str] = os.getenv("VOLCANO_LLM_ENDPOINT_ID")

    # Memory Service配置
    ZEP_API_KEY: Optional[str] = os.getenv("ZEP_API_KEY")
    MEM0_API_KEY: Optional[str] = os.getenv("MEM0_API_KEY")
    MEMORY_PROVIDER: str = os.getenv("MEMORY_PROVIDER", "zep")

    # 火山引擎API配置
    VOLCENGINE_ENDPOINT: Optional[str] = os.getenv("VOLCENGINE_ENDPOINT")

    # 火山引擎Webhook签名验证配置
    VOLCENGINE_WEBHOOK_SECRET: Optional[str] = os.getenv("VOLCENGINE_WEBHOOK_SECRET")
    VOLCENGINE_SIGNATURE_TOLERANCE: int = int(os.getenv("VOLCENGINE_SIGNATURE_TOLERANCE", "300"))  # 5分钟
    # 移除 VOLCENGINE_ENABLE_SIGNATURE_VERIFICATION - 始终启用签名验证提升安全性
    VOLCENGINE_IP_WHITELIST: Optional[str] = os.getenv("VOLCENGINE_IP_WHITELIST")  # 逗号分隔的IP列表

    # 火山引擎RTC配置 - 故事1.4-B
    VOLCANO_ACCESS_KEY_ID: Optional[str] = os.getenv("VOLCANO_ACCESS_KEY_ID")
    VOLCANO_SECRET_ACCESS_KEY: Optional[str] = os.getenv("VOLCANO_SECRET_ACCESS_KEY")
    VOLCANO_RTC_APP_ID: Optional[str] = os.getenv("VOLCANO_RTC_APP_ID")
    VOLCANO_RTC_APP_KEY: Optional[str] = os.getenv("VOLCANO_RTC_APP_KEY")
    # 新增: 是否为火山License用户，如果为True，ASR/TTS大模型不需要AccessToken
    VOLCANO_USE_LICENSE: bool = os.getenv("VOLCANO_USE_LICENSE", "false").lower() == "true"

    # 火山引擎ASR配置
    VOLCANO_ASR_APP_ID: Optional[str] = os.getenv("VOLCANO_ASR_APP_ID")
    VOLCANO_ASR_ACCESS_TOKEN: Optional[str] = os.getenv("VOLCANO_ASR_ACCESS_TOKEN")
    VOLCANO_ASR_CLUSTER: str = os.getenv("VOLCANO_ASR_CLUSTER", "volcengine_streaming_common")

    # 火山引擎TTS配置
    VOLCANO_TTS_APP_ID: Optional[str] = os.getenv("VOLCANO_TTS_APP_ID")
    VOLCANO_TTS_ACCESS_TOKEN: Optional[str] = os.getenv("VOLCANO_TTS_ACCESS_TOKEN")
    VOLCANO_TTS_CLUSTER: str = os.getenv("VOLCANO_TTS_CLUSTER", "volcano_tts")
    API_BASE_URL: str = os.getenv("API_BASE_URL", "http://localhost:8003")

    # LiteLLM Proxy settings (if you decide to use their proxy server)
    # LITELLM_PROXY_URL: Optional[str] = os.getenv("LITELLM_PROXY_URL", None)

    def model_post_init(self, __context: Any) -> None:
        super().model_post_init(__context)
        # Construct DATABASE_URL for Agno (SQLAlchemy compatible)
        # 修复：只有在DATABASE_URL不存在时才从组件构建
        if not self.DATABASE_URL:
            # 从环境变量POSTGRES_DB读取数据库名，避免使用表名
            db_name = os.getenv("POSTGRES_DB", "postgres")
            self.DATABASE_URL = f"postgresql://{self.DB_USER}:{self.DB_PASS}@{self.DB_HOST}:{self.DB_PORT}/{db_name}"
            print(f"DATABASE_URL constructed from components: {self.DATABASE_URL}")
        else:
            print(f"Using existing DATABASE_URL from environment: {self.DATABASE_URL[:50]}...")

        if not self.SUPABASE_URL or not self.SUPABASE_SERVICE_ROLE_KEY:
            print("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env for Supabase SDK operations!")
            # 不使用logger，因为此时logger可能还没初始化
            # raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set.") # Or handle gracefully

# 使用标准的Pydantic配置加载，不再使用fallback机制
settings = Settings()

# 辅助函数来解析CORS_ORIGINS
def get_cors_origins() -> List[str]:
    """将CORS_ORIGINS字符串转换为列表"""
    # 如果包含逗号，解析为列表
    if "," in settings.CORS_ORIGINS:
        return [origin.strip() for origin in settings.CORS_ORIGINS.split(",") if origin.strip()]
    # 否则只返回单个值的列表
    return [settings.CORS_ORIGINS]

# 辅助函数来解析火山引擎IP白名单
def get_volcengine_ip_whitelist() -> Optional[List[str]]:
    """将VOLCENGINE_IP_WHITELIST字符串转换为列表"""
    if not settings.VOLCENGINE_IP_WHITELIST:
        return None

    # 如果包含逗号，解析为列表
    if "," in settings.VOLCENGINE_IP_WHITELIST:
        return [ip.strip() for ip in settings.VOLCENGINE_IP_WHITELIST.split(",") if ip.strip()]
    # 否则只返回单个值的列表
    return [settings.VOLCENGINE_IP_WHITELIST.strip()]

# 简化的全局logger实例 (通常在FastAPI应用启动时配置更佳)
logger = logging.getLogger("api") # Get a root logger for the api

# Logging setup function - this should ideally be called once when settings are loaded.
def setup_logging(log_level_str: str):
    numeric_level = getattr(logging, log_level_str.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level_str}')

    # 获取名为 "api" 的 logger
    api_logger = logging.getLogger("api")
    # 移除特定 logger "api" 的 handlers，如果它有的话
    for handler in api_logger.handlers[:]:
        api_logger.removeHandler(handler)

    api_logger.setLevel(numeric_level)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(process)d - %(threadName)s - %(message)s')
    handler.setFormatter(formatter)
    api_logger.addHandler(handler)
    api_logger.propagate = False # 阻止日志事件传递到根logger的handlers

setup_logging(settings.LOG_LEVEL) # 在settings实例化后立即设置日志

def get_settings() -> Settings:
    """
    返回设置实例的单例函数

    Returns:
        Settings: 全局设置实例
    """
    return settings
