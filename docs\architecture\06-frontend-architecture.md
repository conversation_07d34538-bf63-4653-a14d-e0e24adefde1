# 心桥项目前端架构设计

## 6.1 简介与技术选型

### 架构目标
本方案旨在为"心桥"移动端应用提供一个高性能、可维护、可扩展的前端架构。此架构将严格遵循UI/UX规范，确保为老年用户提供流畅、稳定且零负担的交互体验，并为独立开发者（您）提供一个清晰、高效的开发蓝图。

### 脚手架与核心技术栈
为加速开发并遵循行业最佳实践，项目将基于 **Obytes React Native/Expo 启动脚手架**  进行开发。该脚手架已为我们预置并配置了最优的核心技术栈：

| 类别 | 技术/库 | 用途 |
| :--- | :--- | :--- |
| **核心框架** | React Native + Expo | 跨平台移动应用开发 |
| **语言** | TypeScript | 提供静态类型检查，提升代码健壮性 |
| **UI与样式** | Nativewind v4 | 将Tailwind CSS的开发体验引入React Native |
| **路由** | Expo Router | 基于文件系统的声明式路由 |
| **状态管理** | Zustand | 轻量、简洁的全局状态管理 |
| **数据请求** | React Query + Axios | 高效管理服务器状态和缓存 |
| **列表性能** | @shopify/flash-list | 高性能虚拟化列表 |
| **动画** | react-native-reanimated | 流畅、高性能的动画实现 |

## 6.2 项目结构

我们将遵循Obytes脚手架提供的Monorepo结构，并将前端代码集中在`apps/mobile/`目录下。

```plaintext
xinqiao-app/
└── apps/
    └── mobile/
        ├── app/                # 路由目录 (Expo Router)
        │   ├── (onboarding)/   # 首次引导流程的路由组
        │   └── (chat)/         # 核心对话功能的路由组
        ├── assets/
        │   ├── fonts/
        │   └── images/
        ├── components/
        │   ├── features/       # 【关键】业务功能组件 (Organisms)
        │   │   ├── onboarding/
        │   │   └── chat/
        │   └── ui/             # 【关键】基础UI组件 (Atoms/Molecules)
        ├── services/
        │   └── RtcChatService.ts  # 【关键】封装与Agent API的实时通信
        ├── stores/
        │   └── chatStore.ts    # 【关键】Zustand状态管理
        └── ...
```

## 6.3 UI组件架构

为了实现UI/UX规范中定义的简洁界面和**双模交互**，我们将采用**原子设计(Atomic Design)**思想来组织组件。

### 组件分层策略

* **原子/分子 (Atoms/Molecules) - `src/components/ui/`**
  * **职责：** 此目录存放高度可复用、与业务逻辑无关的基础UI组件。它们是构建界面的"积木"。
  * **示例：** `Button.tsx`, `Text.tsx`, `ChatBubble.tsx`, `KeyboardIcon.tsx`。

* **有机体 (Organisms) - `src/components/features/`**
  * **职责：** 此目录存放与特定业务功能相关的组合组件。它们由原子/分子组件构成，并包含部分UI状态和逻辑。
  * **示例：**
    * `features/onboarding/RoleSelection.tsx`: 角色选择卡片。
    * `features/chat/ChatHistory.tsx`: 对话历史列表。
    * `features/chat/ChatInputController.tsx`: **（双模交互核心）**

### 双模交互界面实现方案

我们将创建一个名为`ChatInputController.tsx`的核心组件来专门处理语音和文本模式的切换，以保持主界面的逻辑清晰。

* **内部状态:** `const [mode, setMode] = useState<'voice' | 'text'>('voice');`
* **渲染逻辑:**
  * `if (mode === 'voice')`: 渲染巨大的圆形**"按住说话"按钮和角落的键盘图标**。
  * `if (mode === 'text')`: 渲染文本输入框、发送按钮和角落的语音图标。
* **职责:**
  * 封装所有与输入模式切换相关的UI状态和**动画**（使用`react-native-reanimated`）。
  * 根据当前模式，调用不同的服务函数（如`RtcChatService.startVoiceStream()`或`RtcChatService.sendTextMessage()`)。

## 6.4 状态管理方案

为了支撑实时对话历史的流畅渲染，并满足性能要求，我们的状态管理方案如下：

### 状态管理库
我们将使用脚手架中预置的 **Zustand**。它足够轻量、高性能，且API简洁，非常适合管理我们的全局UI状态。

### 核心Store设计 (`stores/chatStore.ts`)

```typescript
import { create } from 'zustand';

interface Message { /* ... */ }

interface ChatState {
  messages: Message[];
  isAiResponding: boolean;
  addMessage: (message: Message) => void;
  updateLastMessage: (contentDelta: string) => void;
  setAiResponding: (isResponding: boolean) => void;
  // ... 其他状态和操作
}

export const useChatStore = create<ChatState>((set) => ({
  messages: [],
  isAiResponding: false,
  addMessage: (newMessage) => set((state) => ({ messages: [...state.messages, newMessage] })),
  updateLastMessage: (contentDelta) => set((state) => {
    const lastMessage = state.messages[state.messages.length - 1];
    if (lastMessage && lastMessage.sender === 'ai') {
      lastMessage.content += contentDelta;
      return { messages: [...state.messages] };
    }
    return state;
  }),
  setAiResponding: (isResponding) => set({ isAiResponding: isResponding }),
}));
```

### 实时对话列表渲染性能优化

这是确保用户体验流畅的关键。

* **使用FlashList：** 聊天记录的展示**必须**使用`@shopify/flash-list`组件。它通过视图回收机制，即使在有数千条消息的情况下也能保持极高的渲染性能和低内存占用。

* **组件备忘录 (Memoization):** 每一个独立的对话气泡组件（`ChatBubble.tsx`）都**必须**使用`React.memo`进行包裹，以避免在`messages`数组更新时发生不必要的重渲染。

```typescript
// components/features/chat/ChatBubble.tsx
export const ChatBubble = React.memo(({ message }: { message: Message }) => {
  // ... 渲染逻辑
});
```

* **状态选择性订阅：** Zustand的特性让我们可以在组件中只订阅所需的状态切片，避免因不相关的状态变化导致重渲染。

## 6.5 性能保障架构

本架构设计严格遵守PRD中NFR2定义的性能指标。

* **应用启动性能 (<3s):**
  * **实现：** 依赖Expo EAS的生产构建优化（代码压缩、treeshaking）和Hermes引擎
  * **监控：** 冷启动时间必须< 3秒（与PRD NFR2对齐）

* **交互响应性能 (<100ms):**
  * **实现：**
    * 所有用户操作（如按钮点击）的UI反馈（如按下效果）必须< 100ms
    * 使用`react-native-reanimated`处理所有需要高性能的动画
    * 按钮状态变化、模式切换等核心交互严格控制在100ms内

* **动画帧率 (>45 FPS):**
  * **实现：** 所有动画，特别是模式切换动画，都**必须**使用`react-native-reanimated`并在UI线程上执行
  * **监控：** 关键动画帧率必须>45 FPS，目标60 FPS

* **前端API调用性能:**
  * **目标：** API调用响应时间< 200ms (P95)（与后端架构对齐）
  * **实现：** 使用React Query缓存，错误重试机制，超时处理

* **长列表滚动流畅性：**
  * **实现：** **强制**使用`@shopify/flash-list` 渲染对话历史
  * **监控：** 滚动操作必须保持>45 FPS

## 6.6 API集成层 (`services/`)

### `RtcChatService.ts`

此文件将封装所有与后端`agno_api`的通信逻辑。

* **`prepareAndConnectRtc()`:**
  1. 调用我方后端的`/prepare_session`接口。
  2. 获取到RTC凭证后，初始化火山RTC SDK并建立连接。

* **`sendTextMessage(text: string)`:**
  1. 调用我方后端的`/chat/message` SSE接口。
  2. 使用`fetch` API的`ReadableStream`来处理SSE流。
  3. 监听`text_chunk`等事件，并调用`chatStore`中的actions来更新UI状态。

* **`endSession()`:** 调用我方后端的`/end_session`接口。

* **`handleRtcBinaryMessage()` (新增):**
  1. 监听火山RTC SDK的 `onRoomBinaryMessageReceived` 回调。
  2. 解析二进制消息，根据 `magic number` 区分 `conv` (AI状态) 和 `subv` (实时字幕) 消息。
  3. 将解析后的状态和字幕数据更新到对应的Zustand store (如 `chatStore`)，驱动UI实时更新。

## 6.7 其他技术规范

* **路由:** 严格遵循`expo-router`基于文件系统的路由约定。
* **样式:** 全部使用`Nativewind`。在`tailwind.config.js`中定义和扩展项目的设计规范（颜色、字体等）。
* **测试:** `Jest + React Native Testing Library`。重点测试`ChatInputController`的状态切换逻辑和`chatStore`的actions是否正确。

## 6.8 核心界面详细实现规范

### 主对话视图 (Main Chat View)

这是产品的核心，所有设计细节必须被精确实现。

#### 整体布局
* **顶部 (Header):** 固定区域，显示AI的角色名称（如"我的老朋友"）
* **中部 (Chat History):** 对话气泡列表。**必须使用** `@shopify/flash-list` 实现以保证长列表的滚动性能
* **底部 (Input Area):** **动态交互核心区**，将根据内部状态在"语音模式"和"文本模式"之间切换

#### 双模交互详细实现 (开发实现指南)

**A. 默认状态：语音模式 (Voice-First Mode)**

视觉呈现：
* 屏幕底部中央是一个**巨大、清晰的圆形按钮**。按钮内部是麦克风图标，并配有明确的文字标签："**按住 说话**"。这是整个界面的视觉焦点
* 在该按钮的**左侧**，放置一个小而清晰的图标按钮，图标为"**键盘**"，用于切换到文本模式

交互细节：
* **长按"按住 说话"按钮:** 按钮需有放大和光晕动效，表示"正在倾听"
* **松开按钮:** 动效停止，按钮恢复原状，界面显示"正在思考..."状态

**B. 切换中：流畅的过渡动画 (The Transition)**

触发：用户点击左下角的"键盘"图标

动画指令 (使用 `react-native-reanimated`)：
1. 系统键盘从屏幕底部**平滑地向上滑出**
2. **同时**，底部的"按住 说话"按钮和"键盘"图标，**平滑地淡出 (Fade Out)**
3. 一个**单行文本输入框**和"**发送**"按钮，从键盘上方**平滑地浮现 (Fade In)**
4. 动画时长应控制在 `200ms` 至 `300ms` 之间，使用 `withTiming` 和 `Easing.inOut(Easing.quad)`

**C. 激活状态：文本模式 (Text Input Mode)**

视觉呈现：
* 屏幕底部是系统键盘
* 键盘上方是文本输入框和"发送"按钮
* 原"键盘"图标的位置，现在显示为一个"**麦克风**"图标按钮，用于切换回语音模式

返回语音模式的交互逻辑：
1. **自动返回 (主要路径):** 用户点击"发送"按钮后，消息发出，**键盘应自动收起，界面必须自动、平滑地动画恢复到默认的"语音模式"**
2. **手动返回:** 如果用户不想发送文字，可以点击左侧的"麦克风"图标，键盘收起，界面恢复到"语音模式"
3. **点击空白返回:** 点击对话历史区的空白处，同样可以收起键盘，恢复到"语音模式"

设计原理：这种"用完即走"的设计，遵循了"语音优先"的原则，将文本输入视为一种临时的、辅助性的操作，从而避免了用户在完成文本输入后需要思考"如何返回"的问题，将认知负荷降至最低。

## 6.9 组件库与设计系统实现规范

### 核心组件实现要求

**Button 组件**
* **核心交互按钮 ("按住说话"):** 必须实现清晰的**默认**（呼吸光效）、**按下时**（水波纹动效）、**发送后**（思考中动效）三种状态
* **模式切换图标按钮 (键盘/语音):** 必须使用通用的、用户熟知的图标样式

**ChatBubble 组件**
* **样式:** 严格区分用户和AI的样式（颜色、左右位置）
* **字体:** 严格使用PRD中定义的超大字号 (`>=18pt`)

**TextInput 组件**
* **样式:** 同样需要超大字号，并有清晰的聚焦状态（如边框变色）

## 6.10 风格指南技术实现

### 色彩规范技术配置

在 `tailwind.config.js` 中定义以下色彩系统：

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        // 主色调
        'primary-bg': '#FFFBF5',      // 柔和象牙白 - 主视图背景
        'primary-text': '#5D4037',    // 深棕色 - 大部分文本
        'ai-bubble': '#ECEFF1',       // 蓝灰色 - AI回复气泡背景
        'user-bubble': '#FFE4B5',     // 温暖珊瑚色 - 用户回复气泡背景
        'accent': '#FF7043',          // 暖橙色 - "按住说话"按钮背景
      }
    }
  }
}
```

### 字体规范技术实现

字体家族：使用系统默认字体
* iOS: PingFang SC
* Android: Source Han Sans/Noto Sans

字号配置（与PRD适老化要求对齐）：
* 界面文字最小: 16pt（符合NFR1要求）
* 核心对话内容: 18pt+（符合NFR1要求）
* 主要按钮文字: 20pt
* 标题文字: 24pt

触摸区域标准（与PRD适老化要求对齐）：
* 所有可点击元素: ≥44x44 points（符合NFR1要求）
* 主要交互按钮: ≥60x60 points
* "按住说话"核心按钮: ≥120x120 points

色彩对比度要求（与PRD适老化要求对齐）：
* 文本与背景对比度: >4.5:1（符合NFR1要求）
* 重要信息对比度: >7:1
* 错误/警告信息对比度: >4.5:1

## 6.11 无障碍设计技术检查清单

### WCAG 2.1 AA 级标准实现

开发时需逐项确认的技术要求：

* [ ] **颜色对比度:** 所有文本与背景的对比度**必须** > 4.5:1
* [ ] **点击区域:** 所有可点击元素的触摸区域**不小于** 44x44 points
* [ ] **屏幕阅读器:** 所有可交互元素都**必须**有清晰的`accessibilityLabel`
* [ ] **动态字体:** 应用**必须**支持用户在系统设置中调整字体大小

### 技术实现示例

```typescript
// 无障碍标签示例
<TouchableOpacity
  accessibilityLabel="按住说话，与AI伙伴交流"
  accessibilityHint="长按开始录音，松开结束录音"
  accessibilityRole="button"
>
  <Text>按住 说话</Text>
</TouchableOpacity>
```

## 6.12 动效与微交互技术实现

### 动效实现原则
* **目的:** 提供清晰的反馈、传递温暖的情感，而非炫技
* **技术栈:** **必须**使用`react-native-reanimated`实现所有关键动画

### 核心动画技术实现

**模式切换动画实现:**
```typescript
import { withTiming, Easing } from 'react-native-reanimated';

// 与UX设计文档规范对齐：200-300ms过渡时间
const transition = () => {
  'worklet';
  return withTiming(toValue, {
    duration: 250, // 符合UX要求的200-300ms范围
    easing: Easing.inOut(Easing.quad), // 与UX设计中的平滑过渡要求一致
  });
};

// 具体动画参数配置（与UX设计文档对齐）
export const ANIMATION_CONFIG = {
  MODE_SWITCH: {
    duration: 250,
    easing: Easing.inOut(Easing.quad),
  },
  BUTTON_PRESS: {
    duration: 100, // 符合<100ms响应要求
    easing: Easing.out(Easing.quad),
  },
  TYPING_EFFECT: {
    duration: 50, // 每个字符的显示间隔
    easing: Easing.linear,
  },
  BREATHING_EFFECT: {
    duration: 2000, // 呼吸光效周期
    easing: Easing.inOut(Easing.sine),
  }
};
```

**其他关键动效:**
* **呼吸光效:** 用于"按住说话"按钮的默认状态，以吸引用户注意
* **水波纹动效:** 用户按下按钮时的反馈，模拟真实世界的物理触感
* **打字机效果:** AI的文本回复应逐字或逐词出现，以模拟思考和输入的过程，增加生命感 