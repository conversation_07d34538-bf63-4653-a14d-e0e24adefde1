"""
危机检测服务 - 故事1.7-B
实现架构师建议：
1. 大小写不敏感的匹配，使用text.lower()进行标准化
2. 支持部分匹配和多种表达方式
3. 关键词列表包含常见的自杀倾向、自伤和极度绝望的表达
"""
import logging
import re
from typing import List, Set, Optional, Dict, Any
from dataclasses import dataclass
from api.settings import settings

logger = logging.getLogger(__name__)


@dataclass
class CrisisDetectionResult:
    """危机检测结果"""
    is_crisis: bool
    type: Optional[str] = None
    level: Optional[float] = None
    hotline_info: Optional[Dict[str, str]] = None
    message: Optional[str] = None


class CrisisDetectionService:
    """危机检测服务 - 检测用户输入中的危机信号"""

    def __init__(self, keywords: Optional[List[str]] = None):
        """
        初始化危机检测服务

        Args:
            keywords: 危机关键词列表，如果为None则使用默认关键词
        """
        if keywords is None:
            self.keywords = self.get_default_keywords()
        elif len(keywords) == 0:
            # 如果传入空列表，使用默认关键词（符合配置缺失处理要求）
            logger.warning("传入空关键词列表，使用默认关键词列表")
            self.keywords = self.get_default_keywords()
        else:
            self.keywords = keywords

        # 转换为小写以支持大小写不敏感检测（架构师建议）
        self.keywords_lower = set(keyword.lower() for keyword in self.keywords)

        logger.info(f"危机检测服务初始化完成，关键词数量: {len(self.keywords)}")

    def detect(self, text: str) -> bool:
        """
        检测文本中是否包含危机信号

        实现架构师建议：
        1. 大小写不敏感检测
        2. 支持部分匹配和多种表达方式

        Args:
            text: 要检测的文本

        Returns:
            bool: True表示检测到危机信号，False表示未检测到
        """
        # 处理空输入或None输入
        if not text:
            return False

        # 标准化文本为小写（架构师建议：text.lower()）
        text_lower = text.lower().strip()

        # 如果文本为空（只有空格），返回False
        if not text_lower:
            return False

        try:
            # 检查是否包含任何危机关键词
            for keyword in self.keywords_lower:
                if keyword in text_lower:
                    logger.warning(
                        f"检测到危机信号 - 关键词: '{keyword}', 文本片段: '{text[:50]}...'"
                    )
                    return True

            return False

        except Exception as e:
            # 检测过程中出现异常，记录错误但返回False避免影响正常对话
            logger.error(f"危机检测过程中出现异常: {str(e)}", exc_info=True)
            return False

    def update_keywords(self, new_keywords: List[str]) -> None:
        """
        动态更新关键词列表

        Args:
            new_keywords: 新的关键词列表
        """
        self.keywords = new_keywords
        self.keywords_lower = set(keyword.lower() for keyword in new_keywords)
        logger.info(f"危机关键词列表已更新，新关键词数量: {len(new_keywords)}")

    @staticmethod
    def get_default_keywords() -> List[str]:
        """
        获取默认的危机关键词列表

        架构师建议：关键词列表应包含常见的自杀倾向、自伤和极度绝望的表达

        Returns:
            List[str]: 默认关键词列表
        """
        return [
            # 直接自杀相关
            "自杀", "自s", "想死", "去死", "死了算了",

            # 生活厌倦相关
            "不想活了", "不想活", "活着没意思", "活着无意义", "厌倦生活",

            # 结束生命相关
            "结束生命", "结束自己", "了结自己", "一了百了",

            # 自伤相关
            "自残", "自harm", "伤害自己", "割腕", "割手腕",

            # 绝望表达
            "没有希望", "绝望", "痛苦不堪", "撑不下去",
            "活不下去", "受不了了", "崩溃了",

            # 其他危机表达
            "想要解脱", "一死了之", "离开这个世界"
        ]

    @classmethod
    def from_settings(cls) -> 'CrisisDetectionService':
        """
        从配置文件创建CrisisDetectionService实例

        Returns:
            CrisisDetectionService: 配置的服务实例
        """
        try:
            keywords = settings.CRISIS_KEYWORDS_LIST
            if not keywords:
                logger.warning("配置中的CRISIS_KEYWORDS_LIST为空，使用默认关键词")
                keywords = None
            return cls(keywords=keywords)
        except Exception as e:
            logger.error(f"从配置创建危机检测服务失败: {str(e)}, 使用默认配置")
            return cls()


def get_crisis_detection_service() -> CrisisDetectionService:
    """
    获取危机检测服务实例（单例模式）

    Returns:
        CrisisDetectionService: 危机检测服务实例
    """
    return CrisisDetectionService.from_settings()
