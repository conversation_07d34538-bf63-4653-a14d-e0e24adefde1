# 故事 1.2: 无感身份认证与角色创建流程

## 基本信息
- **故事编号**: 1.2
- **故事标题**: 无感身份认证与角色创建流程
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: 老年用户
- **优先级**: 最高（P0）
- **工作量估计**: 8-12 个工作日
- **依赖关系**: 故事 1.1 (项目基础设置), 故事 1.2-UI (引导流程界面设计), 故事 1.2-B (后端认证服务)
- **Status**: Approved

## 故事描述

作为老年用户，我希望能无缝地进入应用，并通过一次流畅的对话完成我的专属AI伙伴的创建，**以便** 我能快速开始体验产品的核心价值。

## 验收标准

### AC1: 应用启动与无感认证
- [ ] 应用启动时自动完成身份认证，用户无需输入任何登录信息
- [ ] 客户端能成功调用后端获取JWT令牌并本地存储
- [ ] 首次启动和后续启动的用户体验一致且流畅
- [ ] 认证失败时有适当的降级处理和重试机制

### AC2: 角色共创引导流程UI/UX
- [ ] "温暖的初见"引导流程UI/UX与设计稿一致，适合老年用户操作
- [ ] 支持AI角色的命名、身份设定和声音选择
- [ ] 引导流程简洁清晰，每个步骤都有明确的指引
- [ ] 所有UI元素符合适老化设计标准（字体≥16pt，触摸区域≥44x44pt，高对比度）

### AC3: 角色共创对话体验
- [ ] 通过自然对话方式引导用户完成AI角色配置
- [ ] 对话界面支持文本输入，为后续语音功能做准备
- [ ] AI回复显示自然流畅，具有渐现效果（打字机效果）
- [ ] 用户可以随时修改之前的选择

### AC4: 端到端流程完成
- [ ] 用户可以完整地跑通从首次打开App到成功创建AI人设的全流程
- [ ] 角色信息成功保存到后端并在应用重启后正确加载
- [ ] 完成引导后平滑过渡到主对话界面
- [ ] 支持引导流程的中断和恢复

### AC5: 性能与用户体验标准（与PRD NFR2对齐）
- [ ] **应用启动性能**: 冷启动时间< 3秒
- [ ] **交互响应性能**: 所有用户操作的UI反馈< 100ms
- [ ] **动画性能**: 模式切换动画保持>45 FPS，目标60 FPS
- [ ] **API调用性能**: 认证和角色配置API调用< 200ms (P95)
- [ ] **适老化设计验证**: 
  - 字体大小≥16pt（对话内容≥18pt）
  - 触摸区域≥44x44pt
  - 色彩对比度>4.5:1

## Tasks / Subtasks

### 第一阶段：项目架构和认证基础 (2-3天)
- [ ] **项目结构搭建** (AC1)
  - 基于Obytes脚手架初始化React Native + Expo项目
  - 配置NativeWind、Zustand、React Query等核心依赖
  - 设置路径别名和TypeScript配置

- [ ] **认证服务集成** (AC1)
  - 创建`src/services/AuthService.ts`封装后端认证API调用
  - 实现无感身份认证逻辑（设备指纹生成）
  - 集成JWT Token管理和自动刷新机制
  - 配置本地存储用于Token持久化（项目使用react-native-mmkv提供高性能同步存储）

- [ ] **状态管理设计** (AC1)
  - 创建`src/stores/authStore.ts`管理认证状态
  - 设计用户画像和角色配置的全局状态
  - 实现状态持久化和恢复机制

### 第二阶段：引导流程UI组件开发 (3-4天)
- [ ] **基础UI组件** (AC2)
  - 创建`src/components/ui/Button.tsx`（符合适老化设计）
  - 创建`src/components/ui/Text.tsx`（大字体支持）
  - 创建`src/components/ui/Input.tsx`（高对比度设计）
  - 创建`src/components/ui/Card.tsx`（引导卡片容器）

- [ ] **引导流程路由** (AC2)
  - 配置`app/(onboarding)/`路由组
  - 实现`app/onboarding.tsx`主引导页面
  - 创建分步引导的子页面组件
  - 配置页面间的导航和数据传递

- [ ] **角色配置组件** (AC2)
  - 创建`src/components/features/onboarding/RoleSelection.tsx`
  - 实现角色命名输入界面
  - 创建声音选择组件（为未来功能预留）
  - 实现配置预览和确认界面

### 第三阶段：对话式引导体验 (2-3天)
- [ ] **对话界面组件** (AC3)
  - 创建`src/components/features/chat/ChatBubble.tsx`
  - 实现AI回复的打字机效果动画
  - 创建用户输入区域组件
  - 支持对话历史的展示和滚动

- [ ] **对话逻辑实现** (AC3)
  - 设计引导流程的对话脚本和状态机
  - 实现用户输入的验证和处理
  - 集成后端的角色配置API
  - 实现对话的中断和恢复机制

- [ ] **用户体验优化** (AC3)
  - 添加适当的加载状态和动画
  - 实现触摸反馈和无障碍支持
  - 优化键盘处理和界面适配
  - 添加错误处理和重试机制

### 第四阶段：端到端集成和优化 (2天)
- [ ] **API集成测试** (AC4)
  - 完整测试认证流程的API调用
  - 验证角色配置数据的正确传输
  - 测试Token刷新和错误恢复
  - 确保离线状态的优雅处理

- [ ] **流程完整性验证** (AC4)
  - 端到端测试完整引导流程
  - 验证应用重启后的状态恢复
  - 测试不同设备和屏幕尺寸的兼容性
  - 优化性能和内存使用

- [ ] **用户体验抛光** (AC4)
  - 实现流畅的页面过渡动画
  - 优化引导完成后的界面切换
  - 添加适当的成功反馈和庆祝动效
  - 最终的无障碍功能验证

## Dev Notes

CRITICAL: This is a **frontend functionality story**. 
**PREREQUISITE**: Story 1.1-UI must be completed first - all UI components and page layouts are provided by that story.

Load the following standards for implementation:
- `@docs/architecture/mobile-app-tech-stack.md`
- `@docs/architecture/mobile-app-source-tree.md`
- `@docs/architecture/mobile-app-coding-standards.md`

**Technical Guidance from Architecture:**

### Relevant API Endpoints to Consume:
从 `@docs/architecture/03-api-design.md` 中的关键接口：
- **认证相关API**:
  - `POST /api/v1/auth/anonymous-login` - 无感身份认证
  - `POST /api/v1/auth/refresh-token` - Token刷新
  - `POST /api/v1/auth/finalize_onboarding` - 完成引导流程
- **用户管理API**:
  - `GET /api/v1/user/profile` - 获取用户画像
  - `PUT /api/v1/user/profile` - 更新用户画像
  - `GET /api/v1/characters` - 获取AI角色列表
- **数据格式**: 所有API遵循RESTful规范，使用JWT Bearer Token认证

### UI Components to Build/Use:
从 `@docs/architecture/06-frontend-architecture.md` 中的组件架构：
- **原子级组件** (`src/components/ui/`):
  - `Button.tsx` - 符合适老化设计的按钮组件
  - `Text.tsx` - 大字体支持的文本组件
  - `Input.tsx` - 高对比度的输入组件
- **功能级组件** (`src/components/features/onboarding/`):
  - `RoleSelection.tsx` - 角色选择卡片组件
  - `OnboardingChat.tsx` - 引导对话组件
- **状态管理**: 使用Zustand进行全局状态管理
- **样式系统**: 使用NativeWind进行样式开发

### User Flow to Implement:
从 `@docs/prd/ux-design.md` 中的核心用户流程：
1. **温暖的初见流程**:
   - 应用启动 → 自动认证 → 欢迎界面
   - 通过对话式交互引导用户创建AI伙伴
   - 设定AI的身份、姓名和特征
2. **交互原则**:
   - 语音优先，但在引导阶段支持文本输入
   - 对话驱动的功能设置
   - 情感化反馈和温暖的用户体验
3. **设计要求**:
   - 零学习成本，借鉴用户熟悉的交互模式
   - 适老化设计：大字体、高对比度、大触摸区域
   - 清晰的引导和即时反馈

### Testing

Dev Note: Story Requires the following tests:

- [ ] Jest Unit Tests: (nextToFile: true), coverage requirement: 85%
- [ ] Jest with React Native Testing Library Integration Test (Test Location): location: `src/components/features/onboarding/__tests__/`
- [ ] Detox E2E: location: `e2e/onboarding/complete-flow.e2e.ts`

Manual Test Steps:
- 在不同设备上测试完整的引导流程（iPhone、Android不同尺寸）
- 验证无感认证在网络中断后的恢复能力
- 测试字体缩放和无障碍功能的兼容性
- 确认引导流程可以被中断和恢复

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.1（项目基础设置）已完成
- [ ] 故事1.2-B（后端认证服务）已完成并可正常调用
- [ ] 开发环境已配置完成（Node.js、React Native、Expo CLI）
- [ ] 设计稿和交互原型已确认

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 完整的引导流程可以端到端执行
- [ ] 单元测试覆盖率≥85%
- [ ] E2E测试全部通过
- [ ] 代码评审已完成
- [ ] 适老化设计要求全部满足

## 风险与缓解措施

### 主要风险
1. **设备兼容性**: 不同Android设备的适配问题
2. **网络异常**: 认证过程中的网络中断处理
3. **用户体验**: 老年用户对引导流程的理解困难
4. **性能问题**: 在低端设备上的流畅性

### 缓解措施
1. **充分的设备测试和渐进式降级策略**
2. **离线缓存和重试机制，网络状态提示**
3. **多轮用户测试和交互优化，简化步骤**
4. **性能监控和优化，避免过度动画**

## 相关文档引用
- [用户故事原文](../../prd/user-stories.md#故事-12-无感身份认证与角色创建流程)
- [UX设计规范](../../prd/ux-design.md)
- [前端架构设计](../../architecture/06-frontend-architecture.md)
- [API接口设计](../../architecture/03-api-design.md)
- [移动应用技术栈](../../architecture/mobile-app-tech-stack.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md) 