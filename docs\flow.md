## 🎯 **项目概述：心桥AI亲情伴侣**

### **核心使命**
"心桥"是一个专为55-75岁中国老年人设计的AI亲情伴侣移动应用，旨在通过先进的实时语音技术和富有同理心的人工智能，为面临"结构性孤独"的老年群体重建高质量、可靠且充满温暖的情感连接。

### **核心价值主张**
- **有记忆的陪伴**：AI能记住用户的个人信息、喜好和对话历史
- **有温度的交流**：通过角色共创建立个性化的AI伙伴
- **零学习成本**：极致简单的语音优先交互
- **永远有耐心**：24/7可用的情感支持

## 🔧 **MVP阶段核心功能清单**

### **1. 身份认证与角色创建**
- ✅ **无感身份认证**：通过匿名设备ID自动创建用户身份
- ✅ **AI角色共创**：用户通过对话为AI设定身份、命名、选择声音
- ✅ **个性化设置**：创建专属的AI伙伴人设

### **2. 实时语音对话系统**
- ✅ **双模交互界面**：语音优先 + 文本辅助
- ✅ **实时语音流**：基于火山引擎RTC的低延迟对话
- ✅ **智能记忆注入**：通过`MemoryService`实时检索和注入个人记忆上下文
- ✅ **自然语言处理**：支持中文语音识别和合成

### **3. 智能记忆管理 (外部服务驱动)**
- ✅ **会话后记忆处理**：异步分析对话，生成摘要并同步到记忆服务
- ✅ **统一记忆接口**：通过`MemoryService`与可插拔的记忆系统(Zep/Mem0)交互
- ✅ **上下文检索**：利用外部记忆服务实现高效的语义搜索

### **4. 生活助手功能**
- ✅ **对话式提醒**：通过自然语言设置和管理提醒
- ✅ **语音提醒播报**：AI角色语音播报提醒内容
- ✅ **智能时间解析**：理解"明天下午三点"等自然表达

### **5. 安全保障机制**
- ✅ **危机干预系统**：检测负面情绪并提供安全回应
- ✅ **内容安全过滤**：关键词检测和情绪分析
- ✅ **紧急资源引导**：必要时提供专业帮助联系方式

## 🏗️ **技术架构特色**

### **创新技术方案**
- **原生LLM编排模式**：由后端服务自主编排LLM与工具调用。
- **可插拔记忆架构**: 通过`IMemoryService`将对话逻辑与记忆系统解耦，支持Zep/Mem0等专业服务。
- **Agno框架 (备用)**：作为技术储备保留，不参与核心业务流程。
- **端到端优化**：P95延迟<1.5秒的实时体验

### **技术栈组合**
```
前端：React Native + Expo + TypeScript + Nativewind
后端：Python + FastAPI
数据库：Supabase PostgreSQL + 行级安全
语音：火山引擎RTC + ASR/TTS
部署：Serverless + CI/CD
```

## 👥 **目标用户画像**

### **核心用户（使用者）**
- **年龄**：55-75岁的中国老年人
- **特征**：数字融入型，会用微信、抖音等基础应用
- **痛点**：结构性孤独、缺少高质量陪伴、不愿打扰子女
- **需求**：情感倾诉、记忆分享、生活提醒、安全感

### **间接用户（付费方）**
- **年龄**：30-50岁的成年子女
- **特征**：工作繁忙、关心父母但陪伴时间有限
- **动机**：缓解内疚感、改善家庭关系、保障父母安全

## 📊 **商业模式与发展路线**

### **MVP阶段（0-6个月）**
- **目标**：验证AI伴侣的情感连接能力
- **策略**：免费使用，聚焦产品体验优化
- **指标**：用户情感反馈、留存率、使用时长

### **Phase 2（6-12个月）**
- **核心功能**："家庭记忆银行" + "家庭连接门户"
- **商业化**：面向子女的付费订阅服务
- **增值服务**：家庭情感报告、共享体验功能

### **Phase 3-4（1-3年）**
- **生态扩展**：IoT设备联动、社区服务对接
- **规模化**：B2G合作、行业标准制定
- **国际化**：港澳台及日韩市场拓展

## 🎯 **项目成功指标**

### **定性指标（最重要）**
- 收集到5+个感人的用户故事
- 用户反馈中出现"离不开"、"真懂我"等情感词汇
- 建立初步的情感连接和用户信任

### **定量指标**
- 次日留存率 > 60%
- 7日留存率 > 40%
- DAU/MAU > 40%
- 端到端语音延迟 < 1.5秒

## 🚀 **开发计划（12周MVP）**

### **Sprint 1（3周）**：基础设施
- 项目初始化、CI/CD配置
- 无感认证和角色创建流程

### **Sprint 2（3周）**：核心后端
- 实时上下文注入API
- 危机响应协议

### **Sprint 3（4周）**：语音交互
- RTC集成和实时对话

### **Sprint 4（2周）**：记忆和提醒
- 会话后分析与记忆同步
- 基于Function Calling的提醒功能

## 💡 **项目创新点与竞争优势**

### **技术创新**
1. **实时记忆注入架构**：首创的会话前记忆检索方案
2. **双模无缝交互**：语音优先但支持文本补充
3. **适老化设计**：专门为老年用户优化的UI/UX

### **产品创新**
1. **角色共创模式**：用户参与创造专属AI伙伴
2. **情感化交互**：超越工具属性的关系式陪伴
3. **家庭连接桥梁**：连接老年人与子女的情感纽带

### **商业创新**
1. **B2C2C模式**：老年人使用，子女付费的独特模式
2. **情感价值驱动**：以情感连接为核心的商业逻辑
3. **社会价值实现**：解决老龄化社会的实际问题

## 🎉 **总体评估**

这是一个**极具社会意义和商业价值**的创新项目：

### **优势**
- 📈 **巨大市场需求**：中国老龄化趋势明确，情感陪伴需求强烈
- 🚀 **技术先进性**：实时智能代理架构具有技术壁垒
- 💰 **清晰商业模式**：从免费体验到付费服务的转化路径明确
- 🏆 **差异化竞争**：专注老年群体的垂直化AI伴侣产品

### **挑战**
- 🎯 **用户教育成本**：需要证明AI能建立真实情感连接
- ⚡ **技术实现复杂度**：实时语音+记忆系统的工程挑战
- 🔒 **合规要求**：老年用户数据保护的严格要求

# 🚀 心桥项目开发完整指南

## 📋 **快速开始清单**

### 1. **环境准备**
```bash
# 1. 检查必需工具
node --version          # 需要 Node.js 18+
pnpm --version          # 需要 pnpm 9+
python --version        # 需要 Python 3.11+
git --version           # 需要 Git

# 2. 如果没有安装，先安装必需工具
npm install -g pnpm     # 安装 pnpm
# Python 建议使用 pyenv 或 conda 管理版本
```

### 2. **项目初始化**
```bash
# 克隆并进入项目
cd xinqiao

# 安装前端依赖 (在根目录)
pnpm install

# 设置后端环境
cd apps/agent-api
python -m venv .venv
# Windows
.venv\Scripts\activate
# macOS/Linux  
source .venv/bin/activate

# 安装后端依赖
pip install -r requirements.txt
cd ../..
```

## 🛠️ **日常开发工作流**

### **方式 1: 并行开发（推荐）**
```bash
# 在根目录同时启动前后端
pnpm dev

# 这会并行运行：
# - 前端: Expo 开发服务器
# - 后端: FastAPI 服务器 (http://localhost:8000)
```

### **方式 2: 分别开发**
```bash
# 终端 1 - 启动前端
pnpm mobile:dev
# 或者
cd apps/mobile-app && pnpm start

# 终端 2 - 启动后端  
pnpm api:dev
# 或者
cd apps/agent-api && python -m uvicorn api.main:app --reload
```

## 📱 **前端开发最佳实践**

### **开发命令**
```bash
# 开发环境
pnpm mobile:dev                    # 启动开发服务器
pnpm mobile:lint                   # 代码检查
pnpm mobile:test                   # 运行测试
pnpm run type-check                # 类型检查

# 构建和发布
pnpm run build:development:android # 构建 Android 开发版
pnpm run build:staging:ios        # 构建 iOS 测试版
```

### **前端调试技巧**
```bash
# 在 apps/mobile-app/ 目录下

# 清理缓存
npx expo start --clear

# 查看项目健康状态
pnpm doctor

# 运行 E2E 测试
pnpm e2e-test
```

## 🐍 **后端开发最佳实践**

### **开发命令**
```bash
# 在 apps/agent-api/ 目录下

# 开发
python -m uvicorn api.main:app --reload    # 启动开发服务器
python -m pytest                           # 运行测试
ruff check .                               # 代码检查
ruff format .                              # 代码格式化
mypy .                                     # 类型检查
```

### **数据库操作**
```bash
# 设置环境变量 (创建 .env 文件)
cp example.env .env
# 编辑 .env 文件，配置数据库连接

# 运行数据库迁移
python -m alembic upgrade head
```

## 🔄 **Monorepo 操作指南**

### **项目级别命令**
```bash
# 根目录统一操作
pnpm dev            # 并行启动前后端
pnpm build          # 并行构建前后端  
pnpm lint           # 并行检查前后端代码
pnpm test           # 并行运行前后端测试
pnpm type-check     # 并行类型检查

# 单独操作前端
pnpm mobile:dev     # 只启动前端
pnpm mobile:build   # 只构建前端
pnpm mobile:lint    # 只检查前端

# 单独操作后端
pnpm api:dev        # 只启动后端
pnpm api:test       # 只测试后端
pnpm api:lint       # 只检查后端
```

### **跨项目类型共享**
```bash
# 修改共享类型后，前后端会自动同步
# 类型定义位置：
shared/contracts/schema.ts     # TypeScript 类型
shared/contracts/schema.py     # Python Pydantic 模型
```

## 🎯 **代码规范工作流**

### **提交前检查**
```bash
# 在相应子项目目录下运行
cd apps/mobile-app
pnpm run check-all              # 前端完整检查

cd apps/agent-api  
ruff check . && ruff format . && mypy . && python -m pytest  # 后端完整检查
```

### **Git 工作流**
```bash
# 1. 创建功能分支
git checkout -b feature/用户认证

# 2. 开发和提交
git add .
git commit -m "feat: 添加用户认证功能"

# 3. 推送并创建 PR
git push origin feature/用户认证
```

## 🐛 **调试指南**

### **前端调试**
- **React DevTools**: 在 Expo 开发工具中查看组件状态
- **Flipper**: 检查网络请求和状态管理
- **VS Code 断点**: 使用 Expo 的调试配置

### **后端调试**
- **FastAPI 文档**: 访问 `http://localhost:8000/docs` 测试 API
- **Python 调试器**: 使用 `breakpoint()` 或 VS Code 调试配置
- **日志**: 使用FastAPI集成的标准日志系统（如structlog）

## 📊 **环境管理**

### **环境切换**
```bash
# 前端环境
APP_ENV=development pnpm mobile:dev    # 开发环境
APP_ENV=staging pnpm mobile:dev        # 测试环境  
APP_ENV=production pnpm mobile:dev     # 生产环境

# 后端环境 (通过 .env 文件管理)
# development.env, staging.env, production.env
```

## 🏗️ **Cursor IDE 最佳实践**

### **多项目开发**
1. **打开工作区**: 在 Cursor 中打开整个 `xinqiao` 文件夹
2. **自动规则切换**: 
   - 在 `apps/mobile-app/` 下工作时，AI 自动使用 React Native 规则
   - 在 `apps/agent-api/` 下工作时，AI 自动使用 Python/FastAPI 规则
3. **跨项目重构**: AI 可以同时理解前后端代码，支持跨项目的接口修改

### **AI 助手使用技巧**
```bash
# 示例提示词：
"在前端添加新的用户界面，同时更新后端 API 和共享类型定义"
"通过MemoryService从Zep中检索最近的5条记忆"
"为语音功能添加错误处理"
```

## 🚀 **部署准备**

### **前端构建**
```
```