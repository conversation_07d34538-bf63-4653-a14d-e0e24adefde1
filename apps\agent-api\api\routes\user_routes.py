# api/routes/user_routes.py
"""
用户管理路由 - 对应故事1.2-B的AC2
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from api.services.user_service import user_service, UserProfileUpdateData
from api.dependencies.auth import get_current_user
from api.settings import logger

# 导入设置相关的模型和服务
from api.models.schema_models import UserSettings, UserSettingsUpdate, UserSettingsResponse
from api.services.settings_service import settings_service

router = APIRouter(
    prefix="/user",
    tags=["User Management"],
)

# Pydantic模型
class UserProfileResponse(BaseModel):
    id: str  # 测试期望的字段名
    user_id: str
    nickname: Optional[str] = None
    age_range: Optional[str] = None
    core_needs: List[str] = []
    interests: List[str] = []
    communication_style_preference: Optional[str] = None
    preferences: Dict[str, Any] = {}
    onboarding_completed: bool = False
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class UserProfileUpdateRequest(BaseModel):
    nickname: Optional[str] = None
    age_range: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None
    core_needs: Optional[List[str]] = None
    interests: Optional[List[str]] = None
    communication_style_preference: Optional[str] = None
    onboarding_completed: Optional[bool] = None

class UserProfilePatchRequest(BaseModel):
    nickname: Optional[str] = None

@router.get(
    "/profile",
    response_model=UserProfileResponse,
    summary="Get User Profile",
    description="Retrieve user profile information",
)
async def get_user_profile(current_user: Dict[str, Any] = Depends(get_current_user)):
    """获取用户画像 - AC2: 用户管理API"""
    logger.info("Getting user profile")

    try:
        user_id = current_user.get("sub") or current_user.get("id")  # 兼容测试和实际JWT
        profile = await user_service.get_user_profile(user_id)

        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # profile现在是字典格式
        return UserProfileResponse(
            id=profile["id"],  # 已经映射好的id字段
            user_id=profile["user_id"],
            nickname=profile.get("nickname"),
            age_range=profile.get("age_range"),
            core_needs=profile.get("core_needs") or [],
            interests=profile.get("interests") or [],
            communication_style_preference=profile.get("communication_style_preference"),
            preferences=profile.get("preferences") or {},
            onboarding_completed=profile.get("onboarding_completed", False),
            created_at=profile.get("created_at"),
            updated_at=profile.get("updated_at")
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )

@router.put(
    "/profile",
    response_model=UserProfileResponse,
    summary="Update User Profile",
    description="Update user profile information",
)
async def update_user_profile(
    profile_data: UserProfileUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """更新用户画像 - AC2: 用户管理API"""
    logger.info("Updating user profile")

    try:
        user_id = current_user.get("sub") or current_user.get("id")  # 兼容测试和实际JWT

        # 转换为内部数据类型
        update_data = UserProfileUpdateData(
            nickname=profile_data.nickname,
            age_range=profile_data.age_range,
            core_needs=profile_data.core_needs,
            interests=profile_data.interests,
            communication_style_preference=profile_data.communication_style_preference,
            preferences=profile_data.preferences,
            onboarding_completed=profile_data.onboarding_completed
        )

        profile = await user_service.update_user_profile(user_id, update_data)

        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # profile现在是字典格式
        return UserProfileResponse(
            id=profile["id"],  # 已经映射好的id字段
            user_id=profile["user_id"],
            nickname=profile.get("nickname"),
            age_range=profile.get("age_range"),
            core_needs=profile.get("core_needs") or [],
            interests=profile.get("interests") or [],
            communication_style_preference=profile.get("communication_style_preference"),
            preferences=profile.get("preferences") or {},
            onboarding_completed=profile.get("onboarding_completed", False),
            created_at=profile.get("created_at"),
            updated_at=profile.get("updated_at")
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )

@router.patch(
    "/profile",
    response_model=UserProfileResponse,
    summary="Partially Update User Profile",
    description="Partially update user profile information",
)
async def patch_user_profile(
    profile_data: UserProfilePatchRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """部分更新用户画像 - AC2: 用户管理API"""
    logger.info("Partially updating user profile")

    try:
        user_id = current_user.get("sub") or current_user.get("id")  # 兼容测试和实际JWT

        # 转换为内部数据类型，只更新提供的字段
        update_data = UserProfileUpdateData(
            nickname=profile_data.nickname
        )

        profile = await user_service.update_user_profile(user_id, update_data)

        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # profile现在是字典格式
        return UserProfileResponse(
            id=profile["id"],  # 已经映射好的id字段
            user_id=profile["user_id"],
            nickname=profile.get("nickname"),
            age_range=profile.get("age_range"),
            core_needs=profile.get("core_needs") or [],
            interests=profile.get("interests") or [],
            communication_style_preference=profile.get("communication_style_preference"),
            preferences=profile.get("preferences") or {},
            onboarding_completed=profile.get("onboarding_completed", False),
            created_at=profile.get("created_at"),
            updated_at=profile.get("updated_at")
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error patching user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to patch user profile"
        )

@router.post(
    "/characters/{character_id}/bind",
    response_model=Dict[str, Any],
    summary="Bind User Character",
    description="Bind user with AI character",
)
async def bind_user_character(
    character_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """绑定用户与AI角色 - AC3: AI角色管理API"""
    logger.info(f"Binding character: {character_id}")

    try:
        from api.services.character_service import character_service

        user_id = current_user.get("sub") or current_user.get("id")  # 兼容测试和实际JWT
        success = await character_service.bind_user_character(user_id, character_id)

        if success:
            return {"success": True, "message": "Character bound successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to bind character"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error binding character: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bind character"
        )


# 用户设置相关路由 - 故事1.8-B

@router.get(
    "/settings",
    response_model=Dict[str, Any],
    summary="Get User Settings",
    description="Retrieve current user's application settings with elderly-friendly defaults",
)
async def get_user_settings(current_user: Dict[str, Any] = Depends(get_current_user)):
    """获取用户应用设置 - 故事1.8-B AC2: 设置读写API"""
    logger.info("Getting user settings")

    try:
        user_id = current_user.get("sub") or current_user.get("id")  # 兼容测试和实际JWT
        settings = await settings_service.get_settings(user_id, create_if_not_exists=True)

        if not settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User settings not found"
            )

        return settings

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting user settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user settings"
        )


@router.put(
    "/settings",
    response_model=Dict[str, Any],
    summary="Update User Settings",
    description="Update user's application settings with PATCH semantics (partial updates)",
)
async def update_user_settings(
    settings_data: UserSettingsUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """更新用户应用设置 - 故事1.8-B AC2: 设置读写API"""
    logger.info(f"Updating user settings with data: {settings_data.model_dump(exclude_none=True)}")

    try:
        user_id = current_user.get("sub") or current_user.get("id")  # 兼容测试和实际JWT

        # 使用SettingsService的UPSERT机制更新设置
        updated_settings = await settings_service.update_settings(user_id, settings_data)

        if not updated_settings:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user settings"
            )

        return updated_settings

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating user settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user settings"
        )
