# 故事 1.1-Frontend: 项目基础设置（前端部分）

## 基本信息
- **故事编号**: 1.1-Frontend
- **故事标题**: 项目基础设置（前端部分）
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: 前端开发者
- **优先级**: 最高（P0 - 所有前端故事的前置依赖）
- **工作量估计**: 4-6 个工作日
- **依赖关系**: 故事 1.1-B（项目基础设置-后端部分）
- **Status**: Approved

## 故事描述

作为前端开发者，我需要基于Obytes脚手架和心桥架构规范建立一个完整的React Native + Expo项目基础架构，包括适老化设计系统、双模交互组件基础、状态管理、路由导航和性能优化配置，**以便** 为后续所有前端开发工作（认证引导、实时对话、提醒功能等）提供标准化的技术基础和开发环境。

## 验收标准

### AC1: 项目架构和技术栈配置
- [ ] 基于Obytes脚手架成功创建React Native + Expo项目，版本符合技术栈要求
- [ ] TypeScript严格模式配置完成，支持路径别名(@/components, @/lib等)
- [ ] NativeWind v4集成完成，包含适老化设计的色彩和字体系统配置
- [ ] Zustand状态管理和React Query数据管理配置完成
- [ ] 所有核心依赖版本符合mobile-app-tech-stack.md规范

### AC2: UI组件系统和设计规范
- [ ] 基础UI组件库建立（按原子设计模式：atoms/molecules/organisms）
- [ ] 适老化设计系统实现：大字体(>=18pt)、高对比度色彩、大触摸区域(>=44x44pt)
- [ ] 双模交互核心组件ChatInputController基础架构实现
- [ ] 无障碍设计支持配置（accessibilityLabel、动态字体、屏幕阅读器）
- [ ] 动画系统配置（react-native-reanimated）

### AC3: 路由导航和项目结构
- [ ] Expo Router文件系统路由配置，包含认证状态保护机制
- [ ] 项目目录结构符合frontend-architecture.md规范
- [ ] 根布局(_layout.tsx)和全局Provider配置实现
- [ ] 深度链接和导航参数类型安全配置
- [ ] API集成层基础结构(services/RtcChatService.ts)

### AC4: 开发环境和代码质量
- [ ] ESLint、Prettier、TypeScript严格检查配置完成
- [ ] Husky Git hooks和代码质量控制流程配置
- [ ] Jest测试环境和React Native Testing Library集成
- [ ] EAS Build云构建环境配置，支持多环境部署
- [ ] 环境变量管理和安全存储配置

### AC5: 性能优化和用户体验基础
- [ ] @shopify/flash-list高性能列表组件集成
- [ ] 内存管理和状态选择性订阅配置
- [ ] 页面加载性能优化配置（Hermes引擎、代码分割）
- [ ] 错误边界和异常处理机制实现
- [ ] 适老化交互体验验证（响应速度<100ms、动画>45fps）

## Dev Notes

CRITICAL: This is a **frontend story**. Load the following standards for implementation:
- `@docs/architecture/mobile-app-tech-stack.md`
- `@docs/architecture/mobile-app-source-tree.md`
- `@docs/architecture/mobile-app-coding-standards.md`

**Technical Guidance from Architecture:**

### Relevant API Endpoint(s) to Consume:
从 `api-design.md` 中摘录本故事需要调用的API端点和数据结构：
- **健康检查接口**: `GET /api/v1/health` - 验证后端服务可用性
- **基础API结构**: 所有API使用`/api/v1/`前缀，支持版本化设计
- **认证相关**: Supabase Auth集成，JWT令牌管理
- **标准响应格式**: 使用`ApiResponse<T>`统一响应结构
- **错误处理**: 遵循RESTful HTTP状态码标准

### UI Components to Build/Use:
从 `frontend-architecture.md` 中摘录相关的组件设计和状态管理方案：

**原子设计组件层次**:
```typescript
// Atoms (src/components/ui/)
- Button.tsx: 支持primary/secondary/danger变体，适老化大尺寸
- Text.tsx: 超大字号(>=18pt)，高对比度颜色
- Input.tsx: 清晰聚焦状态，超大字号
- Icon.tsx: 清晰可识别的图标系统

// Molecules (src/components/ui/)
- ChatBubble.tsx: 区分用户/AI样式，使用React.memo优化
- TouchableButton.tsx: >=44x44pt触摸区域，即时反馈

// Organisms (src/components/features/)
- ChatInputController.tsx: 双模交互核心，语音/文本切换
- ChatHistory.tsx: 使用@shopify/flash-list高性能渲染
```

**状态管理架构**:
```typescript
// Zustand Store设计 (stores/chatStore.ts)
interface ChatState {
  messages: Message[];
  isAiResponding: boolean;
  addMessage: (message: Message) => void;
  updateLastMessage: (contentDelta: string) => void;
  setAiResponding: (isResponding: boolean) => void;
}
```

**性能优化要求**:
- 所有对话气泡必须使用React.memo防止不必要重渲染
- 长列表强制使用@shopify/flash-list确保滚动流畅性
- 状态选择性订阅避免全局重渲染

### User Flow to Implement:
从 `ux-design.md` 中引用并描述需要实现的核心用户交互流程：

**设计哲学核心**:
- "无形"交互：让用户感觉不到交互存在，只剩温暖交流
- 零学习成本：借鉴微信语音、打电话等熟悉心智模型
- 语音优先：默认语音模式，文本作为辅助

**双模交互流程实现**:
```mermaid
graph TD
    A[默认: 语音模式] -->|长按"说话"按钮| B(开始录音...)
    B -->|松开手指| C{发送语音流}
    A -->|点击"键盘"图标| D[平滑动画切换至文本模式]
    D --> E[文本输入界面]
    E -->|点击"发送"| F[自动恢复语音模式]
    E -->|点击"语音"图标| A
```

**核心交互要求**:
- 界面切换动画：200-300ms，使用withTiming和Easing.inOut
- 按钮状态：默认(呼吸光效) → 按下(水波纹) → 思考中动效
- 无障碍支持：明确的accessibilityLabel和accessibilityHint
- 容错设计：允许犯错，提供多种操作路径

**适老化设计技术要求**:
- 字体：最小16pt，核心内容18pt+，支持动态字体
- 色彩：对比度>4.5:1，柔和暖色调(#FFFBF5背景，#5D4037文字)
- 触摸：所有可点击元素>=44x44pt
- 性能：交互响应<100ms，动画>45fps，页面加载<3s

## Tasks / Subtasks

### 第一阶段：项目搭建和核心配置 (1-2天)
- [ ] **Obytes脚手架项目创建和基础配置**
  - 使用Obytes CLI创建新项目：`npx create-obytes-app@latest`
  - 配置package.json和项目元数据
  - 验证React Native 0.76.6和Expo SDK ~52.0.26版本
  - 配置TypeScript严格模式和路径别名

- [ ] **核心技术栈集成**
  - 安装和配置NativeWind v4.1.21和Tailwind CSS 3.4.4
  - 集成Zustand 4.5.5状态管理
  - 配置React Query @tanstack/react-query 5.52.1
  - 集成react-native-reanimated ~3.16.1动画库

- [ ] **开发工具配置**
  - 配置ESLint 8.57.0、Prettier 3.3.3、TypeScript检查
  - 设置Husky 9.1.5 Git hooks和lint-staged
  - 配置Jest 29.7.0和React Native Testing Library 12.7.2

### 第二阶段：UI组件系统和设计规范 (1-2天)
- [ ] **适老化设计系统配置**
  - 在tailwind.config.js中定义适老化色彩系统
  - 配置大字体支持(xl-plus: 1.375rem, 2xl-plus: 1.75rem)
  - 设置高对比度主题：primary-bg(#FFFBF5)、primary-text(#5D4037)
  - 配置暖色调UI：ai-bubble(#ECEFF1)、user-bubble(#FFE4B5)、accent(#FF7043)

- [ ] **基础UI组件库实现**
  - 创建src/components/ui/目录结构
  - 实现Button组件：支持primary/secondary/danger变体，>=44x44pt触摸区域
  - 实现Text组件：超大字号、高对比度、动态字体支持
  - 实现Input组件：清晰聚焦状态、超大字号、无障碍标签
  - 所有组件使用TypeScript严格类型定义

- [ ] **组件性能优化实现**
  - 创建ChatBubble组件，使用React.memo优化重渲染
  - 实现样式常量提取和性能优化模式
  - 配置组件懒加载和代码分割策略

### 第三阶段：路由导航和项目架构 (1天)
- [ ] **Expo Router配置**
  - 建立src/app/目录文件系统路由结构
  - 实现_layout.tsx根布局组件和全局Provider
  - 配置认证状态路由保护机制
  - 设置深度链接和导航参数类型安全

- [ ] **项目目录结构建立**
  - 按照frontend-architecture.md创建完整目录结构
  - 建立src/components/features/目录（organisms层）
  - 创建src/services/目录和API集成层基础
  - 建立src/stores/目录和状态管理结构

### 第四阶段：双模交互基础和API集成 (1-2天)
- [ ] **ChatInputController核心组件基础**
  - 实现双模切换状态管理：voice/text模式
  - 创建基础UI结构：语音按钮、键盘图标、文本输入框切换
  - 实现模式切换动画基础（200-300ms过渡）
  - 配置无障碍支持：accessibilityLabel和accessibilityHint

- [ ] **状态管理实现**
  - 创建stores/chatStore.ts：消息状态、AI响应状态
  - 实现状态选择性订阅机制
  - 配置React Query客户端和API基础配置
  - 创建services/RtcChatService.ts API集成层基础

- [ ] **高性能列表配置**
  - 集成@shopify/flash-list 1.7.1
  - 实现ChatHistory组件基础结构
  - 配置虚拟化列表性能优化参数
  - 实现消息渲染优化策略

### 第五阶段：构建部署和性能优化 (1天)
- [ ] **EAS Build云构建配置**
  - 配置eas.json多环境构建配置
  - 设置development/staging/production环境变量
  - 配置构建优化和代码压缩
  - 验证Hermes引擎和性能优化配置

- [ ] **错误处理和监控**
  - 集成react-error-boundary错误边界
  - 配置全局异常处理机制
  - 实现开发环境调试工具集成
  - 配置性能监控和日志记录

- [ ] **无障碍和适老化验证**
  - 验证所有组件无障碍标签完整性
  - 测试动态字体和高对比度模式
  - 验证触摸区域尺寸>=44x44pt
  - 性能测试：交互响应<100ms，动画>45fps

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.1-B（项目基础设置-后端部分）已完成
- [ ] 开发环境已配置（Node.js LTS、PNPM 9.12.3）
- [ ] 设计规范和架构文档已确认
- [ ] Expo/EAS账号和云服务访问权限已准备

### 退出条件 (Exit Criteria) 
- [ ] 所有验收标准已通过验证
- [ ] 项目可以成功启动并运行基础界面
- [ ] 代码质量检查工具全部配置并运行通过
- [ ] EAS Build云构建流程至少执行一次成功
- [ ] 适老化设计和无障碍功能验证通过
- [ ] 性能指标达到要求（响应<100ms，动画>45fps）

## 风险与缓解措施

### 主要风险
1. **Obytes脚手架版本兼容性**: 新版本可能与指定技术栈版本不兼容
2. **NativeWind v4配置复杂性**: 新版本配置可能比较复杂
3. **适老化设计测试挑战**: 需要实际老年用户测试验证

### 缓解措施
1. **使用固定版本的脚手架，预先验证兼容性**
2. **详细参考NativeWind官方文档和最佳实践**
3. **建立设计审查流程，使用无障碍测试工具验证**

## 相关文档引用
- [前端架构设计](../../architecture/06-frontend-architecture.md)
- [移动应用技术栈](../../architecture/mobile-app-tech-stack.md)
- [移动应用源文件结构](../../architecture/mobile-app-source-tree.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md)
- [API设计文档](../../architecture/03-api-design.md)
- [用户体验设计](../../prd/ux-design.md)
- [技术需求规格](../../prd/requirements.md)
- [共享Schema契约](../../../shared/contracts/schema.py) 