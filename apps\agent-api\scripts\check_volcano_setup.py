#!/usr/bin/env python3
"""
火山引擎RTC智能体配置检查脚本
用于检查和诊断火山引擎相关配置问题
"""

import os
import sys
import asyncio
import json
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from api.services.volcano_client_service import get_volcano_client_service
from api.utils.volcano_diagnostics import VolcanoDiagnostics


def print_header(title: str):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'-'*40}")
    print(f" {title}")
    print(f"{'-'*40}")


def check_environment_variables():
    """检查环境变量配置"""
    print_section("环境变量检查")

    required_vars = [
        'VOLCANO_RTC_APP_ID',
        'VOLCANO_ACCESS_KEY_ID',
        'VOLCANO_SECRET_ACCESS_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # 只显示前几位和后几位，中间用*代替
            if len(value) > 8:
                masked_value = value[:4] + '*' * (len(value) - 8) + value[-4:]
            else:
                masked_value = '*' * len(value)
            print(f"✅ {var}: {masked_value}")
        else:
            print(f"❌ {var}: 未设置")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n⚠️ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print(f"\n✅ 所有必需的环境变量都已设置")
        return True


def check_service_initialization():
    """检查服务初始化"""
    print_section("服务初始化检查")

    try:
        service = get_volcano_client_service()
        print("✅ VolcanoClientService 初始化成功")

        # 检查配置
        print(f"✅ AppId: {service.app_id[:4]}***{service.app_id[-4:] if len(service.app_id) > 8 else '***'}")
        print(f"✅ AccessKey: {service.access_key[:4]}***{service.access_key[-4:] if len(service.access_key) > 8 else '***'}")
        print(f"✅ SecretKey: {'*' * 8}")

        return True
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False


async def test_api_connection():
    """测试API连接"""
    print_section("API连接测试")

    try:
        result = await VolcanoDiagnostics.test_volcano_connection()

        if result["success"]:
            print("✅ 火山引擎API连接测试成功")
            return True
        else:
            print(f"❌ 火山引擎API连接测试失败: {result['message']}")

            # 分析错误类型
            error_msg = result['message'].lower()
            if 'invalid userid in agentconfig' in error_msg:
                print("\n🔍 错误分析:")
                print("   这个错误通常不是UserId格式问题，而是以下原因之一:")
                print("   1. 跨服务授权未开通 (最常见)")
                print("   2. ASR/TTS/LLM服务未开通或额度用完")
                print("   3. AppId或访问密钥配置错误")
                print("   4. 账号权限不足")
                print("\n📋 建议操作:")
                print("   1. 访问 https://console.volcengine.com/rtc/aigc/iam 检查跨服务授权")
                print("   2. 访问 https://console.volcengine.com/speech 检查语音服务")
                print("   3. 访问 https://console.volcengine.com/ark 检查大模型服务")
            elif 'signaturemismatch' in error_msg:
                print("\n🔍 错误分析: 签名验证失败")
                print("📋 建议操作: 检查AccessKey和SecretKey是否正确")
            elif 'timeout' in error_msg:
                print("\n🔍 错误分析: 网络连接超时")
                print("📋 建议操作: 检查网络连接和防火墙设置")

            return False
    except Exception as e:
        print(f"❌ API连接测试异常: {e}")
        return False


def validate_sample_config():
    """验证示例配置"""
    print_section("配置验证测试")

    sample_config = {
        'ASRConfig': {
            'Provider': 'volcano',
            'ProviderParams': {
                'Mode': 'bigmodel',
                'AppId': '**********',
                'AccessToken': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox',
                'ApiResourceId': 'volc.bigasr.sauc.duration',
                'StreamMode': 0
            }
        },
        'TTSConfig': {
            'Provider': 'volcano_bidirection',
            'ProviderParams': {
                'app': {
                    'appid': '**********',
                    'token': '8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox'
                },
                'audio': {
                    'voice_type': 'zh_male_qingshuangnanda_mars_bigtts',
                    'speech_rate': 0,
                    'pitch_rate': 0
                },
                'ResourceId': 'volc.service_type.10029'
            }
        },
        'LLMConfig': {
            'Mode': 'ArkV3',
            'EndPointId': 'ep-20250704092428-tl9sc',
            'MaxTokens': 1024,
            'Temperature': 0.7,
            'SystemMessages': ['你是一个AI助手。']
        },
        'AgentConfig': {
            'UserId': 'ai-assistant-001',
            'TargetUserId': ['user-001']
        }
    }

    try:
        validation_result = VolcanoDiagnostics.validate_rtc_config(sample_config)

        if validation_result["valid"]:
            print("✅ 配置格式验证通过")
        else:
            print("❌ 配置格式验证失败")
            for error in validation_result["errors"]:
                print(f"   - {error}")

        if validation_result["warnings"]:
            print("⚠️ 配置警告:")
            for warning in validation_result["warnings"]:
                print(f"   - {warning}")

        return validation_result["valid"]
    except Exception as e:
        print(f"❌ 配置验证异常: {e}")
        return False


def print_next_steps(all_passed: bool):
    """打印后续步骤建议"""
    print_section("后续步骤建议")

    if all_passed:
        print("🎉 所有检查都通过了！你的火山引擎RTC智能体配置看起来是正确的。")
        print("\n📋 如果仍然遇到问题，请:")
        print("   1. 检查跨服务授权是否已开通并生效")
        print("   2. 确认所有服务的免费额度是否充足")
        print("   3. 查看详细的故障排除指南")
    else:
        print("⚠️ 发现了一些配置问题，请根据上述检查结果进行修复。")
        print("\n📋 常见解决方案:")
        print("   1. 设置正确的环境变量")
        print("   2. 开通火山引擎跨服务授权")
        print("   3. 确认已开通所需的AI服务")
        print("   4. 检查网络连接")

    print("\n📖 更多帮助:")
    print("   - 故障排除指南: docs/volcano_troubleshooting_guide.md")
    print("   - 火山引擎文档: https://www.volcengine.com/docs/6348/")
    print("   - 跨服务授权: https://console.volcengine.com/rtc/aigc/iam")


async def main():
    """主函数"""
    print_header("火山引擎RTC智能体配置检查")

    print("🔍 正在检查你的火山引擎RTC智能体配置...")
    print("   这个脚本将帮助你诊断常见的配置问题")

    # 执行各项检查
    checks = []

    # 1. 环境变量检查
    checks.append(check_environment_variables())

    # 2. 服务初始化检查
    checks.append(check_service_initialization())

    # 3. API连接测试
    checks.append(await test_api_connection())

    # 4. 配置验证测试
    checks.append(validate_sample_config())

    # 总结结果
    passed_count = sum(checks)
    total_count = len(checks)
    all_passed = passed_count == total_count

    print_header("检查结果总结")
    print(f"✅ 通过: {passed_count}/{total_count}")
    if not all_passed:
        print(f"❌ 失败: {total_count - passed_count}/{total_count}")

    print_next_steps(all_passed)

    return all_passed


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 检查过程中发生异常: {e}")
        sys.exit(1)
