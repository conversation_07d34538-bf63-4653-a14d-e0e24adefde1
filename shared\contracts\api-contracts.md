# API契约文档

**版本：** 1.2  
**日期：** 2025年1月  
**说明：** 本文档是前后端协作的统一API规范，所有其他文档中的API定义必须与此保持一致。

---

## 认证相关API

### 无感身份认证
```http
POST /api/v1/auth/anonymous-login
Content-Type: application/json

Request:
{
  "device_info": {
    "device_id": "string",
    "platform": "ios|android", 
    "app_version": "string",
    "device_model": "string?"
  }
}

Response (200):
{
  "user": {
    "id": "uuid",
    "created_at": "datetime"
  },
  "access_token": "string",
  "refresh_token": "string",
  "expires_in": 3600
}
```

### Token刷新
```http
POST /api/v1/auth/refresh-token
Content-Type: application/json

Request:
{
  "refresh_token": "string"
}

Response (200):
{
  "access_token": "string", 
  "refresh_token": "string",
  "expires_in": 3600
}
```

### 完成引导流程
```http
POST /api/v1/auth/finalize_onboarding
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "userId": "uuid",
  "nickname": "string",
  "core_needs": ["string"],
  "character": {
    "name": "string",
    "role": "string", 
    "voice_id": "string"
  }
}

Response (200):
{
  "success": true,
  "message": "引导完成",
  "user_profile": {
    "id": "uuid",
    "nickname": "string",
    "onboarding_completed": true
  }
}
```

---

## 实时对话API

### 火山RTC事件处理接口
```http
POST /api/v1/chat/rtc_event_handler
Content-Type: application/json
Authorization: Bearer {volcano_shared_secret}

Request:
{
  "event_type": "asr_result",
  "payload": {
    "text": "用户语音识别后的文本",
    "confidence": 0.95,
    "is_final": true
  },
  "custom": "{\"sessionId\":\"uuid\",\"userId\":\"uuid\",\"characterId\":\"uuid\"}"
}

Response (200) - 火山引擎标准格式:
{
  "decision": "speak",
  "parameters": {
    "text": "这是AI生成的，用于TTS合成的最终文本。"
  }
}

Response (200) - 静默处理:
{
  "decision": "silence",
  "parameters": {}
}

Response (200) - 转移到文本模式:
{
  "decision": "transfer_to_text",
  "parameters": {
    "reason": "复杂查询需要文本界面处理"
  }
}

Error Response (400/500):
{
  "decision": "error",
  "parameters": {
    "error_code": "PROCESSING_ERROR",
    "error_message": "处理用户请求时发生错误"
  }
}
```

### RTC会话准备
```http
POST /api/v1/rtc/prepare_session
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "user_id": "uuid",
  "session_id": "uuid",
  "character_id": "uuid"
}

Response (200):
{
  "token": "string",
  "room_id": "string", 
  "user_id": "string",
  "task_id": "string"
}
```

### RTC会话结束
```http
POST /api/v1/rtc/end_session
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "user_id": "uuid",
  "session_id": "uuid",
  "task_id": "string"
}

Response (200):
{
  "success": true,
  "message": "会话已结束，记忆生成任务已启动"
}
```

### 向RTC会话发送命令
```http
POST /api/v1/rtc/sessions/{session_id}/command
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "command": "interrupt | function | ExternalTextToSpeech | ExternalTextToLLM",
  "message": "string? (当command为function/ExternalTextToSpeech/ExternalTextToLLM时)",
  "interrupt_mode": "integer? (当command为ExternalTextToSpeech/ExternalTextToLLM时)"
}

Response (200):
{
  "success": true
}
```

---

## 用户管理API

### 获取用户画像
```http
GET /api/v1/user/profile
Authorization: Bearer {access_token}

Response (200):
{
  "id": "uuid",
  "nickname": "string",
  "age_range": "string",
  "core_needs": ["string"],
  "preferences": {},
  "onboarding_completed": boolean
}
```

### 更新用户画像
```http
PUT /api/v1/user/profile
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "nickname": "string?",
  "core_needs": ["string"]?,
  "preferences": {}?
}

Response (200):
{
  "id": "uuid",
  "nickname": "string",
  // ... 更新后的完整画像
}
```

### 获取用户设置
```http
GET /api/v1/user/settings
Authorization: Bearer {access_token}

Response (200):
{
  "theme": "light|dark|auto",
  "fontSize": "small|medium|large|extra-large",
  "highContrast": "boolean",
  "language": "zh-CN|en-US",
  "notifications": {
    "enabled": "boolean",
    "quietHours": {
      "enabled": "boolean",
      "start": "string (HH:mm)",
      "end": "string (HH:mm)"
    }
  }
}
```

---

## 角色管理API

### 获取角色列表
```http
GET /api/v1/characters
Authorization: Bearer {access_token}

Response (200):
{
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "role": "string",
      "description": "string",
      "voice_options": ["string"]
    }
  ],
  "pagination": {
    "total": 10,
    "page": 1,
    "limit": 20
  }
}
```

---

## 提醒功能API (Reminder API)

### 获取提醒列表
```http
GET /api/v1/reminders
Authorization: Bearer {access_token}

Response (200):
{
  "data": [
    {
      "id": "uuid",
      "content": "string",
      "reminder_time": "datetime",
      "status": "pending|completed|cancelled",
      "pattern_id": "string?"
    }
  ],
  "pagination": {
    "total": 10,
    "page": 1,
    "limit": 20
   }
}

### 创建提醒 (主要由Function Calling在内部调用)
```http
POST /api/v1/reminders
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "content": "string",
  "reminder_time": "datetime"
}

### 更新提醒
```http
PUT /api/v1/reminders/{reminder_id}
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
  "content": "string?",
  "reminder_time": "datetime?"
}

### 删除提醒
```http
DELETE /api/v1/reminders/{reminder_id}
Authorization: Bearer {access_token}

Response (204): No Content

---

## 错误响应规范

所有API在错误情况下返回统一格式：

```http
HTTP 4xx/5xx

{
  "error": {
    "code": "ERROR_CODE",
    "message": "人类可读的错误信息", 
    "details": "详细的错误描述",
    "timestamp": "2025-01-01T00:00:00Z"
  }
}
```

---

## 性能要求

- **API响应时间**: < 200ms (P95)
- **实时对话延迟**: 端到端< 1.5秒 (P95)
- **并发支持**: 1000用户，100并发会话
- **可用性**: 99.5%

---

**维护说明**: 本文档的任何变更都必须同步更新到相关的架构文档和故事文件中。 