# RTC会话管理服务功能说明

## 功能概述

RTC会话管理服务是心桥应用语音对话功能的核心后端服务，负责完整管理RTC语音会话的生命周期。该服务与火山引擎RTC深度集成，为前端提供稳定可靠的语音对话能力。

### 核心能力
- **会话准备**: 为用户创建RTC语音会话，获取连接凭证
- **生命周期管理**: 跟踪会话状态(preparing→active→ended→error)
- **容错机制**: 3次重试+指数退避，确保服务稳定性
- **并发控制**: 每用户最大3个并发会话限制
- **配置管理**: 支持角色个性化的语音和LLM参数

### 技术特性
- **高可用性**: 完整的重试机制和降级策略
- **实时状态**: 会话状态实时跟踪和查询
- **安全性**: 用户权限验证和会话所有权检查
- **扩展性**: 支持动态配置更新

## 核心API端点

### 1. 准备RTC会话
```http
POST /api/v1/rtc/prepare_session
```

**请求体**:
```typescript
{
  userId: string;      // 用户ID
  sessionId: string;   // 会话ID
  characterId: string; // AI角色ID
}
```

**响应体** (201 Created):
```typescript
{
  token: string;    // RTC连接令牌
  roomId: string;   // RTC房间ID
  userId: string;   // 用户ID
  taskId: string;   // 火山引擎任务ID
}
```

### 2. 结束会话
```http
POST /api/v1/rtc/end_session
```

**请求体**:
```typescript
{
  userId: string;    // 用户ID
  sessionId: string; // 会话ID
  taskId: string;    // 火山引擎任务ID
}
```

**响应体**:
```typescript
{
  success: boolean;    // 是否成功结束
  message?: string;    // 响应消息
}
```

### 3. 发送控制命令
```http
POST /api/v1/rtc/sessions/{sessionId}/command
```

**请求体**:
```typescript
{
  command: "interrupt" | "ExternalTextToSpeech"; // 要执行的命令
  message?: string;                            // 命令附带的消息
  interrupt_mode?: number;                     // 中断模式
}
```

**响应体**:
```typescript
{
  success: boolean;    // 是否成功发送命令
}
```

### 4. 查询会话状态
```http
GET /api/v1/rtc/sessions/{sessionId}/status?userId={userId}
```

**响应体**:
```typescript
{
  sessionId: string;           // 会话ID
  status: string;              // 会话状态: preparing|active|ended|error
  startTime?: datetime;        // 开始时间
  endTime?: datetime;          // 结束时间
  taskId?: string;            // 火山引擎任务ID
}
```

### 5. 获取会话配置
```http
GET /api/v1/rtc/sessions/{sessionId}/config?userId={userId}
```

**响应体**:
```typescript
{
  llmConfig: {
    model: string;          // LLM模型名称
    maxTokens: number;      // 最大令牌数
    temperature: number;    // 生成温度
  };
  voiceConfig: {
    voiceType: string;      // 语音类型
    speed: number;          // 语速
    volume: number;         // 音量
  };
  characterConfig: {
    name: string;           // 角色名称
    personality: object;    // 个性设置
  };
}
```

## 数据契约

### 会话状态枚举
```typescript
type SessionStatus = 
  | 'preparing'  // 会话正在准备中
  | 'active'     // 会话已激活，用户可加入
  | 'ended'      // 会话已正常结束
  | 'error';     // 会话出现错误
```

### 错误响应格式
```typescript
{
  detail: string;           // 错误详情
  error_code?: string;      // 错误代码
  status_code: number;      // HTTP状态码
}
```

### 错误代码说明
- `VOLCANO_AUTH_ERROR`: 火山引擎认证失败
- `VOLCANO_SERVICE_UNAVAILABLE`: 火山引擎服务不可用
- `CONCURRENT_LIMIT_EXCEEDED`: 超出并发会话限制
- `SESSION_NOT_FOUND`: 会话不存在
- `INVALID_CHARACTER`: 角色配置无效

## 调用示例与注意事项

### 完整会话流程示例

```typescript
// 1. 准备会话
const prepareSession = async () => {
  try {
    const response = await fetch('/api/v1/rtc/prepare_session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + accessToken
      },
      body: JSON.stringify({
        userId: 'user_12345',
        sessionId: 'session_67890',
        characterId: 'compassionate_listener'
      })
    });

    if (response.status === 201) {
      const data = await response.json();
      console.log('会话准备成功:', data);
      return data;
    }
  } catch (error) {
    console.error('会话准备失败:', error);
  }
};

// 2. 轮询会话状态
const pollSessionStatus = async (sessionId: string, userId: string) => {
  const maxAttempts = 12; // 最多轮询60秒
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const response = await fetch(
        `/api/v1/rtc/sessions/${sessionId}/status?userId=${userId}`,
        {
          headers: { 'Authorization': 'Bearer ' + accessToken }
        }
      );

      const data = await response.json();
      
      if (data.status === 'active') {
        console.log('会话已激活，可以开始对话');
        return data;
      } else if (data.status === 'error') {
        throw new Error('会话准备失败');
      }

      // 等待5秒后再次查询
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
    } catch (error) {
      console.error('状态查询失败:', error);
      break;
    }
  }
  
  throw new Error('会话准备超时');
};

// 3. 结束会话
const endSession = async (sessionId: string, userId: string, taskId: string) => {
  try {
    const response = await fetch('/api/v1/rtc/end_session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + accessToken
      },
      body: JSON.stringify({
        userId,
        sessionId,
        taskId
      })
    });

    const data = await response.json();
    console.log('会话结束:', data);
  } catch (error) {
    console.error('结束会话失败:', error);
  }
};
```

### 关键注意事项

#### 1. 认证管理
```typescript
// 自动刷新token机制
const apiCall = async (url: string, options: RequestInit) => {
  let response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': 'Bearer ' + getAccessToken()
    }
  });

  if (response.status === 401) {
    // Token过期，刷新后重试
    await refreshToken();
    response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': 'Bearer ' + getAccessToken()
      }
    });
  }

  return response;
};
```

#### 2. 错误处理策略
```typescript
const handleRTCError = (error: any, statusCode: number) => {
  switch (statusCode) {
    case 502:
      // 火山引擎认证或配置问题
      showError('服务配置异常，请联系技术支持');
      break;
    case 503:
      // 服务暂时不可用，建议重试
      showError('服务暂时不可用，请稍后重试');
      setTimeout(() => retrySession(), 3000);
      break;
    case 429:
      // 并发会话限制
      showError('同时进行的对话过多，请结束其他会话后再试');
      break;
    case 404:
      // 用户或角色不存在
      showError('用户信息异常，请重新登录');
      break;
    default:
      showError('操作失败，请稍后重试');
  }
};
```

#### 3. 资源管理
```typescript
// 页面卸载时清理会话
useEffect(() => {
  const handleBeforeUnload = async () => {
    if (currentSession?.taskId) {
      // 发送结束会话请求
      navigator.sendBeacon('/api/v1/rtc/end_session', JSON.stringify({
        userId: currentUser.id,
        sessionId: currentSession.sessionId,
        taskId: currentSession.taskId
      }));
    }
  };

  window.addEventListener('beforeunload', handleBeforeUnload);
  
  return () => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
    // 组件卸载时也要清理会话
    if (currentSession?.taskId) {
      endSession(currentSession.sessionId, currentUser.id, currentSession.taskId);
    }
  };
}, [currentSession]);
```

#### 4. 性能优化建议
- **状态轮询**: 建议5秒间隔，避免过于频繁的请求
- **超时处理**: 设置30-60秒的会话准备超时时间
- **并发控制**: 监控用户的活跃会话数，提前提示用户
- **缓存策略**: 会话配置信息可以适当缓存，减少API调用

#### 5. 用户体验指南
- **加载状态**: 会话准备期间显示明确的加载指示
- **错误反馈**: 提供友好的错误提示和重试选项
- **状态同步**: 实时显示会话状态变化
- **优雅降级**: 网络异常时提供备用方案

### 测试建议

#### 推荐测试数据
```typescript
const testData = {
  validUser: {
    userId: 'user_12345',
    sessionId: 'session_67890',
    characterId: 'compassionate_listener'
  },
  invalidUser: {
    userId: 'invalid_user',
    sessionId: 'session_invalid',
    characterId: 'unknown_character'
  }
};
```

#### 关键测试场景
1. **正常流程**: 会话准备→状态轮询→结束会话
2. **并发限制**: 同时创建4个会话验证限流机制
3. **网络异常**: 断网情况下的错误处理
4. **认证过期**: Token过期后的自动刷新
5. **页面刷新**: 会话状态的恢复和清理

---

## 技术支持

如有疑问或需要技术支持，请联系：
- **后端技术负责人**: @dev 
- **QA测试负责人**: @qa
- **产品经理**: @pm

更多详细的API规范请参考：`@shared/contracts/api-contracts.md` 