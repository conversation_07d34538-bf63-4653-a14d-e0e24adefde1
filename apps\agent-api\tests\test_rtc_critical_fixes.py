"""
测试故事1.12-B: RTC Integration Critical Fixes的所有修复项
"""

import pytest
import json
import time
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import Request
import hmac
import hashlib

from api.main import app
from api.utils.volcengine_auth import VolcengineSignatureValidator
from api.services.auth_service import AuthService
from api.services.volcano_client_service import VolcanoClientService
from api.services.chat_orchestration_service import ChatOrchestrationService
from api.services.crisis_detection_service import CrisisDetectionService
from api.settings import settings


class TestAC1WebhookSecurity:
    """AC1: Webhook Security Corrected - 火山引擎签名验证修复"""

    def test_valid_signature_verification(self):
        """测试有效签名验证"""
        # 这个测试将会失败，因为当前的签名验证算法是错误的
        validator = VolcengineSignatureValidator(webhook_secret="test_secret")

        # 构造符合火山引擎官方规范的签名
        event_data = {
            "EventType": "ASRSentenceEnd",
            "EventData": json.dumps({"text": "hello world"}),
            "EventTime": "1234567890",
            "EventId": "test-event-123",
            "Version": "1.0",
            "AppId": "test-app-123",
            "Nonce": "test-nonce-456"
        }

        # 按照官方算法计算签名
        data_to_sort = [
            event_data["EventType"],
            event_data["EventData"],
            event_data["EventTime"],
            event_data["EventId"],
            event_data["Version"],
            event_data["AppId"],
            event_data["Nonce"],
            "test_secret"
        ]
        data_to_sort.sort()
        payload_data = "".join(data_to_sort)
        expected_signature = hashlib.sha256(payload_data.encode('utf-8')).hexdigest()

        # 创建mock request
        mock_request = Mock()
        mock_request.headers = {"Signature": expected_signature}

        # 这个测试应该会失败，因为当前的verify_signature实现是错误的
        result = validator.verify_signature(mock_request, json.dumps(event_data).encode())
        assert result == True, "Valid signature should be accepted"

    def test_invalid_signature_rejection(self):
        """测试无效签名拒绝"""
        validator = VolcengineSignatureValidator(webhook_secret="test_secret")

        event_data = {
            "EventType": "ASRSentenceEnd",
            "EventData": json.dumps({"text": "hello world"}),
            "EventTime": "1234567890",
            "EventId": "test-event-123",
            "Version": "1.0",
            "AppId": "test-app-123",
            "Nonce": "test-nonce-456"
        }

        # 使用错误的签名
        mock_request = Mock()
        mock_request.headers = {"Signature": "invalid_signature"}

        # 这应该抛出HTTPException
        with pytest.raises(Exception):
            validator.verify_signature(mock_request, json.dumps(event_data).encode())

    def test_missing_signature_fields(self):
        """测试缺少签名字段的处理"""
        validator = VolcengineSignatureValidator(webhook_secret="test_secret")

        # 缺少EventType字段
        event_data = {
            "EventData": json.dumps({"text": "hello world"}),
            "EventTime": "1234567890",
            "EventId": "test-event-123",
            "Version": "1.0",
            "AppId": "test-app-123",
            "Nonce": "test-nonce-456"
        }

        mock_request = Mock()
        mock_request.headers = {"Signature": "some_signature"}

        with pytest.raises(Exception):
            validator.verify_signature(mock_request, json.dumps(event_data).encode())


class TestAC2ClientTokenGeneration:
    """AC2: Client Token Generation Implemented - 独立token生成"""

    def test_successful_token_generation(self):
        """测试成功的token生成"""
        # 这个测试将会失败，因为generate_rtc_token方法尚未实现
        auth_service = AuthService()

        room_id = "test-room-123"
        user_id = "test-user-456"

        # 这个方法调用应该会失败，因为方法尚未实现
        token = auth_service.generate_rtc_token(room_id, user_id)

        assert token is not None, "Token should be generated"
        assert len(token) > 0, "Token should not be empty"
        assert isinstance(token, str), "Token should be a string"

    def test_token_expiration_handling(self):
        """测试token过期处理"""
        auth_service = AuthService()

        room_id = "test-room-123"
        user_id = "test-user-456"

        # 生成一个1秒过期的token
        token = auth_service.generate_rtc_token(room_id, user_id, expire_seconds=1)

        # 等待token过期
        time.sleep(2)

        # 这里应该有逻辑来验证token已过期
        # 目前只是基础测试，具体的过期验证需要在实现中添加
        assert token is not None

    @patch('api.services.rtc_session_service.RTCSessionService')
    def test_token_independence_from_volcano_api(self, mock_rtc_service):
        """测试token生成独立于火山API"""
        auth_service = AuthService()

        room_id = "test-room-123"
        user_id = "test-user-456"

        # 即使火山API调用失败，token生成也应该成功
        token = auth_service.generate_rtc_token(room_id, user_id)

        assert token is not None, "Token generation should be independent of Volcano API"


class TestAC3StartVoiceChatAPI:
    """AC3: StartVoiceChat API Call Corrected - 回调配置修复"""

    def test_complete_agent_config_fields(self):
        """测试完整的AgentConfig字段"""
        # 这个测试将会失败，因为当前的_build_voice_chat_config方法缺少必要字段
        volcano_client = VolcanoClientService()

        config = volcano_client._build_voice_chat_config(
            room_id="test-room",
            task_id="test-task",
            user_id="test-user",
            webhook_url="https://example.com/webhook",
            custom_data={"test": "data"}
        )

        agent_config = config.get("AgentConfig", {})

        # 这些断言应该会失败，因为当前实现中缺少这些字段
        assert agent_config.get("EnableConversationStateCallback") == True, \
            "EnableConversationStateCallback should be True"
        assert agent_config.get("ServerMessageURLForRTS") is not None, \
            "ServerMessageURLForRTS should be set"
        assert agent_config.get("ServerMessageSignatureForRTS") is not None, \
            "ServerMessageSignatureForRTS should be set"

    def test_callback_url_validation(self):
        """测试回调URL验证"""
        volcano_client = VolcanoClientService()

        config = volcano_client._build_voice_chat_config(
            room_id="test-room",
            task_id="test-task",
            user_id="test-user",
            webhook_url="https://example.com/webhook",
            custom_data={"test": "data"}
        )

        agent_config = config.get("AgentConfig", {})
        callback_url = agent_config.get("ServerMessageURLForRTS")

        # 验证URL格式
        assert callback_url is not None, "Callback URL should be set"
        assert callback_url.startswith(("http://", "https://")), \
            "Callback URL should be valid HTTP/HTTPS URL"


class TestAC4FunctionCallingRobustness:
    """AC4: Function Calling Hardened - 工具调用健壮性"""

    @pytest.mark.asyncio
    async def test_null_tool_calls_handling(self):
        """测试null tool_calls处理"""
        # 创建mock依赖
        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()

        # 创建ChatOrchestrationService实例
        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service
        )

        # 模拟tool_executor_service.get_tool_definitions返回空列表
        mock_tool_executor_service.get_tool_definitions.return_value = []

        # 模拟LLM响应中tool_calls为null
        llm_response = {
            "content": "Hello",
            "tool_calls": None
        }

        # Mock _call_llm_with_tools方法返回我们的测试响应
        with patch.object(chat_service, '_call_llm_with_tools', return_value=llm_response):
            result = await chat_service._handle_function_calling_loop(
                messages=[{"role": "user", "content": "test"}],
                context={},
                request_id="test-request-id"
            )

            assert result == "Hello", "Should handle null tool_calls gracefully"

    @pytest.mark.asyncio
    async def test_non_array_tool_calls_handling(self):
        """测试非数组tool_calls处理"""
        # 创建mock依赖
        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()

        # 创建ChatOrchestrationService实例
        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service
        )

        # 模拟tool_executor_service.get_tool_definitions返回空列表
        mock_tool_executor_service.get_tool_definitions.return_value = []

        # 模拟LLM响应中tool_calls为字符串
        llm_response = {
            "content": "Hello",
            "tool_calls": "invalid_format"
        }

        # Mock _call_llm_with_tools方法返回我们的测试响应
        with patch.object(chat_service, '_call_llm_with_tools', return_value=llm_response):
            result = await chat_service._handle_function_calling_loop(
                messages=[{"role": "user", "content": "test"}],
                context={},
                request_id="test-request-id"
            )

            assert result == "Hello", "Should handle non-array tool_calls gracefully"

    @pytest.mark.asyncio
    async def test_malformed_tool_call_elements(self):
        """测试格式错误的tool_call元素"""
        # 创建mock依赖
        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()

        # 创建ChatOrchestrationService实例
        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service
        )

        # 模拟tool_executor_service.get_tool_definitions返回空列表
        mock_tool_executor_service.get_tool_definitions.return_value = []

        # 模拟包含无效元素的tool_calls
        llm_response_with_invalid_elements = {
            "content": "Hello",
            "tool_calls": [
                {"function": {"name": "valid_tool", "arguments": "{}"}},
                "invalid_element",
                {"invalid": "structure"}
            ]
        }

        # 模拟第二次调用LLM返回最终响应
        llm_response_final = {
            "content": "Final response",
            "tool_calls": None
        }

        # Mock execute_tool_calls返回工具执行结果
        from api.models.schema_models import ToolResult
        mock_tool_executor_service.execute_tool_calls.return_value = [
            ToolResult(
                tool_call_id="call_1",
                name="valid_tool",
                success=True,
                content="Tool executed successfully"
            )
        ]

        # Mock _call_llm_with_tools方法按顺序返回不同的响应
        with patch.object(chat_service, '_call_llm_with_tools', side_effect=[llm_response_with_invalid_elements, llm_response_final]):
            result = await chat_service._handle_function_calling_loop(
                messages=[{"role": "user", "content": "test"}],
                context={},
                request_id="test-request-id"
            )

            assert result == "Final response", "Should filter out invalid tool_call elements"


class TestAC5CrisisInterventionHardened:
    """AC5: Crisis Intervention Hardened - 危机干预音频禁用"""

    @pytest.mark.asyncio
    async def test_crisis_detection_with_successful_audio_ban(self):
        """测试危机检测与成功音频禁用"""
        # 创建mock依赖
        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()
        mock_crisis_detection_service = Mock()
        mock_volcano_client = Mock()

        # 创建ChatOrchestrationService实例
        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service,
            crisis_detection_service=mock_crisis_detection_service,
            volcano_client=mock_volcano_client
        )

        # 模拟危机消息
        crisis_message = "我想自杀"

        # 模拟危机检测返回True
        mock_crisis_detection_service.detect.return_value = True

        # 模拟ban_user_stream成功
        mock_volcano_client.ban_user_stream = AsyncMock(return_value=True)

        # 调用handle_message
        result = await chat_service.handle_message(
            user_message=crisis_message,
            context={"userId": "test-user", "sessionId": "test-session", "roomId": "test-room"}
        )

        # 验证返回了危机响应
        assert "400-161-9995" in result, "Should return crisis response with hotline"

        # 等待异步任务完成
        await asyncio.sleep(0.1)

        # 验证ban_user_stream被调用
        mock_volcano_client.ban_user_stream.assert_called_once_with(
            room_id="test-room",
            user_id="test-user",
            ban_audio=True,
            ban_video=False,
            duration_seconds=600
        )

    @pytest.mark.asyncio
    async def test_crisis_response_with_audio_ban_failure(self):
        """测试音频禁用失败时的危机响应"""
        # 创建mock依赖
        mock_memory_service = Mock()
        mock_llm_proxy_service = Mock()
        mock_tool_executor_service = Mock()
        mock_prompt_builder_service = Mock()
        mock_crisis_detection_service = Mock()
        mock_volcano_client = Mock()

        # 创建ChatOrchestrationService实例
        chat_service = ChatOrchestrationService(
            memory_service=mock_memory_service,
            llm_proxy_service=mock_llm_proxy_service,
            tool_executor_service=mock_tool_executor_service,
            prompt_builder_service=mock_prompt_builder_service,
            crisis_detection_service=mock_crisis_detection_service,
            volcano_client=mock_volcano_client
        )

        crisis_message = "我想死"

        # 模拟危机检测返回True
        mock_crisis_detection_service.detect.return_value = True

        # 模拟ban_user_stream失败
        mock_volcano_client.ban_user_stream = AsyncMock(side_effect=Exception("API failed"))

        # 即使音频禁用失败，危机响应也应该正常返回
        result = await chat_service.handle_message(
            user_message=crisis_message,
            context={"userId": "test-user", "sessionId": "test-session", "roomId": "test-room"}
        )

        # 验证仍然返回了危机响应
        assert "400-161-9995" in result, "Should return crisis response even if audio ban fails"

    @pytest.mark.asyncio
    async def test_audio_ban_degradation_mechanism(self):
        """测试音频禁用降级机制"""
        volcano_client = VolcanoClientService()

        # 现在ban_user_stream方法已经实现，应该能够正常调用
        # 由于这是一个真实的API调用，可能会失败，但方法本身应该存在
        try:
            result = await volcano_client.ban_user_stream(
                room_id="test-room",
                user_id="test-user",
                ban_audio=True,
                ban_video=False,
                duration_seconds=600
            )
            # 如果API调用成功，结果应该是布尔值
            assert isinstance(result, bool), "ban_user_stream should return a boolean"
        except Exception as e:
            # 如果API调用失败（例如因为配置或网络问题），这是预期的
            # 关键是方法存在并且能够处理错误
            assert "封禁用户" in str(e) or "API" in str(e) or "Connection" in str(e) or "HTTP" in str(e), \
                f"Expected API-related error, got: {e}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
