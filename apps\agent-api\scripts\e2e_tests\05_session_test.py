#!/usr/bin/env python3
"""
会话管理API测试
测试接口:
- POST /api/v1/chat/sessions
- GET /api/v1/chat/sessions
- GET /api/v1/chat/sessions/{session_id}/messages
- PUT /api/v1/chat/sessions/{session_id}/end
"""

import asyncio
import sys
import uuid
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class SessionTester(BaseAPITester):
    """会话管理API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("05_session", base_url)
        self.session_id = None
        self.character_id = None

    async def test_create_session(self):
        """测试创建会话API"""
        self.logger.info("💬 测试创建会话API")

        # 优先使用共享配置中的角色ID
        if not self.character_id:
            # 先尝试从绑定的角色获取
            character_binding = self.get_shared_config("test_results", {}).get("character_binding", {})
            if character_binding.get("character_bound"):
                self.character_id = character_binding.get("character_id")
                self.logger.info(f"✅ 使用已绑定的角色ID: {self.character_id}")
            else:
                # 否则从引导流程获取
                onboarding_data = self.get_shared_config("test_results", {}).get("onboarding", {})
                self.character_id = onboarding_data.get("character_id") or await self.get_character_id()

        session_data = {
            "characterId": self.character_id,
            "topic": "测试会话"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/chat/sessions",
            data=session_data,
            expected_status=201
        )

        # 验证响应并保存会话ID
        if "id" in response:
            self.session_id = response["id"]
            self.logger.info(f"✅ 创建会话成功，会话ID: {self.session_id}")

            # 验证会话数据结构
            expected_fields = ["id", "topic"]
            for field in expected_fields:
                if field in response:
                    self.logger.info(f"✅ 会话数据包含字段: {field}")
                else:
                    self.logger.error(f"❌ 会话数据缺少字段: {field}")

            # 验证角色ID（存储在metadata中）
            metadata = response.get("metadata", {})
            response_character_id = metadata.get("character_id")
            if response_character_id == self.character_id:
                self.logger.info("✅ 角色ID匹配")
            else:
                self.logger.error(f"❌ 角色ID不匹配：期望{self.character_id}，实际{response_character_id}")

            # 保存会话创建结果
            self.save_test_result("session_creation", {
                "session_created": True,
                "session_id": self.session_id,
                "character_id": self.character_id,
                "topic": session_data["topic"]
            })

        else:
            self.logger.error("❌ 会话创建失败，未获取到会话ID")

    async def test_get_sessions_list(self):
        """测试获取会话列表API"""
        self.logger.info("📋 测试获取会话列表API")

        response = await self.make_request(
            "GET",
            "/api/v1/chat/sessions",
            params={"page": 1, "limit": 10}
        )

        # 验证响应结构
        if "sessions" in response:
            self.logger.info("✅ 响应包含会话列表")

            sessions = response["sessions"]
            if len(sessions) > 0:
                self.logger.info(f"✅ 获取到 {len(sessions)} 个会话")

                # 验证会话数据结构
                first_session = sessions[0]
                expected_fields = ["id", "user_id", "topic", "status", "created_at"]
                for field in expected_fields:
                    if field in first_session:
                        self.logger.info(f"✅ 会话数据包含字段: {field}")
                    else:
                        self.logger.error(f"❌ 会话数据缺少字段: {field}")

                # 检查是否包含刚创建的会话
                if self.session_id:
                    session_ids = [session["id"] for session in sessions]
                    if self.session_id in session_ids:
                        self.logger.info("✅ 列表包含刚创建的会话")
                    else:
                        self.logger.warning("⚠️ 列表不包含刚创建的会话")
            else:
                self.logger.warning("⚠️ 会话列表为空")
        else:
            self.logger.error("❌ 响应缺少会话列表")

        # 验证分页信息
        if "pagination" in response:
            self.logger.info("✅ 响应包含分页信息")

            pagination = response["pagination"]
            pagination_fields = ["page", "limit", "total", "pages"]
            for field in pagination_fields:
                if field in pagination:
                    self.logger.info(f"✅ 分页信息包含字段: {field}")
                else:
                    self.logger.error(f"❌ 分页信息缺少字段: {field}")
        else:
            self.logger.error("❌ 响应缺少分页信息")

    async def test_get_session_messages(self):
        """测试获取会话消息API"""
        if not self.session_id:
            self.logger.warning("⚠️ 跳过会话消息测试，因为没有会话ID")
            return

        self.logger.info(f"💬 测试获取会话消息API: {self.session_id}")

        response = await self.make_request(
            "GET",
            f"/api/v1/chat/sessions/{self.session_id}/messages"
        )

        # 验证响应结构
        if "messages" in response:
            self.logger.info("✅ 响应包含消息列表")

            messages = response["messages"]
            self.logger.info(f"✅ 获取到 {len(messages)} 条消息")

            # 如果有消息，验证消息数据结构
            if len(messages) > 0:
                first_message = messages[0]
                expected_fields = ["id", "content", "role"]
                for field in expected_fields:
                    if field in first_message:
                        self.logger.info(f"✅ 消息数据包含字段: {field}")
                    else:
                        self.logger.error(f"❌ 消息数据缺少字段: {field}")
        else:
            self.logger.error("❌ 响应缺少消息列表")

        # 验证分页信息
        if "pagination" in response:
            self.logger.info("✅ 响应包含分页信息")
        else:
            self.logger.warning("⚠️ 响应缺少分页信息")

    async def test_end_session(self):
        """测试结束会话API"""
        if not self.session_id:
            self.logger.warning("⚠️ 跳过结束会话测试，因为没有会话ID")
            return

        self.logger.info(f"🔚 测试结束会话API: {self.session_id}")

        response = await self.make_request(
            "PUT",
            f"/api/v1/chat/sessions/{self.session_id}/end"
        )

        # 验证响应
        if response and "error" not in response:
            self.logger.info("✅ 会话结束成功")

            # 验证响应包含必要信息
            if "message" in response or "status" in response:
                self.logger.info("✅ 响应包含状态信息")
            else:
                self.logger.warning("⚠️ 响应缺少状态信息")
        else:
            self.logger.error("❌ 结束会话失败")

    async def test_session_not_found(self):
        """测试访问不存在会话的API"""
        self.logger.info("🔍 测试访问不存在会话API")

        # 使用格式正确但不存在的UUID
        fake_session_id = "00000000-0000-4000-8000-000000000001"

        # 测试获取不存在会话的消息
        response = await self.make_request(
            "GET",
            f"/api/v1/chat/sessions/{fake_session_id}/messages",
            expected_status=403  # 权限验证在找不到会话之前生效
        )

        if response:
            self.logger.info("✅ 不存在会话返回403状态码（权限检查优先）")
        else:
            self.logger.error("❌ 不存在会话未返回预期状态码")

    async def test_create_session_invalid_character(self):
        """测试使用无效角色ID创建会话"""
        self.logger.info("❌ 测试使用无效角色ID创建会话")

        # 使用格式正确但不存在的UUID
        session_data = {
            "characterId": "00000000-0000-4000-8000-000000000000",
            "topic": "测试会话"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/chat/sessions",
            data=session_data,
            expected_status=500  # 数据库外键约束错误
        )

        # 验证错误响应
        if response and ("error" in response or "detail" in response):
            self.logger.info("✅ 无效角色ID正确返回错误")
        else:
            self.logger.warning("⚠️ 无效角色ID处理可能有问题")

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始会话管理API测试")

        # 确保用户已认证
        await self.ensure_authenticated()

        tests = [
            ("创建会话", self.test_create_session),
            ("获取会话列表", self.test_get_sessions_list),
            ("获取会话消息", self.test_get_session_messages),
            ("结束会话", self.test_end_session),
            ("访问不存在会话", self.test_session_not_found),
            ("无效角色ID创建会话", self.test_create_session_invalid_character),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(0.5)  # 测试间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='会话管理API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with SessionTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
