### **"心桥"AI亲情伴侣 产品需求文档 - 用户故事**
**版本：** 1.2 
**日期：** 2025年1月
**作者：** John，产品经理

> 本文档是完整PRD的第五部分，详细描述MVP阶段的史诗和用户故事。
> 相关文档：[项目概述](./overview.md) | [需求规格](./requirements.md) | [用户体验设计](./ux-design.md) | [技术架构](./technical.md)

#### **5. 史诗和故事 (Epics and Stories)**

我们将MVP的所有工作归纳在一个核心史诗下，并分解为一系列严格遵循"垂直切片"开发顺序的用户故事。

##### **开发实施说明**

**故事分解逻辑：**
- **主故事（1.1-1.7）：** 定义完整功能的业务价值和端到端验收标准
- **子故事分解：** 为支持并行开发，主故事分解为：
  - **-B 后端故事：** 后端服务、API接口、数据库实现
  - **-Frontend 前端故事：** 前端逻辑、状态管理、API集成
  - **-UI 界面故事：** UI组件、交互设计、视觉实现

**开发协作模式：**
- 产品经理关注主故事的业务价值实现
- 后端工程师专注 -B 子故事的技术实现
- 前端工程师专注 -Frontend 和 -UI 子故事
- 所有子故事完成后，主故事的验收标准必须全部满足

##### **史诗 1: MVP - 建立情感连接与核心信任**

**目标：** 交付一个最小可行产品，它能通过一系列精心设计的功能与交互，与第一批种子用户建立起初步的情感连接和牢固的用户信任。

**价值主张：** 通过极致简单的语音交互和具备长期记忆的AI伴侣，为老年用户提供温暖、可靠的情感陪伴体验。

---

##### **故事 1.0: 核心技术栈可行性验证（技术预研）**

**用户角色：** 技术团队/架构师
**故事描述：** 我需要在正式开发前验证所有核心技术栈的可行性，包括火山引擎RTC标准事件回调、原生LLM编排架构、Supabase数据库以及关键API接口标准，**以便** 确保架构选型正确，降低开发风险，为后续MVP开发提供技术保障。

**验收标准：**
1. **火山引擎RTC集成验证**
   - 成功申请火山引擎RTC服务开发者账号并获取API密钥
   - 验证标准RTC事件Webhook能够成功接收ASR结果
   - 成功建立测试用的RTC连接并验证音频传输质量

2. **原生LLM编排架构验证**
   - 实现一个迷你的`ChatOrchestrationService`原型。
   - 验证该服务能成功调用外部记忆服务（Zep/Mem0）。
   - 验证该服务能调用LLM并处理工具调用（Function Calling）的请求/响应循环。

3. **性能基准测试**
   - 端到端对话延迟测试：P95 < 1.5秒
   - 记忆检索性能测试：单次查询 < 200ms
   - 并发处理能力测试：支持至少10个并发会话
   - 验证错误处理和降级机制

4. **技术栈整合验证**
   - 端到端技术栈整合：前端 → API → Agent → 记忆系统 → 数据库
   - 模拟完整对话流程验证
   - 安全机制验证：API认证、请求验证、数据加密

**优先级：** 最高（P0 - 所有开发工作的技术基础）
**工作量估计：** 3-5 个工作日
**依赖关系：** 无（前置验证故事）

---

##### **故事 1.1: 项目基础设置**

**用户角色：** 开发者
**故事描述：** 我希望能建立一个包含版本控制、CI/CD、代码规范的基础项目，并完成所有云服务(Supabase, 火山引擎)的账号设置和数据库初始化，**以便** 为后续所有开发工作奠定稳固的基础。

**验收标准：**
1. **代码仓库配置**
   - 代码仓库已在GitHub上创建，Monorepo结构已建立
   - 项目结构符合架构文档定义的目录结构
   - README文档包含项目介绍和快速开始指南

2. **CI/CD流程配置**
   - 基础的CI流程（lint-check, type-check, test）已在GitHub Actions中配置
   - 支持多环境部署（开发、测试、生产）
   - 代码质量检查和安全扫描已集成

3. **云服务账号配置**
   - 火山引擎前端SDK和后端API的开发/测试账号已申请完毕
   - API密钥和配置信息已安全存储
   - 测试环境已验证服务可用性

4. **数据库初始化**
   - Supabase项目已创建，数据库Schema（不包含`user_memories`表）已根据架构文档初始化
   - 行级别安全(RLS)策略已配置
   - 数据库迁移脚本和种子数据已准备

**优先级：** 最高（P0）
**工作量估计：** 5-8 个工作日
**依赖关系：** 无

---

##### **故事 1.2: 无感身份认证与角色创建流程**

**用户角色：** 老年用户
**故事描述：** 我希望能无缝地进入应用，并通过一次流畅的对话完成我的专属AI伙伴的创建，**以便** 我能快速开始体验产品的核心价值。

**验收标准：**
1. **后端认证服务**
   - 提供了可用的匿名认证和AI人设创建/查询端点
   - JWT Token生成和验证机制正常工作
   - 用户数据的CRUD操作符合安全要求

2. **前端认证集成**
   - 客户端能成功调用Supabase Auth获取JWT令牌
   - 应用启动时自动完成身份认证
   - 无需用户输入任何登录信息

3. **角色共创UI/UX**
   - "温暖的初见"引导流程UI/UX与设计稿一致
   - 支持AI角色的命名、身份设定和声音选择
   - 引导流程简洁清晰，适合老年用户操作

4. **端到端流程验证**
   - 用户可以完整地跑通从首次打开App到成功创建AI人设并将其持久化到后端数据库的全流程
   - 角色信息在应用重启后能正确加载
   - 错误处理和异常情况的用户体验良好

**优先级：** 最高（P0）
**工作量估计：** 8-12 个工作日
**依赖关系：** 故事 1.1

---

##### **故事 1.3: 核心对话编排与RTC事件处理**

**用户角色：** 开发者
**故事描述：** 我需要实现一个能够接收火山云端实时事件的核心API，并通过内部的`ChatOrchestrationService`自动检索记忆、编排LLM和工具调用，**以便** 为实时语音对话提供核心智能。

**验收标准：**
1. **核心API实现**
   - 后端已实现一个`POST /api/v1/chat/rtc_event_handler`接口，并能通过安全验证
   - 接口符合火山引擎的标准事件回调规范
   - 请求验证和错误处理机制完善

2. **记忆检索功能**
   - 当此接口收到用户输入事件时，后端的`ChatOrchestrationService`能通过`IMemoryService`从外部记忆提供商成功检索到相关的记忆
   - 记忆检索的相关性和准确性达到预期

3. **对话编排与响应机制**
   - 后端`ChatOrchestrationService`能成功调用LLM，并处理工具调用。
   - 最终生成的文本回复，能通过Webhook的响应体正确返回给火山引擎。

**优先级：** 最高（P0）
**工作量估计：** 10-15 个工作日
**依赖关系：** 故事 1.1

---

##### **故事 1.4: 实时语音会话流程集成**

**用户角色：** 老年用户
**故事描述：** 我希望能像打电话一样和AI实时聊天，并感觉到它能记住我们聊过的话题，**以便** 获得最自然、最个性化的沟通体验。

**验收标准：**
1. **会话准备阶段**
   - 前端按下"说话"按钮时，成功调用我方后端的`/prepare_session`接口，并获取到RTC连接凭证
   - 会话配置信息正确传递给火山引擎
   - 异常情况下的降级处理机制有效

2. **RTC连接建立**
   - 客户端使用凭证，通过火山RTC SDK，成功连接到火山媒体服务器
   - 音频质量清晰，无明显噪音或断流
   - 连接稳定性符合用户使用要求

3. **实时交互性能**
   - 语音交互流畅，端到端延迟符合性能要求（P95 < 1.5秒）
   - ASR识别准确率达到可用水平
   - TTS生成的语音自然度良好

4. **记忆功能验证**
   - 在对话中提及之前存储过的事实记忆时，AI的回复能够明显体现出它记起了这件事
   - 新的对话内容能够影响后续的AI回复
   - 记忆的准确性和相关性符合预期

**优先级：** 最高（P0）
**工作量估计：** 12-18 个工作日
**依赖关系：** 故事 1.2, 1.3

---

##### **故事 1.5: 会话后分析与记忆同步**

**用户角色：** 开发者
**故事描述：** 我需要在用户结束会话后，触发一个异步任务来分析完整的对话内容，生成摘要，并将其作为元数据同步回外部记忆服务中，**以便** 丰富会话上下文，提升未来的检索效果。

**验收标准：**
1. **会话数据获取**
   - 后端能在会话结束后，获取到本次会话的完整文本记录。
   - 对话记录的完整性和准确性得到保证。

2. **异步摘要与同步**
   - 后端能异步调用LLM服务，根据对话记录生成摘要。
   - 生成的摘要能通过`MemoryService`成功更新到对应的会话元数据中。
   - 整个过程不阻塞会话结束API的响应。

3. **效果验证**
   - 能在外部记忆服务（Zep/Mem0）中查询到已同步的摘要。
   - 验证包含摘要的会话在后续检索中表现更佳。

**优先级：** 高（P1）
**工作量估计：** 5-8 个工作日
**依赖关系：** 故事 1.4

---

##### **故事 1.6: 对话式提醒功能**

**用户角色：** 老年用户
**故事描述：** 我希望能直接在聊天时告诉AI帮我记事，**以便** 我不用去学习复杂的日历或闹钟App。

**验收标准：**
1. **意图识别和内部执行**
   - 后端服务能通过LLM的Function Calling能力识别提醒意图。
   - 工具调用在后端服务内部被`ToolExecutorService`正确执行，并将提醒存入数据库。
   - LLM在收到工具执行结果后，能生成一句自然的、口语化的确认回复（例如“好的，我记下了，明天下午三点提醒您吃药”）。

2. **本地通知设置**
   - 客户端收到该JSON后，能成功触发一个本地推送通知的计划
   - 推送通知的时间准确性符合要求
   - 通知内容包含AI角色的个性化表达

3. **端到端流程验证**
   - 用户能通过对话成功设置一个提醒，并在预定时间准时收到应用的推送通知
   - 提醒确认和取消机制正常工作
   - 异常情况下的处理机制有效

4. **可选功能实现**
   - （可选实现）客户端能请求日历权限，并将提醒写入系统日历
   - 与系统日历的同步机制稳定可靠

**优先级：** 中（P2）
**工作量估计：** 6-10 个工作日
**依赖关系：** 故事 1.4

---

##### **故事 1.7: 基础危机响应协议**

**用户角色：** 开发者
**故事描述：** 我需要为系统构建一个安全底线，在用户表达极端负面情绪时能正确应对，**以便** 保护用户安全和产品信任。

**验收标准：**
1. **安全守卫模块实现**
   - 后端服务中存在一个"安全守卫"模块，所有用户输入都必须先经过该模块处理
   - 关键词库覆盖常见的危机表达
   - 模块的响应速度和准确性符合要求

2. **危机检测机制**
   - 当输入命中危机关键词库时，该模块能成功阻止后续对通用LLM的调用
   - 支持语义级别的情绪分析和风险评估
   - 降低误报率，避免过度敏感

3. **安全响应流程**
   - 此时，模块能从专用的安全知识库中返回一个预设的、安全的安抚与引导脚本给用户
   - 响应内容温和、专业且有帮助
   - 必要时能够提供专业资源的联系方式

**优先级：** 高（P1）
**工作量估计：** 5-8 个工作日
**依赖关系：** 故事 1.3

---

#### **开发计划和里程碑**

**Sprint 1 (4周)：** 基础设施建立
- 故事 1.0: 核心技术栈可行性验证 (1周)
- 故事 1.1: 项目基础设置 (2周，前后端并行)
- 故事 1.7: 基础危机响应协议 (1周)

**Sprint 2 (4周)：** 认证和核心后端
- 故事 1.2: 无感身份认证与角色创建流程 (2.5周，前后端并行)
- 故事 1.3: 实时上下文注入核心后端服务 (2.5周)
- *重叠开发期间：前后端同步集成测试*

**Sprint 3 (4周)：** 实时交互核心
- 故事 1.4: 实时语音会话流程集成 (3周)
- 故事 1.5: 会话后分析与记忆同步 (2周，与1.4并行开始)

**Sprint 4 (2周)：** 提醒功能和集成优化
- 故事 1.6: 对话式提醒功能 (1.5周)
- 端到端测试和性能优化 (0.5周)

**总开发周期：** 14 周（3.5个月，包含并行开发优化）
**测试和优化：** 额外 2 周（已部分并入开发阶段）
**MVP发布目标：** 4 个月内完成

**并行开发策略：**
- 前后端团队在Sprint 2-3期间并行开发
- 每个Sprint结束进行集成测试
- 持续集成和持续部署（CI/CD）确保质量 