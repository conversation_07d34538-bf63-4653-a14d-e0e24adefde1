from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import logging
import time

from api.routes.v1_router import api_v1_router # Assuming v1_router aggregates all versioned routes
from api.settings import settings, get_cors_origins, logger # 导入 get_cors_origins 函数和 logger
from db.supabase_init import check_supabase_connection, AsyncSupabaseClientManager # 导入 AsyncSupabaseClientManager
from api.services.llm_proxy_service import llm_proxy_service # Ensure service is importable for shutdown
from api.middleware.rate_limit import rate_limit_middleware # 导入频率限制中间件

# Initialize logger with fine-grained control
logger = logging.getLogger(settings.PROJECT_NAME)

# Configure logging with specific control for third-party libraries
def configure_logging():
    # Set root logger to WARNING to suppress third-party DEBUG logs
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(name)s - %(levelname)s - %(process)d - %(threadName)s - %(message)s'
    )

    # Set our application logger to the desired level
    app_logger = logging.getLogger(settings.PROJECT_NAME)
    app_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # Set API module logger to the desired level
    api_logger = logging.getLogger("api")
    api_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # Suppress noisy third-party library logs
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("hpack").setLevel(logging.WARNING)
    logging.getLogger("h11").setLevel(logging.WARNING)
    logging.getLogger("h2").setLevel(logging.WARNING)

    return app_logger

logger = configure_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up Agent API...")
    start_time = time.time()
    db_connected = await check_supabase_connection()
    if not db_connected:
        logger.error("Failed to connect to Supabase on startup.")
        # Potentially raise an exception here or handle as critical failure
    else:
        logger.info("Successfully connected to Supabase.")

    # Preload models or other async initializations can go here
    # Example: await some_service.initialize()
    logger.info(f"Agent API startup complete in {time.time() - start_time:.2f} seconds.")

    yield

    # Shutdown
    logger.info("Shutting down Agent API...")
    # Close Supabase client if using AsyncSupabaseClientManager and it holds a client
    supabase_manager = await AsyncSupabaseClientManager.get_instance() # Get instance
    await supabase_manager.aclose() # Call aclose on the instance
    logger.info("Supabase client closed.")

    # Gracefully shutdown other resources if needed
    # Example: await llm_proxy_service.shutdown() # If llm_proxy_service has a shutdown method

    logger.info("Agent API shutdown complete.")

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan # Use the lifespan context manager
)

# 频率限制中间件（需要在CORS之前添加）
app.middleware("http")(rate_limit_middleware)

# CORS Middleware (if needed, from your settings)
if settings.CORS_ORIGINS:
    cors_origins = get_cors_origins()  # 使用辅助函数获取列表
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,  # 直接使用返回的列表
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    logger.info(f"CORS middleware configured with origins: {cors_origins}")

app.include_router(api_v1_router, prefix=settings.API_V1_STR)

# Global Exception Handler for unexpected errors
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception handler caught: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred on the server."},
    )

@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.PROJECT_NAME}! Visit /docs for API documentation."}
