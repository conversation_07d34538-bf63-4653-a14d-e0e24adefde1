from typing import List, Optional, Dict
import json
from api.services.llm_proxy_service import llm_proxy_service # For LLM-based generation
from api.settings import settings, logger

class SuggestedQuestionsService:
    @staticmethod
    async def generate(ai_reply_text: str, context_messages: Optional[List[Dict[str,str]]] = None) -> List[str]:
        if not ai_reply_text:
            return []

        # logger.debug(f"Generating suggested questions for AI reply: {ai_reply_text[:100]}...")

        # For simplicity and speed, start with rule-based or simpler LLM prompt
        # More complex context-aware generation can be added later
        prompt = f"""
        基于以下AI助手的回复，请生成3个相关的、自然的后续问题，以帮助用户继续对话或探索相关话题。
        AI回复: "{ai_reply_text}"
        请只返回一个JSON格式的字符串列表，例如：["问题1?", "问题2?", "问题3?"]。不要包含任何其他解释或前缀。
        确保问题简洁，并且与AI的回复内容紧密相关。
        """

        try:
            # 修复：使用正确的 generate_text 方法替代不存在的 generate_response_stream
            full_raw_response = await llm_proxy_service.generate_text(
                prompt=prompt,
                max_tokens=200,
                temperature=0.7  # Higher temp for more varied questions
            )

            if "LLM_ERROR:" in full_raw_response:
                logger.error(f"LLM error during suggested questions generation: {full_raw_response}")
                return ["能详细说说吗？", "这让你感觉怎么样？"]

            # Attempt to parse JSON from the full_raw_response
            # logger.debug(f"Raw response for suggested questions: {full_raw_response}")
            json_start = full_raw_response.find('[')
            json_end = full_raw_response.rfind(']')

            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = full_raw_response[json_start : json_end+1]
                try:
                    questions = json.loads(json_str)
                    if isinstance(questions, list) and all(isinstance(q, str) for q in questions):
                        logger.info(f"Generated suggested questions: {questions[:3]}")
                        return questions[:3] # Return max 3 questions
                except json.JSONDecodeError as e:
                    logger.warn(f"Failed to parse JSON for suggested questions: {e}. Raw: {json_str}")

            # Fallback if JSON parsing fails or no valid list
            logger.warn(f"Could not parse suggested questions from LLM response: {full_raw_response}")
            return ["能详细说说吗？", "我对此很感兴趣，还有其他方面吗？"] # Fallback questions

        except Exception as e:
            logger.error(f"Error generating suggested questions: {e}", exc_info=True)
            return ["关于这个，您还有其他想法吗？", "接下来想聊些什么呢？"] # General fallback

suggested_questions_service = SuggestedQuestionsService()
