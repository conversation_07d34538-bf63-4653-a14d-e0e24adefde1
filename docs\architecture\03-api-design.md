# 心桥项目API接口设计

## 3.1 核心RTC事件处理接口 (火山RTC对接)

### 接口详情
- **Endpoint & Method:** `POST /api/v1/chat/rtc_event_handler`
- **职责:** 作为一个标准的Webhook，接收火山引擎RTC服务推送的事件（如ASR识别结果）。接收后，将业务逻辑完全委托给`ChatOrchestrationService`处理，并同步返回一个包含文本的JSON响应体，供火山引擎进行TTS合成。
- **安全验证:** **必须**通过火山引擎官方定义的[回调签名算法](https://www.volcengine.com/docs/6348/69820)进行验证。该算法包括对`EventType`, `EventData`, `EventTime`, `EventId`, `Version`, `AppId`, `Nonce`, `SecretKey`等字段进行字母序排序、拼接和SHA256哈希。简单的共享密钥或IP白名单是不够的。

### 请求体验证 (Pydantic)
需根据火山标准事件回调的规范定义Pydantic模型，以解析请求。
```python
class RtcWebhookRequest(BaseModel):
    event_type: str  # e.g., "asr_result"
    payload: dict    # 具体内容依赖于事件类型
    custom: Optional[str] = None # JSON字符串，包含会话上下文
```

### 响应格式
同步返回一个简单的JSON对象。
```json
{
    "decision": "speak",
    "parameters": {
        "text": "这是AI生成的，用于TTS合成的最终文本。"
    }
}
```

## 3.2 实时语音会话控制API

### 准备会话
- **接口:** `POST /api/v1/rtc/prepare_session`
- **职责:** 接受客户端的会话准备请求，并执行两大核心任务：1. 调用火山引擎的`StartVoiceChat` API在云端启动AI智能体。2. **为客户端生成一个用于加入RTC房间的、符合火山规范的、自签名的Token**。
- **请求体:** `{ "userId": "...", "sessionId": "...", "characterId": "..." }`
- **响应体:** `{ "token": "...", "roomId": "...", "userId": "...", "taskId": "..." }`
    - **`token`**: **[重要变更]** 此Token**不是**从火山引擎API直接获取的，而是由心桥后端使用`AppId`和`AppKey`根据[火山Token鉴权规范](https://www.volcengine.com/docs/6348/70121)生成的，专用于客户端SDK的`joinRoom`鉴权。

### 结束会话
- **接口:** `POST /api/v1/rtc/end_session`
- **职责:** 停止火山会话，并触发后续的异步记忆分析流程。
- **请求体:** `{ "userId": "...", "sessionId": "...", "taskId": "..." }`
- **响应体:** `{ "success": true, "message": "会话已结束，记忆生成任务已启动" }`

### 发送控制命令
- **接口:** `POST /api/v1/rtc/sessions/{session_id}/command`
- **职责:** 在一个活跃的RTC会话中，发送一个控制命令，例如手动打断AI的语音输出。
- **请求体:** `{ "command": "interrupt", "message": "..."? }`
- **响应体:** `{ "success": true }`

## 3.3 文本聊天接口 (SSE)

- **Endpoint & Method:** `POST /api/v1/chat/text_message`
- **职责:** 用于处理**非RTC**的、传统的文本聊天请求。它将复用`ChatOrchestrationService`的核心逻辑（记忆检索、LLM编排、工具调用、**危机检测**等），为前端提供纯文本交互的能力。在检测到危机信号时，会返回脚本化回复。
- **请求体:** `{ "message": "...", "sessionId": "...", "characterId": "..." }`

**响应 (SSE `text/event-stream`):**
- `event: text_chunk, data: {"delta": "..."}`
- `event: stream_end, data: {...}`
- `event: error, data: {...}`

## 3.4 会话管理API

### 创建新会话
- **接口:** `POST /api/v1/chat/sessions`
- **请求体:** `{ "userId": "...", "topic": "...", "characterId": "..." }`
- **响应体:** 完整的`ChatSession`对象

### 获取会话列表
- **接口:** `GET /api/v1/chat/sessions`
- **查询参数:** `userId`, `page`, `limit`
- **响应体:** `{ data: ChatSession[], pagination: {...} }`

### 获取会话消息历史
- **接口:** `GET /api/v1/chat/sessions/{sessionId}/messages`
- **查询参数:** `userId`, `page`, `limit`
- **响应体:** `{ data: ChatMessage[], pagination: {...} }`

### 结束会话并生成摘要
- **接口:** `PUT /api/v1/chat/sessions/{sessionId}/end`
- **请求体:** `{ "userId": "..." }`
- **响应体:** `{ success: true, data: { summary, topic, tags } }`

## 3.5 用户画像与设置API

- `GET /api/v1/user/profile`: 获取当前认证用户的完整用户画像
- `PUT /api/v1/user/profile`: 更新当前认证用户的画像信息（部分更新）
- `GET /api/v1/user/settings`: 获取当前认证用户的应用设置
- `PUT /api/v1/user/settings`: 更新当前认证用户的应用设置（部分更新）

## 3.6 认证与引导API

### 无感身份认证
- **接口:** `POST /api/v1/auth/anonymous-login`
- **职责:** 基于设备信息创建匿名用户并返回认证令牌
- **请求体:** `{ "device_info": { "device_id": "...", "platform": "ios|android", "app_version": "..." } }`
- **响应体:** `{ "user": { "id": "...", "created_at": "..." }, "access_token": "...", "refresh_token": "..." }`

### Token刷新
- **接口:** `POST /api/v1/auth/refresh-token`
- **职责:** 使用refresh token获取新的access token
- **请求体:** `{ "refresh_token": "..." }`
- **响应体:** `{ "access_token": "...", "refresh_token": "...", "expires_in": 3600 }`

## 3.7 提醒功能接口 (Function Calling)

基于内部工具调用（Function Calling）机制，提醒的创建由`ChatOrchestrationService`在对话中处理。后端仅需提供以下API供前端进行数据管理：

- **`GET /api/v1/reminders`**: 获取当前用户的所有提醒。
- **`PUT /api/v1/reminders/{id}`**: 更新指定提醒。
- **`DELETE /api/v1/reminders/{id}`**: 删除指定提醒。