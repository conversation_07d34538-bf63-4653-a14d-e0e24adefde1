Title: 配置对话触发模式--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1544164

Markdown Content:
配置对话触发模式--实时音视频-火山引擎

===============

在用户与智能体互动的过程中，你可能需要灵活配置新一轮对话的触发时机，实现更精细的交互体验。RTC 提供手动触发和自动触发两种触发方式，你可根据场景自由选择使用，提升对话的流畅度和自然度。

应用场景
----

| 场景 | 描述 |
| --- | --- |
| 咨询陪练 | * 自动触发：用户语音输入后自动触发新一轮对话。 * 手动触发：用户按住按钮开始说话，结束松开，手动触发新一轮对话。 |
| 在线教育 | * 自动触发：学生自由询问自动触发新一轮对话。 * 手动触发：学生通过点击按钮控制开始和结束说话，结束说话按钮点击后手动触发新一轮会话。 |

前提条件
----

你已参考[场景搭建](https://www.volcengine.com/docs/6348/1310560) 了解 AI 应用的整体构建流程。

自动触发新一轮对话
---------

若开启自动触发新一轮对话功能，RTC 检测到用户输入完整的一句话后自动触发新一轮会话，反应迅速，实现和用户快速流畅交流。

 你需要调用`StartVoiceChat`接口，将`ASRConfig.TurnDetectionMode` 设置为 `0`，即可开启自动触发新一轮对话功能。

注意：

 VAD 配置（SilenceTime）会影响自动触发新一轮对话的行为。若 `SilenceTime` 配置过短，会导致用户的语音输入被过早地判定为结束，触发新一轮会话。若 `SilenceTime` 配置过长，会导致用户的语音输入被延迟，影响用户体验。因此，你需要根据实际场景进行 VAD 配置，以达到最佳的交互效果。

手动触发新一轮对话
---------

若开启手动触发新一轮对话功能，用户输入完整的一句话后，需要你根据收到的输入结束信令或字幕结果决定是否手动触发新一轮会话。精准控制，实现和用户安全交流。

### 步骤 1：编写触发时机逻辑

*   如果你选择使用输入结束信令进行触发时机判断，你需要根据业务逻辑自行编写。
*   如果你选择使用字幕结果进行触发时机判断，你可参看[实时对话式 AI 字幕](https://www.volcengine.com/docs/6348/1337284)实现。

### 步骤 2：设置手动触发新一轮对话模式

调用`StartVoiceChat`接口，`ASRConfig.TurnDetectionMode` 设置为 `1`。

### 步骤 3：手动触发新一轮对话

收到输入结束信令或字幕结果后，你可通过服务端或客户端手动触发新一轮对话，具体方式取决于业务需求。例如，在 AI 应用开发中，若采用服务端响应请求，建议使用服务端触发新一轮对话，以降低请求延迟。

#### 通过服务端触发新一轮对话

调用 [`UpdateVoiceChat`](https://www.volcengine.com/docs/6348/1404671)接口，设置以下参数手动触发新一轮对话：

| 参数 | 类型 | 描述 |
| --- | --- | --- |
| AppId | String | 你的音视频应用的唯一标志，参看[创建 RTC 应用](https://www.volcengine.com/docs/6348/69865#%E6%AD%A5%E9%AA%A44%EF%BC%9A%E5%88%9B%E5%BB%BA-rtc-%E5%BA%94%E7%94%A8%EF%BC%8C%E8%8E%B7%E5%8F%96-appid)获取或创建 AppId。 |
| RoomId | String | AI 通话房间的 ID，需与房间内客户端 SDK 进房时的使用的 RoomId 保持一致。 |
| TaskId | String | 智能体任务 ID。你需要对每个智能体任务自行定义 TaskId，且在后续进行任务更新和结束时也须使用该 TaskId。 |
| Command | String | 填入`FinishSpeechRecognition`，表示触发新一轮对话。 |

你可参看以下示例通过服务端实现触发新一轮对话操作：

```json
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf", 
    "RoomId": "Room1", 
    "TaskId": "task1", 
    "Command": "FinishSpeechRecognition"
}
```

json

#### 通过客户端触发新一轮对话

使用 [`SendUserBinaryMessage`](https://www.volcengine.com/docs/6348/70080#RTCRoom-senduserbinarymessage) 接口触发新一轮对话。

`SendUserBinaryMessage` 传入参数：

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| userId | String | 智能体名称，需与 `StartVoiceChat` 接口中传入的 `UserId` 一致。 |
| buffer | byte[] | 发送的二进制消息内容。消息不超过 46KB。 |
| config | MessageConfig | 消息发送的可靠/有序类型，参看[MessageConfig](https://www.volcengine.com/docs/6348/70083#MessageConfig)。 |

该接口的 `buffer` 参数需要传入特定格式的内容，下图展示了 `buffer` 参数的格式：

![Image 4: alt](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_d5f49c0ca26d16c6e300267b8fd76ef6.jpg)

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| magic_number | binary | 消息格式标识符，当前场景消息格式固定为 `ctrl`，用于标识该消息为控制消息。 |
| length | binary | 触发新一轮对话消息长度，单位为字节，采用大端序（Big-endian）存储方式，用于说明 `control_message` 字段的字节长度。 |
| control_message | binary | 触发新一轮对话行为配置信息，采用 JSON 格式，具体内容格式参看 [control_message 格式](https://www.volcengine.com/docs/6348/1544164#control_message)。 |

control_message

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| Command | String | 控制命令，此处填入 `FinishSpeechRecognition`，表示触发新一轮对话。 |

你可参看以下示例从客户端实现触发新一轮对话操作：

C++

Java

TypeScript

```C++
// 发送触发新一轮对话指令
void sendFinishRecognitionMessage(const std::string &uid) {
    nlohmann::json json_data;
    json_data["Command"] = "FinishSpeechRecognition";
    sendUserBinaryMessage(uid, json_data.dump());
}
void buildBinaryMessage(const std::string& magic_number, const std::string& message, size_t& binary_message_length, std::shared_ptr<uint8_t[]>& binary_message) { //将字符串包装成 TLV
    auto magic_number_length = magic_number.size();
    auto message_length = message.size();

    binary_message_length = magic_number_length + 4 + message_length;
    binary_message = std::shared_ptr<uint8_t[]>(new uint8_t[binary_message_length]);
    std::memcpy(binary_message.get(), magic_number.data(), magic_number_length);
    binary_message[magic_number_length] = static_cast<uint8_t>((message_length >> 24) & 0xFF);
    binary_message[magic_number_length+1] = static_cast<uint8_t>((message_length >> 16) & 0xFF);
    binary_message[magic_number_length+2] = static_cast<uint8_t>((message_length >> 8) & 0xFF);
    binary_message[magic_number_length+3] = static_cast<uint8_t>(message_length & 0xFF);
    std::memcpy(binary_message.get()+magic_number_length+4, message.data(), message_length);
}

int sendUserBinaryMessage(const std::string &uid, const std::string& message) {
    if (rtcRoom_ != nullptr)
    {
        size_t length = 0;
        std::shared_ptr<uint8_t[]> binary_message = nullptr;
        buildBinaryMessage("ctrl", message, length, binary_message);
        return rtcRoom_->sendUserBinaryMessage(uid.c_str(), static_cast<int>(length), binary_message.get());
    }
    return -1;
```

C++

```Java
// 发送触发新一轮对话指令
public void sendFinishRecognitionMessage(String userId) {
    JSONObject json = new JSONObject();
    try {
        json.put("Command", "FinishSpeechRecognition");
    } catch (JSONException e) {
        throw new RuntimeException(e);
    }
    String jsonString = json.toString();
    byte[] buildBinary = buildBinaryMessage("ctrl", jsonString);
    sendUserBinaryMessage(userId, buildBinary);
}
private byte[] buildBinaryMessage(String magic_number, String content) { //将字符串包装成 TLV
    byte[] prefixBytes = magic_number.getBytes(StandardCharsets.UTF_8);
    byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
    int contentLength = contentBytes.length;

    ByteBuffer buffer = ByteBuffer.allocate(prefixBytes.length + 4 + contentLength);
    buffer.order(ByteOrder.BIG_ENDIAN);
    buffer.put(prefixBytes);
    buffer.putInt(contentLength);
    buffer.put(contentBytes);
    return buffer.array();
}

public void sendUserBinaryMessage(String userId, byte[] buffer) {
    if (rtcRoom_ != null) {
        rtcRoom_.sendUserBinaryMessage(userId, buffer, MessageConfig.RELIABLE_ORDERED);
    }
}
```

Java

```TypeScript
import VERTC from '@volcengine/rtc';

/**
 * @brief 智能体配置
 */
const BotName = 'RobotMan_'; // 智能体名称
const CommandKey = 'ctrl'; // 控制命令
const engine = VERTC.createEngine('Your AppID'); // RTC 应用 AppId

/**
 * @brief 指令类型
 */
enum COMMAND {
  /**
   * @brief 触发新一轮对话指令
   */
  FinishSpeechRecognition = 'FinishSpeechRecognition',
};

/**
 * @brief 将字符串包装成 TLV
 */
function stringToTLV(inputString: string, type = '') {
  const typeBuffer = new Uint8Array(4);

  for (let i = 0; i < type.length; i++) {
    typeBuffer[i] = type.charCodeAt(i);
  }

  const lengthBuffer = new Uint32Array(1);
  const valueBuffer = new TextEncoder().encode(inputString);

  lengthBuffer[0] = valueBuffer.length;

  const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);

  tlvBuffer.set(typeBuffer, 0);

  tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
  tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
  tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
  tlvBuffer[7] = lengthBuffer[0] & 0xff;

  tlvBuffer.set(valueBuffer, 8);

  return tlvBuffer.buffer;
};

/**
 * @brief 发送触发新一轮对话指令
 */
engine.sendUserBinaryMessage(
  BotName,
  stringToTLV(
    JSON.stringify({
      Command: COMMAND.FinishSpeechRecognition,
    }),
    CommandKey,
  )
);
```

TypeScript
