<ReadMe.LLM>

<rules>
  <rule_1>The application lifecycle is critical: Always start with `RTCManager.createRTCEngine()` and end with `RTCManager.destroyRTCEngine()`. All operations must happen between these two calls.</rule_1>
  <rule_2>Operations like joining a room or publishing a stream are asynchronous. You MUST listen for the corresponding callbacks in `RTCRoomEventHandler` (e.g., `onRoomStateChanged`) to confirm success or handle failures.</rule_2>
  <rule_3>For a high-quality voice assistant experience, configure the audio scenario and enable active speaker detection as shown in Context 2.</rule_3>
  <rule_4>To understand user commands, use the speech-to-text functionality (`startSubtitle`) detailed in Context 3. This is the primary input method.</rule_4>
  <rule_5>To send structured data like reminders or appointment details, use `sendSEIMessage` for time-sensitive payloads or `sendUserMessage` for general confirmations, as demonstrated in Context 4.</rule_5>
</rules>

<library_description>
This is the Volcengine React Native RTC SDK, a comprehensive toolkit for building real-time audio communication applications. It is optimized for creating a high-performance AI voice assistant with features like clear audio, real-time speech-to-text, and reliable data messaging for reminders and scheduling.
</library_description>

<!-- =================================================================== -->
<!-- Context 1: Core Engine and Room Lifecycle for AI Assistant         -->
<!-- =================================================================== -->
<context_1>
<context_1_description>
This section covers the fundamental lifecycle for an AI Assistant session: creating the engine, creating and joining a room, publishing local audio, and cleaning up resources. This is the required sequence for any session.
</context_1_description>

<context_1_code_snippet>
    <![CDATA[
// --- RTCManager ---
// Creates the engine instance. This is the first step.
createRTCEngine(options: ICreateRTCEngineOptions): Promise<IEngine>;
// Destroys the engine instance. This is the last step.
destroyRTCEngine(): void;

// --- IEngine ---
// Sets the global event handler.
setRtcVideoEventHandler(engineEventHandler: RTCVideoEventHandler): number;
// Creates a room instance.
createRTCRoom(roomId: string): IRoom;
// Starts internal audio capture. Call this before joining the room.
startAudioCapture(): number;

// --- IRoom ---
// Sets the room-specific event handler.
setRTCRoomEventHandler(rtcRoomEventHandler: RTCRoomEventHandler): number;
// Joins the room.
joinRoom(params: IJoinRoomProps): number;
// Publishes the local audio stream to the room.
publishStream(type: MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO): number;
// Leaves the room.
leaveRoom(): number;
// Destroys the room instance.
destroy(): void;

// --- Relevant Interfaces & Callbacks ---
interface ICreateRTCEngineOptions {
  appID: string;
}

interface IJoinRoomProps {
  token: string;
  userId: string;
  roomConfigs?: {
    profile: ChannelProfile; // Use CHANNEL_PROFILE_COMMUNICATION for voice assistant
    isAutoPublish: boolean;
    isAutoSubscribeAudio: boolean;
    isAutoSubscribeVideo: boolean;
  };
}

interface RTCRoomEventHandler {
    onRoomStateChanged?: (roomId: string, uid: string, state: number, extraInfo: string) => void;
    onLeaveRoom?: (stats: RTCRoomStats) => void;
}
    ]]>
</context_1_code_snippet>

<context_1_examples>
    <![CDATA[
import { RTCManager, RTCVideoEventHandler, RTCRoomEventHandler, MediaStreamType, ChannelProfile } from 'volcengine-react-native-rtc';

/**
 * Initiates and manages a full RTC session for the AI assistant.
 * @param {string} appId - Your application ID.
 * @param {string} token - The access token for the room.
 * @param {string} roomId - The room ID for the session.
 * @param {string} userId - The unique ID for the user.
 * @returns {Promise<{engine: IEngine, room: IRoom}>}
 */
async function startAssistantSession(appId, token, roomId, userId) {
  // Step 1: Create the RTC Engine
  const engine = await RTCManager.createRTCEngine({ appID: appId });
  engine.setRtcVideoEventHandler(new RTCVideoEventHandler()); // Set a base handler

  // Step 2: Create the Room instance
  const room = engine.createRTCRoom(roomId);

  // Step 3: Define room event handlers
  const roomEventHandler = new RTCRoomEventHandler();
  roomEventHandler.onRoomStateChanged = (rId, uId, state, extraInfo) => {
    if (state === 0) { // state 0 means join success
      console.log(`User ${uId} successfully joined room ${rId}. Assistant is ready.`);
      // Step 5: Publish local audio stream after successfully joining
      room.publishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
    } else {
      console.error(`Room state changed to error state: ${state}, info: ${extraInfo}`);
    }
  };
  roomEventHandler.onLeaveRoom = () => {
    console.log('Successfully left the room. Cleaning up...');
    // Step 7: Clean up resources after leaving
    room.destroy();
    RTCManager.destroyRTCEngine();
  };
  room.setRTCRoomEventHandler(roomEventHandler);
  
  // Step 4: Start local audio capture and join the room
  await engine.startAudioCapture();
  const joinResult = room.joinRoom({
    token: token,
    userId: userId,
    roomConfigs: {
      profile: ChannelProfile.CHANNEL_PROFILE_COMMUNICATION,
      isAutoPublish: false, // We will publish manually after joining
      isAutoSubscribeAudio: true, // Automatically listen to the AI assistant
      isAutoSubscribeVideo: false, // No video needed
    }
  });

  if (joinResult !== 0) {
      console.error("joinRoom call failed immediately with code:", joinResult);
  }

  // To end the session, call: room.leaveRoom();
  return { engine, room };
}
    ]]>
</context_1_examples>
</context_1>


<!-- =================================================================== -->
<!-- Context 2: High-Quality Audio & Voice Activity Detection (VAD)     -->
<!-- =================================================================== -->
<context_2>
<context_2_description>
This section details how to configure audio for a high-quality, clear voice assistant experience. It includes setting the audio scenario, profile, and enabling VAD to detect when the user is speaking, which is crucial for UI feedback and conversation flow management.
</context_2_description>

<context_2_code_snippet>
    <![CDATA[
// --- IEngine ---
// Sets the audio scenario. Use 'COMMUNICATION' for clear voice calls.
setAudioScenario(audioScenario: AudioScenarioType.AUDIO_SCENARIO_COMMUNICATION): number;

// Enables periodic reporting of audio properties.
enableAudioPropertiesReport(config: IAudioPropertiesConfig): number;

// --- Callbacks (in RTCVideoEventHandler) ---
// Periodically reports local audio properties, including VAD result.
onLocalAudioPropertiesReport?: (audioPropertiesInfos: Array<LocalAudioPropertiesInfo>) => void;

// --- Relevant Interfaces ---
interface IAudioPropertiesConfig {
    interval: number; // e.g., 200 (ms)
    enableVad: boolean; // Must be true to get VAD results
}

interface AudioPropertiesInfo {
    vad: number; // 1 for speech, 0 for silence
    linearVolume: number;
}
    ]]>
</context_2_code_snippet>

<context_2_examples>
    <![CDATA[
import { AudioScenarioType } from 'volcengine-react-native-rtc';

/**
 * Optimizes audio settings and enables voice activity detection.
 * @param {IEngine} engine - The initialized RTC engine instance.
 * @param {function(boolean): void} onSpeakingStateChange - Callback to notify UI of speaking status.
 */
function setupVoiceDetection(engine, onSpeakingStateChange) {
  // Step 1: Set audio scenario for clear, echo-free communication.
  engine.setAudioScenario(AudioScenarioType.AUDIO_SCENARIO_COMMUNICATION);

  // Step 2: Configure and enable audio properties reporting for VAD.
  engine.enableAudioPropertiesReport({
    interval: 200, // Report every 200ms
    enableVad: true,
  });

  // Step 3: Handle the VAD results in the engine's event handler.
  const handler = new RTCVideoEventHandler();
  handler.onLocalAudioPropertiesReport = (audioPropertiesInfos) => {
    // We are interested in the main microphone stream (index 0)
    const mainMicInfo = audioPropertiesInfos.find(info => info.streamIndex === 0);
    if (mainMicInfo) {
      const isSpeaking = mainMicInfo.audioPropertiesInfo.vad === 1;
      // Use the callback to update application state/UI
      onSpeakingStateChange(isSpeaking);
    }
  };
  engine.setRtcVideoEventHandler(handler);
}

// Usage:
// const { engine } = await startAssistantSession(...);
// setupVoiceDetection(engine, (isSpeaking) => {
//   console.log(isSpeaking ? "User is speaking." : "User is silent.");
//   // e.g., setUiState({ isListening: isSpeaking });
// });
    ]]>
</context_2_examples>
</context_2>


<!-- =================================================================== -->
<!-- Context 3: Speech-to-Text (via Subtitle Service)                   -->
<!-- =================================================================== -->
<context_3>
<context_3_description>
This section explains how to use the built-in speech-to-text functionality. The library exposes this as a "subtitle" service. By starting this service in recognition mode, you can get real-time text transcripts of a user's speech, which is essential for the AI assistant to understand commands.
</context_3_description>

<context_3_code_snippet>
    <![CDATA[
// --- IRoom ---
// Starts the speech-to-text service.
startSubtitle(subtitleConfig: SubtitleConfig): number;
// Stops the speech-to-text service.
stopSubtitle(): number;

// --- Callbacks (in RTCRoomEventHandler) ---
// Reports the state of the subtitle service (started, stopped, error).
onSubtitleStateChanged?: (state: SubtitleState, errorCode: SubtitleErrorCode, errorMessage: string) => void;
// Delivers the transcribed text.
onSubtitleMessageReceived?: (subtitles: Array<SubtitleMessage>) => void;

// --- Relevant Interfaces ---
interface SubtitleConfig {
  mode: SubtitleMode.SUBTITLE_MODE_RECOGINTE; // Must be recognition mode
  targetLanguage: string; // e.g., 'en-US'
}

interface SubtitleMessage {
  userId: string;
  text: string;
  definite: boolean; // True if this is a complete, final sentence.
}
    ]]>
</context_3_code_snippet>

<context_3_examples>
    <![CDATA[
import { SubtitleMode } from 'volcengine-react-native-rtc';

/**
 * Manages the speech-to-text functionality.
 * @param {IRoom} room - The joined IRoom instance.
 * @param {function(string): void} onFinalTranscript - Callback for when a final sentence is transcribed.
 */
function manageTranscription(room, onFinalTranscript) {
    // Attach listeners to the room's event handler
    const handler = room.getRTCRoomEventHandler(); // Assuming a getter exists or was previously set
    handler.onSubtitleStateChanged = (state, errorCode, msg) => {
        if (state === 2) { console.error(`Transcription service error: ${msg}`); }
    };
    handler.onSubtitleMessageReceived = (subtitles) => {
        subtitles.forEach(subtitle => {
            // Process only final, complete sentences to avoid partial commands.
            if (subtitle.definite) {
                console.log(`Final transcript from ${subtitle.userId}: "${subtitle.text}"`);
                onFinalTranscript(subtitle.text);
            }
        });
    };

    // Function to start transcription
    const start = () => {
        room.startSubtitle({
            mode: SubtitleMode.SUBTITLE_MODE_RECOGINTE,
            targetLanguage: 'en-US' // Or other supported language
        });
        console.log("Transcription started.");
    };

    // Function to stop transcription
    const stop = () => {
        room.stopSubtitle();
        console.log("Transcription stopped.");
    };
    
    return { start, stop };
}

// Usage:
// const { room } = await startAssistantSession(...);
// const transcriptionManager = manageTranscription(room, (text) => {
//   console.log("Sending to AI for processing:", text);
//   // sendTextToAI(text);
// });
// transcriptionManager.start();
    ]]>
</context_3_examples>
</context_3>


<!-- =================================================================== -->
<!-- Context 4: Data Communication for Reminders & Appointments         -->
<!-- =================================================================== -->
<context_4>
<context_4_description>
This section shows how to send and receive data. Use `sendSEIMessage` to send small, time-sensitive data payloads (like a reminder trigger) synchronized with the audio stream. Use `sendUserMessage` for reliable text-based communication, such as confirming an appointment.
</context_4_description>

<context_4_code_snippet>
    <![CDATA[
// --- IEngine ---
// Sends a message embedded within the audio stream. Low latency.
sendSEIMessage(streamIndex: StreamIndex.STREAM_INDEX_MAIN, message: ArrayBuffer, repeatCount: number): number;

// --- IRoom ---
// Sends a text message to a specific user in the room.
sendUserMessage(userId: string, messageStr: string, config: MessageConfig.RELIABLE_ORDERED): number;

// --- Callbacks ---
// (in RTCVideoEventHandler) Triggered when an SEI message is received.
onSEIMessageReceived?: (remoteStreamKey: RemoteStreamKey, message: ArrayBuffer) => void;
// (in RTCRoomEventHandler) Triggered when a user message is received.
onUserMessageReceived?: (uid: string, message: string) => void;
    ]]>
</context_4_code_snippet>

<context_4_examples>
    <![CDATA[
/**
 * Sends a structured command or data to a specific user (e.g., the AI backend).
 * @param {IRoom} room - The joined IRoom instance.
 * @param {string} aiUserId - The user ID of the AI assistant.
 * @param {object} data - The JavaScript object to send.
 */
function sendDataToAI(room, aiUserId, data) {
    const payload = JSON.stringify(data);
    // Use reliable, ordered messaging for important commands.
    room.sendUserMessage(aiUserId, payload, 0); // 0 is RELIABLE_ORDERED
    console.log("Sent data to AI:", payload);
}

/**
 * Handles incoming data messages from the AI.
 * @param {IRoom} room - The joined IRoom instance.
 * @param {string} aiUserId - The user ID of the AI assistant.
 * @param {function(object): void} onDataReceived - Callback to process the received data.
 */
function listenForAIData(room, aiUserId, onDataReceived) {
    const handler = room.getRTCRoomEventHandler();
    handler.onUserMessageReceived = (uid, message) => {
        if (uid === aiUserId) {
            try {
                const data = JSON.parse(message);
                onDataReceived(data);
            } catch (e) {
                console.error("Failed to parse message from AI:", message);
            }
        }
    };
}

// Usage:
// const { room } = await startAssistantSession(...);
// const AI_USER_ID = 'ai_assistant_backend';
//
// listenForAIData(room, AI_USER_ID, (data) => {
//   if (data.type === 'appointment_confirmation') {
//     console.log(`Appointment confirmed for ${data.details.time}`);
//     // Update UI
//   }
// });
//
// // To set a reminder
// sendDataToAI(room, AI_USER_ID, {
//   command: 'SET_APPOINTMENT',
//   details: { subject: 'Project sync', time: '2025-05-10T14:00:00Z' }
// });
    ]]>
</context_4_examples>
</context_4>

</ReadMe.LLM>