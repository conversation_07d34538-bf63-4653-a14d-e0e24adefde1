# 故事 1.4: 实时语音会话流程集成

## 基本信息
- **故事编号**: 1.4
- **故事标题**: 实时语音会话流程集成
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: 老年用户
- **优先级**: 最高（P0）
- **工作量估计**: 12-18 个工作日
- **依赖关系**: 故事 1.4-UI (对话界面设计), 故事 1.2 (前端认证流程), 故事 1.3 (后端实时服务), 故事 1.4-B (后端RTC集成)
- **Status**: Approved

## 故事描述

作为老年用户，我希望能像打电话一样和AI实时聊天，并感觉到它能记住我们聊过的话题，**以便** 获得最自然、最个性化的沟通体验。

## 验收标准

### AC1: 双模交互界面实现
- [ ] 实现"语音优先，文本辅助"的双模交互界面
- [ ] 语音模式：大型"按住说话"按钮，支持长按录音
- [ ] 文本模式：完整的文本输入界面，支持键盘交互
- [ ] 模式切换动画流畅自然，符合设计规范（200-300ms过渡）

### AC2: 实时语音交互体验
- [ ] 集成火山引擎RTC SDK，建立稳定的语音连接
- [ ] 实现"按住说话"的录音交互，具有清晰的视觉反馈
- [ ] 支持通过RTC数据通道(`subv`流)实时显示用户和AI的字幕
- [ ] 根据RTC数据通道(`conv`流)显示AI的实时状态（如"聆听中"、"思考中"）
- [ ] AI语音回复播放流畅，无明显延迟或断流

### AC3: 对话历史管理
- [ ] 使用FlashList实现高性能的对话历史渲染
- [ ] 支持用户和AI消息的差异化显示（左右布局、颜色区分）
- [ ] 实现对话的自动滚动和记忆位置功能
- [ ] 支持对话历史的分页加载和缓存管理

### AC4: 记忆功能用户体验
- [ ] AI的回复内容能体现出对之前对话的记忆（由后端服务实现）
- [ ] 实现会话的优雅开始和结束
- [ ] 支持会话中断和恢复功能
- [ ] 新会话能够体现之前会话的记忆连续性

## Tasks / Subtasks

### 第一阶段：核心对话界面组件 (4-5天)
- [ ] **主对话界面布局** (AC1, AC3)
  - 创建`app/(chat)/index.tsx`主对话页面
  - 实现顶部AI角色信息显示区域
  - 创建中部对话历史展示区域（FlashList）
  - 设计底部双模输入控制区域

- [ ] **ChatBubble组件完善** (AC3)
  - 基于故事1.2的基础ChatBubble，扩展支持语音消息和实时字幕（部分消息）的渲染
  - 实现用户消息和AI消息的差异化样式
  - 添加消息状态指示（发送中、已送达、播放中）
  - 支持消息的时间戳和阅读状态

- [ ] **AI状态指示器组件** (AC2)
  - 创建`AIStatusIndicator.tsx`组件
  - 根据从`conv`流接收到的AI状态（聆听中、思考中、回答中）显示不同的UI反馈

- [ ] **高性能列表渲染** (AC3)
  - 集成`@shopify/flash-list`替代标准FlatList
  - 实现消息组件的React.memo优化
  - 配置虚拟化渲染的性能参数
  - 实现智能的滚动位置管理

### 第二阶段：双模输入控制器 (3-4天)
- [ ] **ChatInputController核心组件** (AC1)
  - 创建`src/components/features/chat/ChatInputController.tsx`
  - 实现语音/文本模式的状态管理
  - 配置模式切换的动画效果（react-native-reanimated）
  - 支持键盘事件的自动处理

- [ ] **语音输入界面** (AC1, AC2)
  - 设计大型"按住说话"按钮（符合适老化设计）
  - 实现长按录音的交互逻辑和视觉反馈
  - 添加录音状态的动画效果（呼吸光效、水波纹）
  - 集成角落的"键盘"切换图标

- [ ] **文本输入界面** (AC1)
  - 实现文本输入框和发送按钮布局
  - 支持键盘弹出时的界面自适应
  - 添加角落的"语音"切换图标
  - 实现发送后自动返回语音模式的逻辑

### 第三阶段：火山RTC SDK集成 (3-4天)
- [ ] **RTC服务层实现** (AC2)
  - 创建`src/services/RtcChatService.ts`
  - 集成火山引擎RTC SDK（React Native版本）
  - 实现RTC连接的建立和管理
  - 处理音频权限请求和设备检测

- [ ] **实现RTC二进制消息处理器** (AC2)
  - 在`RtcChatService.ts`中，实现对`onRoomBinaryMessageReceived`回调的监听。
  - 创建解析器，用于处理接收到的二进制数据。
  - 根据`magic number`区分`conv`（AI状态）和`subv`（实时字幕）消息。
  - 将解析后的数据更新到对应的Zustand store中（`chatStore`, `aiStatusStore`等）。

- [ ] **会话生命周期管理** (AC2, AC4)
  - 实现`prepareAndConnectRtc()`方法调用后端准备接口
  - 管理RTC会话的开始、进行中、结束状态
  - 实现会话异常的检测和恢复机制
  - 支持会话的主动终止和清理

- [ ] **实时音频处理** (AC2)
  - 实现实时语音数据的传输和接收
  - 处理音频播放的队列和缓冲管理
  - 添加音频质量的监控和优化
  - 实现语音中断和恢复的平滑处理

### 第四阶段：文本对话集成 (2-3天)
- [ ] **SSE流处理** (AC1, AC4)
  - 创建`src/services/TextChatService.ts`
  - 实现SSE事件流的解析和处理
  - 支持AI回复的流式显示（打字机效果）
  - 处理网络异常和重连逻辑

- [ ] **消息状态管理** (AC3, AC4)
  - 扩展chatStore支持实时消息更新
  - 实现消息的乐观更新和错误回滚
  - 支持消息重发和状态同步
  - 管理会话的本地缓存和持久化

- [ ] **记忆体验集成** (AC4)
  - 在对话界面显示AI记忆相关的提示
  - 实现新会话时的记忆连续性提示
  - 支持记忆相关消息的特殊标识
  - 添加记忆功能的用户引导说明

### 第五阶段：性能优化和用户体验 (2-3天)
- [ ] **性能监控和优化** (AC2, AC3)
  - 实现端到端延迟的监控（目标<1.5秒）
  - 优化FlashList的渲染性能
  - 减少不必要的重渲染和内存泄漏
  - 配置音频缓冲和网络优化参数

- [ ] **用户体验优化** (AC1, AC2)
  - 实现触觉反馈（iOS Haptic Feedback）
  - 添加适当的加载状态和进度指示
  - 优化键盘处理和界面适配
  - 实现无障碍功能的全面支持

- [ ] **错误处理和降级** (AC2, AC4)
  - 实现网络异常时的优雅降级
  - 添加音频设备异常的处理逻辑
  - 支持RTC连接失败时的文本模式切换
  - 提供清晰的错误提示和恢复指导

## Dev Notes

CRITICAL: This is a **frontend functionality story**. 
**PREREQUISITE**: Story 1.1-UI must be completed first - all UI components and page layouts are provided by that story.

Load the following standards for implementation:
- `@docs/architecture/mobile-app-tech-stack.md`
- `@docs/architecture/mobile-app-source-tree.md`
- `@docs/architecture/mobile-app-coding-standards.md`

**Technical Guidance from Architecture:**

### Relevant API Endpoints to Consume:
从 `@docs/architecture/03-api-design.md` 和 `shared/contracts/` 中的关键接口：
- **RTC会话控制**:
  - `POST /api/v1/rtc/prepare_session` - 准备RTC会话，获取连接凭证。请求体为 `PrepareSessionRequest`。
  - `POST /api/v1/rtc/end_session` - 结束会话并触发后端的会话后分析。
- **文本对话**:
  - `POST /api/v1/chat/text_message` - 文本聊天（SSE流）。请求体为 `TextMessageRequest`。
  - 响应格式：`event: text_chunk`, `event: stream_end`, `event: error`

### UI Components to Build/Use:
从 `@docs/architecture/06-frontend-architecture.md` 中的组件架构：
- **核心双模交互组件**: `ChatInputController.tsx`
  - 状态管理：`const [mode, setMode] = useState<'voice' | 'text'>('voice')`
  - 动画：使用`react-native-reanimated`实现200-300ms过渡
  - 交互：调用`RtcChatService`或`TextChatService`
- **AI状态指示器**: `AIStatusIndicator.tsx` - 响应`conv`流，显示AI当前状态。
- **高性能列表**: 使用`@shopify/flash-list`，其`renderItem`中的`ChatBubble`需支持渲染部分消息(字幕)。
- **状态管理**: Zustand的`chatStore`
- **服务层**: `RtcChatService.ts`封装火山RTC SDK交互，并包含二进制消息解析逻辑。

### User Flow to Implement:
从 `@docs/prd/ux-design.md` 中的核心交互流程（已更新）：
1. **默认语音模式**: 大型"按住说话"按钮。
2. **用户说话**: 长按录音，UI实时显示用户说话的字幕(`subv`流)。
3. **AI聆听/思考**: 松开按钮后，AI状态指示器显示"聆听中"或"思考中"(`conv`流)。
4. **AI回复**: AI开始说话，UI实时显示AI回复的字幕(`subv`流)，同时播放TTS语音。
5. **文本交互**: 切换到文本模式，输入文字，发送后自动返回语音模式。

### Technical Architecture Requirements:
- **火山RTC SDK集成**: React Native版本，支持实时音频传输和**二进制数据通道**。
- **性能要求**: 端到端延迟P95 < 1.5秒，列表滚动 > 45fps
- **FlashList优化**: 虚拟化长列表，React.memo优化消息组件，必须设置estimatedItemSize
- **动画要求**: 使用reanimated，避免JS线程阻塞

### FlashList性能优化要点:
**CRITICAL**: `estimatedItemSize` 是FlashList的**必需参数**，缺少会导致性能问题和警告。

```typescript
// 正确的FlashList使用方式
<FlashList
  data={messages}
  renderItem={({ item }) => <ChatBubble message={item} />}
  estimatedItemSize={100} // 【必需参数】估算每个item的高度，非常重要！
  getItemType={(item) => item.type} // 性能优化：不同类型分别回收
  keyExtractor={(item) => item.id}
/>

// 消息组件优化
const ChatBubble = React.memo(({ message }) => {
  // 【重要】避免在组件内使用key prop，会阻止view回收
  return (
    <View> {/* 不要添加key prop */}
      <Text>{message.content}</Text>
    </View>
  );
});
```

**FlashList性能最佳实践**:
1. **estimatedItemSize**: 必须提供准确的估算高度
2. **getItemType**: 为不同类型的消息提供不同的类型标识
3. **React.memo**: 包装消息组件避免不必要的重渲染
4. **避免key属性**: 在FlashList内部组件中不要使用key属性

## Testing

Dev Note: Story Requires the following tests:

- [ ] Jest Unit Tests: (nextToFile: true), coverage requirement: 85%
- [ ] Jest with React Native Testing Library Integration Test: location: `src/components/features/chat/__tests__/`
- [ ] **新增测试**: 针对RTC二进制消息解析器的单元测试。
- [ ] **新增测试**: 验证UI能否根据`conv`和`subv`流正确更新的集成测试。
- [ ] Detox E2E: location: `e2e/chat/voice-text-interaction.e2e.ts`

Manual Test Steps:
- 在真实设备上测试完整的语音对话流程
- 验证双模切换的动画流畅性和响应速度
- 测试不同网络条件下的音频质量和延迟
- 确认FlashList在大量消息下的性能表现
- 测试音频权限和设备异常的处理情况
- **新增测试**: 验证实时字幕和AI状态显示是否准确、及时。

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.2（前端认证流程）已完成
- [ ] 故事1.3（后端实时服务）已完成
- [ ] 故事1.4-B（后端RTC集成）已完成并可正常调用
- [ ] 火山引擎RTC SDK账号和API密钥已配置
- [ ] 测试设备具备音频录制和播放能力

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 双模交互界面完全按设计实现
- [ ] 实时语音对话端到端延迟符合性能要求
- [ ] FlashList渲染性能达标
- [ ] 单元测试覆盖率≥85%
- [ ] E2E测试全部通过，包括网络异常场景

## 风险与缓解措施

### 主要风险
1. **RTC SDK集成复杂性**: React Native版本的兼容性和稳定性
2. **音频权限管理**: 不同平台的权限请求和处理差异
3. **网络延迟问题**: 影响实时对话的用户体验
4. **性能瓶颈**: 长对话历史的渲染和内存管理

### 缓解措施
1. **充分的SDK测试和官方文档研读，备用方案准备**
2. **标准化权限请求流程，优雅的权限拒绝处理**
3. **网络质量监控、自适应码率、降级策略**
4. **FlashList优化、消息分页、内存监控**

## 性能指标

### 关键性能要求
- 语音对话端到端延迟 P95 < 1.5秒
- 模式切换动画帧率 > 45fps
- FlashList滚动性能 > 60fps
- 内存使用增长 < 2MB/分钟

### 监控指标
- RTC连接成功率 > 95%
- 音频质量评分 > 4.0/5.0
- UI响应时间 < 100ms
- 崩溃率 < 0.1%

## 相关文档引用
- [用户故事原文](../../prd/user-stories.md#故事-14-实时语音会话流程集成)
- [UX设计规范](../../prd/ux-design.md)
- [前端架构设计](../../architecture/06-frontend-architecture.md)
- [API接口设计](../../architecture/03-api-design.md)
- [移动应用技术栈](../../architecture/mobile-app-tech-stack.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md) 