# api/services/user_service.py
"""
用户服务实现 - 对应故事1.2-B的AC2
实现用户画像管理、设置管理和数据操作
"""
import os
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass

from supabase import create_client, Client

from api.settings import logger

# Supabase配置
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

@dataclass
class UserProfile:
    """用户画像数据类"""
    user_id: str
    nickname: Optional[str] = None
    age_range: Optional[str] = None
    core_needs: Optional[List[str]] = None
    interests: Optional[List[str]] = None
    communication_style_preference: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None
    onboarding_completed: bool = False
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

@dataclass
class UserProfileUpdateData:
    """用户画像更新数据类"""
    nickname: Optional[str] = None
    age_range: Optional[str] = None
    core_needs: Optional[List[str]] = None
    interests: Optional[List[str]] = None
    communication_style_preference: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None
    onboarding_completed: Optional[bool] = None

class UserService:
    """用户管理服务"""

    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户画像，返回字典格式以便与API响应兼容"""
        try:
            logger.info(f"Getting user profile for user: {user_id}")

            response = self.supabase.table("user_profiles").select("*").eq("user_id", user_id).execute()

            if not response.data:
                logger.warning(f"User profile not found for user: {user_id}")
                return None

            profile_data = response.data[0]

            # 返回字典格式，确保包含id字段（测试期望）
            result = {
                "id": profile_data["user_id"],  # 映射为id字段
                "user_id": profile_data["user_id"],
                "nickname": profile_data.get("nickname"),
                "age_range": profile_data.get("age_range"),
                "core_needs": profile_data.get("core_needs"),
                "interests": profile_data.get("interests"),
                "communication_style_preference": profile_data.get("communication_style_preference"),
                "preferences": profile_data.get("preferences"),
                "onboarding_completed": profile_data.get("onboarding_completed", False),
                "created_at": profile_data.get("created_at"),
                "updated_at": profile_data.get("updated_at")
            }

            return result

        except Exception as e:
            logger.exception(f"Error getting user profile: {e}")
            raise

    async def create_user_profile(self, user_id: str, profile_data: Optional[UserProfileUpdateData] = None) -> UserProfile:
        """创建用户画像"""
        try:
            logger.info(f"Creating user profile for user: {user_id}")

            now = datetime.now(timezone.utc).isoformat()

            # 构建插入数据
            insert_data = {
                "user_id": user_id,
                "created_at": now,
                "updated_at": now
            }

            if profile_data:
                if profile_data.nickname:
                    insert_data["nickname"] = profile_data.nickname
                if profile_data.age_range:
                    insert_data["age_range"] = profile_data.age_range
                if profile_data.core_needs:
                    insert_data["core_needs"] = profile_data.core_needs
                if profile_data.interests:
                    insert_data["interests"] = profile_data.interests
                if profile_data.communication_style_preference:
                    insert_data["communication_style_preference"] = profile_data.communication_style_preference
                if profile_data.preferences:
                    insert_data["preferences"] = profile_data.preferences
                if profile_data.onboarding_completed is not None:
                    insert_data["onboarding_completed"] = profile_data.onboarding_completed

            response = self.supabase.table("user_profiles").insert(insert_data).execute()

            if not response.data:
                raise Exception("Failed to create user profile")

            created_profile = response.data[0]
            logger.info(f"Successfully created user profile for user: {user_id}")

            return UserProfile(
                user_id=created_profile["user_id"],
                nickname=created_profile.get("nickname"),
                age_range=created_profile.get("age_range"),
                core_needs=created_profile.get("core_needs"),
                interests=created_profile.get("interests"),
                communication_style_preference=created_profile.get("communication_style_preference"),
                preferences=created_profile.get("preferences"),
                onboarding_completed=created_profile.get("onboarding_completed", False),
                created_at=created_profile.get("created_at"),
                updated_at=created_profile.get("updated_at")
            )

        except Exception as e:
            logger.exception(f"Error creating user profile: {e}")
            raise

    async def update_user_profile(self, user_id: str, update_data: Union[UserProfileUpdateData, Dict[str, Any]]) -> Dict[str, Any]:
        """更新用户画像，支持字典和数据类两种输入格式"""
        try:
            logger.info(f"Updating user profile for user: {user_id}")

            # 构建更新数据
            db_update_data = {
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            # 处理不同的输入类型
            if isinstance(update_data, dict):
                # 字典格式输入
                for key, value in update_data.items():
                    if key in ["nickname", "age_range", "core_needs", "interests",
                              "communication_style_preference", "preferences", "onboarding_completed"]:
                        if value is not None:
                            db_update_data[key] = value
            else:
                # UserProfileUpdateData格式输入
                if update_data.nickname is not None:
                    db_update_data["nickname"] = update_data.nickname
                if update_data.age_range is not None:
                    db_update_data["age_range"] = update_data.age_range
                if update_data.core_needs is not None:
                    db_update_data["core_needs"] = update_data.core_needs
                if update_data.interests is not None:
                    db_update_data["interests"] = update_data.interests
                if update_data.communication_style_preference is not None:
                    db_update_data["communication_style_preference"] = update_data.communication_style_preference
                if update_data.preferences is not None:
                    db_update_data["preferences"] = update_data.preferences
                if update_data.onboarding_completed is not None:
                    db_update_data["onboarding_completed"] = update_data.onboarding_completed

            response = self.supabase.table("user_profiles").update(db_update_data).eq("user_id", user_id).execute()

            if not response.data:
                logger.warning(f"User profile not found for update: {user_id}")
                return None

            updated_profile = response.data[0]
            logger.info(f"Successfully updated user profile for user: {user_id}")

            # 返回字典格式，确保包含id字段
            result = {
                "id": updated_profile["user_id"],  # 映射为id字段
                "user_id": updated_profile["user_id"],
                "nickname": updated_profile.get("nickname"),
                "age_range": updated_profile.get("age_range"),
                "core_needs": updated_profile.get("core_needs"),
                "interests": updated_profile.get("interests"),
                "communication_style_preference": updated_profile.get("communication_style_preference"),
                "preferences": updated_profile.get("preferences"),
                "onboarding_completed": updated_profile.get("onboarding_completed", False),
                "created_at": updated_profile.get("created_at"),
                "updated_at": updated_profile.get("updated_at")
            }

            return result

        except Exception as e:
            logger.exception(f"Error updating user profile: {e}")
            raise

# 全局用户服务实例
user_service = UserService()
