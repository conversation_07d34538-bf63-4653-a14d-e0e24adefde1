# 4. 可插拔记忆服务 (Pluggable Memory System)

**版本：** 3.0  
**日期：** 2025年7月6日  
**作者：** @architect

## 4.1 核心设计理念

为了实现最大化的灵活性、专业性和可扩展性，我们已将记忆系统从Agno框架中完全解耦，升级为一个独立、抽象、可插拔的服务层。此举旨在废弃原有的`PostgresMemoryDb`实现，拥抱更专业的第三方记忆云服务。

**核心目标：**
- **专业化**: 利用Zep AI和Mem0 AI等专业服务提供的先进记忆管理功能（如自动摘要、实体提取、图记忆等）。
- **灵活性**: 允许系统在不同的记忆提供商之间动态切换，以应对不同的业务需求或成本考量。
- **解耦**: 使`agent-api`的核心业务逻辑（如对话编排、工具调用）与记忆的底层实现完全分离。

## 4.2 架构：`IMemoryService` 抽象层

新架构的核心是位于 `apps/agent-api/api/services/memory_service.py` 的 `IMemoryService` 抽象基类。它定义了所有记忆服务的统一契约。

### 4.2.1 `IMemoryService` 接口定义 (伪代码)

```python
# file: apps/agent-api/api/services/memory_service.py

from abc import ABC, abstractmethod

class IMemoryService(ABC):
    """
    定义了所有记忆服务提供商必须实现的统一接口。
    """

    @abstractmethod
    async def get_memory_context(self, session_id: str, user_input: str) -> str:
        """
        根据用户当前输入，从记忆库中检索相关信息，并构建成可直接注入到LLM Prompt的上下文字符串。

        Args:
            session_id: 当前会话的唯一标识符。
            user_input: 用户的最新输入，用于相关性搜索。

        Returns:
            一个格式化好的字符串，包含所有相关的记忆信息。如果无相关记忆，则返回空字符串。
        """
        pass

    @abstractmethod
    async def add_conversation_turn(self, session_id: str, user_input: str, ai_response: str) -> None:
        """
        将一轮完整的用户-AI对话添加到记忆库中。

        Args:
            session_id: 当前会话的唯一标识符。
            user_input: 用户的输入。
            ai_response: AI的回复。
        """
        pass
```

### 4.2.2 工厂函数 `get_memory_service`

此工厂函数是整个架构的动态切换中枢。它通过读取环境变量`MEMORY_PROVIDER`来决定实例化哪个具体的服务实现。

```python
# file: apps/agent-api/api/services/memory_service.py (continued)

import os

# ... (ZepMemoryServiceImpl 和 Mem0MemoryServiceImpl 的定义) ...

def get_memory_service() -> IMemoryService:
    """
    根据环境变量 MEMORY_PROVIDER 的值，实例化并返回对应的记忆服务实现。
    """
    provider = os.environ.get("MEMORY_PROVIDER", "zep").lower()
    
    if provider == "zep":
        # ... 初始化并返回 ZepMemoryServiceImpl 实例
        return ZepMemoryServiceImpl()
    elif provider == "mem0":
        # ... 初始化并返回 Mem0MemoryServiceImpl 实例
        return Mem0MemoryServiceImpl()
    else:
        raise ValueError(f"不支持的记忆服务提供商: {provider}")

```

## 4.3 具体实现类

### 4.3.1 `ZepMemoryServiceImpl`

此实现基于 `zep-cloud` Python SDK，并参考了 `zep_demo_fixed.py` 的核心逻辑。

**伪代码实现:**
```python
# file: apps/agent-api/api/services/memory_service.py (continued)

from zep_cloud.client import AsyncZep
from zep_cloud import Message

class ZepMemoryServiceImpl(IMemoryService):
    def __init__(self):
        self.zep_client = AsyncZep(api_key=os.environ.get("ZEP_API_KEY"))

    async def get_memory_context(self, session_id: str, user_input: str) -> str:
        try:
            # 1. 从Zep检索记忆和上下文
            memory = await self.zep_client.memory.get(session_id=session_id)
            if not memory:
                return ""

            # 2. 构建上下文字符串
            context_parts = []
            if hasattr(memory, 'context') and memory.context:
                context_parts.append(f"背景摘要:\n{memory.context}")
            
            if hasattr(memory, 'messages') and memory.messages:
                recent_history = "\n".join([f"- {m.role_type}: {m.content}" for m in memory.messages[-5:]])
                context_parts.append(f"最近对话:\n{recent_history}")

            return "\n\n".join(context_parts) if context_parts else ""
            
        except Exception as e:
            # 异常处理：打印日志并返回空，确保不阻塞主流程
            print(f"从Zep检索记忆失败: {e}")
            return ""

    async def add_conversation_turn(self, session_id: str, user_input: str, ai_response: str) -> None:
        try:
            # 1. 构建Zep消息对象
            messages = [
                Message(role_type="user", content=user_input),
                Message(role_type="assistant", content=ai_response),
            ]
            
            # 2. 添加到Zep记忆
            await self.zep_client.memory.add(session_id=session_id, messages=messages)
        except Exception as e:
            print(f"向Zep添加记忆失败: {e}")

```

### 4.3.2 `Mem0MemoryServiceImpl`

此实现基于 `mem0ai` Python SDK，并参考了 `mem0_demo1_improved.py` 的核心逻辑。

**伪代码实现:**
```python
# file: apps/agent-api/api/services/memory_service.py (continued)

from mem0 import AsyncMemoryClient

class Mem0MemoryServiceImpl(IMemoryService):
    def __init__(self):
        self.mem0_client = AsyncMemoryClient(api_key=os.environ.get("MEM0_API_KEY"))
        self.user_id = "default_user" # 在实际应用中，这应该从会话或Token中获取

    async def get_memory_context(self, session_id: str, user_input: str) -> str:
        try:
            # 1. 使用用户输入作为查询，搜索相关记忆
            search_result = await self.mem0_client.search(
                query=user_input,
                user_id=self.user_id, # Mem0 API需要user_id
                limit=5,
                version="v2"
            )

            # 2. 格式化搜索结果
            memories = []
            if search_result and hasattr(search_result, 'results'):
                memories = search_result.results
            
            if not memories:
                return ""
                
            formatted_memories = "\n".join([f"- {m.get('memory', '')}" for m in memories])
            return f"相关记忆:\n{formatted_memories}"

        except Exception as e:
            print(f"从Mem0检索记忆失败: {e}")
            return ""

    async def add_conversation_turn(self, session_id: str, user_input: str, ai_response: str) -> None:
        try:
            # 1. 构建Mem0消息字典
            messages = [
                {"role": "user", "content": user_input},
                {"role": "assistant", "content": ai_response}
            ]
            
            # 2. 添加到Mem0记忆
            # 注意：session_id可能需要作为元数据存储
            await self.mem0_client.add(
                messages=messages,
                user_id=self.user_id,
                metadata={"session_id": session_id},
                version="v2"
            )
        except Exception as e:
            print(f"向Mem0添加记忆失败: {e}")
```

## 4.4 集成到应用中

在 `FastAPI` 应用中，我们遵循清晰的分层架构。`IMemoryService` 作为底层服务，被注入到 `ChatOrchestrationService` 中，而不是直接暴露给API路由层。路由层仅依赖于编排服务。

**集成示例:**
```python
# 1. 在 `ChatOrchestrationService` 中注入并使用 `IMemoryService`
# file: apps/agent-api/api/services/chat_orchestration_service.py

class ChatOrchestrationService:
    def __init__(
        self,
        memory_service: IMemoryService,
        llm_proxy: LLMProxyService,
        tool_executor: ToolExecutorService
        # ...其他服务通过依赖注入传入
    ):
        self.memory_service = memory_service
        # ...

    async def handle_message(self, user_message: str, context: dict):
        session_id = context.get("session_id")

        # a. 从记忆服务获取上下文
        memory_context = await self.memory_service.get_memory_context(
            session_id=session_id,
            user_input=user_message
        )
        
        # b. 构建Prompt，结合记忆，然后调用LLM...
        prompt = self.prompt_builder.build(memory_context, user_message)
        ai_response = await self.llm_proxy.chat(prompt) # 简化示例
        
        # c. 异步将新一轮对话添加到记忆中
        self.background_tasks.add_task(
            self.memory_service.add_conversation_turn,
            session_id=session_id,
            user_input=user_message,
            ai_response=ai_response
        )
        
        return {"response": ai_response}


# 2. 在路由层，只依赖 `ChatOrchestrationService`
# file: apps/agent-api/api/routes/chat_routes.py

@router.post("/chat")
async def handle_chat(
    request: ChatRequest,
    # 编排器是路由层唯一的业务逻辑依赖
    orchestrator: ChatOrchestrationService = Depends(get_orchestrator)
):
    """
    此端点接收聊天请求，并将其完全委托给编排服务处理。
    """
    response = await orchestrator.handle_message(
        user_message=request.message,
        context={"session_id": request.session_id}
    )
    return response
```

### 从现有代码迁移的最佳实践
本次架构升级的核心是将记忆层抽象并外化。在具体的开发实现过程中，强烈建议开发者团队遵循以下迁移策略，以最大化地复用现有代码资产：
- **复用服务层结构**: `apps/agent-api/api/services/`下的服务（如`session_service.py`, `user_service.py`）已经定义了清晰的业务边界。新功能的开发应尽量复用或扩展这些服务，而不是从零开始。
- **借鉴路由与依赖注入**: `apps/agent-api/api/routes/`下的路由文件展示了FastAPI依赖注入和请求验证的最佳实践。新API的实现应遵循这些模式。
- **迁移数据模型**: `apps/agent-api/api/models/`下的Pydantic模型是前后端契约的基础。在开发新功能时，应优先复用和扩展这些模型，而不是创建重复的定义。
- **保留核心业务流**: 尽管底层的`agent.arun()`调用被替换，但围绕它构建的业务流程（如错误处理、日志记录、异步任务分发）仍然具有很高的参考价值。开发者应将这些流程平移到新的`ChatOrchestrationService`中。

简而言之，我们的目标是“替换引擎，保留车身”。通过充分参考和借鉴现有代码，我们可以显著加快开发速度，并保证新旧功能在风格和质量上的一致性。 