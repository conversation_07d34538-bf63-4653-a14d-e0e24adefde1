---
description: "A rule to guide AI interactions with the Supabase database using MCP tools."
globs: 
  - "apps/agent-api/api/services/**/*.py"
  - "apps/agent-api/db/**/*.py"
  - "apps/agent-api/tests/integration/**/*.py"
  - "shared/contracts/schema.py"
alwaysApply: true
---

# 数据库交互与验证协议 (Supabase)

## 核心原则

当你正在阅读、编写或修改任何与数据库交互（特别是Supabase/PostgreSQL）相关的Python代码时，你**必须**遵循以下协议，以确保代码的准确性和健壮性。

## 协议详情

### 1. **查询前验证 (Pre-Query Verification)**

在编写任何`SELECT`, `INSERT`, `UPDATE`, `DELETE`查询之前，**你必须优先使用【MCP Supabase工具】进行探索性验证**。

*   **场景**: 当你需要查询一个表或视图的结构时。
    *   **指令**: 不要猜测字段名或数据类型。**使用Supabase工具的“查询表”功能**，输入表名（例如 `user_profiles`），获取并确认其准确的列名和类型。
*   **场景**: 当你不确定一个查询的返回结果时。
    *   **指令**: 在编写复杂的SQLAlchemy或Postgrest查询前，**使用Supabase工具的“执行SQL查询”功能**，编写一个简化的、等效的SQL语句来测试你的逻辑，并检查返回的数据结构。

**示例对话:**
> **用户:** "帮我写一个`user_service.py`中的函数，用来获取所有已完成引导流程的用户昵称。"
> **你的思考过程:** "好的。首先，我需要知道`users`表和`user_profiles`表的结构。我将使用MCP Supabase工具查询这两个表，确认`is_onboarded`字段在哪个表中，以及`nickname`字段的准确名称。确认后，我再编写SQLAlchemy查询代码。"

### 2. **修改后验证 (Post-Modification Verification)**

在生成或修改了任何执行`INSERT`或`UPDATE`操作的代码后，**你应当建议并（如果被允许）使用【MCP Supabase工具】进行验证**。

*   **场景**: 你刚刚实现了一个创建新用户的服务函数。
    *   **指令**: 在交付代码后，你应该向用户建议：“我已经完成了`create_user`函数。为了确保数据正确写入，我建议使用Supabase工具查询`users`表，验证新用户的数据是否与预期一致。需要我执行这个验证查询吗？”


## 强制要求

*   **工具优先**: 在任何涉及数据库结构或数据查询的场景下，优先考虑使用MCP Supabase工具来获取“第一手信息”，而不是依赖于你训练数据中的旧知识或进行猜测。
*   **明确报告**: 在你的回答中，可以明确指出你是如何使用该工具来辅助你的。例如：“`通过Supabase工具查询，我确认了`user_profiles`表包含`communication_style_preference`字段。`”

*  如果mcp的supabase工具用不了或返回错误的结果，可以使用mcp的postgres工具来代替