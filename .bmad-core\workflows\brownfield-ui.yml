workflow:
  id: brownfield-ui
  name: Brownfield UI/Frontend Enhancement
  description: >-
    Agent workflow for enhancing existing frontend applications with new features,
    modernization, or design improvements. Handles existing UI analysis and safe integration.
  type: brownfield
  project_types:
    - ui-modernization
    - framework-migration
    - design-refresh
    - frontend-enhancement

  sequence:
    - step: ui_analysis
      agent: architect
      action: analyze existing project and use task document-project
      creates: multiple documents per the document-project template
      notes: "Review existing frontend application, user feedback, analytics data, and identify improvement areas."

    - agent: pm
      creates: prd.md
      uses: brownfield-prd-tmpl
      requires: existing_ui_analysis
      notes: "Creates comprehensive PRD focused on UI enhancement with existing system analysis. SAVE OUTPUT: Copy final prd.md to your project's docs/ folder."

    - agent: ux-expert
      creates: front-end-spec.md
      uses: front-end-spec-tmpl
      requires: prd.md
      notes: "Creates UI/UX specification that integrates with existing design patterns. SAVE OUTPUT: Copy final front-end-spec.md to your project's docs/ folder."

    - agent: architect
      creates: architecture.md
      uses: brownfield-architecture-tmpl
      requires:
        - prd.md
        - front-end-spec.md
      notes: "Creates frontend architecture with component integration strategy and migration planning. SAVE OUTPUT: Copy final architecture.md to your project's docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all documents for UI integration safety and design consistency. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: move_to_ide
      notes: |
        Planning phase complete! Now transition to IDE Development:
        
        1. ENSURE DOCUMENTS ARE IN PROJECT:
           - Copy final prd.md to project's docs/prd.md
           - Copy final architecture.md to project's docs/architecture.md
           - All documents must be in the project before proceeding
        
        2. SHARD DOCUMENTS (in IDE):
           - Option A: Use PO agent to shard: @po then ask to shard docs/prd.md
           - Option B: Manual: Drag shard-doc task + docs/prd.md into chat
           - This creates docs/prd/ and docs/architecture/ folders with sharded content
        
        3. START DEVELOPMENT CYCLE:
           a. SM Agent (New Chat): @sm → *create
              - Creates next story from sharded docs
              - Review and approve story (Draft → Approved)
           
           b. Dev Agent (New Chat): @dev
              - Implements approved story
              - Updates File List with all changes
              - Marks story as "Review" when complete
           
           c. QA Agent (New Chat): @qa → review-story
              - Senior dev review with refactoring ability
              - Fixes small issues directly
              - Leaves checklist for remaining items
              - Updates story status (Review → Done or stays Review)
           
           d. If QA left unchecked items:
              - Dev Agent (New Chat): Address remaining items
              - Return to QA for final approval
        
        4. REPEAT: Continue cycle for all epic stories
        
        Reference: data#bmad-kb:IDE Development Workflow

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: UI Enhancement] --> B[analyst: analyze existing UI]
        B --> C[pm: prd.md]
        C --> D[ux-expert: front-end-spec.md]
        D --> E[architect: architecture.md]
        E --> F[po: validate with po-master-checklist]
        F --> G{PO finds issues?}
        G -->|Yes| H[Return to relevant agent for fixes]
        G -->|No| I[Move to IDE Environment]
        H --> F

        style I fill:#90EE90
        style C fill:#FFE4B5
        style D fill:#FFE4B5
        style E fill:#FFE4B5
    ```

  decision_guidance:
    when_to_use:
      - UI enhancement requires coordinated stories
      - Design system changes needed
      - New component patterns required
      - User research and testing needed
      - Multiple team members will work on related changes

  handoff_prompts:
    analyst_to_pm: "UI analysis complete. Create comprehensive PRD with UI integration strategy."
    pm_to_ux: "PRD ready. Save it as docs/prd.md, then create the UI/UX specification."
    ux_to_architect: "UI/UX spec complete. Save it as docs/front-end-spec.md, then create the frontend architecture."
    architect_to_po: "Architecture complete. Save it as docs/architecture.md. Please validate all artifacts for UI integration safety."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    complete: "All planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."
