#!/usr/bin/env python3
"""
心桥项目 - 快速API测试脚本
快速验证核心API接口的可用性
"""

import asyncio
import json
import sys
import os
from datetime import datetime
import uuid

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import httpx
from test_config import TestConfig

class QuickAPITester:
    """快速API测试器"""

    def __init__(self):
        self.base_url = TestConfig.BASE_URL
        self.client = None
        self.access_token = None
        self.user_id = None
        self.test_count = 0
        self.success_count = 0

    async def setup(self):
        """初始化"""
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=TestConfig.TIMEOUT
        )
        print(f"🔧 测试目标: {self.base_url}")

    async def teardown(self):
        """清理"""
        if self.client:
            await self.client.aclose()

    async def test_api(self, method: str, endpoint: str, data=None, expect_code=200, use_auth=True):
        """测试API接口"""
        self.test_count += 1

        # 设置请求头
        headers = TestConfig.get_test_headers()
        if use_auth and self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"

        try:
            print(f"📤 {method} {endpoint}")

            response = await self.client.request(
                method=method,
                url=f"/api/v1{endpoint}",
                json=data,
                headers=headers
            )

            if response.status_code == expect_code:
                print(f"✅ {response.status_code} - 成功")
                self.success_count += 1
                return response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
            else:
                print(f"❌ {response.status_code} - 失败")
                print(f"   响应: {response.text}")
                return None

        except Exception as e:
            print(f"❌ 异常: {str(e)}")
            return None

    async def run_quick_tests(self):
        """运行快速测试"""
        print("🚀 开始快速API测试...\n")

        # 1. 健康检查
        print("=" * 50)
        print("1. 健康检查")
        print("=" * 50)
        await self.test_api("GET", "/health", use_auth=False)

        # 2. 匿名登录
        print("\n" + "=" * 50)
        print("2. 匿名登录")
        print("=" * 50)
        device_id = f"{TestConfig.TEST_DEVICE_ID_PREFIX}{uuid.uuid4().hex[:8]}"
        login_data = {
            "device_info": TestConfig.get_device_info(device_id)
        }

        result = await self.test_api("POST", "/auth/anonymous-login", login_data, use_auth=False)
        if result:
            self.access_token = result.get("access_token")
            self.user_id = result.get("user", {}).get("id")
            print(f"   用户ID: {self.user_id}")

        # 3. 获取用户画像
        print("\n" + "=" * 50)
        print("3. 获取用户画像")
        print("=" * 50)
        await self.test_api("GET", "/user/profile")

        # 4. 获取AI角色列表
        print("\n" + "=" * 50)
        print("4. 获取AI角色列表")
        print("=" * 50)
        chars_result = await self.test_api("GET", "/characters")
        character_id = None
        if chars_result and "data" in chars_result:
            characters = chars_result["data"]
            if characters:
                character_id = characters[0]["id"]
                print(f"   找到角色: {characters[0].get('name', 'Unknown')}")

        # 5. 创建会话
        print("\n" + "=" * 50)
        print("5. 创建会话")
        print("=" * 50)
        session_id = None
        if character_id:
            session_data = {
                "characterId": character_id,
                "topic": TestConfig.TEST_SESSION_TOPIC
            }
            session_result = await self.test_api("POST", "/chat/sessions", session_data, expect_code=201)
            if session_result:
                session_id = session_result.get("id")
                print(f"   会话ID: {session_id}")

        # 6. 获取会话列表
        print("\n" + "=" * 50)
        print("6. 获取会话列表")
        print("=" * 50)
        await self.test_api("GET", "/chat/sessions")

        # 7. 创建提醒
        print("\n" + "=" * 50)
        print("7. 创建提醒")
        print("=" * 50)
        from datetime import datetime, timedelta, timezone
        future_time = datetime.now(timezone.utc) + timedelta(hours=1)
        reminder_data = {
            "content": TestConfig.TEST_REMINDER_CONTENT,
            "reminder_time": future_time.isoformat()
        }
        reminder_result = await self.test_api("POST", "/reminders/", reminder_data, expect_code=201)
        reminder_id = None
        if reminder_result:
            reminder_id = reminder_result.get("id")
            print(f"   提醒ID: {reminder_id}")

        # 8. 获取提醒列表
        print("\n" + "=" * 50)
        print("8. 获取提醒列表")
        print("=" * 50)
        await self.test_api("GET", "/reminders/")

        # 9. 测试刷新token
        print("\n" + "=" * 50)
        print("9. 刷新访问令牌")
        print("=" * 50)
        if result and "refresh_token" in result:
            refresh_data = {"refresh_token": result["refresh_token"]}
            await self.test_api("POST", "/auth/refresh-token", refresh_data, use_auth=False)

        # 10. 测试连接状态
        print("\n" + "=" * 50)
        print("10. 聊天连接状态")
        print("=" * 50)
        await self.test_api("GET", "/chat/connections/status")

        # 输出结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        print(f"总测试数: {self.test_count}")
        print(f"成功数: {self.success_count}")
        print(f"失败数: {self.test_count - self.success_count}")
        print(f"成功率: {self.success_count/self.test_count*100:.1f}%")

        if self.success_count == self.test_count:
            print("\n🎉 所有测试都通过了！API服务正常运行。")
        else:
            print(f"\n⚠️  有 {self.test_count - self.success_count} 个测试失败，请检查服务状态。")

        print("=" * 60)


async def main():
    """主函数"""
    tester = QuickAPITester()

    try:
        await tester.setup()
        await tester.run_quick_tests()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    finally:
        await tester.teardown()


if __name__ == "__main__":
    asyncio.run(main())
