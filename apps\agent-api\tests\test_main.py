"""
基础API测试 - 对应AC1: 后端项目结构建立
"""
import pytest
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)

class TestHealthCheck:
    """场景1: 健康检查接口 - 服务正常启动"""

    def test_health_endpoint_exists(self):
        """测试健康检查接口是否存在并返回正确状态"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200

        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data

class TestAPIDocumentation:
    """场景2: API文档自动生成"""

    def test_swagger_ui_accessible(self):
        """测试Swagger UI是否可访问"""
        response = client.get("/docs")
        assert response.status_code == 200
        # 检查返回的是HTML内容（Swagger UI）
        assert "text/html" in response.headers.get("content-type", "")

    def test_openapi_json_accessible(self):
        """测试OpenAPI JSON schema是否可访问"""
        response = client.get("/api/v1/openapi.json")
        assert response.status_code == 200

        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "Agent API"

class TestRootEndpoint:
    """测试根端点基本功能"""

    def test_root_endpoint(self):
        """测试根端点返回欢迎信息"""
        response = client.get("/")
        assert response.status_code == 200

        data = response.json()
        assert "message" in data
        assert "Agent API" in data["message"]
