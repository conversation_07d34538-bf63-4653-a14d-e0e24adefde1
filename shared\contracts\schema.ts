// 共享的 TypeScript 类型定义
// 确保前后端接口定义一致

export interface User {
  id: string;
  device_fingerprint: string;  // 修复：对应数据库字段 device_fingerprint
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  user_id: string;
  nickname?: string;
  age_range?: string;  // 修复：对应数据库字段 age_range (varchar)
  core_needs: string[];
  interests: string[];  // 新增：对应数据库字段 interests
  communication_style_preference?: string;  // 新增：对应数据库字段
  preferences: Record<string, any>;
  onboarding_completed: boolean;  // 修复：对应数据库字段 onboarding_completed
  allow_chat_analysis: boolean;  // 新增：对应数据库字段 allow_chat_analysis
  personality_summary_ai?: string;  // AI生成的性格总结
  created_at: string;
  updated_at: string;
}

export interface Character {
  id: string;
  name: string;
  description?: string;  // 修复：数据库中可为空
  voice_id?: string;  // 修复：数据库中可为空
  personality: Record<string, any>;
  is_default: boolean;  // 新增：对应数据库字段 is_default
  created_at: string;
}

export interface ChatSession {
  id: string;
  user_id?: string;  // 修复：数据库中可为空
  character_id?: string;  // 修复：数据库中可为空
  topic?: string;
  summary?: string;
  status: 'active' | 'ended' | 'error' | 'completed' | 'archived' | 'deleted';  // 修复：完整状态枚举
  rtc_task_id?: string;
  analysis_status: 'pending' | 'processing' | 'completed' | 'failed' | 'timeout' | 'sync_failed';  // 新增：对应数据库字段
  last_message_at?: string;  // 新增：对应数据库字段
  ended_at?: string;  // 新增：对应数据库字段
  tags?: string[];  // 新增：对应数据库字段
  metadata?: Record<string, any>;  // 新增：对应数据库字段
  topic_type?: string;  // 新增：对应数据库字段
  created_at: string;
  updated_at: string;
}

export interface ChatMessage {
  id: string;
  session_id?: string;  // 修复：数据库中可为空
  user_id?: string;  // 新增：对应数据库字段
  role: 'user' | 'assistant' | 'system' | 'tool';  // 修复：添加 tool 角色
  content: string;
  content_type: string;  // 新增：对应数据库字段
  message_type: string;  // 新增：对应数据库字段
  emotion_category?: string;  // 新增：对应数据库字段
  emotion_intensity?: number;  // 新增：对应数据库字段
  tokens_used?: number;  // 新增：对应数据库字段
  status: string;  // 新增：对应数据库字段
  quoted_message_id?: string;  // 新增：对应数据库字段
  reactions?: Record<string, any>;  // 新增：对应数据库字段
  is_edited: boolean;  // 新增：对应数据库字段
  edit_count: number;  // 新增：对应数据库字段
  deleted_at?: string;  // 新增：对应数据库字段
  is_deleted: boolean;  // 新增：对应数据库字段
  structured_data?: Record<string, any>;  // 新增：对应数据库字段
  metadata?: Record<string, any>;
  created_at: string;
  updated_at?: string;  // 新增：对应数据库字段
}

// 新增：危机事件接口
export interface CrisisEvent {
  id: string;
  session_id?: string;
  user_id?: string;
  risk_level: string;
  triggered_keywords?: string[];
  confidence_score?: number;
  response_template?: string;
  event_timestamp: string;
}

// 新增：用户角色绑定接口
export interface UserCharacterBinding {
  id: string;
  user_id: string;
  character_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 新增：RTC会话接口
export interface RtcSession {
  session_id: string;
  user_id: string;
  character_id?: string;
  task_id?: string;
  room_id?: string;
  status: string;
  voice_config?: Record<string, any>;
  created_at?: string;
  started_at?: string;
  ended_at?: string;
  error_message?: string;
  volcano_response?: Record<string, any>;
}

// 提醒相关接口
export interface Reminder {
  id?: string;
  user_id?: string;  // 修复：数据库中可为空
  content: string;
  reminder_time: string;
  status: 'pending' | 'triggered' | 'completed' | 'cancelled';
  pattern_id?: string;
  function_call_payload?: Record<string, any>;  // 新增：对应数据库字段
  created_at?: string;
  updated_at?: string;
}

export interface ReminderPattern {
  id?: string;
  user_id: string;
  name: string;
  pattern_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  pattern_config: Record<string, any>;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

// 用户设置相关接口
export interface UserSettings {
  user_id: string;
  theme: 'auto' | 'light' | 'dark';
  font_size: 'small' | 'medium' | 'large' | 'extra_large';
  high_contrast: boolean;
  language: 'zh-CN' | 'en-US';
  notifications_enabled: boolean;
  quiet_hours_enabled: boolean;
  quiet_hours_start?: string;  // "HH:MM" 格式
  quiet_hours_end?: string;    // "HH:MM" 格式
  updated_at?: string;
}

export interface UserSettingsUpdate {
  userId?: string;
  theme?: 'auto' | 'light' | 'dark';
  font_size?: 'small' | 'medium' | 'large' | 'extra_large';
  high_contrast?: boolean;
  language?: 'zh-CN' | 'en-US';
  notifications_enabled?: boolean;
  quiet_hours_enabled?: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
}

// API 请求和响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface TextMessageRequest {
  message: string;
  sessionId: string;
  characterId?: string;
}

export interface PrepareSessionRequest {
  userId: string;
  sessionId: string;
  characterId: string;
}

export interface RtcCredentials {
  token: string;
  roomId: string;
  userId: string;
  taskId: string;
}

export type PrepareSessionResponse = ApiResponse<RtcCredentials>;

// 认证相关接口
export interface DeviceInfo {
  device_id: string;
  platform: string;
  app_version: string;
}

export interface AnonymousLoginRequest {
  device_info: DeviceInfo;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export type AuthResponse = ApiResponse<AuthTokens>;

export interface RefreshTokenRequest {
  refresh_token: string;
}

// 会话创建相关接口
export interface CreateSessionRequest {
  // 移除userId字段，现在从JWT Token中获取
  topic?: string;
  characterId?: string;
  topicType?: string;
  metadata?: Record<string, any>;
}

// RTC会话准备接口
export interface PrepareSessionRequest {
  // 移除userId字段，现在从JWT Token中获取
  sessionId: string;
  characterId: string;
}

// RTC会话结束接口
export interface EndSessionRequest {
  // 移除userId字段，现在从JWT Token中获取
  sessionId: string;
  taskId: string;
}

// 引导完成接口
export interface OnboardingRequest {
  // 移除userId字段，现在从JWT Token中获取
  nickname: string;
  core_needs: string[];
  interests?: string[];
  communication_style_preference?: string;
  allow_chat_analysis?: boolean;
  character?: {
    name: string;
    role: string;
    voice_id: string;
  };
}

export type SessionListResponse = ApiResponse<ChatSession[]>;
export type SessionResponse = ApiResponse<ChatSession>;

// 用户画像相关接口
export interface OnboardingProfileData {
  core_needs: string[];
  interests: string[];
  communication_style_preference: string;
  allow_chat_analysis: boolean;
}

export type UserProfileResponse = ApiResponse<UserProfile>;

// 角色相关接口
export type CharacterListResponse = ApiResponse<Character[]>;
export type CharacterResponse = ApiResponse<Character>;

export interface CharacterBindRequest {
  character_id: string;
}

// Function Calling 相关接口
export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

export interface ToolResult {
  tool_call_id: string;
  content: string;
  success: boolean;
  error?: string;
}

export interface FunctionCallingRequest {
  tool_calls: ToolCall[];
}

export interface FunctionCallingResponse {
  results: ToolResult[];
}

// 提醒相关响应接口
export type ReminderListResponse = ApiResponse<Reminder[]>;
export type ReminderResponse = ApiResponse<Reminder>;

export interface CreateReminderRequest {
  content: string;
  reminder_time: string;
  pattern_id?: string;
}

export interface UpdateReminderRequest {
  content?: string;
  reminder_time?: string;
  status?: 'pending' | 'triggered' | 'completed' | 'cancelled';
}

// 用户设置响应接口
export type UserSettingsResponse = ApiResponse<UserSettings>;
