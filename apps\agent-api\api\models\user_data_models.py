from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from pydantic import ConfigDict

# 导入本地schema中的用户设置模型
from api.models.schema_models import (
    UserSettings,
    UserSettingsUpdate,
    UserSettingsResponse,
    NotificationSettings,
    QuietHoursSettings
)

# ---- User Profile Models ----

class UserProfileBase(BaseModel):
    # Fields from user_profiles table (examples, adjust based on actual schema from database.types.ts.txt)
    core_needs: Optional[List[str]] = Field(None, description="用户的核心需求")
    interests: Optional[List[str]] = Field(None, description="用户的兴趣")
    communication_style_preference: Optional[str] = Field(None, description="沟通风格偏好")
    personality_summary_ai: Optional[str] = Field(None, description="AI生成的性格总结 (只读)")
    allow_chat_analysis: Optional[bool] = Field(True, description="是否允许聊天分析以改进服务")
    # Add other fields from user_profiles as needed, matching database.types.ts.txt for nullability and type

class UserProfileDB(UserProfileBase): # Represents data from user_profiles table
    user_id: str # Foreign key to users table

class UserDataFromAuth(BaseModel): # Represents data typically from users table or auth provider
    id: str = Field(description="用户ID (UUID from auth.users)")
    email: Optional[EmailStr] = Field(None, description="用户邮箱")
    nickname: Optional[str] = Field(None, description="用户昵称 (来自users.raw_user_meta_data或自定义)")
    avatar_url: Optional[str] = Field(None, description="用户头像URL (来自users.raw_user_meta_data或自定义)")
    # last_sign_in_at: Optional[datetime] = Field(None, description="最后登录时间") # Example from users table

class UserProfileResponse(UserDataFromAuth, UserProfileBase):
    """
    Response model for GET /api/v1/user/profile
    Combines data from 'users' (via auth) and 'user_profiles' tables.
    """
    created_at: Optional[datetime] = Field(None, description="用户画像记录创建时间 (UTC from user_profiles)")
    updated_at: Optional[datetime] = Field(None, description="用户画像记录更新时间 (UTC from user_profiles)")
    # Ensure all fields from UserDataFromAuth and UserProfileBase are included or inherited

    model_config = ConfigDict(populate_by_name=True, from_attributes=True) # for SQLAlchemy ORM compatibility if used directly


class UserProfileUpdateRequest(BaseModel):
    """
    Request model for PUT /api/v1/user/profile
    Allows partial updates. All fields are optional.
    """
    userId: str = Field(..., description="要更新画像的用户ID")
    nickname: Optional[str] = Field(None, min_length=1, max_length=50, description="要更新的昵称")
    avatar_url: Optional[str] = Field(None, description="要更新的头像URL")
    core_needs: Optional[List[str]] = Field(None, description="更新的核心需求列表")
    interests: Optional[List[str]] = Field(None, description="更新的兴趣列表")
    communication_style_preference: Optional[str] = Field(None, description="更新的沟通风格偏好")
    allow_chat_analysis: Optional[bool] = Field(None, description="更新的是否允许聊天分析的偏好")
    # Add other updatable fields from user_profiles as needed

# ---- User Settings Models ----
# 注意：UserSettingsUpdateRequest 现在使用 shared.contracts.schema.UserSettingsUpdate
UserSettingsUpdateRequest = UserSettingsUpdate

# <<< 新增开始: 用于 /auth/finalize-onboarding 端点的模型 >>>

class FinalizeOnboardingRequest(BaseModel):
    userId: str = Field(..., description="从前端Supabase Auth获取的已认证用户ID")
    nickname: str = Field(..., min_length=1, max_length=50, description="用户填写的昵称")
    core_needs: List[str] = Field(default_factory=list, description="用户的核心需求列表, 例如: [\"emotional_support\", \"stress_relief\"]")
    interests: List[str] = Field(default_factory=list, description="用户的兴趣列表, 例如: [\"电影\", \"音乐\"]")
    communication_style_preference: str = Field("balanced", description="用户偏好的沟通风格, 例如: \"direct\", \"gentle\", \"humorous\"")
    allow_chat_analysis: bool = Field(False, description="用户是否允许聊天分析以改进画像和产品")
    initial_settings: Optional[Dict[str, Any]] = Field(None, description="可选的初始应用设置")

class FinalizeOnboardingData(BaseModel):
    userId: str
    message: str
    userProfile: Optional[UserProfileResponse] = None # Reusing existing UserProfileResponse
    userSettings: Optional[UserSettingsResponse] = None # Reusing existing UserSettingsResponse

class FinalizeOnboardingResponse(BaseModel):
    success: bool
    data: Optional[FinalizeOnboardingData] = None
    message: Optional[str] = None # For top-level error messages if needed

# <<< 新增结束 >>>
