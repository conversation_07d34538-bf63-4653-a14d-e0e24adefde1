# 心桥项目安全性、部署与技术预研

## 7.1 安全性考虑 (Security)

### 认证
所有需要用户身份的API均需通过Supabase Auth提供的JWT进行保护。`agno_api`在接收到请求后，必须验证JWT的有效性。

### 授权
数据库层面，将对所有表启用并配置严格的行级别安全（RLS）策略，确保用户只能访问自己的数据。

### API安全
- **Webhook安全**: 来自火山云的`/api/v1/chat/rtc_event_handler`回调请求，**必须**通过[火山引擎官方回调签名算法](https://www.volcengine.com/docs/6348/69820)进行验证。简单的共享密钥或IP白名单只能作为辅助手段，不能替代请求签名。
- **客户端RTC Token**: 客户端加入RTC房间所使用的Token，**必须**由我方后端根据[火山引擎Token鉴权规范](https://www.volcengine.com/docs/6348/70121)生成和签名。严禁在客户端生成或硬编码Token。

### 数据传输
所有客户端与API、API与云服务之间的通信必须强制使用HTTPS/WSS进行加密。

### 隐私保护
严格遵守中国的《个人信息保护法》，确保用户数据的安全存储和处理。

## 7.2 技术预研建议 (Critical)

**在正式开发前，强烈建议进行以下技术预研：**

### 1. 接口标准验证
- 使用火山引擎提供的[验证工具](https://www.volcengine.com/docs/6348/1415216)验证接口合规性
- 确认Webhook响应格式符合火山标准

### 2. 性能基准测试
- 测试端到端延迟是否能控制在1.5秒以内
- 验证记忆检索的性能表现
- 测试并发处理能力

### 3. 多轮对话验证
- 确认`HistoryLength`参数的实际效果
- 测试对话上下文的传递机制

### 4. 错误处理机制
- 测试各种异常情况的处理
- 验证错误恢复和重试机制

## 7.3 部署与运维 (Deployment & Operations)

### `agno_api` (FastAPI)
建议部署在支持Serverless的云平台上（如Vercel Serverless Functions, AWS Lambda, Google Cloud Run），以实现自动扩缩容，轻松应对流量波动。

### 客户端 (React Native/Expo)
通过Expo Application Services (EAS)进行构建和发布，简化提审App Store和Google Play的流程。

### CI/CD
在GitHub Actions中设置工作流，实现代码提交后自动运行测试、Linter，并自动部署到预览或生产环境。

### 监控与日志
- 集成应用性能监控(APM)工具
- 设置错误监控和告警机制
- 配置用户行为分析

## 7.4 数据模型设计 (Pydantic & Supabase)

核心数据表将包括：
- `users`: 用户基本信息
- `user_profiles`: 用户画像详情  
- `user_settings`: 用户应用设置
- `characters`: AI角色配置
- `chat_sessions`: 聊天会话
- `chat_messages`: 聊天消息

所有API的请求和响应体都应定义为严格的Pydantic模型。

## 7.5 环境配置建议

### 开发环境
- 本地开发使用Supabase本地实例
- 集成测试使用Supabase云端测试项目
- Mock火山引擎API进行本地开发

### 测试环境
- 完整的云服务集成
- 真实的火山引擎API测试
- 性能基准测试

### 生产环境
- 高可用性部署
- 自动备份和灾难恢复
- 严格的安全配置

## 7.6 风险评估与缓解

### 技术风险
- **火山引擎集成复杂度**
  - 缓解：提前进行技术预研和POC验证
  - 备选方案：准备自研语音方案作为备选

### 性能风险
- **端到端延迟超标**
  - 缓解：分层优化，从网络、存储、计算各层面优化
  - 监控：实时监控性能指标

### 合规风险
- **数据安全合规**
  - 缓解：严格按照法规要求设计数据架构
  - 审计：定期进行安全审计

### 业务风险
- **用户接受度**
  - 缓解：充分的用户测试和反馈收集
  - 迭代：快速响应用户需求进行产品调整 