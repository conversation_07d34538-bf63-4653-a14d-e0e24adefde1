# 心桥移动应用源文件结构文档

## 概述

本文档详细描述了心桥移动应用的源文件组织结构，遵循 Obytes 脚手架的最佳实践和项目特定的模块化设计，确保代码的可维护性、可扩展性和团队协作效率。

## 项目根目录结构

```
apps/mobile-app/
├── src/                     # 源代码根目录
│   ├── api/                 # API 层 - 网络请求和数据获取
│   ├── app/                 # 应用页面 - Expo Router 文件路由
│   ├── components/          # 组件库 - 可复用UI组件
│   ├── lib/                 # 工具库 - 核心功能和工具函数
│   ├── stores/              # 全局状态管理 (Zustand)
│   ├── translations/        # 国际化 - 多语言翻译文件
│   └── types/               # 类型定义 - 全局TypeScript类型
├── assets/                  # 静态资源
└── ... 配置文件
```

## 详细目录结构

### 📁 src/api - API 数据层

**职责**: 封装所有与后端的 HTTP 通信，使用 React Query 管理服务器状态。

```
src/api/
├── index.ts                 # API 模块统一导出
├── client.ts                # Axios 客户端实例和拦截器配置
├── crisis.ts                # 危机干预相关API Hooks
├── chat.ts                  # 实时对话、会话历史相关API Hooks
├── reminders.ts             # 提醒功能相关API Hooks
└── user.ts                  # 用户画像、设置相关API Hooks
```

### 📱 src/app - 页面路由层

**职责**: 应用的所有页面和路由，使用 Expo Router 基于文件的路由系统。

```
src/app/
├── _layout.tsx              # 根布局 - 全局Provider(QueryClient, ErrorBoundary等)
├── +html.tsx                # HTML模板 - Web端渲染配置  
├── (onboarding)/            # 引导流程页面组 (首次启动)
│   ├── _layout.tsx         # 引导流程布局
│   └── ... (多个引导步骤页面)
├── (app)/                   # 主应用页面组 (认证后)
│   ├── _layout.tsx         # Tab或主导航布局
│   ├── index.tsx           # 主对话界面
│   ├── reminders.tsx       # 提醒管理页面
│   └── settings/           # 设置页面组
│       ├── index.tsx
│       ├── theme.tsx
│       └── permissions.tsx
└── login.tsx                # 登录页面 (如果需要显式登录)
```

### 🧩 src/components - 组件库

**职责**: 存放所有可复用的UI组件，遵循原子设计思想。

```
src/components/
├── features/                # 业务功能级组件 (Organisms)
│   ├── chat/                # 对话功能相关组件
│   │   ├── ChatBubble.tsx
│   │   ├── ChatInputController.tsx
│   │   └── MessageList.tsx
│   ├── reminders/           # 提醒功能相关组件
│   │   ├── ReminderCard.tsx
│   │   └── ReminderConfirmation.tsx
│   └── crisis/              # 危机干预组件
│       └── BreathingExercise.tsx
└── ui/                      # 基础UI组件库 (Atoms/Molecules)
    ├── index.tsx           # UI组件统一导出
    ├── Button.tsx
    ├── Input.tsx
    ├── Card.tsx
    ├── Modal.tsx
    └── icons/              # 图标组件
```

### 🛠️ src/lib - 工具库

**职责**: 存放非UI的、可重用的核心逻辑和工具函数。

```
src/lib/
├── auth/                    # 认证模块 (Token管理, Supabase Auth封装)
├── chat/                    # 对话核心逻辑 (WebSocket/SSE客户端, 消息处理)
├── error-reporting.ts       # 错误上报服务 (Sentry)
├── i18n/                    # 国际化配置
├── permissions.ts           # 权限管理服务
├── notifications.ts         # 本地推送通知服务
├── storage.ts               # 本地存储 (MMKV)
└── utils.ts                 # 通用工具函数
```

### 🗂️ src/stores - 全局状态管理

**职责**: 使用 Zustand 定义和管理应用的全局状态。

```
src/stores/
├── authStore.ts             # 用户认证状态
├── chatStore.ts             # 对话消息和状态
├── settingsStore.ts         # 应用设置状态
└── reminderStore.ts         # 提醒数据状态
```

### 🌐 src/translations - 国际化文件

**职责**: 存放不同语言的翻译资源文件。

```
src/translations/
├── en.json                  # 英文翻译
└── zh.json                  # 中文翻译
```

### 📝 src/types - 类型定义

**职责**: 存放全局共享的 TypeScript 类型定义。

```
src/types/
└── index.ts                 # 全局类型定义 (如 User, Message 等)
```

## 文件命名规范

- **React组件**: `PascalCase.tsx` (如: `ChatBubble.tsx`)
- **页面/路由**: `kebab-case.tsx` (如: `reminders.tsx`)
- **Hooks**: `use-*.ts` (如: `use-chat-connection.ts`)
- **服务/工具**: `kebab-case.ts` (如: `notification-service.ts`)

## 代码组织最佳实践

1.  **分层清晰**: UI (components), 业务逻辑 (features), 状态 (stores), 数据 (api) 各司其职。
2.  **功能内聚**: 特定功能（如 `chat`）的所有相关代码（组件、hooks、服务）都组织在对应的功能模块下。
3.  **Hooks驱动**: 复杂的业务逻辑和副作用应封装在自定义Hooks中。
4.  **绝对路径导入**: 优先使用 `@/` 别名进行导入，提高可读性。

---
*本文档描述了心桥移动应用当前的源文件结构，旨在提供清晰、模块化和可扩展的代码组织方案。* 