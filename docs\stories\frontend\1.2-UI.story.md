# 故事 1.2-UI: 引导流程界面设计

## 基本信息
- **故事编号**: 1.2-UI
- **故事标题**: 引导流程界面设计
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: UX设计师 + 前端开发者
- **优先级**: 最高（P0）
- **工作量估计**: 5-8 个工作日
- **依赖关系**: 故事 1.1-UI (基础设计系统与组件库)
- **Status**: Approved

## 故事描述

作为UX设计师和前端开发者，我需要设计并实现"温暖的初见"引导流程的所有界面和交互体验，**以便** 为老年用户提供零负担的AI角色创建体验，建立情感连接的第一印象。

## 验收标准

### AC1: 启动与欢迎界面设计
- [ ] 完成应用启动页面的视觉设计和动效实现
- [ ] 设计温暖的首次欢迎界面，体现品牌温度和亲和力
- [ ] 实现平滑的启动到引导流程的过渡体验
- [ ] 所有界面符合适老化设计标准和无障碍要求

### AC2: 角色共创流程界面
- [ ] 设计完整的对话式角色创建界面流程
- [ ] 实现渐进式信息收集的可视化界面
- [ ] 创建AI角色预览和确认的交互界面
- [ ] 设计引导完成后的庆祝和成功界面

### AC3: 引导流程导航和状态
- [ ] 设计清晰的进度指示器和步骤导航
- [ ] 实现前进/后退/跳过等导航操作的界面
- [ ] 设计引导流程中的加载和等待状态界面
- [ ] 实现引导流程的中断和恢复界面

### AC4: 响应式适配和动效实现
- [ ] 完成引导界面在不同设备尺寸的适配
- [ ] 实现页面切换和状态变化的动画效果
- [ ] 优化引导流程的性能和加载体验
- [ ] 验证界面在低端设备上的表现

## Tasks / Subtasks

### 第一阶段：启动与欢迎界面设计 (1-2天)
- [ ] **启动页面设计实现** (AC1)
  - 设计应用启动页面（Splash Screen）的完整视觉
  - 创建品牌Logo展示和加载动画效果
  - 实现启动页面的响应式布局适配
  - 添加启动异常和网络错误的提示界面

- [ ] **欢迎引导首页设计** (AC1)
  - 设计"温暖的初见"欢迎页面布局
  - 创建老年用户友好的引导说明界面
  - 实现开始按钮和界面引导元素
  - 设计首次使用的温暖提示和帮助信息

### 第二阶段：角色创建核心界面 (2-3天)
- [ ] **角色创建对话界面** (AC2)
  - 设计对话式角色配置的主界面布局
  - 创建AI引导语言的展示区域
  - 实现用户输入区域的视觉设计
  - 设计对话历史和进度的可视化展示

- [ ] **角色信息收集界面** (AC2)
  - 设计角色命名输入界面的视觉样式
  - 创建角色特征选择的卡片式布局
  - 实现角色性格和声音选择的界面
  - 设计信息验证和错误提示的界面

### 第三阶段：导航控制和状态界面 (1-2天)
- [ ] **进度指示器设计** (AC3)
  - 设计引导流程的步骤指示器
  - 创建当前步骤和总体进度的可视化
  - 实现步骤间跳转的导航界面
  - 设计进度保存和恢复的提示界面

- [ ] **导航操作界面** (AC3)
  - 设计前进/后退/跳过按钮的视觉样式
  - 创建导航确认和警告的对话框
  - 实现快速跳转和步骤预览的界面
  - 设计导航操作的无障碍支持

### 第四阶段：优化和集成验证 (1天)
- [ ] **响应式适配优化** (AC4)
  - 验证所有界面在不同屏幕尺寸的表现
  - 优化横屏和竖屏模式的布局适配
  - 测试安全区域和刘海屏的适配效果
  - 调整界面元素的间距和比例

- [ ] **性能和动效优化** (AC4)
  - 优化页面切换动画的性能表现
  - 减少界面渲染和内存占用
  - 测试低端设备上的流畅度
  - 调整动画时长和缓动效果

## Dev Notes

CRITICAL: This is a **UI design and implementation story** focused on **onboarding flow interfaces**. 
**PREREQUISITE**: Story 1.1-UI (基础设计系统与组件库) must be completed first.

### 技术栈说明
- **框架**: React Native + Expo
- **状态管理**: Context API（引导流程状态）+ AsyncStorage（持久化）
- **动画库**: React Native Reanimated 3
- **导航**: Expo Router (file-based routing)
- **UI组件**: 基于1.1-UI故事的设计系统组件库
- **无障碍**: React Native Accessibility API

### 关键数据结构
```typescript
// 角色配置数据结构
interface CharacterProfile {
  id?: string;
  name: string;
  traits: string[];
  personality: '温和' | '活泼' | '智慧' | '幽默';
  voice: 'female-gentle' | 'male-warm' | 'neutral-friendly';
  avatar?: string;
  createdAt?: Date;
}

// 引导流程状态管理
interface OnboardingState {
  currentStep: number;
  totalSteps: number;
  character: Partial<CharacterProfile>;
  isComplete: boolean;
  canGoBack: boolean;
  hasStarted: boolean;
}

// 引导步骤定义
type OnboardingStep = 
  | 'welcome'
  | 'character-chat'
  | 'character-name'
  | 'character-traits'
  | 'character-preview'
  | 'completion';
```

### Core Pages to Implement:
```typescript
// 启动和欢迎页面
app/splash.tsx           // 应用启动页面
app/welcome.tsx          // 首次欢迎页面

// 引导流程页面群
app/(onboarding)/
├── _layout.tsx          // 引导流程布局 + Context Provider
├── character-chat.tsx   // 对话式角色创建
├── character-name.tsx   // 角色命名页面
├── character-traits.tsx // 角色特征选择
├── character-preview.tsx// 角色预览确认
└── completion.tsx       // 完成庆祝页面

// 状态管理和工具
lib/onboarding/
├── context.tsx          // OnboardingContext
├── types.ts             // 数据类型定义
└── utils.ts             // 引导流程工具函数
```

### API集成点
```typescript
// 角色管理API
POST /api/v1/characters        // 创建角色
PUT /api/v1/characters/:id     // 更新角色信息
GET /api/v1/characters/:id     // 获取角色详情

// 引导流程API
GET /api/v1/onboarding/progress    // 获取引导进度
POST /api/v1/onboarding/complete   // 完成引导流程
POST /api/v1/onboarding/save       // 保存引导进度
```

### 依赖的基础组件（来自1.1-UI）
- `Button` - 主要操作按钮和次要按钮
- `Input` - 角色命名输入框
- `Card` - 特征选择卡片
- `ProgressIndicator` - 引导进度条
- `Text` - 各级标题和正文文本
- `Icon` - 界面图标
- `Modal` - 确认对话框
- `SafeAreaView` - 安全区域布局

### 所需环境变量
```typescript
// 在 .env 或 app.config.ts 中配置
EXPO_PUBLIC_API_URL=         // API基础URL
EXPO_PUBLIC_ONBOARDING_TIMEOUT=30000  // 引导流程超时时间(ms)
EXPO_PUBLIC_ENABLE_ANALYTICS=true     // 是否启用引导流程分析
```

### 异常处理和边界情况
- **网络错误**: 显示友好的重试界面，支持离线模式下的本地保存
- **API超时**: 提供跳过当前步骤或稍后完成的选项
- **输入验证**: 实时验证用户输入，提供清晰的错误提示
- **应用切换**: 自动保存当前进度，支持从中断点恢复
- **低内存设备**: 优化动画性能，降级处理复杂动效
- **屏幕旋转**: 保持界面状态，适配横竖屏布局变化

## Testing

Dev Note: Story Requires the following tests:

- [ ] **Visual Regression Tests**: Storybook截图对比，覆盖所有引导页面的不同状态
- [ ] **User Flow Tests**: 完整引导流程的E2E自动化测试（包括中断恢复）
- [ ] **Accessibility Tests**: 引导界面的无障碍功能验证（屏幕阅读器、键盘导航）
- [ ] **State Management Tests**: OnboardingContext状态变化和持久化测试
- [ ] **API Integration Tests**: 角色创建和引导进度保存的API集成测试
- [ ] **Animation Performance Tests**: 页面切换动画在低端设备的性能测试

Manual Test Steps:
- 在不同设备尺寸（小屏手机、平板、横屏）上测试完整引导流程
- 验证适老化设计标准：字体大小、对比度、触摸目标尺寸
- 测试引导流程的中断和恢复功能（应用切换、网络断开）
- 验证无障碍功能：VoiceOver/TalkBack、动态字体支持
- 测试异常情况：API错误、网络超时、数据验证失败

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.1-UI（基础设计系统与组件库）已完成并交付
- [ ] 以下基础组件已可用：Button、Input、Card、ProgressIndicator、Text、Icon、Modal、SafeAreaView
- [ ] 品牌色彩系统和字体系统已确定并实现
- [ ] 引导流程的UX规范和交互设计已明确审批
- [ ] 适老化设计标准和无障碍要求已明确定义
- [ ] 开发环境已配置React Native Reanimated 3

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 引导流程界面设计完成并获得审批
- [ ] 所有引导页面代码实现完成

### 交付物 (Deliverables)
- [ ] **设计文件**: 引导流程界面的Figma设计稿
- [ ] **页面实现**: 所有引导相关页面的完整代码
- [ ] **组件扩展**: 引导流程特有的UI组件

## 后续依赖关系

### 🔗 此故事完成后解锁的故事：
- **故事1.2**: 无感身份认证与角色创建流程（功能实现）

## 相关文档引用
- [UX设计规范 - 适老化设计标准](../../prd/ux-design.md#适老化设计标准) - 引导界面的字体大小、对比度要求
- [UX设计规范 - 无障碍设计指南](../../prd/ux-design.md#无障碍设计) - 语音导航和屏幕阅读器支持
- [UX设计规范 - 情感化设计原则](../../prd/ux-design.md#情感化设计) - 温暖亲和的视觉风格指导
- [基础设计系统 - Button组件](./1.1-UI.story.md#button组件) - 主要和次要按钮样式
- [基础设计系统 - Input组件](./1.1-UI.story.md#input组件) - 角色命名输入框样式
- [基础设计系统 - Card组件](./1.1-UI.story.md#card组件) - 特征选择卡片布局
- [基础设计系统 - 颜色系统](./1.1-UI.story.md#颜色系统) - 品牌色彩和语义色彩
- [基础设计系统 - 字体系统](./1.1-UI.story.md#字体系统) - 标题和正文字体规范 