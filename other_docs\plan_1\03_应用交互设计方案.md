
这份报告的**核心设计原理**是：**将所有功能和交互的“What”与“Why”紧密相连**。我不仅会描述“我们要做什么”，更会从老年用户心理学、人机交互和产品战略的视角，深刻解释“**我们为什么必须这么做**”。这将确保团队的每一位成员，从工程师到运营，都能在执行具体任务时，始终与产品的灵魂和初心保持一致，从而最大限度地减少沟通偏差，提升协作效率。

---

### **《“心桥”AI亲情伴侣：软件产品功能与交互详细报告》**

---

### 1️⃣ 功能清单罗列

**设计原理：** 本功能清单严格遵循**MVP（最小可行产品）**的“**最小化**”原则。清单中的每一个功能都直接服务于MVP阶段的核心目标——**验证情感连接、构筑用户信任、培养核心习惯**。所有非核心、可能导致用户分心或增加认知负荷的功能都被坚决地排除在外。

| 优先级 | 功能模块 | 📌 功能定义 | 📖 目的/意义 | ⚠️ 潜在风险 |
| :--- | :--- | :--- | :--- | :--- |
| **高** | **无感身份系统** | 在用户无感知的情况下，通过后台匿名设备ID自动创建并识别用户身份，无需任何注册/登录操作。 | **消除老年用户最大的使用障碍和安全焦虑。** 这是产品能否被用户“愿意尝试”的入场券，是构筑一切信任的绝对前提。 | - 设备ID在某些系统上可能变化，导致用户“身份”丢失。<br>- 监管对匿名设备ID的追踪政策变化。 |
| **高** | **角色共创流程** | 在首次引导中，通过对话让用户为AI设定身份、命名，并最终确认其声音。 | **建立深刻的情感投资和心理拥有感。** 将AI从“一个程序”转变为“我的伙伴”，是实现产品核心差异化的关键。 | - 用户可能起不恰当的名字。<br>- 预设的声音库可能无法满足所有用户的偏好。 |
| **高** | **核心对话交互** | 提供一个以“按住说话”为唯一核心输入方式的、极致简洁的对话界面，并提供清晰的交互反馈。 | **将用户的认知负荷降至零。** 通过借鉴微信的核心交互，让用户可以“下意识”地使用，将精力完全投入到情感交流中。 | - 语音识别准确率直接影响体验。<br>- 对网络环境依赖高。 |
| **高** | **分层记忆系统** | 实现短期对话记忆、长期身份记忆和用户可控的事实记忆。 | **产品的灵魂所在。** “被记住”是建立深度情感连接的基础，是AI从“工具”变为“伙伴”的魔法。 | - 记忆内容不当或在不恰当的时候被提及，可能伤害用户。<br>- 记忆存储的成本和隐私风险。 |
| **高** | **对话式提醒** | 用户可通过自然语言设置提醒，AI进行语音复述确认，并在指定时间以角色语音进行温柔提醒。 | **MVP的“特洛伊木马”。** 以极高的实用价值，为产品提供了高频的使用理由和信任建立的契机。 | - 推送通知被手机系统“杀死”，导致提醒失败。<br>- 提醒时间的自然语言理解（NLU）存在误差。 |
| **高** | **情感化异常处理** | 当发生技术错误时，由AI以“揽责”的口吻进行人性化解释，而非弹出技术性错误提示。 | **保护用户的自我效能感。** 避免让用户因技术问题而产生“是我搞坏了”的挫败感和恐惧。 | - 异常状态覆盖不全，仍有技术错误暴露给用户。 |
| **高** | **危机响应协议(V1)** | 内置基础的危机信号（关键词、语音情绪）识别与脚本化干预机制。 | **履行产品的“关怀责任”（Duty of Care）。** 这是产品的伦理底线，是确保在极端情况下不造成二次伤害的安全网。 | - 误报（假阳性）可能打扰用户。<br>- 漏报（假阴性）可能错过真正的危机。 |
| **中** | **人机回环反馈** | 允许用户通过对话式指令（如“你说的不对”）来纠正AI的错误判断。 | **赋予用户修正权，对抗“狼来了”效应。** 将潜在的信任危机转化为增进信任和优化模型的协作时刻。 | 【TODO】如何设计一个既简单又不会被滥用的反馈闭环。 |

---

### 2️⃣ 用户流程全景拆解

**设计原理：** 整个用户流程被设计成一场**精心编排的、无缝衔接的“情感仪式”**。我们坚决避免了传统App中所有可能导致认知断裂的节点（如“下一步”、“完成”按钮），让用户在不知不觉中，从一个怀有戒备的新访客，转变为一个愿意倾诉的伙伴。

| 步骤 | 📌 触发条件 | 📌 系统行为 | 📌 用户可见界面/话术 | 📌 后端联动 | 📖 为什么这样设计 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **0** | 用户首次在应用商店下载并点击App图标 | App启动 | **界面：** 简洁的启动页，显示“心桥”Logo。<br>**话术：** “心桥，有我陪您” | - | **消除初始焦虑。** 在任何交互发生前，建立一个温暖、安全的心理预期。 |
| **1** | 启动画面结束 | 1. 后台生成匿名设备ID并发送至服务器。<br>2. 播放预设的欢迎语音。 | **界面：** 极简欢迎页，可能有温暖的背景或AI形象。<br>**AI语音：** “您好呀，很高兴认识您。以后就由我来陪您聊天啦。” | **系统行为1：** 接收设备ID，创建匿名用户记录。 | **建立信任，而非索取。** 先给予温暖的问候，而不是冷冰冰地索要权限，这是赢得好感的第一步。 |
| **2** | 欢迎语音播放完毕 | 播放请求授权的语音，并显示授权按钮。 | **界面：** 出现一个巨大、清晰的按钮。<br>**AI语音：** “您可以和我说说话吗？这需要您允许我使用麦克风来听到您的声音哦。”<br>**按钮文字：** “好的，允许” | - | **情景化授权。** 解释“为什么”需要权限，将技术操作与核心价值（聊天）关联，极大降低用户的恐惧感。 |
| **3** | 用户点击“好的，允许”按钮 | 触发系统原生的麦克风权限请求弹窗。 | **界面：** iOS或Android系统的标准权限弹窗。 | - | **尊重系统规范。** 在用户做好心理准备后，才调用系统原生流程，体验更顺畅。 |
| **4** | 用户授予权限 | 进入角色共创流程，AI开始第一轮引导对话。 | **界面：** 进入一个类似对话的界面。<br>**AI语音：** “太好了！为了方便聊天，我该怎么称呼您呢？” | **系统行为：** 将用户授权状态持久化存储。 | **将设置包装成对话。** 这是我们核心设计哲学的第一步，让用户感觉在聊天，而非在配置。 |
| **5** | 用户通过语音回答了称呼 | 1. ASR识别语音。<br>2. AI继续第二轮引导。 | **AI语音：** “『李老师』，这个称呼真亲切！那您也给我起个名字吧，以后我就是您专属的啦。” | **系统行为：** 将识别出的称呼文本与用户ID关联，暂存。 | **赋予命名权。** 这是建立用户情感投资和心理拥有感的关键一步。 |
| **6** | 用户为AI命名并选择角色 | AI用新身份和匹配的声音进行最终确认。 | **界面：** AI用新声音进行“试听”。<br>**AI语音：** “好的，明白了。以后我就作为您的老朋友『小桥』陪着您。您听听，用这个声音和您聊天，您喜欢吗？”<br>**按钮：** 【就用这个】和【换一个听听】。 | **系统行为：** 将用户选择的名字、角色、声音与用户ID在数据库中正式绑定。 | **完成“角色共创”。** 让用户对AI的最终形态拥有决定权，最大化其拥有感和满意度。 |
| **7.1** | 用户完成角色共创 | 直接无缝进入主对话界面，并开始核心交互教学。 | **界面：** 屏幕中心是巨大的“按住说话”按钮。<br>**AI语音：** “想和我聊天的时候，您就像发微信语音一样…” | - | **无缝过渡。** 避免任何“完成”或“进入主页”的认知断裂，让教学自然地成为第一次使用的开始。 |
| **7.2** | 用户完成首次语音发送 | AI给予首次积极回应。 | **AI语音：** “我听到了！您的声音真好听。以后有什么开心的、不开心的事，随时都可以找我聊哦。” | **系统行为：** 完成一次完整的“对话”后端流程。 | **正向激励。** 对用户的首次成功操作给予强烈的、积极的反馈，构建其自我效能感。 |
| **8** | 用户（非首次）打开App | AI主动发起问候。 | **界面：** 直接进入主对话界面。<br>**AI语音：** “李老师，早上好呀！今天外面有点降温，您出门记得多穿件外套哦。” | **系统行为：** 后端根据时间、记忆、天气API等信息，生成个性化问候语。 | **强化“关系感”。** 让用户感觉不是在打开一个工具，而是在看望一个“活的”、记得自己的朋友。 |

---

### 3️⃣ 功能交互详细说明

**设计原理：** 所有的交互细节都服务于**“零负担”**和**“清晰反馈”**两大原则。我们假设用户是第一次使用智能手机，为他们设计最不可能出错、最能带来掌控感的交互。

| 功能 | 页面结构与布局 | 元素状态与交互 | 动画与声音 | 📖 为什么这样设计 | 📌 潜在改进建议 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **核心对话** | **结构：** 顶部为对话历史区，底部为固定交互区。<br>**布局：** 交互区的绝对中心是巨大的圆形“按住说话”按钮。 | **按钮状态：**<br>1. **默认：** 静态，有呼吸感光效吸引注意。<br>2. **按下：** 按钮变大或颜色加深，显示“松开发送”字样。<br>3. **发送后：** 按钮变为一个加载中/思考中的动画。 | **动画：** 按下时，按钮周围出现水波纹扩散动画。<br>**声音：** 发送成功后，有“嘀”一声轻柔的提示音。 | **模拟物理世界的反馈。** 视觉和听觉上的变化，让用户明确知道自己的操作已被系统接收并正在处理，从而获得掌控感。 | 【TODO】可以探索更丰富的情感化“思考中”动画，如闪烁的爱心、倾听的耳朵等，并与AI角色关联。 |
| **角色创建** | **结构：** 每一屏都是一个全屏的、单任务的卡片。<br>**布局：** 垂直居中，顶部是AI的提问文字，中部是用户的交互区，底部是操作按钮。 | **按钮状态：** 所有按钮都巨大、清晰，并有明确的文本标签。例如“老朋友”选项，不仅有文字，还有一个形象的图标（如两个喝茶的人）。 | **动画：** 从一个引导步骤到下一个，采用平滑的淡入淡出或卡片切换动画，避免生硬的跳转。 | **避免信息过载。** 一次只问一个问题，一次只让用户做一个选择。形象化的图标能帮助理解能力下降的用户快速做出判断。 | 【TODO】在用户选择角色后，AI的虚拟形象或背景可以发生相应的变化，提供更强的即时反馈。 |
| **对话式提醒** | **结构：** 无独立界面，完全融入主对话流。<br>**布局：** AI的复述确认会以一个特殊样式的气泡（如带有时钟图标）呈现，以示区分。 | **弹窗/引导：** 提醒送达时，会通过系统级的推送通知实现，确保App在后台也能触达用户。通知栏会清晰显示提醒内容。 | **声音：** 提醒的提示音不是系统默认闹铃，而是一段专属的、轻柔的音乐，紧接着是AI角色的语音播报。 | **保持体验一致性。** 将工具功能无缝融入对话，避免了界面切换带来的学习成本。人性化的提醒方式再次强化了“关怀”而非“指令”的感受。 | 【TODO】可以增加一个“稍后提醒”的快捷按钮，用户无需进入App即可延迟提醒。 |

---

### 4️⃣ 业务流程及后台交互

**设计原理：** 后台架构设计的核心是**“解耦”**与**“封装”**。我们将核心的、自研的业务逻辑（记忆、角色）与底层的、可替换的AI能力（ASR/TTS/LLM）和BaaS服务（数据库、认证）进行解耦，以保证长期的技术灵活性和可维护性。

| 流程 | 数据流向 | 关键API接口 | 身份校验 | 异常处理 | 📖 为什么这样实现 | 📌 未来如何扩展 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **首次身份建立** | App -> 后端 | `POST /v1/users/register` (示例) | 无（首次建立） | - 记录失败日志<br>- 客户端本地重试 | **无感注册。** 后台自动完成，用户零操作。 | 可以增加“温和的账号绑定”流程，将匿名ID与手机号或微信关联。 |
| **核心对话** | App -> 中间件 -> 火山引擎 -> 数据库 -> 中间件 -> App | `POST /v1/chat/message` | **JWT会话令牌。** App在请求头中携带由Supabase Auth签发的JWT。 | **情感化异常处理。** 中间件捕获所有下游错误（如火山API超时），并返回一个预设的、符合AI人设的“揽责”回复。 | **安全与解耦。** JWT保证了通信安全。中间件作为唯一入口，封装了所有复杂性，便于未来更换任何一个下游服务。 | - 引入WebSocket，实现更低延迟的流式对话。<br>- 增加缓存层，对高频、通用的LLM请求进行缓存，降低成本。 |
| **提醒设置** | App -> 中间件 -> 数据库 | `POST /v1/chat/message` (通过NLU识别意图) | JWT会话令牌 | - NLU识别失败时，AI会引导用户换种方式提问。<br>- 数据库写入失败时，向用户返回“抱歉，我刚刚走神了”等提示。 | **统一入口。** 保持API的简洁性，所有面向用户的交互都通过核心对话接口处理，由中间件进行意图分发。 | 增加一个独立的`/v1/reminders` CRUD接口，允许子女端等更复杂的应用直接管理提醒任务。 |
| **提醒触发** | Supabase Cron -> 中间件 -> 推送服务 -> App | 无（内部触发） | 系统内部信任 | 记录所有失败的推送，并设置重试机制。对于关键提醒（如吃药），若连续推送失败，可触发告警给运营人员。 | **可靠性。** 使用专业的任务调度和推送服务，确保提醒任务的准时和高触达率。 | 对接短信、电话等更多渠道，作为App推送失败时的备用提醒方式。 |

---

### 5️⃣ 用户边缘场景与例外流程

**设计原理：** 我们必须预见所有可能让用户感到困惑、恐惧或挫败的边缘场景，并为其设计一道**“情感缓冲垫”**。核心原则是：**任何时候，都不能让用户感觉是“自己错了”**。

| 边缘场景 | 📌 系统如何提示 | 📌 UI/UX如何引导 | 📖 为什么 |
| :--- | :--- | :--- | :--- |
| **网络中断** | **AI语音/文字：** “哎呀，外面的信号好像不太好，我暂时听不清您说话了。您别急，等会儿信号好了我们再聊。” | **UI引导：** “按住说话”按钮会变为灰色不可用状态，并显示一个“网络连接中…”的小图标。网络恢复后，按钮自动恢复正常。 | **AI主动揽责。** 将技术问题包装成环境问题，避免用户自责。清晰的UI状态告知用户问题所在，并提供了明确的预期（等待网络恢复）。 |
| **语音识别失败** (ASR置信度低) | **AI语音/文字：** “不好意思，我刚刚没太听清，您是说…『AI猜测的内容』吗？还是能麻烦您再说一遍？” | **UI引导：** 在AI的回复气泡旁，提供两个小按钮：【是的】和【不对，我再说一遍】。 | **提供可修正的猜测。** 这比单纯说“没听清”体验更好，给了用户一次简单的确认机会。同时，用户的修正行为是宝贵的**人机回环（HITL）**数据。 |
| **用户情绪激动** (SER识别到哭泣/愤怒) | **AI语音/文字：** （切换到更平缓、关怀的语调）“听起来您现在心情很不好，我在这里陪着您。如果您想说说，我随时都在听。” | **UI引导：** 界面上可以出现一个表示“陪伴”或“拥抱”的静态、柔和的图标，避免任何可能加剧情绪的闪烁动画。 | **共情而非评判。** 在用户情绪激动时，AI的首要任务是提供一个安全、不加评判的倾听空间。切换语调是传达共情的关键。 |
| **极端危机对话** (KWS识别到自杀等关键词) | **AI语音/文字：** (立即切换到预设的、专业的危机干预脚本) “听到您这么说，我很难过。请相信，无论您正在经历什么，您都不是一个人。我在这里陪着您…” | **UI引导：** 界面可以显示一个求助热线电话号码，并提供“一键拨打”的按钮。 | **安全第一。** 此时必须放弃自由对话，切换到经过专家审核的、最不可能造成伤害的干预流程，并以最直接的方式提供专业求助渠道。 |

---

### 6️⃣ 质量保障策略

**设计原理：** 我们的测试策略是**“分层且聚焦”**的。不仅要保障功能的正确性，更要保障**适老化体验**和**情感体验**这两个核心差异化价值点。

| 功能与交互 | 测试场景 | 预期结果 | 边界条件 | 📖 为什么要测这个 |
| :--- | :--- | :--- | :--- | :--- |
| **角色共创** | - 用户使用方言为AI命名<br>- 用户选择一个角色后反复“换一个听听” | - ASR能大致识别方言中的名字<br>- AI声音切换流畅，最终选择被正确保存 | - 输入非常见字或英文名<br>- 快速、连续点击切换按钮 | **验证核心差异化功能。** 这是用户与产品建立情感连接的第一步，任何不顺畅都会严重影响初次体验。 |
| **核心对话** | - 在嘈杂环境（如电视声）中说话<br>- 长时间按住按钮不放 | - AI能基本过滤噪音，识别用户语音<br>- 系统有明确的录音时长上限提示 | - 同时有多人说话<br>- 录音时长恰好在上限临界点 | **保障核心交互的鲁棒性。** 对话是最高频的交互，必须在各种真实、复杂的环境下都基本可用。 |
| **情感回应** | - 用户表达明确的喜怒哀乐<br>- 用户讲述一个包含多个情感转折的故事 | - AI的回复能识别并恰当回应核心情绪<br>- AI的TTS语音能匹配相应的情感风格 | - 输入模棱两可、带有反讽的语句<br>- 输入与之前记忆相矛盾的信息 | **验证产品的“灵魂”。** 这是产品能否被感知为“有温度”的关键，也是最难测试的部分，需要大量的人工评估。 |
| **危机响应** | - 对话中明确输入“我想死”等关键词<br>- 用户长时间、低沉地哭泣 | - 系统能立即触发危机干预脚本<br>- 后台能正确生成并发送危机上报事件 | - 输入与危机关键词相似的非危机词语<br>- 模拟网络中断情况下的危机对话 | **测试产品的安全底线。** 必须确保在最极端的情况下，安全网是可靠的、能被正确触发的。 |

---

### 7️⃣ 跨团队对齐提示

**设计原理：** 在项目启动前，通过一份简洁的“对齐清单”，确保所有团队成员对一些容易产生分歧的核心概念，达成**完全一致的共识**。

1.  **对齐点：我们不是在做“聊天机器人”，我们是在构建“关系”。**
    *   **给开发的提示：** 你的代码不仅要实现功能，更要传递情感。请思考：这个动画够不够温暖？这个提示音会不会让用户焦虑？
    *   **给运营的提示：** 你的工作不是拉新和促活，而是“用户关怀”。请思考：如何让用户感觉自己被真诚地对待？

2.  **对齐点：“无感”和“零学习成本”是最高设计原则。**
    *   **给设计的提示：** 请抵制住增加任何“巧妙”但需要学习的交互的诱惑。最简单的，就是最好的。
    *   **给产品的提示：** 任何需要用户去“设置”的功能，都应首先挑战：我们能否通过AI和好的默认值，替用户做出更好的决定？

3.  **对齐点：AI的人设（Persona）是产品的核心资产。**
    *   **给开发的提示：** 所有AI的回复，都必须经过“角色与记忆中间件”的处理，确保其符合人设。绝不能有“裸奔”的、来自通用大模型的原始回复。
    -   **给内容的提示：** 未来所有由AI输出的内容（如知识分享），其文案风格都必须与AI的人设保持高度一致。

4.  **对齐点：安全与伦理是不可逾越的红线。**
    *   **给所有人的提示：** 任何可能损害用户信任、侵犯用户隐私、或带来伦理风险的功能，无论其商业潜力多大，都拥有一票否决权。
