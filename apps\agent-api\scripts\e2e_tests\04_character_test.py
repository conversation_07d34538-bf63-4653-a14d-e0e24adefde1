#!/usr/bin/env python3
"""
角色管理API测试
测试接口:
- GET /api/v1/characters
- GET /api/v1/characters/{character_id}
"""

import asyncio
import sys
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class CharacterTester(BaseAPITester):
    """角色管理API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("04_character", base_url)
        self.character_id = None

    async def test_get_characters_list(self):
        """测试获取角色列表API"""
        self.logger.info("🎭 测试获取角色列表API")

        response = await self.make_request(
            "GET",
            "/api/v1/characters",
            params={"page": 1, "limit": 10}
        )

        # 验证响应结构
        if "data" in response:
            self.logger.info("✅ 响应包含数据列表")

            if len(response["data"]) > 0:
                self.logger.info(f"✅ 获取到 {len(response['data'])} 个角色")

                # 保存第一个角色ID用于后续测试
                first_character = response["data"][0]
                self.character_id = first_character.get("id")

                # 验证角色数据结构
                expected_fields = ["id", "name"]
                for field in expected_fields:
                    if field in first_character:
                        self.logger.info(f"✅ 角色数据包含字段: {field}")
                    else:
                        self.logger.error(f"❌ 角色数据缺少字段: {field}")

                # 可选字段验证
                optional_fields = ["description", "voice_id", "personality", "is_default"]
                for field in optional_fields:
                    if field in first_character:
                        self.logger.info(f"✅ 角色数据包含可选字段: {field}")
                    else:
                        self.logger.info(f"ℹ️ 角色数据不包含可选字段: {field}")
            else:
                self.logger.warning("⚠️ 角色列表为空")
        else:
            self.logger.error("❌ 响应缺少数据列表")

        # 验证分页信息
        if "pagination" in response:
            self.logger.info("✅ 响应包含分页信息")

            pagination = response["pagination"]
            pagination_fields = ["page", "limit", "total", "pages"]
            for field in pagination_fields:
                if field in pagination:
                    self.logger.info(f"✅ 分页信息包含字段: {field}")
                else:
                    self.logger.error(f"❌ 分页信息缺少字段: {field}")
        else:
            self.logger.error("❌ 响应缺少分页信息")

    async def test_get_character_details(self):
        """测试获取角色详情API"""
        if not self.character_id:
            self.logger.warning("⚠️ 跳过角色详情测试，因为没有角色ID")
            return

        self.logger.info(f"🔍 测试获取角色详情API: {self.character_id}")

        response = await self.make_request(
            "GET",
            f"/api/v1/characters/{self.character_id}"
        )

        # 验证响应结构
        if response and "error" not in response:
            self.logger.info("✅ 成功获取角色详情")

            # 验证角色详情数据结构
            expected_fields = ["id", "name"]
            for field in expected_fields:
                if field in response:
                    self.logger.info(f"✅ 角色详情包含字段: {field}")
                    if field == "id" and response[field] == self.character_id:
                        self.logger.info("✅ 角色ID匹配")
                else:
                    self.logger.error(f"❌ 角色详情缺少字段: {field}")

            # 可选字段验证
            optional_fields = ["description", "voice_id", "personality", "is_default", "created_at"]
            for field in optional_fields:
                if field in response:
                    self.logger.info(f"✅ 角色详情包含可选字段: {field}")
                else:
                    self.logger.info(f"ℹ️ 角色详情不包含可选字段: {field}")
        else:
            self.logger.error("❌ 获取角色详情失败")

    async def test_get_character_not_found(self):
        """测试获取不存在角色的详情API"""
        self.logger.info("🔍 测试获取不存在角色详情API")

        fake_character_id = "non-existent-character-id"

        response = await self.make_request(
            "GET",
            f"/api/v1/characters/{fake_character_id}",
            expected_status=404
        )

        # 验证404响应
        if response:
            self.logger.info("✅ 不存在角色返回404状态码")
        else:
            self.logger.error("❌ 不存在角色未返回预期状态码")

    async def test_characters_pagination(self):
        """测试角色列表分页功能"""
        self.logger.info("📄 测试角色列表分页功能")

        # 测试不同的分页参数
        test_cases = [
            {"page": 1, "limit": 5},
            {"page": 2, "limit": 3},
            {"page": 1, "limit": 20},
        ]

        for params in test_cases:
            self.logger.info(f"测试分页参数: {params}")

            response = await self.make_request(
                "GET",
                "/api/v1/characters",
                params=params
            )

            if "data" in response and "pagination" in response:
                pagination = response["pagination"]

                # 验证分页参数是否正确返回
                if pagination.get("page") == params["page"]:
                    self.logger.info(f"✅ 页码参数正确: {params['page']}")
                else:
                    self.logger.error(f"❌ 页码参数错误: 期望{params['page']}, 实际{pagination.get('page')}")

                if pagination.get("limit") == params["limit"]:
                    self.logger.info(f"✅ 限制参数正确: {params['limit']}")
                else:
                    self.logger.error(f"❌ 限制参数错误: 期望{params['limit']}, 实际{pagination.get('limit')}")

                # 验证返回的数据量
                actual_count = len(response["data"])
                expected_count = min(params["limit"], pagination.get("total", 0))
                if actual_count <= expected_count:
                    self.logger.info(f"✅ 返回数据量合理: {actual_count}")
                else:
                    self.logger.error(f"❌ 返回数据量异常: {actual_count}")
            else:
                self.logger.error("❌ 分页测试响应格式错误")

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始角色管理API测试")

        # 确保用户已认证
        await self.ensure_authenticated()

        tests = [
            ("获取角色列表", self.test_get_characters_list),
            ("获取角色详情", self.test_get_character_details),
            ("获取不存在角色", self.test_get_character_not_found),
            ("角色列表分页", self.test_characters_pagination),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(0.5)  # 测试间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='角色管理API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with CharacterTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
