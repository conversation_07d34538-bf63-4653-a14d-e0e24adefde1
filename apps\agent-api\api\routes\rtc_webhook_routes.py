"""
RTC Webhook路由 - 处理火山引擎RTC事件回调
"""
import logging
import json
import time
from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request
from fastapi.responses import JSONResponse
from datetime import datetime, timezone

from api.models.rtc_models import (
    RtcWebhookRequest,
    RtcWebhookResponse,
    RtcWebhookErrorResponse,
    VolcengineStandardErrorResponse,
    VolcengineResponseMetadata,
    VolcengineErrorInfo,
    FunctionCallPayload
)
from api.services.chat_orchestration_service import (
    ChatOrchestrationService,
    get_chat_orchestration_service
)
from api.utils.volcengine_auth import (
    VolcengineSignatureValidator,
    create_volcengine_validator
)
from api.settings import settings, get_volcengine_ip_whitelist
from db.session import get_session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from api.models.schema_models import ToolCall

import struct
import base64

logger = logging.getLogger(__name__)

router = APIRouter(tags=["RTC Webhook"])

# 依赖注入
OrchestratorDep = Annotated[ChatOrchestrationService, Depends(get_chat_orchestration_service)]
DatabaseDep = Annotated[AsyncSession, Depends(get_session)]


def get_volcengine_validator() -> VolcengineSignatureValidator:
    """获取火山引擎签名验证器实例"""
    if not settings.VOLCENGINE_WEBHOOK_SECRET:
        logger.warning("未配置VOLCENGINE_WEBHOOK_SECRET，签名验证将被跳过")
        # 返回一个虚拟验证器，用于开发环境
        return create_volcengine_validator("dummy-secret-for-dev", settings.VOLCENGINE_SIGNATURE_TOLERANCE)

    return create_volcengine_validator(
        settings.VOLCENGINE_WEBHOOK_SECRET,
        settings.VOLCENGINE_SIGNATURE_TOLERANCE
    )


ValidatorDep = Annotated[VolcengineSignatureValidator, Depends(get_volcengine_validator)]


def create_volcengine_error_response(
    request_id: str,
    error_code: str,
    error_message: str,
    action: str = "RtcWebhookHandler",
    version: str = "2024-12-01"
) -> VolcengineStandardErrorResponse:
    """创建符合火山引擎标准的错误响应"""
    error_info = VolcengineErrorInfo(
        Code=error_code,
        Message=error_message
    )

    response_metadata = VolcengineResponseMetadata(
        RequestId=request_id,
        Action=action,
        Version=version,
        Error=error_info
    )

    return VolcengineStandardErrorResponse(
        ResponseMetadata=response_metadata,
        Result={}
    )


@router.post("/rtc_event_handler", response_model=RtcWebhookResponse)
async def handle_rtc_event(
    webhook_request: RtcWebhookRequest,
    request: Request,
    orchestrator: OrchestratorDep,
    background_tasks: BackgroundTasks,
    db: DatabaseDep,
    validator: ValidatorDep
) -> JSONResponse:
    """
    接收火山RTC事件回调，处理并返回AI回复用于TTS。

    Args:
        webhook_request: RTC Webhook请求数据
        request: FastAPI原始请求对象
        orchestrator: 对话编排服务
        background_tasks: 后台任务处理
        db: 数据库会话
        validator: 火山引擎签名验证器

    Returns:
        包含AI回复的JSON响应
    """
    start_time = time.time()
    request_id = webhook_request.request_id or f"rtc_{int(time.time() * 1000)}"

    try:
        logger.info(f"[{request_id}] 收到RTC事件: {webhook_request.event_type}")

        # 1. 安全验证实现 - 🔒 始终执行签名验证
        try:
            # 获取原始请求体
            request_body = await request.body()

            # 🔒 无条件验证签名 - 移除可禁用选项提升安全性
            validator.verify_signature(request, request_body)

            # 验证IP白名单（如果配置了）
            allowed_ips = get_volcengine_ip_whitelist()
            if allowed_ips:
                validator.verify_ip_whitelist(request, allowed_ips)

            logger.info(f"[{request_id}] 安全验证通过")

        except HTTPException as auth_error:
            logger.error(f"[{request_id}] 安全验证失败: {auth_error.detail}")
            # 返回符合火山引擎标准的401未授权错误
            error_response = create_volcengine_error_response(
                request_id=request_id,
                error_code="AuthenticationFailed",
                error_message="签名验证失败"
            )
            return JSONResponse(
                status_code=auth_error.status_code,
                content=error_response.model_dump()
            )

        # --- 核心修复点：解析EventData payload ---
        webhook_request.parse_payload()
        logger.debug(f"[{request_id}] Webhook payload 已解析, 类型: {type(webhook_request.payload)}")

        # 2. 解析上下文信息
        custom_data = webhook_request.get_custom_data()
        user_id = webhook_request.get_user_id()
        session_id = webhook_request.get_session_id()
        character_id = webhook_request.get_character_id()

        # 3. 基于event_type的事件分发逻辑（故事1.14-B AC-4）
        if webhook_request.EventType == "VoiceChat":
            # VoiceChat是唯一的AI事件类型，需要解析EventData来判断具体的子事件类型
            try:
                event_data = json.loads(webhook_request.EventData)

                # 检查是否是Function Calling相关的事件
                if 'message' in event_data and isinstance(event_data.get('message'), list):
                    # 这可能是Function Calling事件
                    webhook_request.payload = FunctionCallPayload(message=event_data['message'])
                    return await _handle_function_call_event(
                        webhook_request, orchestrator,
                        user_id, session_id, request_id
                    )

                # 检查是否是包含用户语音文本的ASR结果事件
                elif (event_data.get('RunStage') == 'asrFinish' and
                      'UserMessage' in event_data and
                      event_data.get('UserMessage')):
                    # 用户说话结束，包含ASR识别的文本内容
                    # 创建ASR payload，包含用户说话的文本内容
                    from api.models.rtc_models import AsrPayload
                    webhook_request.payload = AsrPayload(
                        text=event_data['UserMessage'],
                        timestamp=str(event_data.get('EventTime', int(time.time() * 1000))),
                        is_final=True
                    )
                    return await _handle_asr_event(
                        webhook_request, orchestrator, background_tasks, db,
                        user_id, session_id, character_id, request_id, start_time
                    )

                # 其他VoiceChat子事件，按状态事件处理
                else:
                    return await _handle_voice_chat_status_event(
                        webhook_request, request_id
                    )

            except (json.JSONDecodeError, KeyError) as e:
                logger.error(f"[{request_id}] 解析EventData失败: {e}")
                # 默认按VoiceChat事件处理
                return await _handle_voice_chat_status_event(
                    webhook_request, request_id
                )

        elif webhook_request.EventType == "ASR_SENTENCE_END":
            # ASR语音识别结束事件 - 虽然不在官方文档标准列表中，但实际会被发送
            logger.info(f"[{request_id}] 处理ASR_SENTENCE_END事件")

            # ASR_SENTENCE_END事件的payload已经是正确的格式，直接调用处理函数
            return await _handle_asr_event(
                webhook_request, orchestrator, background_tasks, db,
                user_id, session_id, character_id, request_id, start_time
            )

        else:
            # 其他非AI相关的RTC事件类型 - 记录日志并返回成功
            logger.info(f"[{request_id}] 收到非AI事件类型: {webhook_request.EventType}")

        from api.models.rtc_models import RtcWebhookResponse
        response = RtcWebhookResponse(
            text="",  # 空回复
            status="success",
            request_id=request_id
        )

        return JSONResponse(
            status_code=200,
            content=response.model_dump()
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        total_duration = time.time() - start_time
        logger.error(f"[{request_id}] 处理RTC事件时发生错误: {str(e)}, 耗时: {total_duration:.3f}s", exc_info=True)

        # 返回符合火山引擎标准的错误响应，但不中断火山引擎的流程
        error_response = create_volcengine_error_response(
            request_id=request_id,
            error_code="InternalError",
            error_message="内部服务错误，请稍后再试"
        )

        return JSONResponse(
            status_code=503,
            content=error_response.model_dump()
        )


async def _handle_asr_event(
    webhook_request: RtcWebhookRequest,
    orchestrator: ChatOrchestrationService,
    background_tasks: BackgroundTasks,
    db: AsyncSession,
    user_id: str,
    session_id: str,
    character_id: str,
    request_id: str,
    start_time: float
) -> JSONResponse:
    """处理ASR文本识别事件"""

    # 确保payload是AsrPayload类型
    if not hasattr(webhook_request.payload, 'text'):
        raise HTTPException(status_code=400, detail="ASR事件缺少text字段")

    user_message = webhook_request.payload.text
    if not user_message:
        raise HTTPException(status_code=400, detail="用户消息不能为空")

    logger.info(f"[{request_id}] 处理ASR事件 - 用户: {user_id}, 会话: {session_id}, 消息: {user_message[:50]}...")

    # 记录消息处理开始时间
    message_start_time = time.time()

    # 异步保存用户消息到数据库
    background_tasks.add_task(
        _save_user_message_async,
        db_session=db,
        user_id=user_id,
        session_id=session_id,
        message=user_message,
        request_id=request_id
    )

    # 构建上下文并委托给编排服务
    context = {
        "userId": user_id,
        "sessionId": session_id,
        "characterId": character_id or "default",
        "eventType": webhook_request.event_type,
        "requestId": request_id
    }

    # 调用编排服务处理消息
    orchestration_start_time = time.time()
    ai_response = await orchestrator.handle_message(
        user_message=user_message,
        context=context
    )
    orchestration_end_time = time.time()

    # 记录性能指标
    orchestration_duration = orchestration_end_time - orchestration_start_time
    total_duration = time.time() - start_time

    logger.info(f"[{request_id}] ASR事件性能指标 - 编排服务耗时: {orchestration_duration:.3f}s, 总耗时: {total_duration:.3f}s")

    # 异步保存AI回复到数据库
    background_tasks.add_task(
        _save_ai_response_async,
        db_session=db,
        user_id=user_id,
        session_id=session_id,
        response=ai_response,
        request_id=request_id,
        processing_duration=orchestration_duration
    )

    # 构建响应
    from api.models.rtc_models import RtcWebhookResponse
    response = RtcWebhookResponse(
        text=ai_response,
        status="success",
        request_id=request_id
    )

    logger.info(f"[{request_id}] 成功处理ASR事件 - 回复长度: {len(ai_response)}, 总耗时: {total_duration:.3f}s")

    return JSONResponse(
        status_code=200,
        content=response.model_dump()
    )


async def _handle_function_call_event(
    webhook_request: RtcWebhookRequest,
    orchestrator: ChatOrchestrationService,
    user_id: str,
    session_id: str,
    request_id: str
) -> JSONResponse:
    """处理Function Calling工具调用事件 - 符合官方文档规范

    根据火山引擎官方文档，Function Calling通过以下方式接收：
    1. 火山引擎通过ServerMessageUrl发送HTTP POST请求
    2. 请求体包含message数组和signature字段（JSON格式）或二进制格式
    3. 解析message中的工具调用指令
    4. 执行本地工具后，通过UpdateVoiceChat返回结果
    """

    logger.info(f"[{request_id}] 开始处理Function Calling事件")

    try:
        # 1. 验证和解析Function Calling数据格式
        # 根据官方文档，Function Calling数据结构应该包含message和signature
        event_data = json.loads(webhook_request.EventData)

        # 检查是否是二进制消息格式（Base64编码）
        if 'message' in event_data and isinstance(event_data['message'], str):
            # 处理二进制消息格式
            try:
                # Base64解码
                binary_data = base64.b64decode(event_data['message'])
                tool_calls_data = _parse_binary_function_call_message(binary_data, request_id)

                if not tool_calls_data:
                    raise HTTPException(status_code=400, detail="二进制Function Calling消息解析失败")

            except Exception as e:
                logger.error(f"[{request_id}] 二进制消息解析失败: {e}")
                raise HTTPException(status_code=400, detail=f"二进制消息解析失败: {str(e)}")

        elif 'message' in event_data and isinstance(event_data['message'], list):
            # 处理JSON消息格式（向后兼容）
            tool_calls_data = event_data['message']
        else:
            raise HTTPException(status_code=400, detail="Function Calling事件缺少有效的message字段")

        # 验证signature（如果存在）
        if 'signature' in event_data:
            expected_signature = settings.VOLCENGINE_WEBHOOK_SECRET
            if event_data['signature'] != expected_signature:
                logger.warning(f"[{request_id}] Function Calling签名验证失败")
                raise HTTPException(status_code=401, detail="Function Calling签名验证失败")

        if not isinstance(tool_calls_data, list) or not tool_calls_data:
            raise HTTPException(status_code=400, detail="Function Calling message字段格式错误")

        logger.info(f"[{request_id}] 解析到 {len(tool_calls_data)} 个工具调用")

        # 2. 构建工具调用对象（按官方文档格式）
        tool_calls = []

        for tool_msg in tool_calls_data:
            # 官方格式：{"id": "call_cx", "type": "function", "function": {"name": "get_weather", "arguments": "{...}"}}
            tool_call_id = tool_msg.get('id')
            tool_type = tool_msg.get('type')
            function_info = tool_msg.get('function', {})

            if tool_type != 'function':
                logger.warning(f"[{request_id}] 跳过非function类型的工具调用: {tool_type}")
                continue

            function_name = function_info.get('name')
            arguments_str = function_info.get('arguments', '{}')

            if not tool_call_id or not function_name:
                logger.error(f"[{request_id}] 工具调用数据不完整: {tool_msg}")
                continue

            try:
                arguments = json.loads(arguments_str) if isinstance(arguments_str, str) else arguments_str
            except json.JSONDecodeError as e:
                logger.error(f"[{request_id}] 无法解析工具参数: {arguments_str}, 错误: {e}")
                continue

            tool_call = ToolCall(
                id=tool_call_id,
                name=function_name,
                arguments=arguments
            )
            tool_calls.append(tool_call)

        if not tool_calls:
            raise HTTPException(status_code=400, detail="没有有效的工具调用")

        # 3. 执行本地工具调用
        from api.services.tool_executor_service import get_tool_executor_service
        tool_executor = await get_tool_executor_service()

        # 设置执行上下文
        context = {
            "userId": user_id,
            "sessionId": session_id,
            "requestId": request_id
        }

        logger.info(f"[{request_id}] 开始执行工具调用")
        tool_results = await tool_executor.execute_tool_calls(tool_calls, context)

        # 4. 获取会话信息用于UpdateVoiceChat
        # 从custom字段获取会话信息
        custom_data = webhook_request.get_custom_data()
        room_id = custom_data.get('roomId')
        task_id = custom_data.get('taskId')

        if not room_id or not task_id:
            logger.error(f"[{request_id}] 缺少必要的会话信息: roomId={room_id}, taskId={task_id}")
            raise HTTPException(status_code=400, detail="缺少roomId或taskId信息")

        # 5. 将结果通过UpdateVoiceChat返回给火山引擎
        success_count = 0
        for result in tool_results:
            try:
                # 构建返回消息格式
                message_content = {
                    "ToolCallID": result.tool_call_id,
                    "Content": result.content
                }
                message_json = json.dumps(message_content, ensure_ascii=False)

                logger.info(f"[{request_id}] 调用UpdateVoiceChat: {result.tool_call_id}")

                # 调用UpdateVoiceChat API - 添加重试机制
                update_result = await _call_update_voice_chat_with_retry(
                    volcano_client=orchestrator.volcano_client,
                    room_id=room_id,
                    task_id=task_id,
                    message=message_json,
                    request_id=request_id,
                    max_retries=3
                )

                success_count += 1
                logger.info(f"[{request_id}] UpdateVoiceChat调用成功: {result.tool_call_id}")

            except Exception as e:
                logger.error(f"[{request_id}] UpdateVoiceChat调用失败: {result.tool_call_id}, 错误: {e}")
                # 继续处理其他结果，不中断整个流程

        if success_count == 0:
            raise HTTPException(status_code=500, detail="所有工具调用结果返回失败")

        # 6. 返回成功响应（不包含TTS文本，由RTC处理后续回复）
        response = RtcWebhookResponse(
            text="",  # Function Calling不需要返回文本
            status="success",
            request_id=request_id
        )

        logger.info(f"[{request_id}] Function Calling处理完成，成功返回 {success_count} 个结果")

        return JSONResponse(
            status_code=200,
            content=response.model_dump()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[{request_id}] Function Calling处理异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Function Calling处理失败: {str(e)}")


def _parse_binary_function_call_message(binary_data: bytes, request_id: str) -> list:
    """解析二进制Function Calling消息

    根据官方文档，二进制格式为：
    | magic number (4 bytes) | length (4 bytes, big-endian) | Tool_Calls (JSON) |
    magic number固定为 "tool"
    """
    try:
        if len(binary_data) < 8:  # 至少需要8字节的头部
            logger.error(f"[{request_id}] 二进制消息长度不足: {len(binary_data)}")
            return []

        # 解析magic number (前4字节)
        magic_number = binary_data[:4].decode('utf-8')
        if magic_number != 'tool':
            logger.error(f"[{request_id}] 无效的magic number: {magic_number}, 期望: tool")
            return []

        # 解析length (接下来4字节，大端序)
        length = struct.unpack('>I', binary_data[4:8])[0]

        # 验证长度
        if len(binary_data) != 8 + length:
            logger.error(f"[{request_id}] 消息长度不匹配: 期望 {8 + length}, 实际 {len(binary_data)}")
            return []

        # 解析Tool_Calls内容 (JSON格式)
        tool_calls_json = binary_data[8:8+length].decode('utf-8')
        tool_calls_data = json.loads(tool_calls_json)

        # 验证格式
        if isinstance(tool_calls_data, dict) and 'tool_calls' in tool_calls_data:
            return tool_calls_data['tool_calls']
        elif isinstance(tool_calls_data, list):
            return tool_calls_data
        else:
            logger.error(f"[{request_id}] 无效的tool_calls格式: {type(tool_calls_data)}")
            return []

    except Exception as e:
        logger.error(f"[{request_id}] 二进制消息解析异常: {e}")
        return []


async def _call_update_voice_chat_with_retry(
    volcano_client,
    room_id: str,
    task_id: str,
    message: str,
    request_id: str,
    max_retries: int = 3
):
    """
    调用UpdateVoiceChat API with重试机制

    实现架构师建议的重试机制：
    - 3次重试，5秒超时
    - 指数退避
    - 详细错误记录
    """
    import asyncio

    for attempt in range(max_retries):
        try:
            # 使用asyncio.wait_for设置超时
            result = await asyncio.wait_for(
                volcano_client.update_voice_chat(
                    room_id=room_id,
                    task_id=task_id,
                    command="function",
                    message=message
                ),
                timeout=5.0  # 5秒超时
            )

            # 检查调用是否成功
            if result.get('success'):
                return result  # 成功，返回结果
            else:
                logger.warning(f"[{request_id}] UpdateVoiceChat返回失败，尝试 {attempt + 1}/{max_retries}，错误: {result.get('error')}")

        except asyncio.TimeoutError:
            logger.warning(f"[{request_id}] UpdateVoiceChat调用超时，尝试 {attempt + 1}/{max_retries}")
        except Exception as e:
            logger.warning(f"[{request_id}] UpdateVoiceChat调用失败，尝试 {attempt + 1}/{max_retries}，错误: {e}")

        # 如果不是最后一次尝试，等待后重试（指数退避）
        if attempt < max_retries - 1:
            wait_time = 2 ** attempt  # 1s, 2s, 4s
            logger.info(f"[{request_id}] 等待 {wait_time} 秒后重试")
            await asyncio.sleep(wait_time)

    # 所有重试都失败
    raise Exception(f"UpdateVoiceChat调用失败，已重试 {max_retries} 次")


async def _handle_voice_chat_status_event(
    webhook_request: RtcWebhookRequest,
    request_id: str
) -> JSONResponse:
    """处理智能体状态变化事件 - 符合官方文档规范"""

    logger.info(f"[{request_id}] 处理VoiceChat状态变化事件")

    # 确保payload是VoiceChatPayload类型
    if not hasattr(webhook_request.payload, 'RunStage'):
        raise HTTPException(status_code=400, detail="VoiceChat事件缺少RunStage字段")

    run_stage = webhook_request.payload.RunStage
    event_type = webhook_request.payload.EventType
    user_id = webhook_request.payload.UserID
    task_id = webhook_request.payload.TaskId
    room_id = webhook_request.payload.RoomId
    round_id = webhook_request.payload.RoundID

    logger.info(f"[{request_id}] VoiceChat事件 - RunStage: {run_stage}, EventType: {event_type}, "
                f"UserID: {user_id}, TaskId: {task_id}, RoomId: {room_id}, RoundID: {round_id}")

    # 根据RunStage进行不同的处理
    if run_stage == "taskStart":
        logger.info(f"[{request_id}] 智能体任务开始")
    elif run_stage == "taskStop":
        logger.info(f"[{request_id}] 智能体任务结束")
    elif run_stage == "beginAsking":
        logger.info(f"[{request_id}] 房间用户开始说话")
    elif run_stage == "asrFinish":
        logger.info(f"[{request_id}] 房间用户结束说话，ASR处理完成")
        # 这是关键事件！用户说话结束，应该触发AI回复处理
        # TODO: 这里应该调用对话编排服务生成AI回复
    elif run_stage == "answerFinish":
        logger.info(f"[{request_id}] 智能体说话完成")
    elif run_stage in ["asr", "llm", "tts"]:
        logger.info(f"[{request_id}] 处理阶段: {run_stage}")
    elif run_stage == "preParamCheck":
        logger.warning(f"[{request_id}] 参数校验错误")
        if webhook_request.payload.ErrorInfo:
            error_info = webhook_request.payload.ErrorInfo
            logger.error(f"[{request_id}] 错误详情: {error_info}")
    else:
        logger.warning(f"[{request_id}] 未知RunStage: {run_stage}")

    # 检查是否有错误
    if event_type == 1 and webhook_request.payload.ErrorInfo:
        error_info = webhook_request.payload.ErrorInfo
        logger.error(f"[{request_id}] 智能体任务错误: {error_info}")

    # TODO: 添加实时推送机制，将状态变化推送给前端
    # TODO: 根据RunStage实现具体的业务逻辑

    from api.models.rtc_models import RtcWebhookResponse
    response = RtcWebhookResponse(
        text="",  # VoiceChat状态事件不需要文本回复
        status="success",
        request_id=request_id
    )

    return JSONResponse(
        status_code=200,
        content=response.model_dump()
    )


async def _save_user_message_async(
    db_session: AsyncSession,
    user_id: str,
    session_id: str,
    message: str,
    request_id: str = None,
    max_retries: int = 3
):
    """
    异步保存用户消息到数据库

    实现架构师建议：异步任务可靠性保障，包含重试机制和失败监控
    """
    for attempt in range(max_retries + 1):
        try:
            # 使用原始SQL插入，避免复杂的ORM配置
            insert_sql = text("""
                INSERT INTO chat_messages (session_id, role, content, message_type, metadata, created_at)
                VALUES (:session_id, 'user', :content, 'text', :metadata, :created_at)
            """)

            metadata = {
                "request_id": request_id,
                "user_id": user_id,
                "source": "rtc_webhook",
                "attempt": attempt + 1
            }

            await db_session.execute(insert_sql, {
                "session_id": session_id,
                "content": message,
                "metadata": json.dumps(metadata),
                "created_at": datetime.now(timezone.utc)
            })

            await db_session.commit()
            logger.info(f"[{request_id}] 成功保存用户消息 - 用户: {user_id}, 会话: {session_id}, 尝试: {attempt + 1}")
            return  # 成功则退出

        except Exception as e:
            await db_session.rollback()

            if attempt < max_retries:
                # 指数退避策略
                backoff_delay = 2 ** attempt
                logger.warning(f"[{request_id}] 保存用户消息失败，第{attempt + 1}次尝试: {str(e)}，将在{backoff_delay}秒后重试")
                import asyncio
                await asyncio.sleep(backoff_delay)
            else:
                # 最终失败监控
                logger.error(f"[{request_id}] 保存用户消息最终失败，已尝试{max_retries + 1}次: {str(e)}")
                # 这里可以添加告警或监控系统通知


async def _save_ai_response_async(
    db_session: AsyncSession,
    user_id: str,
    session_id: str,
    response: str,
    request_id: str = None,
    processing_duration: float = None,
    max_retries: int = 3
):
    """
    异步保存AI回复到数据库

    实现架构师建议：异步任务可靠性保障，包含重试机制和失败监控
    """
    for attempt in range(max_retries + 1):
        try:
            # 使用原始SQL插入，避免复杂的ORM配置
            insert_sql = text("""
                INSERT INTO chat_messages (session_id, role, content, message_type, metadata, created_at)
                VALUES (:session_id, 'assistant', :content, 'text', :metadata, :created_at)
            """)

            metadata = {
                "request_id": request_id,
                "user_id": user_id,
                "source": "rtc_webhook",
                "processing_duration": processing_duration,
                "attempt": attempt + 1
            }

            await db_session.execute(insert_sql, {
                "session_id": session_id,
                "content": response,
                "metadata": json.dumps(metadata),
                "created_at": datetime.now(timezone.utc)
            })

            await db_session.commit()
            logger.info(f"[{request_id}] 成功保存AI回复 - 用户: {user_id}, 会话: {session_id}, 尝试: {attempt + 1}")
            return  # 成功则退出

        except Exception as e:
            await db_session.rollback()

            if attempt < max_retries:
                # 指数退避策略
                backoff_delay = 2 ** attempt
                logger.warning(f"[{request_id}] 保存AI回复失败，第{attempt + 1}次尝试: {str(e)}，将在{backoff_delay}秒后重试")
                import asyncio
                await asyncio.sleep(backoff_delay)
            else:
                # 最终失败监控
                logger.error(f"[{request_id}] 保存AI回复最终失败，已尝试{max_retries + 1}次: {str(e)}")
                # 这里可以添加告警或监控系统通知


# 健康检查端点（可选）
@router.get("/rtc_health")
async def rtc_health_check():
    """RTC服务健康检查"""
    return {"status": "healthy", "service": "rtc_webhook"}
