from supabase import create_client, Client
from supabase._async.client import AsyncClient, create_client as async_create_client
from api.settings import settings, logger # 导入logger便于记录关键信息
import asyncio

# 同步客户端管理器，单例模式
class SupabaseClientManager:
    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        self._client = None

    @property
    def client(self) -> Client:
        """获取同步Supabase客户端实例，使用延迟初始化"""
        if self._client is None:
            self._client = create_client(
                settings.SUPABASE_URL,
                settings.SUPABASE_SERVICE_ROLE_KEY
            )
            logger.debug("同步Supabase客户端已初始化")
        return self._client

# 异步客户端管理器，支持异步上下文和连接池管理
class AsyncSupabaseClientManager:
    _instance = None
    _lock = asyncio.Lock()
    _client: AsyncClient | None = None # 明确类型提示
    _test_mode = False  # 新增：测试模式标志

    @classmethod
    def set_test_mode(cls, enabled: bool = True):
        """设置测试模式标志，用于单元测试环境

        Args:
            enabled: 是否启用测试模式，默认为True
        """
        cls._test_mode = enabled
        if enabled:
            # 重置实例，确保测试模式生效
            cls._instance = None
            logger.debug("AsyncSupabaseClientManager测试模式已启用")
        else:
            logger.debug("AsyncSupabaseClientManager测试模式已禁用")

    @classmethod
    async def get_instance(cls):
        """获取单例实例，线程安全，支持测试模式"""
        if cls._test_mode:
            # 测试模式下每次都创建新实例，避免单例导致的事件循环问题
            logger.debug("AsyncSupabaseClientManager在测试模式下创建新实例")
            return cls()

        # 正常模式使用单例
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    async def get_client(self) -> AsyncClient:
        """获取异步Supabase客户端实例，支持延迟初始化"""
        if self._client is None:
            async with self._lock:
                if self._client is None:
                    if not settings.SUPABASE_URL or not settings.SUPABASE_SERVICE_ROLE_KEY:
                        logger.error("Supabase URL 或 Service Key 未配置。")
                        raise ValueError("Supabase URL 和 Service Key 必须在环境变量中配置。")
                    self._client = await async_create_client(
                        settings.SUPABASE_URL,
                        settings.SUPABASE_SERVICE_ROLE_KEY
                    )
                    logger.debug("异步Supabase客户端已初始化")
        return self._client

    async def aclose(self):
        """关闭异步Supabase客户端"""
        if self._client: # 检查 self._client 是否存在
            async with self._lock: # 确保关闭操作的原子性
                client_to_close = self._client
                self._client = None # 先置为 None，防止关闭过程中仍被获取
                if client_to_close:
                    try:
                        # 尝试关闭底层的 postgrest (httpx) 客户端
                        if hasattr(client_to_close, '_postgrest') and hasattr(client_to_close._postgrest, 'aclose'):
                            await client_to_close._postgrest.aclose()
                            logger.debug("异步Supabase客户端的 _postgrest session 已成功关闭。")
                        elif hasattr(client_to_close, 'session') and hasattr(client_to_close.session, 'aclose'):
                            await client_to_close.session.aclose()
                            logger.debug("异步Supabase客户端的 session 已成功关闭。")
                        else:
                            logger.warning("无法在Supabase客户端上找到可关闭的 session 或 _postgrest。")
                    except Exception as e:
                        logger.error(f"关闭异步Supabase客户端时发生错误: {e}", exc_info=True)
                else:
                    logger.debug("Supabase client (client_to_close) was None, nothing to close.")
        else:
            logger.debug("Supabase client (self._client) was already None, nothing to close in aclose.")

        # 重置单例，以便在需要时可以重新初始化。
        # 测试模式下不需要重置全局单例，因为每次都创建新实例
        if not self.__class__._test_mode and self.__class__._instance == self:
             self.__class__._instance = None
             logger.debug("AsyncSupabaseClientManager 单例已重置。")


# 创建同步客户端管理器单例实例
_sync_manager = SupabaseClientManager.get_instance()

def get_client() -> Client:
    """获取同步Supabase客户端实例

    Returns:
        Client: Supabase同步客户端
    """
    return _sync_manager.client

async def get_supabase_client() -> AsyncClient:
    """获取Supabase异步客户端实例

    现在使用异步单例模式，避免每次调用都创建新的客户端实例

    Returns:
        AsyncClient: Supabase异步客户端
    """
    manager = await AsyncSupabaseClientManager.get_instance()
    return await manager.get_client()

# 添加客户端健康检查函数
async def check_supabase_connection() -> bool:
    """检查Supabase连接是否正常

    Returns:
        bool: 连接正常返回True，否则返回False
    """
    try:
        client = await get_supabase_client()
        # 执行一个简单查询测试连接，使用确实存在的users表
        response = await client.from_("users").select("id").limit(1).execute()
        logger.debug(f"Supabase health check response: {response}")
        return True
    except Exception as e:
        logger.error(f"Supabase连接检查失败: {e}", exc_info=True)
        return False
