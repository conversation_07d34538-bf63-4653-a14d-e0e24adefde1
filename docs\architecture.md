### **"心桥"项目完整技术架构设计文档**
**版本：** 3.0
**日期：** 2025年7月10日
**作者：** @architect

#### **1. 简介 (Introduction)**

本文档是"心桥"项目的完整技术架构设计方案，涵盖了后端智能服务和前端移动应用的详细设计。

**后端架构核心：**
本方案的核心是设计一个高度模块化、服务化的`agent-api`后端。该后端通过标准的Webhook模式接收火山引擎RTC服务的实时语音(ASR)事件，然后通过内部的**对话编排服务 (`ChatOrchestrationService`)**，自主完成与大语言模型(LLM)的交互、工具调用(Function Calling)和记忆检索。

这种**原生LLM编排（Native LLM Orchestration）**架构，使我们能够完全掌控对话逻辑，实现复杂的、多步骤的AI交互，同时与底层LLM及记忆服务（Zep/Mem0）保持松耦合，从而在保证实时性的前提下，实现最深度、最自然的个性化陪伴体验。

**前端架构核心：**
前端采用React Native + Expo技术栈，基于Obytes脚手架构建高性能、用户友好的移动应用。重点实现双模交互（语音/文本）界面，确保为老年用户提供零负担的使用体验。

#### **2. 高层架构 (High Level Architecture)**

*   **2.1. 核心组件 (Core Components)**

    *   **客户端 (React Native/Expo):** 负责UI呈现、用户交互、状态管理，并集成火山引擎RTC前端SDK。
    *   **心桥后端服务 (FastAPI):** 项目的核心业务后端和智能中枢。它负责：
        1.  **会话管理：** 启动和结束火山RTC会话。
        2.  **LLM原生编排：** 通过`ChatOrchestrationService`直连并编排火山引擎LLM，管理上下文、提示和工具调用循环。
        3.  **记忆管理：** 通过`IMemoryService`抽象层，对接并管理专业的记忆服务（Zep AI或Mem0 AI）。
        4.  **标准API服务：** 提供用户画像、提醒管理等常规RESTful API。
    *   **火山RTC云服务 (Volcano Engine):** 作为项目的**"通信与感知主干"**，负责所有媒体处理，包括ASR、TTS和实时媒体传输。
    *   **专业记忆服务 (Zep AI / Mem0 AI):** 作为项目的**"可插拔记忆中枢"**，提供先进的对话记忆存储和检索能力。
    *   **Supabase (BaaS):** 作为我们的**"持久化与认证基座"**，提供PostgreSQL数据库、用户认证(Auth)、对象存储(Storage)等服务。

*   **2.2. 核心交互流程: 原生LLM编排模式**
    1.  **[前端]** 用户发起语音聊天，调用我方后端的`/prepare_session`接口。
    2.  **[后端]** `心桥后端服务`调用火山`StartVoiceChat` API（标准模式），为客户端获取RTC连接凭证。
    3.  **[实时对话循环]**
        a. **[火山云 -> 我方后端]** 火山云将ASR文本通过Webhook实时POST到我方`/rtc_event_handler`接口。
        b. **[我方后端 - 智能核心]** `ChatOrchestrationService`执行核心逻辑：
            - 调用`IMemoryService`检索记忆。
            - 构建包含记忆、历史和工具定义的Prompt。
            - 调用LLM，并处理可能的多轮工具调用。
            - 生成最终AI回复文本。
        c. **[我方后端 -> 火山云]** 将最终AI回复文本通过Webhook响应体返回给火山云。
        d. **[火山云 -> 前端]** 火山云完成TTS合成并通过RTC将语音流传回给前端。

*   **2.3. 高层架构图**
    ```mermaid
    sequenceDiagram
        participant Client as 客户端 (React Native)
        participant BackendService as 心桥后端服务 (FastAPI)
        participant VolcanoRTC as 火山引擎RTC云
        participant LLMService as 火山引擎LLM
        participant MemoryService as 可插拔记忆服务

        alt RTC实时语音对话
            Client->>BackendService: 1. /prepare_session
            activate BackendService
            BackendService->>VolcanoRTC: 2. StartVoiceChat (配置ASR/TTS)
            VolcanoRTC-->>BackendService: 3. 返回RTC连接凭证
            BackendService-->>Client: 4. 返回凭证
            deactivate BackendService

            Client-->>VolcanoRTC: 5. [RTC SDK] 建立实时语音连接
            
            loop 实时对话
                Client->>VolcanoRTC: 用户语音
                VolcanoRTC->>VolcanoRTC: ASR处理
                VolcanoRTC->>BackendService: 6. Webhook: onMessage(ASR文本)
                activate BackendService
                
                BackendService->>MemoryService: 7. 检索相关记忆
                MemoryService-->>BackendService: 返回记忆
                
                BackendService->>LLMService: 8. 调用LLM(含记忆、工具定义)
                
                alt LLM请求工具调用
                    LLMService-->>BackendService: 9. 返回工具调用请求
                    BackendService->>BackendService: 10. (内部)执行工具
                    BackendService->>LLMService: 11. 提交工具结果，继续生成
                end
                
                LLMService-->>BackendService: 12. 返回最终AI回复 (流式)
                
                BackendService-->>VolcanoRTC: 13. Webhook Response: (TTS文本)
                
                note right of BackendService: 异步处理
                BackendService->>MemoryService: 14. [async] 存储新一轮对话
                
                deactivate BackendService
                
                VolcanoRTC->>VolcanoRTC: TTS合成
                VolcanoRTC-->>Client: AI语音流
            end
        end
    ```

#### **3. API接口设计 (最终版)**

*   **3.1. 核心实时流处理接口**
    *   **RTC事件处理入口 (火山RTC对接):** `POST /api/v1/chat/rtc_event_handler`
        *   **职责:** 接收火山引擎的事件回调（如ASR结果），并将其委托给`ChatOrchestrationService`处理。
    *   **文本入口 (前端直接调用):** `POST /api/v1/chat/text_message`
        *   **职责:** 用于处理纯文本聊天请求，复用`ChatOrchestrationService`的核心逻辑。
*   **3.2. 实时语音会话控制API**
    *   `POST /api/v1/rtc/prepare_session`:
        *   **职责:** 为前端获取RTC连接凭证，启动一个标准的火山RTC会话。
    *   `POST /api/v1/rtc/end_session`:
        *   **职责:** 停止火山会话，并触发后续的异步记忆分析流程。
    *   `POST /api/v1/rtc/sessions/{session_id}/command`:
        *   **职责:** 在会话中发送控制命令，如手动中断。
*   **3.3. 用户与角色管理API**
    *   `GET/PUT /api/v1/user/profile`
    *   `GET/PUT /api/v1/user/settings`
    *   `GET /api/v1/characters`
*   **3.4. 认证与引导API**
    *   `POST /api/v1/auth/anonymous-login`
    *   `POST /api/v1/auth/refresh-token`
    *   `POST /api/v1/auth/finalize_onboarding`
*   **3.5. 提醒管理API**
    *   `GET/POST/PUT/DELETE /api/v1/reminders`
    *   **注意**: 提醒的创建主要由LLM通过内部工具调用完成，而非独立的API。

#### **4. 可插拔记忆服务 (Pluggable Memory System)**

*   **4.1. 核心设计理念**
    为了实现最大化的灵活性、专业性和可扩展性，我们已将记忆系统从Agno框架中完全解耦，升级为一个独立、抽象、可插拔的服务层。

*   **4.2. 架构：`MemoryService` 抽象层**
    新架构的核心是`MemoryService`抽象基类，它定义了所有记忆服务的统一契约。

    *   **接口定义 (伪代码):**
        ```python
        class MemoryService(ABC):
            @abstractmethod
            async def search_memory(self, session_id: str, query: str) -> List[Memory]:
                # ...
                pass

            @abstractmethod
            async def add_memory(self, session_id: str, user_message: str, assistant_message: str) -> None:
                # ...
                pass
            
            @abstractmethod
            async def update_session_metadata(self, session_id: str, metadata: dict) -> None:
                # ...
                pass
        ```
    *   **工厂函数 `get_memory_service`:**
        此函数读取环境变量`MEMORY_PROVIDER`('zep'或'mem0')，来决定实例化`ZepMemoryService`还是`Mem0MemoryService`。

*   **4.3. 具体实现类**
    *   **`ZepMemoryService`:** 基于`zep-cloud` SDK实现。
    *   **`Mem0MemoryService`:** 基于`mem0ai` SDK实现。

#### **5. 数据模型 (Pydantic & Supabase)**

核心数据表将包括：`users`, `user_profiles`, `user_settings`, `characters`, `chat_sessions`, `chat_messages`, `reminders`。**`user_memories`表已被废弃。** 所有API的请求和响应体都应遵循`shared/contracts/schema.py`中定义的Pydantic模型。

#### **6. 安全性考虑 (Security)**

  * **认证:** 所有需要用户身份的API均需通过Supabase Auth提供的JWT进行保护。
  * **授权:** 数据库层面，将对所有表启用并配置严格的行级别安全（RLS）策略。
  * **API安全:** 来自火山云的`/rtc_event_handler`回调请求，必须通过共享密钥或IP白名单进行验证。
  * **数据传输:** 所有客户端与API、API与云服务之间的通信必须强制使用HTTPS/WSS进行加密。

#### **7. 后端详细设计**

*   **7.1. 模块职责**
    *   **`api/services/`:**
        *   **`chat_orchestration_service.py`:** 封装核心对话逻辑，集成记忆、LLM和工具服务。
        *   **`llm_proxy_service.py`:** 封装对底层大语言模型（如火山方舟）的直接API调用。
        *   **`tool_executor_service.py`:** 定义和执行应用的所有可用工具（Functions）。
        *   **`memory_service.py`:** 定义`IMemoryService`接口及具体实现（Zep/Mem0）。
        *   **`user_service.py`, `session_service.py`等:** 处理各自领域的业务逻辑。

*   **7.2. 测试策略**
    *   **单元测试:** 对所有Service模块进行高覆盖率测试，Mock掉外部依赖。
    *   **集成测试 (关键):**
        *   验证`/rtc_event_handler`和`/text_message`的端到端流程。
        *   重点测试包含多步工具调用的复杂对话场景。
    *   **性能测试:** 测试端到端延迟和并发处理能力。

#### **8. 前端架构设计 (摘要)**
前端架构的核心是利用`React Native + Expo`技术栈，实现一个响应迅速、用户友好的界面。
*   **状态管理**: 使用`Zustand`管理全局UI状态，如会话消息列表。
*   **数据获取**: 使用`React Query`管理服务器状态，如用户画像、提醒列表等。
*   **核心组件**:
    *   **`ChatInputController`**: 实现语音/文本双模输入的切换和交互逻辑。
    *   **`FlashList`**: **强制**使用此组件渲染对话历史，以保证长列表的滚动性能。
*   **服务层 (`services/`)**: 封装所有对后端API的调用逻辑。

---

*本文档为项目的完整技术实现提供了详尽的指导方案。*
