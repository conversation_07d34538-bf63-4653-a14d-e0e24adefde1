import cachetools
from typing import Optional, Dict, Any
from datetime import datetime, timezone, time
from supabase._async.client import AsyncClient
from db.supabase_init import get_supabase_client
from api.settings import logger
from api.models.schema_models import UserSettings, UserSettingsUpdate, NotificationSettings, QuietHoursSettings
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from postgrest.exceptions import APIError
import asyncio
from fastapi import HTTPException


class SettingsService:
    _settings_cache = cachetools.TTLCache(maxsize=200, ttl=1800)  # Cache for 30 minutes
    _cache_lock = asyncio.Lock()

    @classmethod
    def get_default_settings(cls) -> Dict[str, Any]:
        """
        获取老年人友好的默认设置
        架构师要求：大字体、高对比度可选、简化通知
        """
        return {
            'theme': 'auto',
            'font_size': 'large',  # 老年人友好：大字体
            'high_contrast': False,  # 可选高对比度
            'language': 'zh-CN',
            'notifications_enabled': True,  # 简化通知：默认启用
            'quiet_hours_enabled': False,
            'quiet_hours_start': None,
            'quiet_hours_end': None,
        }

    @classmethod
    def _convert_db_to_api_format(cls, db_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将数据库格式转换为API响应格式
        数据库字段 -> API字段映射
        """
        if not db_data:
            return {}

        # 处理time字段转换为字符串
        quiet_start = db_data.get('quiet_hours_start')
        quiet_end = db_data.get('quiet_hours_end')

        quiet_start_str = None
        quiet_end_str = None

        if quiet_start:
            if isinstance(quiet_start, time):
                quiet_start_str = quiet_start.strftime('%H:%M')
            elif isinstance(quiet_start, str):
                quiet_start_str = quiet_start

        if quiet_end:
            if isinstance(quiet_end, time):
                quiet_end_str = quiet_end.strftime('%H:%M')
            elif isinstance(quiet_end, str):
                quiet_end_str = quiet_end

        return {
            'user_id': db_data.get('user_id'),
            'theme': db_data.get('theme', 'auto'),
            'font_size': db_data.get('font_size', 'large'),
            'high_contrast': db_data.get('high_contrast', False),
            'language': db_data.get('language', 'zh-CN'),
            'notifications_enabled': db_data.get('notifications_enabled', True),
            'quiet_hours_enabled': db_data.get('quiet_hours_enabled', False),
            'quiet_hours_start': quiet_start_str,
            'quiet_hours_end': quiet_end_str,
            'updated_at': db_data.get('updated_at')
        }

    @classmethod
    def _convert_api_to_db_format(cls, api_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        将API格式转换为数据库格式
        API字段 -> 数据库字段映射
        """
        db_data = {
            'user_id': user_id,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }

        # 基础字段映射 - 现在API和DB使用相同字段名
        if 'theme' in api_data:
            db_data['theme'] = api_data['theme']
        if 'font_size' in api_data:
            db_data['font_size'] = api_data['font_size']
        if 'high_contrast' in api_data:
            db_data['high_contrast'] = api_data['high_contrast']
        if 'language' in api_data:
            db_data['language'] = api_data['language']
        if 'notifications_enabled' in api_data:
            db_data['notifications_enabled'] = api_data['notifications_enabled']
        if 'quiet_hours_enabled' in api_data:
            db_data['quiet_hours_enabled'] = api_data['quiet_hours_enabled']
        if 'quiet_hours_start' in api_data:
            db_data['quiet_hours_start'] = api_data['quiet_hours_start']
        if 'quiet_hours_end' in api_data:
            db_data['quiet_hours_end'] = api_data['quiet_hours_end']

        return db_data

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception)
    )
    async def get_settings(self, user_id: str, create_if_not_exists: bool = True) -> Dict[str, Any]:
        """
        获取用户设置，如果不存在则返回默认设置并可选择性创建
        使用缓存提高性能
        """
        async with self._cache_lock:
            cached_settings = self._settings_cache.get(user_id)
            if cached_settings:
                logger.debug(f"SettingsService: Cache hit for user settings '{user_id}'.")
                return cached_settings

        logger.debug(f"SettingsService: Cache miss for user settings '{user_id}'. Fetching from DB.")
        client: AsyncClient = await get_supabase_client()

        try:
            response = await client.table("user_settings") \
                .select("*") \
                .eq("user_id", user_id) \
                .execute()

            # 检查是否有数据返回
            if response and response.data and len(response.data) > 0:
                # 转换为API格式
                api_settings = self._convert_db_to_api_format(response.data[0])

                async with self._cache_lock:
                    self._settings_cache[user_id] = api_settings
                logger.debug(f"SettingsService: Fetched settings for '{user_id}'.")
                return api_settings
            else:
                logger.info(f"SettingsService: No settings found for user '{user_id}'. Using defaults.")

                # 准备默认设置API格式
                default_db_settings = self.get_default_settings()
                default_api_settings = self._convert_db_to_api_format({
                    'user_id': user_id,
                    **default_db_settings,
                    'updated_at': None
                })

                if create_if_not_exists:
                    try:
                        # 使用UPSERT创建默认设置
                        db_payload = {
                            'user_id': user_id,
                            **default_db_settings,
                            'updated_at': datetime.now(timezone.utc).isoformat()
                        }

                        insert_response = await client.table("user_settings") \
                            .upsert(db_payload, on_conflict="user_id") \
                            .execute()

                        if insert_response and insert_response.data and len(insert_response.data) > 0:
                            created_settings = self._convert_db_to_api_format(insert_response.data[0])
                            logger.info(f"SettingsService: Created default settings for user '{user_id}' in DB.")

                            async with self._cache_lock:
                                self._settings_cache[user_id] = created_settings
                            return created_settings
                        else:
                            logger.error(f"SettingsService: Failed to create default settings for '{user_id}'.")
                    except Exception as e_insert:
                        logger.error(f"SettingsService: Exception creating default settings for {user_id}: {e_insert}", exc_info=True)

                return default_api_settings

        except APIError as e:
            logger.error(f"APIError fetching settings for {user_id}: {e.message}")
            raise HTTPException(status_code=500, detail=f"Database error fetching settings: {e.message}")
        except Exception as e:
            logger.error(f"Unexpected error fetching settings for {user_id}: {e}", exc_info=True)
            raise

    async def update_settings(self, user_id: str, update_data: UserSettingsUpdate) -> Dict[str, Any]:
        """
        更新用户设置。使用UPSERT逻辑实现并发安全
        实现PATCH语义：只更新提供的字段
        使缓存失效以确保一致性
        """
        client: AsyncClient = await get_supabase_client()

        # 将Pydantic模型转换为字典，排除None值
        update_dict = update_data.model_dump(exclude_none=True)

        # 移除userId字段（前端可能传递，但我们使用JWT中的user_id）
        if "userId" in update_dict:
            del update_dict["userId"]

        if not update_dict:
            logger.debug(f"SettingsService: No fields to update for user settings '{user_id}'.")
            # 无效化缓存并返回当前设置
            async with self._cache_lock:
                if user_id in self._settings_cache:
                    del self._settings_cache[user_id]
            return await self.get_settings(user_id, create_if_not_exists=False)

        logger.debug(f"SettingsService: Updating settings for user_id='{user_id}' with data: {update_dict}")

        # 转换为数据库格式
        db_payload = self._convert_api_to_db_format(update_dict, user_id)

        try:
            # 使用UPSERT实现并发安全的更新
            upsert_response = await client.table("user_settings") \
                .upsert(db_payload, on_conflict="user_id") \
                .execute()

            if upsert_response and upsert_response.data and len(upsert_response.data) > 0:
                logger.debug(f"SettingsService: Successfully upserted settings for '{user_id}'.")

                # 无效化缓存
                async with self._cache_lock:
                    if user_id in self._settings_cache:
                        del self._settings_cache[user_id]
                        logger.debug(f"SettingsService: Cache invalidated for user settings '{user_id}'.")

                # 获取最新数据确保一致性
                return await self.get_settings(user_id, create_if_not_exists=False)
            else:
                # 修复：新版supabase-py在出错时会直接抛出异常，不需要检查.error属性
                logger.error(f"SettingsService: Failed to upsert settings for '{user_id}'. Response returned empty data.")
                raise HTTPException(status_code=500, detail="Failed to update user settings")

        except APIError as e:
            logger.error(f"SettingsService: APIError upserting settings for {user_id}: {e.message}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Database error updating settings: {e.message}")
        except Exception as e:
            logger.error(f"SettingsService: Unexpected error upserting settings for {user_id}: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Internal server error updating settings")


# 全局实例
settings_service = SettingsService()
