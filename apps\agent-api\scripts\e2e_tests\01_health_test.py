#!/usr/bin/env python3
"""
健康检查API测试
测试接口: GET /api/v1/health

注意：运行此测试会清理所有历史测试数据，适合开始新的测试轮次
"""

import asyncio
import sys
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class HealthTester(BaseAPITester):
    """健康检查API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        # 先清理历史数据
        self.cleanup_test_data()
        super().__init__("01_health", base_url)

    async def test_health_check(self):
        """测试健康检查API"""
        self.logger.info("🏥 测试健康检查API")

        response = await self.make_request(
            "GET",
            "/api/v1/health",
            include_auth=False
        )

        # 验证响应格式
        if "status" in response and response["status"] == "healthy":
            self.logger.info("✅ 健康检查响应格式正确")
        else:
            self.logger.error("❌ 健康检查响应格式错误")

        if "timestamp" in response:
            self.logger.info("✅ 响应包含时间戳")
        else:
            self.logger.error("❌ 响应缺少时间戳")

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始健康检查API测试")

        try:
            await self.test_health_check()
        except Exception as e:
            self.logger.error(f"❌ 测试异常: {e}")
            self.test_results["failed"] += 1
            self.test_results["errors"].append({"test": "health_check", "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='健康检查API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with HealthTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
