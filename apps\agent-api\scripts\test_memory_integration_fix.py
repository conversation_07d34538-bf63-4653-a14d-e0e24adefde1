#!/usr/bin/env python3
"""
测试脚本：验证修复后的memory service集成

验证：
1. chat_orchestration_service 中的 memory 调用是否正确
2. session_analysis_service 中的 memory 调用是否正确
3. reminder_service 中的 memory 调用是否正确
"""

import asyncio
import os
import sys
import traceback
from unittest.mock import AsyncMock, MagicMock, patch

# 添加项目根目录到 Python 路径
script_dir = os.path.dirname(os.path.abspath(__file__))
agent_api_dir = os.path.dirname(script_dir)
project_root = os.path.dirname(os.path.dirname(agent_api_dir))
sys.path.insert(0, project_root)
sys.path.insert(0, agent_api_dir)

from api.services.memory_service import Mem0MemoryServiceImpl, get_memory_service
from api.services.chat_orchestration_service import ChatOrchestrationService
from api.services.session_analysis_service import SessionAnalysisService
from api.services.reminder_service import ReminderService
from api.settings import logger


async def test_chat_orchestration_memory_calls():
    """测试 ChatOrchestrationService 中的 memory 调用"""
    print("🧪 测试 ChatOrchestrationService 的 memory 调用...")

    # 创建 mock memory service
    mock_memory_service = AsyncMock()
    mock_memory_service.get_memory_context.return_value = {
        "memories": [{"role": "system", "content": "用户喜欢咖啡"}],
        "context": "用户喜欢咖啡"
    }
    mock_memory_service.search_memory.return_value = [
        {"role": "system", "content": "用户喜欢咖啡"}
    ]

    # 创建服务实例
    chat_service = ChatOrchestrationService()
    chat_service.memory_service = mock_memory_service

    try:
        # 测试 _retrieve_memory_context 方法
        context = {
            "userId": "test_user_123",
            "sessionId": "test_session_456"
        }
        user_message = "我今天想喝点什么？"
        request_id = "test_request_001"

        # 这应该调用 memory_service.search_memory
        result = await chat_service._retrieve_memory_context(user_message, context, request_id)

        print("✅ ChatOrchestrationService._retrieve_memory_context 调用成功")
        print(f"   返回结果: {result}")

        # 验证调用参数
        mock_memory_service.search_memory.assert_called_with(
            user_id="test_user_123",
            query=user_message,
            limit=5
        )
        print("✅ memory_service.search_memory 调用参数正确")

        return True

    except Exception as e:
        print(f"❌ ChatOrchestrationService 测试失败: {e}")
        traceback.print_exc()
        return False


async def test_session_analysis_memory_calls():
    """测试 SessionAnalysisService 中的 memory 调用"""
    print("\n🧪 测试 SessionAnalysisService 的 memory 调用...")

    # 创建 mock memory service
    mock_memory_service = AsyncMock()
    mock_memory_service.update_session_metadata.return_value = True

    # 创建服务实例
    session_service = SessionAnalysisService(memory_service=mock_memory_service)

    try:
        # 测试 _sync_to_memory_service 方法
        session_id = "test_session_789"
        summary = "用户询问了关于咖啡的问题"
        metadata = {"duration": 120, "message_count": 3}
        user_id = "test_user_456"

        await session_service._sync_to_memory_service(session_id, summary, metadata, user_id)

        print("✅ SessionAnalysisService._sync_to_memory_service 调用成功")

        # 验证调用参数
        mock_memory_service.update_session_metadata.assert_called_with(
            user_id, session_id, summary, metadata
        )
        print("✅ memory_service.update_session_metadata 调用参数正确")

        return True

    except Exception as e:
        print(f"❌ SessionAnalysisService 测试失败: {e}")
        traceback.print_exc()
        return False


async def test_reminder_service_memory_calls():
    """测试 ReminderService 中的 memory 调用"""
    print("\n🧪 测试 ReminderService 的 memory 调用...")

    # 这个测试比较复杂，因为 ReminderService 有很多依赖
    # 我们只验证方法签名是否正确

    try:
        # 检查方法签名
        from api.services.memory_service import IMemoryService
        import inspect

        # 验证 add_memory 方法签名
        add_memory_sig = inspect.signature(IMemoryService.add_memory)
        params = list(add_memory_sig.parameters.keys())

        expected_params = ['self', 'user_id', 'session_id', 'human_message', 'assistant_message', 'metadata']
        if params == expected_params:
            print("✅ IMemoryService.add_memory 方法签名正确")
            return True
        else:
            print(f"❌ IMemoryService.add_memory 方法签名错误:")
            print(f"   期望: {expected_params}")
            print(f"   实际: {params}")
            return False

    except Exception as e:
        print(f"❌ ReminderService 测试失败: {e}")
        traceback.print_exc()
        return False


async def test_memory_service_methods():
    """测试 MemoryService 的方法接口"""
    print("\n🧪 测试 MemoryService 方法接口...")

    try:
        # 测试 get_memory_service 函数
        memory_service = await get_memory_service("mem0")
        print("✅ get_memory_service 函数正常工作")

        # 验证所有必要方法存在
        required_methods = [
            'add_memory',
            'get_memories',
            'search_memory',
            'delete_memory',
            'get_memory_context',
            'update_session_metadata'
        ]

        for method_name in required_methods:
            if hasattr(memory_service, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法缺失")
                return False

        return True

    except Exception as e:
        print(f"❌ MemoryService 接口测试失败: {e}")
        traceback.print_exc()
        return False


async def test_import_compatibility():
    """测试导入兼容性"""
    print("\n🧪 测试导入兼容性...")

    try:
        # 测试所有相关模块的导入
        from api.services.chat_orchestration_service import ChatOrchestrationService
        from api.services.session_analysis_service import SessionAnalysisService
        from api.services.reminder_service import ReminderService
        from api.services.memory_service import Mem0MemoryServiceImpl, get_memory_service

        print("✅ 所有服务模块导入成功")

        # 测试服务实例化
        chat_service = ChatOrchestrationService()
        session_service = SessionAnalysisService()
        print("✅ 所有服务实例化成功")

        return True

    except Exception as e:
        print(f"❌ 导入兼容性测试失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("🔧 Memory Service 修复验证测试")
    print("=" * 60)

    tests = [
        ("导入兼容性", test_import_compatibility),
        ("MemoryService方法接口", test_memory_service_methods),
        ("ChatOrchestrationService调用", test_chat_orchestration_memory_calls),
        ("SessionAnalysisService调用", test_session_analysis_memory_calls),
        ("ReminderService调用", test_reminder_service_memory_calls),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 正在运行: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))

    # 打印总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1

    print(f"\n📊 总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！Memory Service 修复成功！")
        return True
    else:
        print("💥 部分测试失败，需要进一步检查。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
