@echo off
setlocal EnableDelayedExpansion

echo.
echo ====================================================================
echo                    Xinqiao API E2E Test Launcher
echo ====================================================================
echo.

:: 显示当前目录信息
echo Current Directory: %CD%
echo.

:: 检查测试脚本是否存在
if not exist "apps\agent-api\scripts\e2e_api_test_fixed.py" (
    echo ERROR: Test script file not found
    echo    Expected location: apps\agent-api\scripts\e2e_api_test_fixed.py
    echo    Please run this script from project root directory
    echo.
    pause
    exit /b 1
)

:: 切换到测试脚本目录
echo Switching to test script directory...
cd /d "apps\agent-api"

:: 检查是否已在conda环境中
echo Checking conda environment...
echo Current environment: %CONDA_DEFAULT_ENV%
if "%CONDA_DEFAULT_ENV%"=="xinqiao-py312" (
    echo Already in xinqiao-py312 environment
) else (
    echo Activating conda environment: xinqiao-py312
    call conda activate xinqiao-py312
)
if errorlevel 1 (
    echo ERROR: Unable to activate conda environment xinqiao-py312
    echo    Please ensure environment exists: conda create -n xinqiao-py312 python=3.12
    echo.
    pause
    exit /b 1
)

echo Environment setup complete
echo.

:: 检查测试目标服务器
set "TEST_URL=http://localhost:8003"
if not "%~1"=="" (
    set "TEST_URL=%~1"
)

echo Test target server: !TEST_URL!
echo.

:: 检查服务器是否可访问
echo Checking server connection...
curl -s -o nul -w "%%{http_code}" !TEST_URL!/api/v1/health --max-time 5 > temp_response.txt 2>nul
if exist temp_response.txt (
    set /p response=<temp_response.txt
    del temp_response.txt
    if "!response!"=="200" (
        echo Server connection OK
    ) else (
        echo WARNING: Server response status code !response!, test may fail
    )
) else (
    echo WARNING: Cannot connect to server, please ensure backend service is running
    echo    Start command: python api/main.py
)

echo.
echo Starting E2E API tests...
echo ====================================================================
echo.

:: Run test script
python scripts\e2e_api_test_fixed.py --url !TEST_URL!

:: Check execution result
if errorlevel 1 (
    echo.
    echo Test execution failed
    echo.
) else (
    echo.
    echo Test execution completed
    echo.
)

:: Show log file location
if exist "logs\" (
    echo Test logs saved to:
    echo    Detailed logs: %CD%\logs\
    echo.
    
    :: List latest log files
    echo Latest generated log files:
    for /f "delims=" %%i in ('dir /b /o-d logs\e2e_test_* 2^>nul ^| head -n 5') do (
        echo    - logs\%%i
    )
    echo.
)

echo Usage Instructions:
echo    - Default test URL: http://localhost:8003
echo    - Custom URL: %~nx0 http://your-server:port
echo    - View detailed logs: Open apps\agent-api\logs\ directory
echo.
echo ====================================================================

pause 