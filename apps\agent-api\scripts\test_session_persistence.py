#!/usr/bin/env python3
"""
会话持久性测试脚本
================

用于测试智能体的会话持久性功能，验证重启后是否能恢复记忆。

使用方法：
1. 第一次运行：python test_session_persistence.py --phase 1
2. 第二次运行：python test_session_persistence.py --phase 2

或者直接运行：python test_session_persistence.py
会自动进行两阶段测试
"""

import asyncio
import argparse
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from advanced_memory_agent import XinqiaoAgent, AgentConfig

async def test_phase_1():
    """第一阶段：建立记忆"""
    print("🧪 第一阶段测试：建立记忆")
    print("=" * 40)

    # 创建配置
    config = AgentConfig.from_env()
    print(f"👤 用户ID: {config.user_id}")
    print(f"💬 会话ID: {config.session_id}")

    # 创建智能体
    agent = XinqiaoAgent(config)

    # 初始化
    if not await agent.initialize():
        print("❌ 智能体初始化失败")
        return False

    print("✅ 智能体初始化成功")

    # 添加一些记忆
    test_memories = [
        "我的名字是Alex Hu",
        "我来自浙江杭州",
        "我是一名软件工程师",
        "我正在开发心桥AI项目",
        "我喜欢喝龙井茶"
    ]

    print("\n📝 添加测试记忆...")
    for memory in test_memories:
        print(f"  添加: {memory}")
        response = await agent.chat(memory)
        print(f"  AI回复: {response[:50]}...")
        await asyncio.sleep(0.5)

    # 测试记忆检索
    print("\n🔍 测试记忆检索...")
    test_query = "请告诉我你对我了解的信息"
    response = await agent.chat(test_query)
    print(f"查询: {test_query}")
    print(f"回复: {response}")

    # 显示统计信息
    stats = await agent.get_memory_stats()
    print(f"\n📊 记忆统计: 总计 {stats['total_memories']} 条记忆")

    await agent.cleanup()
    print("\n✅ 第一阶段测试完成")
    print("💾 记忆已保存，请运行第二阶段测试验证持久性")
    return True

async def test_phase_2():
    """第二阶段：验证记忆恢复"""
    print("🧪 第二阶段测试：验证记忆恢复")
    print("=" * 40)

    # 创建配置（应该恢复之前的会话）
    config = AgentConfig.from_env()
    print(f"👤 用户ID: {config.user_id}")
    print(f"💬 会话ID: {config.session_id}")

    # 创建智能体
    agent = XinqiaoAgent(config)

    # 初始化
    if not await agent.initialize():
        print("❌ 智能体初始化失败")
        return False

    print("✅ 智能体初始化成功")

    # 验证记忆恢复
    print("\n🔍 验证记忆恢复...")
    test_queries = [
        "你还记得我的名字吗？",
        "我来自哪里？",
        "我的职业是什么？",
        "我在开发什么项目？",
        "我喜欢喝什么茶？"
    ]

    success_count = 0
    for query in test_queries:
        print(f"\n查询: {query}")
        response = await agent.chat(query)
        print(f"回复: {response}")

        # 简单的记忆验证（检查是否包含相关信息）
        if any(keyword in response.lower() for keyword in ["alex", "杭州", "工程师", "心桥", "龙井"]):
            print("✅ 记忆恢复成功")
            success_count += 1
        else:
            print("❌ 记忆可能未恢复")

        await asyncio.sleep(0.5)

    # 显示统计信息
    stats = await agent.get_memory_stats()
    print(f"\n📊 记忆统计: 总计 {stats['total_memories']} 条记忆")

    await agent.cleanup()

    print(f"\n📈 测试结果: {success_count}/{len(test_queries)} 项记忆恢复成功")
    if success_count >= len(test_queries) // 2:
        print("✅ 会话持久性测试通过")
        return True
    else:
        print("❌ 会话持久性测试失败")
        return False

async def full_test():
    """完整的两阶段测试"""
    print("🚀 会话持久性完整测试")
    print("=" * 50)

    # 检查环境变量
    if not os.getenv("VOLCENGINE_API_KEY") or not os.getenv("VOLCANO_LLM_ENDPOINT_ID"):
        print("❌ 错误：请设置环境变量 VOLCENGINE_API_KEY 和 VOLCANO_LLM_ENDPOINT_ID")
        return

    # 第一阶段
    success_1 = await test_phase_1()
    if not success_1:
        print("❌ 第一阶段测试失败")
        return

    print("\n⏳ 等待3秒模拟程序重启...")
    await asyncio.sleep(3)

    # 第二阶段
    success_2 = await test_phase_2()

    # 最终结果
    print(f"\n🎯 最终结果:")
    print(f"  第一阶段: {'✅ 成功' if success_1 else '❌ 失败'}")
    print(f"  第二阶段: {'✅ 成功' if success_2 else '❌ 失败'}")

    if success_1 and success_2:
        print("🎉 会话持久性测试完全通过！")
    else:
        print("😞 会话持久性测试失败，需要检查配置")

async def main():
    parser = argparse.ArgumentParser(description="会话持久性测试")
    parser.add_argument("--phase", type=int, choices=[1, 2],
                       help="指定测试阶段：1=建立记忆，2=验证恢复")

    args = parser.parse_args()

    try:
        if args.phase == 1:
            await test_phase_1()
        elif args.phase == 2:
            await test_phase_2()
        else:
            await full_test()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
