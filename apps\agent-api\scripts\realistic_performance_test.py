#!/usr/bin/env python3
"""
真实场景性能测试脚本

模拟真实用户使用模式：
1. 用户间隔分布访问（避免过于集中）
2. 不同类型的API调用组合
3. 模拟真实的用户行为间隔
4. 验证多用户体验优化效果
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import List, Dict, Any
from dataclasses import dataclass, field
from datetime import datetime
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RealisticTestResult:
    """真实测试结果"""
    test_name: str
    users: int
    duration: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    max_response_time: float
    min_response_time: float
    qps: float
    errors: List[str] = field(default_factory=list)

    @property
    def success_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100

class RealisticPerformanceTestRunner:
    """真实场景性能测试运行器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=50, limit_per_host=25)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "RealisticPerformanceTest/1.0"}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求并记录性能指标"""
        start_time = time.time()
        url = f"{self.base_url}{endpoint}"

        try:
            async with self.session.request(method, url, **kwargs) as response:
                response_time = time.time() - start_time

                # 读取响应内容
                if response.content_type == 'application/json':
                    content = await response.json()
                else:
                    content = await response.text()

                return {
                    "success": response.status < 400,
                    "status_code": response.status,
                    "response_time": response_time,
                    "content": content,
                    "headers": dict(response.headers)
                }

        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "error": str(e),
                "content": None
            }

    async def simulate_user_activity(self, user_id: int, duration: int = 60) -> List[Dict[str, Any]]:
        """
        模拟单个用户的真实活动模式

        Args:
            user_id: 用户ID
            duration: 测试持续时间（秒）

        Returns:
            请求结果列表
        """
        logger.info(f"👤 用户{user_id} 开始活动模拟（{duration}秒）")

        results = []
        start_time = time.time()

        while time.time() - start_time < duration:
            # 模拟用户行为间隔（1-5秒）
            await asyncio.sleep(random.uniform(1.0, 5.0))

            # 随机选择API调用类型
            api_calls = [
                ("GET", "/api/v1/health"),  # 心跳检查
                ("GET", "/api/v1/health"),  # 更频繁的健康检查
                ("GET", "/api/v1/health"),  # 健康检查权重最高
            ]

            method, endpoint = random.choice(api_calls)

            # 发送请求
            result = await self.make_request(method, endpoint)
            result["user_id"] = user_id
            result["timestamp"] = time.time()
            results.append(result)

            # 添加用户思考时间
            await asyncio.sleep(random.uniform(0.5, 2.0))

        logger.info(f"✅ 用户{user_id} 活动完成，发送 {len(results)} 个请求")
        return results

    async def test_realistic_multiuser_scenario(self, num_users: int = 10, duration: int = 30) -> RealisticTestResult:
        """
        测试真实多用户场景

        Args:
            num_users: 并发用户数
            duration: 测试持续时间（秒）

        Returns:
            测试结果
        """
        logger.info(f"🎭 开始真实多用户场景测试")
        logger.info(f"   并发用户: {num_users}")
        logger.info(f"   测试时长: {duration}秒")
        logger.info(f"   预期QPS: ~{num_users * 2}（每用户平均2请求/秒）")

        start_time = time.time()

        # 模拟用户逐渐加入（避免雷击效应）
        user_tasks = []
        for i in range(num_users):
            # 用户间隔0.5秒加入
            await asyncio.sleep(0.5)
            task = asyncio.create_task(self.simulate_user_activity(i, duration))
            user_tasks.append(task)

        # 等待所有用户完成
        all_results = await asyncio.gather(*user_tasks)

        test_duration = time.time() - start_time

        # 汇总统计
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        response_times = []
        errors = []

        for user_results in all_results:
            for result in user_results:
                total_requests += 1
                if result["success"]:
                    successful_requests += 1
                else:
                    failed_requests += 1
                    error_msg = result.get("error", f"Status: {result.get('status_code')}")
                    errors.append(f"User{result['user_id']}: {error_msg}")

                response_times.append(result["response_time"])

        # 创建结果对象
        test_result = RealisticTestResult(
            test_name="真实多用户场景测试",
            users=num_users,
            duration=test_duration,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            qps=total_requests / test_duration if test_duration > 0 else 0,
            errors=errors[:10]  # 只保留前10个错误
        )

        return test_result

    async def test_burst_traffic_handling(self, burst_users: int = 20, burst_duration: int = 10) -> RealisticTestResult:
        """
        测试突发流量处理能力

        Args:
            burst_users: 突发用户数
            burst_duration: 突发持续时间（秒）

        Returns:
            测试结果
        """
        logger.info(f"🚀 开始突发流量测试")
        logger.info(f"   突发用户: {burst_users}")
        logger.info(f"   突发时长: {burst_duration}秒")

        start_time = time.time()

        async def burst_user_activity(user_id: int):
            """突发用户活动（更密集的请求）"""
            results = []
            end_time = start_time + burst_duration

            while time.time() < end_time:
                result = await self.make_request("GET", "/api/v1/health")
                result["user_id"] = user_id
                results.append(result)

                # 突发流量间隔更短
                await asyncio.sleep(random.uniform(0.1, 0.5))

            return results

        # 同时启动所有突发用户
        user_tasks = [burst_user_activity(i) for i in range(burst_users)]
        all_results = await asyncio.gather(*user_tasks)

        test_duration = time.time() - start_time

        # 汇总统计
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        response_times = []
        errors = []

        for user_results in all_results:
            for result in user_results:
                total_requests += 1
                if result["success"]:
                    successful_requests += 1
                else:
                    failed_requests += 1
                    error_msg = result.get("error", f"Status: {result.get('status_code')}")
                    errors.append(f"User{result['user_id']}: {error_msg}")

                response_times.append(result["response_time"])

        test_result = RealisticTestResult(
            test_name="突发流量处理测试",
            users=burst_users,
            duration=test_duration,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            qps=total_requests / test_duration if test_duration > 0 else 0,
            errors=errors[:10]
        )

        return test_result

def print_realistic_test_results(results: List[RealisticTestResult]):
    """打印真实测试结果"""
    logger.info("\n" + "=" * 80)
    logger.info("📊 真实场景性能测试结果")
    logger.info("=" * 80)

    for result in results:
        logger.info(f"\n🔍 {result.test_name}")
        logger.info(f"   👥 并发用户: {result.users}")
        logger.info(f"   ⏱️  测试时长: {result.duration:.1f}秒")
        logger.info(f"   📊 总请求数: {result.total_requests}")
        logger.info(f"   ✅ 成功率: {result.success_rate:.1f}%")
        logger.info(f"   ⚡ QPS: {result.qps:.1f}")
        logger.info(f"   🏃 平均响应时间: {result.avg_response_time:.3f}秒")
        logger.info(f"   📈 最大响应时间: {result.max_response_time:.3f}秒")
        logger.info(f"   📉 最小响应时间: {result.min_response_time:.3f}秒")

        if result.errors:
            logger.info(f"   ❌ 错误数: {len(result.errors)}")
            for error in result.errors[:3]:  # 显示前3个错误
                logger.info(f"      - {error}")

    # 总体评估
    logger.info("\n🎯 P1性能调优真实场景评估:")

    # 检查是否有测试结果
    if not results:
        logger.info("   ⚠️ 没有可用的测试结果")
        return

    # 多用户场景评估
    multiuser_test = next((r for r in results if "多用户" in r.test_name), None)
    if multiuser_test:
        if multiuser_test.success_rate > 95:
            logger.info("   ✅ 多用户并发体验优秀 - 高成功率且响应稳定")
        elif multiuser_test.success_rate > 85:
            logger.info("   ⚠️ 多用户并发体验良好 - 但还有优化空间")
        else:
            logger.info("   ❌ 多用户并发需要优化 - 成功率偏低")

    # 突发流量评估
    burst_test = next((r for r in results if "突发" in r.test_name), None)
    if burst_test:
        if burst_test.success_rate > 80:
            logger.info("   ✅ 突发流量处理能力强 - 系统扛得住压力")
        elif burst_test.success_rate > 60:
            logger.info("   ⚠️ 突发流量处理基本可用 - 建议进一步优化")
        else:
            logger.info("   ❌ 突发流量处理需要加强 - 可能需要更多资源")

    # 响应时间评估
    avg_response_times = [r.avg_response_time for r in results if r.avg_response_time > 0]
    if avg_response_times:
        overall_avg = sum(avg_response_times) / len(avg_response_times)
        if overall_avg < 0.1:
            logger.info("   ✅ 响应时间优秀 - 用户体验极佳")
        elif overall_avg < 0.5:
            logger.info("   ✅ 响应时间良好 - 用户体验满意")
        else:
            logger.info("   ⚠️ 响应时间需要优化 - 影响用户体验")

async def main():
    """主函数"""
    logger.info("🎭 启动真实场景性能测试")

    async with RealisticPerformanceTestRunner() as test_runner:
        try:
            results = []

            # 测试1：真实多用户场景（温和测试）
            logger.info("\n🎯 测试1：真实多用户并发场景")
            multiuser_result = await test_runner.test_realistic_multiuser_scenario(
                num_users=8,  # 8个用户
                duration=20   # 20秒测试
            )
            results.append(multiuser_result)

            # 间隔休息
            logger.info("\n⏸️ 休息5秒，避免频率限制影响...")
            await asyncio.sleep(5)

            # 测试2：突发流量处理（压力测试）
            logger.info("\n🎯 测试2：突发流量处理能力")
            burst_result = await test_runner.test_burst_traffic_handling(
                burst_users=12,  # 12个突发用户
                burst_duration=8  # 8秒突发
            )
            results.append(burst_result)

            # 打印结果
            print_realistic_test_results(results)

            logger.info("\n✨ 真实场景性能测试完成！")
            logger.info("💡 建议：P1优化在真实场景下表现如何？")

        except KeyboardInterrupt:
            logger.info("\n⏹️ 测试被用户中断")
        except Exception as e:
            logger.error(f"\n❌ 测试运行失败: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(main())
