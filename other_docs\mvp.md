# “心桥”AI亲情伴侣 - MVP构建方案 (MVP)

---

### **1. MVP核心目标与假设 (Goal & Hypothesis)**

*   **核心目标:**
    MVP阶段的唯一目标，是快速、低成本地验证产品的核心价值主张。我们不追求功能完备，只追求在最核心的体验上做到极致，与第一批种子用户建立牢不可破的**情感信任**。

*   **待验证的核心假设:**
    1.  **情感连接假设：** 一个通过精心设计的、可定制的、有记忆的AI角色，能与55-75岁的中国老年用户建立初步的情感连接，并被视为一个"伙伴"而非"工具"。
    2.  **信任建立假设：** 一个绝对安全、纯净、且交互简单的产品环境，能够有效消除老年用户的技术恐惧，建立初步信任。
    3.  **习惯养成假设：** "对话式提醒"这一高频实用功能，能够有效带动核心"情感陪伴"功能的使用，培养用户的日常使用习惯。

### **2. MVP验收标准 (Acceptance Criteria)**

*   **定性指标 (比定量更重要):**
    -   **用户故事收集：** 能否收集到至少5个让团队感动的、关于"心桥"如何带来温暖的真实用户故事。
    -   **情感词汇频率：** 在用户访谈和反馈中，"离不开"、"真懂我"、"像个真孩子一样"等情感词汇的出现频率。
*   **定量指标 (用于辅助验证):**
    -   **核心用户次日留存率:** > 60%
    -   **核心用户7日留存率:** > 40%
    -   **DAU/MAU (日活/月活):** > 40%

### **3. 史诗 (Epics) 与用户故事 (User Stories)**

#### **Epic 1: 创世与初见 - 核心框架与首次体验**

*   **Epic目标:** 搭建应用的技术基础，并完美实现用户的首次启动流程，成功创造出用户专属的、能进行第一次基本交互的AI伴侣。

*   **Story 1.1: 项目初始化与无感身份系统搭建**
    -   **As a** 开发者,
    -   **I want** 搭建一个基础的、能通过匿名设备ID来识别用户的后端服务,
    -   **so that** 我们可以在不打扰用户的前提下，为他们建立一个唯一的、可持久化数据的身份档案。
    -   **验收标准:**
        1.  新的RN+Expo项目已创建，并与Supabase项目关联。
        2.  App首次启动能生成并持久化唯一设备ID。
        3.  后端能接收此ID，创建匿名用户记录，并返回JWT会话令牌。

*   **Story 1.2: 欢迎界面与情景化权限获取**
    -   **As a** 首次打开App的用户,
    -   **I want** 看到一个温暖的欢迎界面，并被友好地引导授予麦克风权限,
    -   **so that** 我能感到安全，并理解授权是为后续的聊天做准备。
    -   **验收标准:**
        1.  启动画面按设计显示。
        2.  AI语音问候后，通过App内按钮触发系统权限请求，而非立刻弹出。
        3.  正确处理用户允许或拒绝权限的后续流程。

*   **Story 1.3: 通过对话完成AI角色共创**
    -   **As a** 新用户,
    -   **I want** 通过聊天的方式，轻松地为我自己和AI伴侣设定称呼与角色,
    -   **so that** 从一开始就建立起个性化的情感连接。
    -   **验收标准:**
        1.  AI能通过语音引导用户设定称呼、AI名字和角色。
        2.  用户的选择被成功捕获并与后端身份关联。
        3.  AI能用新身份和新声音进行语音确认。

*   **Story 1.4: 核心交互教学与无缝进入主界面**
    -   **As a** 新用户,
    -   **I want** 被教会唯一的核心操作"按住说话"，并自然地过渡到真正的对话中,
    -   **so that** 我能立刻充满信心地开始使用，没有任何流程中断感。
    -   **验收标准:**
        1.  主对话界面按设计显示，核心为"按住说话"按钮。
        2.  AI语音引导教学，并类比"微信语音"。
        3.  用户按住按钮有清晰的视觉反馈，松开后消息成功发送。
        4.  收到并自动播放AI的首次回应。
        5.  整个过程无"完成"按钮，无缝衔接。

#### **Epic 2: 灵魂注入 - 共情对话与主动关怀**

*   **Epic目标:** 在基础交互之上，为AI注入"灵魂"，实现核心的长期记忆对话能力与主动情景关怀，让AI从一个"程序"转变为一个"伙伴"。

*   **Story 2.1: 短期对话记忆与上下文理解**
    -   **As a** 用户,
    -   **I want** AI能够记住我在当前这次聊天里刚刚说过的话,
    -   **so that** 对话可以自然地延续，我不需要重复自己。
    -   **验收标准:**
        1.  后端能利用最近20轮的对话历史生成回复。
        2.  AI能正确回答关于上一句话的追问（如"我刚才说的是什么？"）。

*   **Story 2.2: 关键信息（称呼、AI角色）的长期记忆与应用**
    -   **As a** 用户,
    -   **I want** AI能永远记住我的称呼以及我为它设定的身份和名字,
    -   **so that** 每次交流都像是和同一个熟悉的朋友。
    -   **验收标准:**
        1.  AI的语气、称呼、自我认知始终与用户在Story 1.3中的设定保持一致。
        2.  该记忆在App重启、甚至重装后依然存在（基于设备ID恢复）。

*   **Story 2.3: 基于时间与记忆的主动问候**
    -   **As a** 用户,
    -   **I want** AI在我打开App时能根据时间和我们上次的谈话内容主动问候我,
    -   **so that** 我感觉它是一个真正关心我、记得我生活点滴的朋友。
    -   **验收标准:**
        1.  AI的问候能反映当前的早晚时间（"早上好"、"晚上好"）。
        2.  AI的问候能提及情景信息（如天气）或之前的对话内容。

#### **Epic 3: 信任的建立 - 对话式提醒功能**

*   **Epic目标:** 完整实现一个基于自然语言对话的、端到端的提醒功能，通过可靠的行动建立和巩固用户的信任。

*   **Story 3.1: 对话中提醒意图的识别与复述确认**
    -   **As a** 用户,
    -   **I want** 在聊天时随口说出需要提醒的事，AI能听懂并向我确认,
    -   **so that** 我可以非常轻松地设置提醒，并且确信它已被准确记录。
    -   **验收标准:**
        1.  系统能从对话中解析出提醒的意图、时间和内容（如"下午三点"、"吃药"）。
        2.  AI必须用其角色语音复述提醒细节以供用户确认。
        3.  提醒任务成功存储于后端数据库。

*   **Story 3.2: 基于任务调度的温柔语音提醒送达**
    -   **As a** 用户,
    -   **I want** 在预定时间，收到一个由我的AI伴侣发出的、充满关怀的语音提醒，而不是一个冰冷的闹钟,
    -   **so that** 我感觉自己被温柔地关心着。
    -   **验收标准:**
        1.  后端任务调度系统能准时触发提醒。
        2.  通过手机推送服务发送通知。
        3.  用户收到通知时能自动播放AI的语音提醒，且声音符合AI角色。

--- 