[tool:pytest]
# === 基础配置 ===
minversion = 6.0
addopts =
    -ra
    --strict-markers
    --strict-config
    --disable-warnings
    --tb=short
    --durations=10

# === 测试目录 ===
testpaths = tests

# === Python路径配置 ===
# 添加项目根目录到Python路径，使得可以导入shared模块
pythonpath = . ../.. ../../shared

# === Python文件匹配模式 ===
python_files = tests/*.py test_*.py *_test.py

# === 测试函数匹配模式 ===
python_functions = test_*

# === 测试类匹配模式 ===
python_classes = Test* *Tests

# === 标记配置 ===
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    database: marks tests that require database connection
    external_api: marks tests that require external API access
    mock: marks tests that use mocking
    vector: marks tests related to vector operations
    auth: marks tests related to authentication
    api: marks tests related to API endpoints

# === 过滤器配置 ===
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# === 异步测试配置 ===
asyncio_mode = auto

# === 日志配置 ===
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# === 覆盖率配置（如果使用pytest-cov） ===
# 这些配置也可以通过命令行参数传递
# --cov=api --cov=db --cov=agents
# --cov-report=html --cov-report=xml --cov-report=term-missing
