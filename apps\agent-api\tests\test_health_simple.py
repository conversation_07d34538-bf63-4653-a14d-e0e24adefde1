"""
简化的健康检查测试 - 避免复杂的依赖问题
"""
import pytest
from fastapi.testclient import TestClient
from fastapi import FastAPI
from api.routes.health import health_router

# 创建一个最小的FastAPI应用来测试健康检查
app = FastAPI()
app.include_router(health_router, prefix="/api/v1")

client = TestClient(app)

def test_health_endpoint_returns_correct_format():
    """测试健康检查接口是否返回正确格式 - 这个测试应该失败"""
    response = client.get("/api/v1/health")
    assert response.status_code == 200

    data = response.json()
    # 这些断言应该失败，因为当前接口返回 "success" 而不是 "healthy"
    assert "status" in data
    assert data["status"] == "healthy"  # 当前返回 "success"，所以会失败
    assert "timestamp" in data  # 当前没有timestamp字段，所以会失败
