# 心桥项目技术架构文档

## 📋 架构文档概览

本目录包含"心桥"项目的完整技术架构设计文档，已按模块化方式组织为7个专门的文档片段，每个片段专注于特定的技术领域。

## 📚 文档结构

### 核心架构文档

1. **[项目概述](./01-project-overview.md)**
   - 项目简介与核心理念
   - 后端架构核心思想（原生LLM编排模式）
   - 前端架构核心思想（双模交互界面）
   - 技术特色总结

2. **[高层架构](./02-high-level-architecture.md)**
   - 核心组件介绍（客户端、心桥后端服务、火山RTC、Supabase）
   - 原生LLM编排模式详解
   - 系统交互流程与时序图
   - 端到端的架构设计

3. **[API接口设计](./03-api-design.md)**
   - 核心实时流处理接口（火山RTC对接）
   - 实时语音会话控制API
   - 传统文本聊天接口
   - 会话管理与用户画像API

4. **[可插拔记忆服务](./04-pluggable-memory-system.md)**
   - IMemoryService抽象层设计
   - Zep/Mem0服务实现
   - 工厂模式与依赖注入
   - 与对话编排服务的集成

5. **[后端详细设计](./05-backend-design.md)**
   - 内部模块设计（Service层架构）
   - 性能与健壮性要求
   - 测试策略（单元、集成、性能测试）
   - 数据模型设计

6. **[前端架构设计](./06-frontend-architecture.md)**
   - React Native + Expo技术栈
   - UI组件架构（原子设计模式）
   - 双模交互界面实现方案
   - 状态管理与性能保障

7. **[安全性、部署与技术预研](./07-security-deployment.md)**
   - 安全性考虑与实现
   - 部署与运维方案
   - 技术预研建议
   - 风险评估与缓解策略

## 🎯 阅读指南

### 对于技术架构师
建议按顺序阅读：
1. [项目概述](./01-project-overview.md) → [高层架构](./02-high-level-architecture.md)
2. [安全性、部署与技术预研](./07-security-deployment.md)

### 对于后端开发者
重点阅读：
1. [高层架构](./02-high-level-architecture.md)
2. [API接口设计](./03-api-design.md)
3. [可插拔记忆服务](./04-pluggable-memory-system.md)
4. [后端详细设计](./05-backend-design.md)

### 对于前端开发者
重点阅读：
1. [高层架构](./02-high-level-architecture.md)
2. [API接口设计](./03-api-design.md)
3. [前端架构设计](./06-frontend-architecture.md)

### 对于运维工程师
重点阅读：
1. [项目概述](./01-project-overview.md)
2. [安全性、部署与技术预研](./07-security-deployment.md)
3. [后端详细设计](./05-backend-design.md)

## 🔗 相关文档

- **[原始完整架构文档](../architecture.md)** - 包含所有架构内容的完整版本
- **[产品需求文档](../prd.md)** - 产品功能与需求规格
- **[开发工作流程](../flow.md)** - 日常开发指南
- **[UI/UX设计规范](../uxui.md)** - 界面设计规范

## 🚀 快速开始

如果您是新加入的开发者：

1. **了解项目背景** → [项目概述](./01-project-overview.md)
2. **理解整体架构** → [高层架构](./02-high-level-architecture.md)
3. **查看具体实现** → 根据您的角色选择相应的技术文档
4. **了解部署流程** → [安全性、部署与技术预研](./07-security-deployment.md)

## 💡 关键技术特色

- **原生LLM编排模式** - 由后端服务自主编排LLM与工具调用。
- **可插拔记忆系统** - 将对话逻辑与记忆实现解耦，支持专业的外部记忆服务。
- **双模交互界面** - 语音与文本无缝切换的用户体验。
- **高性能优化** - 端到端延迟控制和渲染性能保障。

## 📝 文档维护

这些架构文档与项目代码保持同步更新。如需修改架构设计，请：

1. 更新对应的架构文档
2. 确保与其他文档的一致性
3. 通知相关开发团队成员

---

**最后更新：** 2025年1月 | **维护者：** Winston, Architect 