import asyncio
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from openai import AsyncOpenAI
from mem0 import AsyncMemoryClient
from dotenv import load_dotenv
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- 1. 配置 ---
load_dotenv(dotenv_path="../1.env")

# API 配置
MEM0_API_KEY = os.environ.get("MEM0_API_KEY")
ARK_API_KEY = os.environ.get("VOLCENGINE_API_KEY")
VOLCANO_LLM_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
VOLCANO_LLM_ENDPOINT_ID = os.environ.get("VOLCANO_LLM_ENDPOINT_ID")

# 用户和配置
USER_ID = "demo_user_volcano_mem0_chat_001"
MEMORY_RETENTION_DAYS = 30
SEARCH_SCORE_THRESHOLD = 0.7
MAX_MEMORIES_PER_SEARCH = 5

# 系统提示
SYSTEM_PROMPT = """你是一个温暖、体贴的情感陪伴AI助手。
你拥有长期记忆，能够记住用户的情感状态、重要事件、偏好和我们的对话历史。
请以温和、共情的语调回应，关注用户的情感需求。
当用户感到难过时给予安慰，开心时分享喜悦，困惑时提供支持。
利用你的记忆为用户提供连续性和个性化的情感支持。"""

# 记忆分类系统
MEMORY_CATEGORIES = {
    "personal_info": "个人信息",
    "preferences": "偏好习惯",
    "emotional_state": "情感状态",
    "relationships": "人际关系",
    "activities": "活动事件",
    "goals": "目标计划"
}

# 情感关键词映射
EMOTION_KEYWORDS = {
    "joy": ["开心", "高兴", "快乐", "兴奋", "愉快", "满足"],
    "sadness": ["难过", "伤心", "沮丧", "痛苦", "失落", "孤独"],
    "anxiety": ["焦虑", "担心", "紧张", "害怕", "不安", "忧虑"],
    "anger": ["生气", "愤怒", "气愤", "恼火", "烦躁", "愤慨"],
    "confusion": ["困惑", "迷茫", "不知道", "纠结", "犹豫", "疑惑"]
}

class MemoryManager:
    """优化的记忆管理器"""

    def __init__(self, mem0_client: AsyncMemoryClient, user_id: str):
        self.client = mem0_client
        self.user_id = user_id

    async def search_memories(
        self,
        query: str,
        limit: int = MAX_MEMORIES_PER_SEARCH,
        threshold: float = SEARCH_SCORE_THRESHOLD,
        categories: Optional[List[str]] = None
    ) -> Tuple[List[Dict], Optional[str]]:
        """
        优化的记忆搜索：使用最新的过滤器和阈值控制
        """
        try:
            # 检测情感状态
            detected_emotion = self._detect_emotion(query)

            # 构建搜索过滤器
            filters = {
                "AND": [
                    {"user_id": self.user_id}
                ]
            }

            # 如果检测到情感，添加情感过滤
            if detected_emotion:
                filters["AND"].append({
                    "OR": [
                        {"categories": {"contains": "emotional_state"}},
                        {"metadata": {"emotion": detected_emotion}}
                    ]
                })

            # 如果指定了分类，添加分类过滤
            if categories:
                filters["AND"].append({
                    "categories": {"contains": categories}
                })

            # 执行搜索（使用v2 API）
            search_result = await self.client.search(
                query=query,
                user_id=self.user_id,
                limit=limit,
                threshold=threshold,
                filters=filters,
                version="v2",
                output_format="v1.1"
            )

            # 处理搜索结果
            memories = []
            if search_result and hasattr(search_result, 'results'):
                memories = search_result.results
            elif isinstance(search_result, list):
                memories = search_result

            # 按相关性和重要性排序
            sorted_memories = self._rank_memories(memories, query)

            return sorted_memories[:limit], detected_emotion

        except Exception as e:
            logger.error(f"记忆搜索失败: {e}")
            return [], None

    def _detect_emotion(self, text: str) -> Optional[str]:
        """检测文本中的情感"""
        text_lower = text.lower()
        for emotion, keywords in EMOTION_KEYWORDS.items():
            if any(keyword in text_lower for keyword in keywords):
                return emotion
        return None

    def _rank_memories(self, memories: List[Dict], query: str) -> List[Dict]:
        """为记忆排序：基于相关性和重要性"""
        if not memories:
            return []

        # 简单的排序策略：优先显示最相关和最新的记忆
        return sorted(
            memories,
            key=lambda m: (
                m.get('score', 0),  # 相关性分数
                m.get('created_at', ''),  # 创建时间
                1 if m.get('categories', []) and 'personal_info' in m.get('categories', []) else 0  # 个人信息优先
            ),
            reverse=True
        )

    async def add_memory(
        self,
        messages: List[Dict],
        detected_emotion: Optional[str] = None,
        custom_metadata: Optional[Dict] = None
    ) -> Optional[Dict]:
        """
        优化的记忆添加：使用v2 API和图形记忆功能
        """
        try:
            # 构建元数据
            metadata = {
                "timestamp": datetime.now().isoformat(),
                "session_type": "emotional_companion",
                "retention_days": MEMORY_RETENTION_DAYS
            }

            # 添加情感信息
            if detected_emotion:
                metadata["emotion"] = detected_emotion
                metadata["emotional_context"] = True

            # 添加自定义元数据
            if custom_metadata:
                metadata.update(custom_metadata)

            # 自动分类
            categories = self._categorize_messages(messages, detected_emotion)

            # 使用v2 API添加记忆（推荐方式）
            add_result = await self.client.add(
                messages=messages,
                user_id=self.user_id,
                metadata=metadata,
                categories=categories,
                version="v2",  # 使用推荐的v2版本
                output_format="v1.1",
                enable_graph=True  # 启用图形记忆功能
            )

            return add_result

        except Exception as e:
            logger.error(f"记忆添加失败: {e}")
            return None

    def _categorize_messages(self, messages: List[Dict], emotion: Optional[str]) -> List[str]:
        """自动为消息分类"""
        categories = []

        # 合并消息内容进行分析
        content = " ".join([msg.get('content', '') for msg in messages])
        content_lower = content.lower()

        # 基于关键词的分类
        personal_keywords = ["我叫", "我的名字", "我住在", "我来自", "我是"]
        if any(keyword in content_lower for keyword in personal_keywords):
            categories.append("personal_info")

        preference_keywords = ["我喜欢", "我讨厌", "我爱", "我不喜欢", "我偏好"]
        if any(keyword in content_lower for keyword in preference_keywords):
            categories.append("preferences")

        activity_keywords = ["我去了", "我做了", "我参加", "我完成了", "我学习"]
        if any(keyword in content_lower for keyword in activity_keywords):
            categories.append("activities")

        goal_keywords = ["我想", "我计划", "我希望", "我的目标", "我要"]
        if any(keyword in content_lower for keyword in goal_keywords):
            categories.append("goals")

        # 如果检测到情感，添加情感分类
        if emotion:
            categories.append("emotional_state")

        # 默认分类
        if not categories:
            categories.append("general")

        return categories

    async def cleanup_old_memories(self) -> int:
        """清理过期记忆"""
        try:
            # 计算过期时间
            cutoff_date = datetime.now() - timedelta(days=MEMORY_RETENTION_DAYS)

            # 获取所有记忆
            all_memories = await self.client.get_all(
                user_id=self.user_id,
                version="v2",
                output_format="v1.1"
            )

            if not all_memories or not hasattr(all_memories, 'results'):
                return 0

            # 找出过期的记忆
            expired_memories = []
            for memory in all_memories.results:
                created_at = memory.get('created_at', '')
                if created_at:
                    try:
                        created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        if created_date < cutoff_date:
                            # 检查是否为重要记忆（个人信息等）
                            categories = memory.get('categories', [])
                            if 'personal_info' not in categories:
                                expired_memories.append(memory.get('id'))
                    except:
                        continue

            # 删除过期记忆
            deleted_count = 0
            for memory_id in expired_memories:
                try:
                    await self.client.delete(memory_id)
                    deleted_count += 1
                except:
                    continue

            return deleted_count

        except Exception as e:
            logger.error(f"清理记忆失败: {e}")
            return 0


class EmotionalChatbot:
    """情感聊天机器人"""

    def __init__(self):
        self.mem0_client = None
        self.llm_client = None
        self.memory_manager = None

    async def initialize(self):
        """初始化客户端"""
        if not all([MEM0_API_KEY, ARK_API_KEY, VOLCANO_LLM_ENDPOINT_ID]):
            raise ValueError("缺少必要的API密钥或配置")

        self.mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)
        self.llm_client = AsyncOpenAI(
            base_url=VOLCANO_LLM_BASE_URL,
            api_key=ARK_API_KEY,
        )
        self.memory_manager = MemoryManager(self.mem0_client, USER_ID)

        logger.info("情感聊天机器人初始化完成")

    async def chat(self, user_input: str) -> str:
        """处理用户输入并生成回复"""
        try:
            # 1. 搜索相关记忆
            relevant_memories, detected_emotion = await self.memory_manager.search_memories(
                query=user_input,
                limit=MAX_MEMORIES_PER_SEARCH
            )

            # 2. 构建LLM提示
            llm_messages = await self._build_llm_messages(
                user_input, relevant_memories, detected_emotion
            )

            # 3. 调用LLM生成回复
            ai_response = await self._generate_response(llm_messages)

            # 4. 异步保存记忆（不阻塞响应）
            asyncio.create_task(self._save_conversation(
                user_input, ai_response, detected_emotion
            ))

            return ai_response

        except Exception as e:
            logger.error(f"聊天处理失败: {e}")
            return "抱歉，我遇到了一些问题，请稍后再试。"

    async def _build_llm_messages(
        self,
        user_input: str,
        memories: List[Dict],
        emotion: Optional[str]
    ) -> List[Dict]:
        """构建LLM消息"""
        messages = [{"role": "system", "content": SYSTEM_PROMPT}]

        # 添加记忆上下文
        if memories:
            memory_context = self._format_memory_context(memories, emotion)
            messages.append({
                "role": "system",
                "content": memory_context
            })

        # 添加用户输入
        messages.append({"role": "user", "content": user_input})

        return messages

    def _format_memory_context(self, memories: List[Dict], emotion: Optional[str]) -> str:
        """格式化记忆上下文"""
        context_parts = ["以下是相关的记忆信息："]

        for memory in memories:
            memory_text = memory.get('memory', '')
            categories = memory.get('categories', [])
            if memory_text:
                category_info = f"[{', '.join(categories)}]" if categories else ""
                context_parts.append(f"- {memory_text} {category_info}")

        if emotion:
            emotion_hint = f"\n\n用户当前可能的情感状态：{emotion}，请以温暖、共情的方式回应。"
            context_parts.append(emotion_hint)

        context_parts.append("\n请根据这些记忆信息和情感状态来回应用户。")

        return "\n".join(context_parts)

    async def _generate_response(self, messages: List[Dict]) -> str:
        """生成AI回复"""
        try:
            response = await self.llm_client.chat.completions.create(
                model=VOLCANO_LLM_ENDPOINT_ID,
                messages=messages,
                max_tokens=1024,
                temperature=0.7,
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"LLM响应生成失败: {e}")
            return "抱歉，我现在无法生成回复。"

    async def _save_conversation(
        self,
        user_input: str,
        ai_response: str,
        emotion: Optional[str]
    ):
        """异步保存对话记忆"""
        try:
            conversation_messages = [
                {"role": "user", "content": user_input},
                {"role": "assistant", "content": ai_response}
            ]

            await self.memory_manager.add_memory(
                messages=conversation_messages,
                detected_emotion=emotion,
                custom_metadata={"conversation_type": "chat"}
            )

        except Exception as e:
            logger.error(f"保存对话记忆失败: {e}")

    async def maintenance(self):
        """定期维护任务"""
        try:
            # 清理过期记忆
            deleted_count = await self.memory_manager.cleanup_old_memories()
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条过期记忆")
        except Exception as e:
            logger.error(f"维护任务失败: {e}")


# 主要功能函数
async def main_chat():
    """主聊天功能"""
    chatbot = EmotionalChatbot()

    try:
        await chatbot.initialize()

        print("🤖 情感陪伴AI助手已启动")
        print("💡 我拥有持久记忆功能，会记住我们的对话和你的情感状态")
        print("📝 输入 'quit' 或 'exit' 退出聊天")
        print("🔧 输入 'maintenance' 执行维护任务")

        conversation_count = 0

        while True:
            user_input = await asyncio.to_thread(input, f"\n👤 你: ")

            if user_input.lower() in ['quit', 'exit']:
                print("👋 再见！我会记住我们的对话。")
                break

            if user_input.lower() == 'maintenance':
                await chatbot.maintenance()
                print("🔧 维护任务完成")
                continue

            # 处理用户输入
            ai_response = await chatbot.chat(user_input)
            print(f"🤖 AI: {ai_response}")

            # 定期执行维护
            conversation_count += 1
            if conversation_count % 10 == 0:
                await chatbot.maintenance()

    except Exception as e:
        logger.error(f"聊天程序错误: {e}")
        print(f"❌ 程序出现错误: {e}")


async def test_improved_features():
    """测试改进后的功能"""
    print("🧪 测试改进后的mem0功能...")

    try:
        chatbot = EmotionalChatbot()
        await chatbot.initialize()

        # 测试记忆添加
        test_messages = [
            {"role": "user", "content": "我叫张三，我很开心今天学会了新技能"},
            {"role": "assistant", "content": "太棒了张三！学习新技能确实是件让人开心的事情。"}
        ]

        result = await chatbot.memory_manager.add_memory(
            messages=test_messages,
            detected_emotion="joy"
        )

        print(f"✅ 记忆添加测试: {result}")

        # 测试记忆搜索
        memories, emotion = await chatbot.memory_manager.search_memories(
            query="我的名字",
            categories=["personal_info"]
        )

        print(f"✅ 记忆搜索测试: 找到 {len(memories)} 条记忆")

        # 测试对话
        response = await chatbot.chat("你还记得我的名字吗？")
        print(f"✅ 对话测试: {response}")

    except Exception as e:
        logger.error(f"测试失败: {e}")


if __name__ == "__main__":
    print("=== 改进版 Mem0 + 火山引擎 情感陪伴AI助手 ===")
    print("选择操作:")
    print("1. 开始聊天")
    print("2. 测试改进功能")
    print("3. 退出")

    try:
        choice = input("请输入选择 (1-3): ").strip()

        if choice == "1":
            asyncio.run(main_chat())
        elif choice == "2":
            asyncio.run(test_improved_features())
        elif choice == "3":
            print("👋 再见！")
        else:
            print("无效选择，启动聊天模式...")
            asyncio.run(main_chat())

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
