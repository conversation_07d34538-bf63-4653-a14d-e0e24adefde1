"""
API测试配置文件
"""

import os
from typing import Dict, Any

class TestConfig:
    """测试配置类"""

    # 基础配置
    BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8003")
    TIMEOUT = 30.0

    # 测试数据配置
    TEST_DEVICE_ID_PREFIX = "test_device_"
    TEST_USER_NICKNAME_PREFIX = "测试用户_"
    TEST_SESSION_TOPIC = "端到端测试会话"
    TEST_REMINDER_CONTENT = "测试提醒内容"

    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "api_e2e_test.log"

    # 测试控制开关
    ENABLE_TESTS = {
        "health_check": True,
        "authentication": True,
        "user_management": True,
        "character_management": True,
        "session_management": True,
        "reminder_management": True,
        "rtc_session": True,
        "chat_connections": True,
    }

    # 性能要求
    PERFORMANCE_THRESHOLDS = {
        "api_response_time": 2.0,  # 秒
        "health_check_time": 0.5,  # 秒
        "auth_time": 3.0,  # 秒
    }

    # 用户设置测试数据
    USER_SETTINGS_TEST_DATA = {
        "theme": "light",
        "font_size": "large",
        "high_contrast": True,
        "notifications_enabled": True,
        "quiet_hours_enabled": False,
        "quiet_hours_start": "22:00",
        "quiet_hours_end": "08:00",
        "language": "zh-CN"
    }

    # 用户画像测试数据
    USER_PROFILE_TEST_DATA = {
        "age_range": "55-65",
        "core_needs": ["情感陪伴", "健康提醒", "新闻资讯"],
        "interests": ["历史", "健康", "家庭"],
        "communication_style_preference": "温和友善"
    }

    @classmethod
    def get_test_headers(cls) -> Dict[str, str]:
        """获取测试请求头"""
        return {
            "Content-Type": "application/json",
            "User-Agent": "XinQiao-E2E-Test/1.0"
        }

    @classmethod
    def get_device_info(cls, device_id: str) -> Dict[str, Any]:
        """获取设备信息"""
        return {
            "device_id": device_id,
            "platform": "ios",
            "app_version": "1.0.0"
        }
