
## 🏗️ 核心功能模块

### 1. 认证与用户管理
- **匿名认证系统** - 基于设备指纹的无感认证
- **JWT Token管理** - 访问令牌和刷新令牌机制
- **用户画像管理** - 个性化设置和偏好配置
- **引导流程** - 新用户onboarding

### 2. 对话服务
- **SSE流式文本对话** - 实时流式响应
- **RTC语音对话** - 基于火山引擎的实时语音交互
- **Function Calling** - LLM工具调用能力
- **记忆系统集成** - 跨会话记忆管理

### 3. 会话管理
- **会话生命周期管理** - 创建、维护、结束会话
- **会话后分析** - 自动生成摘要和洞察
- **记忆服务同步** - 与外部记忆服务(Zep/Mem0)同步

### 4. 智能提醒
- **自然语言提醒创建** - 通过对话设置提醒
- **提醒CRUD操作** - 完整的提醒管理API
- **时间解析** - 支持中文自然语言时间表达

### 5. 危机干预
- **关键词检测** - 实时检测危机相关表达
- **脚本化回复** - 专业危机干预回复
- **心理援助热线推荐** - 400-161-9995

## 📋 API接口详细列表

### 认证服务 (`/api/v1/auth`)
```
POST /anonymous-login       # 匿名用户登录
POST /refresh-token         # Token刷新
POST /finalize_onboarding   # 完成引导流程
```

### 用户管理 (`/api/v1/user`)
```
GET  /profile              # 获取用户画像
PUT  /profile              # 更新用户画像
GET  /settings             # 获取用户设置
PUT  /settings             # 更新用户设置
```

### AI角色管理 (`/api/v1/characters`)
```
GET  /                     # 获取角色列表
POST /{character_id}/bind   # 绑定用户角色
```

### 文本对话 (`/api/v1/chat`)
```
POST /text_message          # SSE流式文本对话
POST /rtc_event_handler     # RTC事件回调处理
```

### RTC会话管理 (`/api/v1/rtc`)
```
POST /prepare_session       # 准备RTC会话
POST /end_session          # 结束RTC会话
GET  /sessions/{id}/status  # 查询会话状态
GET  /sessions/{id}/config  # 获取会话配置
```

### 提醒服务 (`/api/v1/reminders`)
```
POST /                     # 创建提醒
GET  /                     # 获取提醒列表
PUT  /{reminder_id}        # 更新提醒
DELETE /{reminder_id}      # 删除提醒
```

### 会话管理 (`/api/v1/chat/sessions`)
```
POST /end_session          # 结束文本会话
GET  /{sessionId}/analysis_status  # 查询分析状态
```

### 健康检查 (`/api/v1`)
```
GET  /health               # 服务健康检查
```

## 🔄 核心工作流程图

### 1. RTC语音对话流程

```mermaid
sequenceDiagram
    participant 移动端
    participant 后端接口
    participant 火山引擎RTC
    participant 豆包大模型
    participant 记忆服务
    participant 工具服务

    移动端->>后端接口: POST /rtc/prepare_session
    后端接口->>火山引擎RTC: 启动语音聊天接口
    火山引擎RTC-->>后端接口: 返回令牌、房间号、任务号
    后端接口-->>移动端: 返回RTC连接凭证

    Note over 移动端, 火山引擎RTC: 建立RTC连接，开始语音交互

    火山引擎RTC->>后端接口: 回调钩子：语音识别事件
    
    后端接口->>后端接口: 危机检测（优先级最高）
    alt 检测到危机
        后端接口->>火山引擎RTC: 禁用用户音频流
        后端接口-->>火山引擎RTC: 返回脚本化危机干预回复
    else 正常对话
        后端接口->>记忆服务: 检索用户记忆（3秒超时）
        记忆服务-->>后端接口: 返回记忆数据
        
        后端接口->>豆包大模型: 调用豆包接口（包含工具定义）
        豆包大模型-->>后端接口: 返回回复（可能包含工具调用）
        
        alt 包含工具调用
            后端接口->>火山引擎RTC: 更新语音聊天（命令="函数"）
            后端接口->>工具服务: 执行工具调用
            工具服务-->>后端接口: 返回工具结果
            火山引擎RTC->>后端接口: 回调钩子：函数调用事件
            后端接口->>豆包大模型: 发送工具结果，获取最终回复
            豆包大模型-->>后端接口: 返回最终AI回复
        end
        
        后端接口->>记忆服务: 异步更新对话记忆
        后端接口-->>火山引擎RTC: 返回AI回复（用于语音合成）
    end
    
    移动端->>后端接口: POST /rtc/end_session
    后端接口->>火山引擎RTC: 停止语音聊天接口
    后端接口->>后端接口: 异步会话分析任务
    后端接口-->>移动端: 会话结束确认
```

### 2. SSE文本对话流程

```mermaid
sequenceDiagram
    participant 移动端
    participant 后端接口
    participant 危机检测
    participant 记忆服务
    participant 豆包大模型
    participant 数据库

    移动端->>后端接口: POST /chat/text_message (SSE连接)
    
    后端接口->>危机检测: 危机检测（最高优先级）
    alt 检测到危机
        危机检测-->>后端接口: 返回危机检测结果
        后端接口-->>移动端: SSE: 危机警报事件
        loop 流式危机回复
            后端接口-->>移动端: SSE: 文本块事件（脚本化回复）
        end
        后端接口-->>移动端: SSE: 流结束
    else 正常对话
        后端接口->>数据库: 异步保存用户消息
        
        后端接口->>记忆服务: 快速检索记忆（3秒超时）
        记忆服务-->>后端接口: 返回记忆数据或空上下文
        
        后端接口->>豆包大模型: 真正流式调用语言模型接口
        
        loop 真实流式响应（30秒超时保护）
            豆包大模型-->>后端接口: 数据块
            后端接口-->>移动端: SSE: 文本块事件
        end
        
        后端接口->>数据库: 异步保存AI回复
        后端接口->>记忆服务: 异步更新记忆
        后端接口-->>移动端: SSE: 流结束事件
    end
```
### 3. 智能提醒Function Calling流程

```mermaid
sequenceDiagram
    participant 用户
    participant 后端接口
    participant 豆包大模型
    participant 工具执行器
    participant 火山引擎
    participant 数据库

    用户->>后端接口: "明天下午3点提醒我开会"
    
    后端接口->>豆包大模型: 调用豆包接口（包含工具定义）
    豆包大模型-->>后端接口: 返回工具调用（设置提醒）
    
    Note over 后端接口: 检测到工具调用请求
    
    alt RTC模式（异步处理）
        后端接口->>火山引擎: 更新语音聊天（命令="函数"）
        后端接口->>工具执行器: 执行设置提醒工具
        工具执行器->>工具执行器: 解析时间并处理回退("明天下午3点")
        工具执行器->>数据库: 创建提醒记录
        数据库-->>工具执行器: 返回提醒ID
        工具执行器-->>后端接口: 返回执行结果
        
        火山引擎->>后端接口: 回调钩子：函数调用事件
        后端接口->>豆包大模型: 发送工具执行结果
        豆包大模型-->>后端接口: 生成确认回复
        后端接口-->>火山引擎: 返回最终回复
        
    else 文本模式（同步处理）
        后端接口->>工具执行器: 直接执行工具
        工具执行器-->>后端接口: 返回结果
        后端接口->>豆包大模型: 发送工具结果
        豆包大模型-->>后端接口: 生成确认回复
        后端接口-->>用户: "好的，我已为您设置明天下午3点的开会提醒"
    end
    
    Note over 数据库: 定时任务触发
    数据库->>后端接口: 提醒时间到达
    后端接口->>用户: 发送提醒通知
```
### 4. 会话后分析异步处理流程

```mermaid
sequenceDiagram
    participant 移动端
    participant 后端接口
    participant 分析服务
    participant 豆包大模型
    participant 记忆服务
    participant 数据库

    移动端->>后端接口: POST /rtc/end_session 或 /sessions/end_session
    后端接口-->>移动端: 立即返回（会话已结束）
    
    Note over 后端接口: 启动后台异步任务
    
    后端接口->>分析服务: 触发会话分析任务
    
    分析服务->>数据库: 查询会话中的所有消息
    数据库-->>分析服务: 返回对话历史
    
    alt 大型会话（>4000 令牌）
        分析服务->>分析服务: 分块处理对话内容
        loop 处理每个块
            分析服务->>豆包大模型: 调用摘要接口
            豆包大模型-->>分析服务: 返回分块摘要
        end
        分析服务->>分析服务: 合并所有分块摘要
    else 正常会话
        分析服务->>豆包大模型: 直接调用摘要接口
        豆包大模型-->>分析服务: 返回会话摘要
    end
    
    分析服务->>数据库: 保存会话摘要
    
    分析服务->>记忆服务: 同步到外部记忆服务
    alt 记忆服务可用
        记忆服务-->>分析服务: 同步成功
    else 记忆服务失败
        分析服务->>分析服务: 降级处理，记录错误
    end
    
    分析服务->>数据库: 更新分析状态为已完成
    
    Note over 分析服务: 分析任务完成（30-60秒）
```

### 5. 整体系统架构

```mermaid
graph TB
    subgraph "移动端"
        移动应用[React Native 应用]
    end
    
    subgraph "后端接口服务"
        网关[接口网关/路由]
        认证[认证服务]
        对话[对话服务]
        RTC会话[RTC会话管理]
        提醒[提醒服务]
        用户[用户管理]
        危机[危机检测]
        分析[会话分析]
    end
    
    subgraph "外部服务"
        火山引擎[火山引擎RTC]
        豆包[豆包大模型]
        Zep记忆[Zep记忆服务]
        Mem0记忆[Mem0记忆服务]
    end
    
    subgraph "数据存储"
        数据库[(PostgreSQL)]
        缓存[(Redis缓存)]
    end
    
    移动应用 --> 网关
    网关 --> 认证
    网关 --> 对话
    网关 --> RTC会话
    网关 --> 提醒
    网关 --> 用户
    
    对话 --> 危机
    对话 --> 豆包
    对话 --> Zep记忆
    对话 --> Mem0记忆
    
    RTC会话 --> 火山引擎
    RTC会话 --> 分析
    
    分析 --> 豆包
    分析 --> Zep记忆
    分析 --> Mem0记忆
    
    认证 --> 数据库
    对话 --> 数据库
    RTC会话 --> 数据库
    提醒 --> 数据库
    用户 --> 数据库
    分析 --> 数据库
    
    认证 --> 缓存
    对话 --> 缓存
    
    style 危机 fill:#ffcccc
    style 认证 fill:#ccffcc
    style 数据库 fill:#cceeff
    style 火山引擎 fill:#ffffcc
    style 豆包 fill:#ffccff
```

## 🔧 关键技术特点

### 1. **异步处理架构**
- **FastAPI异步框架** - 高并发处理能力
- **背景任务机制** - 会话分析、记忆同步等耗时操作异步处理
- **流式响应** - SSE实现实时对话体验

### 2. **容错与降级机制**
- **3次重试+指数退避** - 外部API调用容错
- **断路器模式** - 防止级联故障
- **优雅降级** - 记忆服务失败时继续提供基础功能

### 3. **安全机制**
- **JWT认证** - 无状态安全认证
- **火山引擎V4签名** - API调用安全验证
- **HMAC签名验证** - Webhook安全验证
- **RLS数据隔离** - 数据库级别权限控制

### 4. **多模态集成**
- **火山引擎RTC** - 实时语音交互
- **豆包大模型** - 智能对话生成
- **外部记忆服务** - 个性化记忆管理

### 5. **智能功能**
- **Function Calling** - LLM工具调用能力
- **危机干预** - 关键词检测+脚本化回复
- **自然语言处理** - 时间解析、意图识别
