# API端到端测试

这个目录包含了项目的所有API接口的端到端测试，支持独立运行和数据共享的链式测试。测试系统具有以下特性：

## 🔗 新特性：测试数据共享

- **自动数据传递**：每个测试完成后会将重要信息保存到共享配置文件
- **智能数据复用**：后续测试会自动读取并使用前面测试的结果
- **历史数据清理**：运行 `01_health_test.py` 会清理所有历史数据，适合开始新的测试轮次
- **链式测试支持**：支持按顺序运行测试，实现完整的用户流程验证

## 🏗️ 测试结构

```
e2e_tests/
├── base_tester.py           # 基础测试工具类（支持数据共享）
├── 01_health_test.py        # 健康检查API测试（清理历史数据）
├── 02_auth_test.py          # 认证API测试（保存用户认证信息）
├── 03_user_test.py          # 用户管理API测试（使用认证信息）
├── 04_character_test.py     # 角色管理API测试（保存角色信息）
├── 05_session_test.py       # 会话管理API测试（使用角色信息）
├── 06_chat_test.py          # 聊天服务API测试（使用会话信息）
├── 07_reminder_test.py      # 提醒管理API测试（保存提醒信息）
├── 08_rtc_test.py           # RTC会话API测试（使用角色信息）
├── 09_rtc_webhook_test.py   # RTC Webhook API测试（使用认证信息）
├── run_all_tests.py         # 运行所有测试的主脚本
├── run_sequential_tests.py  # 顺序运行测试脚本（新增）
├── shared_config.json       # 共享配置文件（自动生成）
├── logs/                    # 测试日志目录
└── README.md               # 本文件
```

## 📋 API接口清单

### 1. 健康检查 (01_health_test.py)
- `GET /api/v1/health` - 健康检查

### 2. 认证服务 (02_auth_test.py)
- `POST /api/v1/auth/anonymous-login` - 匿名登录
- `POST /api/v1/auth/refresh-token` - 刷新Token
- `POST /api/v1/auth/finalize_onboarding` - 完成引导流程

### 3. 用户管理 (03_user_test.py)
- `GET /api/v1/user/profile` - 获取用户画像
- `PUT /api/v1/user/profile` - 更新用户画像
- `PATCH /api/v1/user/profile` - 部分更新用户画像
- `POST /api/v1/user/characters/{character_id}/bind` - 绑定角色
- `GET /api/v1/user/settings` - 获取用户设置
- `PUT /api/v1/user/settings` - 更新用户设置

### 4. 角色管理 (04_character_test.py)
- `GET /api/v1/characters` - 获取角色列表
- `GET /api/v1/characters/{character_id}` - 获取角色详情

### 5. 会话管理 (05_session_test.py)
- `POST /api/v1/chat/sessions` - 创建会话
- `GET /api/v1/chat/sessions` - 获取会话列表
- `GET /api/v1/chat/sessions/{session_id}/messages` - 获取会话消息
- `PUT /api/v1/chat/sessions/{session_id}/end` - 结束会话

### 6. 聊天服务 (06_chat_test.py)
- `POST /api/v1/chat/text_message` - 文本聊天SSE
- `GET /api/v1/chat/connections/status` - 聊天连接状态

### 7. 提醒管理 (07_reminder_test.py)
- `GET /api/v1/reminders` - 获取提醒列表
- `POST /api/v1/reminders` - 创建提醒
- `PUT /api/v1/reminders/{reminder_id}` - 更新提醒
- `DELETE /api/v1/reminders/{reminder_id}` - 删除提醒

### 8. RTC会话 (08_rtc_test.py)
- `POST /api/v1/rtc/prepare_session` - 准备RTC会话
- `POST /api/v1/rtc/end_session` - 结束RTC会话
- `GET /api/v1/rtc/sessions/{session_id}/status` - 获取会话状态
- `GET /api/v1/rtc/sessions/{session_id}/config` - 获取会话配置

### 9. RTC Webhook (09_rtc_webhook_test.py)
- `POST /api/v1/chat/rtc_event_handler` - RTC事件处理

## 🚀 使用方法

### 🔗 推荐：顺序链式测试（新）

使用新的顺序测试脚本，实现完整的用户流程测试：

```bash
# 运行完整的顺序测试流程
python run_sequential_tests.py

# 从指定测试开始运行
python run_sequential_tests.py --start-from auth

# 只运行特定的测试组合
python run_sequential_tests.py --tests health auth user

# 查看当前共享配置状态
python run_sequential_tests.py --show-config

# 指定服务器URL
python run_sequential_tests.py --url http://localhost:8003
```

### 📋 重要：测试顺序说明

测试有严格的执行顺序，每个测试依赖前面测试的结果：

1. **health** - 🏥 清理历史数据，开始新测试
2. **auth** - 🔐 用户登录和引导流程
3. **user** - 👤 使用认证信息更新用户画像
4. **character** - 🎭 获取和验证角色信息
5. **session** - 💬 使用绑定的角色创建会话
6. **chat** - 🗨️ 使用已创建的会话进行聊天
7. **reminder** - ⏰ 创建和管理用户提醒
8. **rtc** - 📞 实时通信会话管理
9. **rtc_webhook** - 🔗 事件回调处理

### 运行单个测试

每个测试文件仍可以独立运行：

```bash
# ⚠️ 注意：运行01会清理所有历史数据
python 01_health_test.py

# 运行认证API测试（会自动加载已有配置）
python 02_auth_test.py

# 指定服务器URL
python 03_user_test.py --url http://localhost:8003

# 查看帮助
python 04_character_test.py --help
```

### 运行所有测试（原有方式）

使用原有的并行测试脚本：

```bash
# 运行所有测试（并行，无数据共享）
python run_all_tests.py

# 运行指定测试
python run_all_tests.py --tests health auth user

# 生成详细报告
python run_all_tests.py --detailed-report
```

### 常用参数

所有测试脚本都支持以下参数：

- `--url` / `-u`: 指定目标服务器URL (默认: http://localhost:8003)
- `--help`: 显示帮助信息

主脚本额外支持：

- `--tests` / `-t`: 指定要运行的测试
- `--detailed-report` / `-d`: 生成详细报告文件
- `--list-tests` / `-l`: 列出所有可用测试

## 📊 日志和报告

### 📄 共享配置文件

新的测试系统会自动生成和维护 `shared_config.json` 文件：

```json
{
  "device_id": "test-device-uuid",
  "platform": "android",
  "app_version": "1.0.0",
  "nickname": "测试用户",
  "character_id": "character-uuid",
  "auth": {
    "access_token": "jwt-token",
    "refresh_token": "refresh-token",
    "user_id": "user-uuid",
    "updated_at": "2024-01-20T10:30:00"
  },
  "test_results": {
    "onboarding": {
      "onboarding_completed": true,
      "character_id": "character-uuid",
      "nickname": "测试用户",
      "completed_at": "2024-01-20T10:31:00",
      "test_name": "02_auth"
    },
    "user_profile": {
      "profile_updated": true,
      "current_nickname": "测试用户_updated",
      "completed_at": "2024-01-20T10:32:00",
      "test_name": "03_user"
    }
  }
}
```

### 日志结构

每次运行测试都会在 `logs/` 目录下生成日志文件：

```
logs/
├── {测试名称}_{时间戳}.log     # 单个测试的详细日志
├── all_tests_report_{时间戳}.json  # 所有测试的JSON报告
└── all_tests_summary_{时间戳}.txt  # 所有测试的可读摘要
```

### 日志命名规则

- 单个测试：`{测试名称}_{YYYYMMDD_HHMMSS}.log`
- 汇总报告：`all_tests_report_{YYYYMMDD_HHMMSS}.json`
- 可读摘要：`all_tests_summary_{YYYYMMDD_HHMMSS}.txt`

### 日志内容

每个日志文件包含：
- 请求和响应的详细信息
- 测试执行状态
- 错误详情和异常信息
- 性能数据（响应时间等）

## 🔧 测试特性

### 1. 独立性
- 每个测试文件都可以独立运行
- 自动处理用户认证
- 自动获取必要的测试数据（如角色ID）

### 2. 完整性
- 涵盖所有API接口
- 包含正常和异常情况测试
- 验证响应格式和数据正确性

### 3. 可维护性
- 统一的测试基类
- 清晰的日志记录
- 友好的错误信息

### 4. 灵活性
- 支持指定服务器URL
- 支持选择性运行测试
- 支持多种报告格式

## 📝 添加新测试

如果需要添加新的API测试：

1. 创建新的测试文件：`10_new_api_test.py`
2. 继承 `BaseAPITester` 类
3. 实现具体的测试方法
4. 在 `run_all_tests.py` 中注册新测试

示例：

```python
#!/usr/bin/env python3
"""
新API测试
测试接口: POST /api/v1/new/endpoint
"""

import asyncio
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent))
from base_tester import BaseAPITester

class NewApiTester(BaseAPITester):
    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("10_new_api", base_url)

    async def test_new_endpoint(self):
        """测试新端点"""
        response = await self.make_request("POST", "/api/v1/new/endpoint")
        # 添加验证逻辑...

    async def run_tests(self):
        await self.ensure_authenticated()
        await self.test_new_endpoint()
        self.print_results()

async def main():
    async with NewApiTester() as tester:
        await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否启动
   - 确认URL是否正确
   - 检查网络连接

2. **认证失败**
   - 确保匿名登录API正常工作
   - 检查Token格式和有效性

3. **测试超时**
   - 检查服务器响应时间
   - 适当增加超时时间

4. **依赖错误**
   - 确保安装了所需的Python包
   - 检查Python版本兼容性

### 调试技巧

1. 查看详细日志文件了解具体错误
2. 单独运行失败的测试进行调试
3. 使用 `--url` 参数测试不同环境
4. 检查服务器日志确认问题根源

## 📚 技术栈

- **Python 3.8+**
- **aiohttp** - 异步HTTP客户端
- **asyncio** - 异步编程支持
- **pytest** - 测试框架基础

## 🤝 贡献

如果发现问题或有改进建议，请：

1. 提交Issue描述问题
2. 提供重现步骤和日志
3. 提交Pull Request改进代码

## 📄 许可证

本项目遵循项目根目录的许可证规定。
