#!/usr/bin/env python3
"""
心桥AI智能体 - 高级记忆管理系统
=====================================

这是一个功能完整的AI智能体，具备：
1. 多层记忆系统（短期、中期、长期记忆）
2. 记忆备份与恢复机制
3. 智能记忆分类和管理
4. 响应速度优化
5. 错误处理和重试机制
6. 记忆持久化和同步
7. 记忆搜索和检索优化
8. 记忆压缩和整理
9. 记忆导入导出功能
10. 自动备份机制

作者: Alex Hu
版本: 1.0.0
"""

import asyncio
import json
import os
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import logging
import aiofiles
import pickle
import hashlib
from concurrent.futures import ThreadPoolExecutor
import threading
from contextlib import asynccontextmanager

from openai import AsyncOpenAI
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path="../1.env")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xinqiao_agent.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ============================================================================
# 配置类和枚举
# ============================================================================

class MemoryType(Enum):
    """记忆类型枚举"""
    SHORT_TERM = "short_term"    # 短期记忆（当前对话）
    MEDIUM_TERM = "medium_term"  # 中期记忆（会话级别）
    LONG_TERM = "long_term"      # 长期记忆（用户级别）
    CRITICAL = "critical"        # 关键记忆（永久保存）

class MemoryImportance(Enum):
    """记忆重要性等级"""
    VERY_HIGH = 5    # 非常重要
    HIGH = 4         # 重要
    MEDIUM = 3       # 中等
    LOW = 2          # 低
    VERY_LOW = 1     # 非常低

class MemoryCategory(Enum):
    """记忆分类"""
    PERSONAL = "personal"         # 个人信息
    PREFERENCE = "preference"     # 偏好设置
    RELATIONSHIP = "relationship" # 关系信息
    GOAL = "goal"                # 目标和计划
    CONTEXT = "context"          # 上下文信息
    FACT = "fact"                # 事实信息
    EMOTION = "emotion"          # 情感信息
    BEHAVIOR = "behavior"        # 行为模式
    SKILL = "skill"              # 技能和能力
    OTHER = "other"              # 其他

@dataclass
class MemoryEntry:
    """记忆条目数据结构"""
    id: str
    content: str
    memory_type: MemoryType
    importance: MemoryImportance
    category: MemoryCategory
    created_at: datetime
    updated_at: datetime
    accessed_at: datetime
    access_count: int = 0
    embedding: Optional[List[float]] = None
    metadata: Dict[str, Any] = None
    tags: List[str] = None
    related_memories: List[str] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.tags is None:
            self.tags = []
        if self.related_memories is None:
            self.related_memories = []

@dataclass
class AgentConfig:
    """智能体配置"""
    # 基础配置
    user_id: str = "default_user"
    session_id: str = "default_session"
    agent_name: str = "心桥AI助手"

    # 火山引擎配置
    volcano_api_key: str = ""
    volcano_base_url: str = "https://ark.cn-beijing.volces.com/api/v3"
    volcano_endpoint_id: str = ""

    # Zep配置
    zep_api_key: str = ""
    zep_enabled: bool = True

    # 记忆系统配置
    memory_db_path: str = "memory_db"
    max_short_term_memories: int = 50
    max_medium_term_memories: int = 200
    max_long_term_memories: int = 1000
    memory_retention_days: int = 365

    # 性能配置
    max_concurrent_operations: int = 10
    cache_size: int = 100
    cache_ttl: int = 300

    # 备份配置
    backup_enabled: bool = True
    backup_interval: int = 3600  # 1小时
    max_backups: int = 168       # 一周

    # 压缩配置
    compression_enabled: bool = True
    compression_threshold: float = 0.8

    @classmethod
    def from_env(cls) -> 'AgentConfig':
        """从环境变量创建配置"""
        # 生成持久化的会话ID
        session_file = Path("session_info.json")
        if session_file.exists():
            try:
                with open(session_file, 'r') as f:
                    session_data = json.load(f)
                    session_id = session_data.get("session_id", "default_session")
                    user_id = session_data.get("user_id", "default_user")
                    logger.info(f"恢复会话: user_id={user_id}, session_id={session_id}")
            except Exception as e:
                logger.warning(f"恢复会话信息失败: {e}，使用默认会话")
                session_id = "default_session"
                user_id = "default_user"
        else:
            # 创建新的会话信息
            import uuid
            session_id = f"session_{uuid.uuid4().hex[:8]}"
            user_id = f"user_{uuid.uuid4().hex[:8]}"

            try:
                with open(session_file, 'w') as f:
                    json.dump({
                        "session_id": session_id,
                        "user_id": user_id,
                        "created_at": datetime.now().isoformat()
                    }, f)
                logger.info(f"创建新会话: user_id={user_id}, session_id={session_id}")
            except Exception as e:
                logger.error(f"保存会话信息失败: {e}")

        return cls(
            user_id=user_id,
            session_id=session_id,
            volcano_api_key=os.getenv("VOLCENGINE_API_KEY", ""),
            volcano_endpoint_id=os.getenv("VOLCANO_LLM_ENDPOINT_ID", ""),
            zep_api_key=os.getenv("ZEP_API_KEY", ""),
            zep_enabled=os.getenv("ZEP_API_KEY", "").startswith("z_"),
        )

# ============================================================================
# 记忆存储引擎
# ============================================================================

class MemoryStorage:
    """记忆存储引擎"""

    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.db_path.mkdir(exist_ok=True)
        self._lock = asyncio.Lock()
        self._cache: Dict[str, MemoryEntry] = {}
        self._dirty_cache: set = set()

    async def save_memory(self, memory: MemoryEntry) -> None:
        """保存记忆"""
        async with self._lock:
            self._cache[memory.id] = memory
            self._dirty_cache.add(memory.id)
            await self._flush_if_needed()

    async def load_memory(self, memory_id: str) -> Optional[MemoryEntry]:
        """加载记忆"""
        async with self._lock:
            if memory_id in self._cache:
                return self._cache[memory_id]

            # 改用JSON格式存储，避免pickle的类路径问题
            json_path = self.db_path / f"{memory_id}.json"
            pkl_path = self.db_path / f"{memory_id}.pkl"

            # 优先尝试JSON格式
            if json_path.exists():
                try:
                    async with aiofiles.open(json_path, 'r', encoding='utf-8') as f:
                        data = json.loads(await f.read())
                        memory = self._dict_to_memory(data)
                        self._cache[memory_id] = memory
                        return memory
                except Exception as e:
                    logger.error(f"加载JSON记忆失败 {memory_id}: {e}")

            # 兼容旧的pickle格式
            elif pkl_path.exists():
                try:
                    with open(pkl_path, 'rb') as f:
                        memory = pickle.load(f)
                        self._cache[memory_id] = memory
                        # 转换为JSON格式保存
                        await self._save_memory_as_json(memory)
                        pkl_path.unlink()  # 删除旧文件
                        return memory
                except Exception as e:
                    logger.error(f"加载pickle记忆失败 {memory_id}: {e}")
            return None

    async def delete_memory(self, memory_id: str) -> bool:
        """删除记忆"""
        async with self._lock:
            self._cache.pop(memory_id, None)
            self._dirty_cache.discard(memory_id)

            # 删除JSON文件
            json_path = self.db_path / f"{memory_id}.json"
            if json_path.exists():
                try:
                    json_path.unlink()
                    return True
                except Exception as e:
                    logger.error(f"删除JSON记忆文件失败 {memory_id}: {e}")
            # 删除pickle文件
            pkl_path = self.db_path / f"{memory_id}.pkl"
            if pkl_path.exists():
                try:
                    pkl_path.unlink()
                    return True
                except Exception as e:
                    logger.error(f"删除pickle记忆文件失败 {memory_id}: {e}")
            return False

    async def search_memories(
        self,
        query: str = None,
        memory_type: MemoryType = None,
        category: MemoryCategory = None,
        importance: MemoryImportance = None,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """搜索记忆"""
        async with self._lock:
            # 确保所有记忆都在缓存中
            await self._load_all_memories()

            results = []
            for memory in self._cache.values():
                if self._match_criteria(memory, query, memory_type, category, importance):
                    results.append(memory)

            # 按重要性和访问时间排序
            results.sort(key=lambda m: (m.importance.value, m.accessed_at), reverse=True)
            return results[:limit]

    async def get_all_memories(self) -> List[MemoryEntry]:
        """获取所有记忆"""
        async with self._lock:
            await self._load_all_memories()
            return list(self._cache.values())

    async def _load_all_memories(self) -> None:
        """加载所有记忆到缓存"""
        for file_path in self.db_path.glob("*.json"):
            memory_id = file_path.stem
            if memory_id not in self._cache:
                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        data = json.loads(await f.read())
                        memory = self._dict_to_memory(data)
                        self._cache[memory_id] = memory
                except Exception as e:
                    logger.error(f"加载JSON记忆失败 {memory_id}: {e}")
        for file_path in self.db_path.glob("*.pkl"):
            memory_id = file_path.stem
            if memory_id not in self._cache:
                try:
                    with open(file_path, 'rb') as f:
                        memory = pickle.load(f)
                        self._cache[memory_id] = memory
                        # 转换为JSON格式保存
                        async with aiofiles.open(self.db_path / f"{memory_id}.json", 'w', encoding='utf-8') as f:
                            await f.write(json.dumps(self._memory_to_dict(memory), ensure_ascii=False, indent=2))
                        file_path.unlink() # 删除旧文件
                except Exception as e:
                    logger.error(f"加载pickle记忆失败 {memory_id}: {e}")

    async def _flush_if_needed(self) -> None:
        """必要时刷新缓存到磁盘"""
        if len(self._dirty_cache) > 10:  # 当有10个或更多脏数据时刷新
            await self._flush_cache()

    async def _flush_cache(self) -> None:
        """刷新缓存到磁盘"""
        for memory_id in self._dirty_cache:
            if memory_id in self._cache:
                memory = self._cache[memory_id]
                # 优先保存为JSON格式
                try:
                    json_path = self.db_path / f"{memory_id}.json"
                    async with aiofiles.open(json_path, 'w', encoding='utf-8') as f:
                        await f.write(json.dumps(self._memory_to_dict(memory), ensure_ascii=False, indent=2))
                except Exception as e:
                    logger.error(f"保存JSON记忆失败 {memory_id}: {e}")
                # 兼容旧的pickle格式
                try:
                    pkl_path = self.db_path / f"{memory_id}.pkl"
                    with open(pkl_path, 'wb') as f:
                        pickle.dump(memory, f)
                except Exception as e:
                    logger.error(f"保存pickle记忆失败 {memory_id}: {e}")
        self._dirty_cache.clear()

    async def _save_memory_as_json(self, memory: MemoryEntry) -> None:
        """将记忆保存为JSON格式"""
        try:
            json_path = self.db_path / f"{memory.id}.json"
            data = self._memory_to_dict(memory)
            async with aiofiles.open(json_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, ensure_ascii=False, indent=2))
        except Exception as e:
            logger.error(f"保存JSON记忆失败 {memory.id}: {e}")

    def _memory_to_dict(self, memory: MemoryEntry) -> dict:
        """将记忆对象转换为字典"""
        return {
            "id": memory.id,
            "content": memory.content,
            "memory_type": memory.memory_type.value,
            "importance": memory.importance.value,
            "category": memory.category.value,
            "created_at": memory.created_at.isoformat(),
            "updated_at": memory.updated_at.isoformat(),
            "accessed_at": memory.accessed_at.isoformat(),
            "access_count": memory.access_count,
            "embedding": memory.embedding,
            "metadata": memory.metadata,
            "tags": memory.tags,
            "related_memories": memory.related_memories
        }

    def _dict_to_memory(self, data: dict) -> MemoryEntry:
        """将字典转换为记忆对象"""
        return MemoryEntry(
            id=data["id"],
            content=data["content"],
            memory_type=MemoryType(data["memory_type"]),
            importance=MemoryImportance(data["importance"]),
            category=MemoryCategory(data["category"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            accessed_at=datetime.fromisoformat(data["accessed_at"]),
            access_count=data.get("access_count", 0),
            embedding=data.get("embedding"),
            metadata=data.get("metadata", {}),
            tags=data.get("tags", []),
            related_memories=data.get("related_memories", [])
        )

    def _match_criteria(
        self,
        memory: MemoryEntry,
        query: str,
        memory_type: MemoryType,
        category: MemoryCategory,
        importance: MemoryImportance
    ) -> bool:
        """检查记忆是否匹配搜索条件"""
        if memory_type and memory.memory_type != memory_type:
            return False
        if category and memory.category != category:
            return False
        if importance and memory.importance != importance:
            return False
        if query and query.lower() not in memory.content.lower():
            return False
        return True

# ============================================================================
# Zep记忆集成
# ============================================================================

class ZepMemoryManager:
    """Zep记忆管理器"""

    def __init__(self, api_key: str, user_id: str, session_id: str):
        self.api_key = api_key
        self.user_id = user_id
        self.session_id = session_id
        self.client = None
        self.enabled = api_key.startswith("z_")

    async def initialize(self) -> bool:
        """初始化Zep客户端"""
        if not self.enabled:
            return False

        try:
            from zep_cloud.client import AsyncZep
            from zep_cloud import Message

            self.client = AsyncZep(api_key=self.api_key)
            self.Message = Message

            # 添加用户和会话
            await self._setup_user_and_session()
            return True
        except Exception as e:
            logger.error(f"Zep初始化失败: {e}")
            return False

    async def _setup_user_and_session(self) -> None:
        """设置用户和会话"""
        # 先尝试获取现有用户，如果不存在再创建
        try:
            # 尝试获取用户信息
            user_info = await self.client.user.get(user_id=self.user_id)
            logger.info(f"Zep用户已存在: {self.user_id}")
        except Exception as get_error:
            # 用户不存在，尝试创建
            try:
                await self.client.user.add(
                    user_id=self.user_id,
                    email=f"{self.user_id.replace('_', '')}@example.com",  # 修正email格式
                    first_name=self.user_id.split('_')[0].title(),
                    last_name="User"
                )
                logger.info(f"Zep用户创建成功: {self.user_id}")
            except Exception as create_error:
                logger.warning(f"Zep用户创建失败: {create_error}")
                # 如果创建失败，记录错误但不阻止程序运行

        # 尝试创建会话
        try:
            # 先检查会话是否存在
            session_info = await self.client.memory.get_session(session_id=self.session_id)
            logger.info(f"Zep会话已存在: {self.session_id}")
        except Exception as get_session_error:
            # 会话不存在，尝试创建
            try:
                await self.client.memory.add_session(
                    session_id=self.session_id,
                    user_id=self.user_id
                )
                logger.info(f"Zep会话创建成功: {self.session_id}")
            except Exception as create_session_error:
                logger.warning(f"Zep会话创建失败: {create_session_error}")
                # 如果创建失败，记录错误但不阻止程序运行

    async def add_message(self, role: str, content: str) -> None:
        """添加消息到Zep"""
        if not self.enabled or not self.client:
            return

        try:
            # 修正：Message需要同时提供role_type和role参数
            message = self.Message(
                role_type=role,
                role=role,
                content=content
            )
            await self.client.memory.add(
                session_id=self.session_id,
                messages=[message]
            )
        except Exception as e:
            logger.error(f"添加Zep消息失败: {e}")

    async def get_memory_context(self) -> str:
        """获取记忆上下文"""
        if not self.enabled or not self.client:
            return ""

        try:
            # 使用正确的API获取记忆
            memory = await self.client.memory.get(session_id=self.session_id)
            if memory and hasattr(memory, 'context') and memory.context:
                return memory.context
        except Exception as e:
            logger.error(f"获取Zep记忆上下文失败: {e}")
        return ""

    async def get_recent_messages(self, limit: int = 5) -> List[Dict[str, str]]:
        """获取最近的消息"""
        if not self.enabled or not self.client:
            return []

        try:
            # 修正：使用更可靠的方式获取会话消息
            memory = await self.client.memory.get(session_id=self.session_id)
            if memory:
                messages = []
                # 根据实际的memory对象结构来获取消息
                # 这里需要根据实际返回的数据结构进行调整
                if hasattr(memory, 'messages') and memory.messages:
                    for msg in memory.messages[-limit:]:
                        # 确保正确处理消息的role映射
                        role_type = getattr(msg, 'role_type', 'user')
                        content = getattr(msg, 'content', '')

                        # 标准化role格式
                        if role_type.lower() in ['human', 'user']:
                            role = 'user'
                        elif role_type.lower() in ['assistant', 'ai']:
                            role = 'assistant'
                        else:
                            role = 'user'  # 默认值

                        messages.append({"role": role, "content": content})
                return messages
        except Exception as e:
            logger.error(f"获取Zep最近消息失败: {e}")
        return []

    async def health_check(self) -> bool:
        """检查Zep服务健康状态"""
        if not self.enabled or not self.client:
            return False

        try:
            # 尝试获取会话信息来检查连接
            await self.client.memory.get(session_id=self.session_id)
            return True
        except Exception as e:
            logger.error(f"Zep健康检查失败: {e}")
            return False

    async def cleanup(self) -> None:
        """清理资源"""
        if self.client:
            # 这里可以添加清理逻辑，如果Zep客户端需要的话
            pass

# ============================================================================
# 记忆分类器
# ============================================================================

class MemoryClassifier:
    """记忆分类器"""

    def __init__(self, llm_client: AsyncOpenAI, endpoint_id: str):
        self.llm_client = llm_client
        self.endpoint_id = endpoint_id
        self._classification_cache = {}

    async def classify_memory(self, content: str) -> Tuple[MemoryImportance, MemoryCategory]:
        """对记忆进行分类"""
        cache_key = hashlib.md5(content.encode()).hexdigest()
        if cache_key in self._classification_cache:
            return self._classification_cache[cache_key]

        prompt = f"""
请分析以下对话内容的重要性和分类。

对话内容：{content}

请返回JSON格式的分析结果：
{{
    "importance": "VERY_HIGH|HIGH|MEDIUM|LOW|VERY_LOW",
    "category": "PERSONAL|PREFERENCE|RELATIONSHIP|GOAL|CONTEXT|FACT|EMOTION|BEHAVIOR|SKILL|OTHER",
    "reasoning": "分类理由"
}}

重要性等级说明：
- VERY_HIGH: 非常重要的个人信息、目标、关系等
- HIGH: 重要的偏好、计划、情感等
- MEDIUM: 一般的对话内容、观点等
- LOW: 普通的闲聊、简单回应等
- VERY_LOW: 无关紧要的内容

分类说明：
- PERSONAL: 个人基本信息（姓名、年龄、职业等）
- PREFERENCE: 偏好设置（喜好、习惯等）
- RELATIONSHIP: 关系信息（家庭、朋友、同事等）
- GOAL: 目标和计划（愿望、规划等）
- CONTEXT: 上下文信息（当前状态、环境等）
- FACT: 事实信息（客观知识、数据等）
- EMOTION: 情感信息（情绪、感受等）
- BEHAVIOR: 行为模式（习惯、行为等）
- SKILL: 技能和能力（专业技能、爱好等）
- OTHER: 其他类型

重要：只返回纯JSON，不要任何其他文字。
"""

        try:
            response = await self.llm_client.chat.completions.create(
                model=self.endpoint_id,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.1
            )

            result_text = response.choices[0].message.content.strip()

            # 清理可能的多余文本，只保留JSON部分
            json_start = result_text.find('{')
            json_end = result_text.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                result_text = result_text[json_start:json_end]

            result = json.loads(result_text)

            # 安全地获取分类结果
            importance_str = result.get("importance", "MEDIUM")
            category_str = result.get("category", "OTHER")

            # 验证分类值的有效性
            try:
                importance = MemoryImportance[importance_str]
            except KeyError:
                logger.warning(f"无效的重要性等级: {importance_str}，使用默认值")
                importance = MemoryImportance.MEDIUM

            try:
                category = MemoryCategory[category_str]
            except KeyError:
                logger.warning(f"无效的分类: {category_str}，使用默认值")
                category = MemoryCategory.OTHER

            self._classification_cache[cache_key] = (importance, category)
            return importance, category

        except json.JSONDecodeError as e:
            logger.error(f"记忆分类JSON解析失败: {e}, 原文: {result_text[:100]}...")
            # 返回默认分类
            return MemoryImportance.MEDIUM, MemoryCategory.OTHER
        except Exception as e:
            logger.error(f"记忆分类失败: {e}")
            # 返回默认分类
            return MemoryImportance.MEDIUM, MemoryCategory.OTHER

# ============================================================================
# 记忆管理器
# ============================================================================

class MemoryManager:
    """记忆管理器"""

    def __init__(self, config: AgentConfig):
        self.config = config
        self.storage = MemoryStorage(config.memory_db_path)
        self.classifier = None
        self.zep_manager = None
        self._executor = ThreadPoolExecutor(max_workers=config.max_concurrent_operations)
        self._background_tasks = []

    async def initialize(self, llm_client: AsyncOpenAI) -> None:
        """初始化记忆管理器"""
        self.classifier = MemoryClassifier(llm_client, self.config.volcano_endpoint_id)

        if self.config.zep_enabled:
            self.zep_manager = ZepMemoryManager(
                self.config.zep_api_key,
                self.config.user_id,
                self.config.session_id
            )
            await self.zep_manager.initialize()

        # 启动后台任务
        self._start_background_tasks()

    async def add_memory(
        self,
        content: str,
        memory_type: MemoryType = None,
        force_importance: MemoryImportance = None,
        force_category: MemoryCategory = None
    ) -> str:
        """添加记忆"""
        memory_id = str(uuid.uuid4())

        # 自动分类（如果未强制指定）
        if force_importance is None or force_category is None:
            importance, category = await self.classifier.classify_memory(content)
            if force_importance is not None:
                importance = force_importance
            if force_category is not None:
                category = force_category
        else:
            importance, category = force_importance, force_category

        # 根据重要性确定记忆类型
        if memory_type is None:
            if importance == MemoryImportance.VERY_HIGH:
                memory_type = MemoryType.CRITICAL
            elif importance == MemoryImportance.HIGH:
                memory_type = MemoryType.LONG_TERM
            elif importance == MemoryImportance.MEDIUM:
                memory_type = MemoryType.MEDIUM_TERM
            else:
                memory_type = MemoryType.SHORT_TERM

        # 创建记忆条目
        now = datetime.now()
        memory = MemoryEntry(
            id=memory_id,
            content=content,
            memory_type=memory_type,
            importance=importance,
            category=category,
            created_at=now,
            updated_at=now,
            accessed_at=now
        )

        # 保存记忆
        await self.storage.save_memory(memory)

        # 添加到Zep（如果启用）
        if self.zep_manager:
            await self.zep_manager.add_message("user", content)

        logger.info(f"添加记忆成功 {memory_id}: {content[:50]}...")
        return memory_id

    async def search_memories(
        self,
        query: str,
        memory_type: MemoryType = None,
        category: MemoryCategory = None,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """搜索记忆"""
        memories = await self.storage.search_memories(
            query=query,
            memory_type=memory_type,
            category=category,
            limit=limit
        )

        # 更新访问时间
        for memory in memories:
            memory.accessed_at = datetime.now()
            memory.access_count += 1
            await self.storage.save_memory(memory)

        return memories

    async def get_context_memories(self, limit: int = 10) -> List[MemoryEntry]:
        """获取上下文记忆"""
        # 获取最近访问的重要记忆
        all_memories = await self.storage.get_all_memories()

        # 过滤和排序
        context_memories = [
            m for m in all_memories
            if m.importance.value >= MemoryImportance.MEDIUM.value
        ]

        # 按重要性和最近访问时间排序
        context_memories.sort(
            key=lambda m: (m.importance.value, m.accessed_at.timestamp()),
            reverse=True
        )

        return context_memories[:limit]

    async def compress_memories(self) -> int:
        """压缩记忆"""
        if not self.config.compression_enabled:
            return 0

        all_memories = await self.storage.get_all_memories()

        # 按类型分组
        by_type = {}
        for memory in all_memories:
            if memory.memory_type not in by_type:
                by_type[memory.memory_type] = []
            by_type[memory.memory_type].append(memory)

        compressed_count = 0

        # 压缩短期记忆
        if MemoryType.SHORT_TERM in by_type:
            short_term = by_type[MemoryType.SHORT_TERM]
            if len(short_term) > self.config.max_short_term_memories:
                # 删除最旧的低重要性记忆
                to_remove = sorted(
                    short_term,
                    key=lambda m: (m.importance.value, m.accessed_at.timestamp())
                )[:len(short_term) - self.config.max_short_term_memories]

                for memory in to_remove:
                    await self.storage.delete_memory(memory.id)
                    compressed_count += 1

        # 类似处理中期和长期记忆
        # ... (省略其他类型的压缩逻辑)

        logger.info(f"记忆压缩完成，删除了 {compressed_count} 条记忆")
        return compressed_count

    async def backup_memories(self, backup_path: str) -> bool:
        """备份记忆"""
        try:
            all_memories = await self.storage.get_all_memories()
            backup_data = {
                "timestamp": datetime.now().isoformat(),
                "user_id": self.config.user_id,
                "session_id": self.config.session_id,
                "memories": [self.storage._memory_to_dict(memory) for memory in all_memories]
            }

            backup_file = Path(backup_path)
            backup_file.parent.mkdir(exist_ok=True)

            async with aiofiles.open(backup_file, 'w') as f:
                await f.write(json.dumps(backup_data, indent=2, ensure_ascii=False))

            logger.info(f"记忆备份完成: {backup_file}")
            return True
        except Exception as e:
            logger.error(f"记忆备份失败: {e}")
            return False

    async def restore_memories(self, backup_path: str) -> bool:
        """恢复记忆"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False

            async with aiofiles.open(backup_file, 'r') as f:
                backup_data = json.loads(await f.read())

            # 恢复记忆
            for memory_data in backup_data["memories"]:
                memory = self.storage._dict_to_memory(memory_data)
                await self.storage.save_memory(memory)

            logger.info(f"记忆恢复完成: {len(backup_data['memories'])} 条记忆")
            return True
        except Exception as e:
            logger.error(f"记忆恢复失败: {e}")
            return False

    def _start_background_tasks(self) -> None:
        """启动后台任务"""
        # 定期压缩
        if self.config.compression_enabled:
            task = asyncio.create_task(self._periodic_compression())
            self._background_tasks.append(task)

        # 定期备份
        if self.config.backup_enabled:
            task = asyncio.create_task(self._periodic_backup())
            self._background_tasks.append(task)

    async def _periodic_compression(self) -> None:
        """定期压缩任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时压缩一次
                await self.compress_memories()
            except Exception as e:
                logger.error(f"定期压缩失败: {e}")

    async def _periodic_backup(self) -> None:
        """定期备份任务"""
        while True:
            try:
                await asyncio.sleep(self.config.backup_interval)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backups/memory_backup_{timestamp}.json"
                await self.backup_memories(backup_path)
            except Exception as e:
                logger.error(f"定期备份失败: {e}")

# ============================================================================
# 主智能体类
# ============================================================================

class XinqiaoAgent:
    """心桥AI智能体"""

    def __init__(self, config: AgentConfig):
        self.config = config
        self.llm_client = None
        self.memory_manager = None
        self.conversation_count = 0
        self.start_time = datetime.now()

    async def initialize(self) -> bool:
        """初始化智能体"""
        # 初始化LLM客户端
        self.llm_client = AsyncOpenAI(
            base_url=self.config.volcano_base_url,
            api_key=self.config.volcano_api_key,
        )

        # 测试连接
        if not await self._test_llm_connection():
            logger.error("火山引擎LLM连接失败")
            return False

        # 初始化记忆管理器
        self.memory_manager = MemoryManager(self.config)
        await self.memory_manager.initialize(self.llm_client)

        logger.info(f"智能体初始化完成: {self.config.agent_name}")
        return True

    async def _test_llm_connection(self) -> bool:
        """测试LLM连接"""
        try:
            response = await self.llm_client.chat.completions.create(
                model=self.config.volcano_endpoint_id,
                messages=[{"role": "user", "content": "你好，请回复'测试成功'"}],
                max_tokens=20,
            )
            result = response.choices[0].message.content
            logger.info(f"LLM连接测试成功: {result}")
            return True
        except Exception as e:
            logger.error(f"LLM连接测试失败: {e}")
            return False

    async def chat(self, user_input: str) -> str:
        """进行对话"""
        self.conversation_count += 1

        # 构建系统提示
        system_prompt = await self._build_system_prompt()

        # 构建消息历史
        messages = [{"role": "system", "content": system_prompt}]

        # 添加记忆上下文
        context_memories = await self.memory_manager.get_context_memories(limit=5)
        if context_memories:
            memory_context = "\n".join([
                f"[记忆] {m.content}" for m in context_memories
            ])
            messages.append({
                "role": "system",
                "content": f"相关记忆:\n{memory_context}"
            })

        # 添加Zep记忆上下文（增强错误处理）
        if self.memory_manager.zep_manager:
            try:
                # 首先检查Zep服务健康状态
                if await self.memory_manager.zep_manager.health_check():
                    zep_context = await self.memory_manager.zep_manager.get_memory_context()
                    if zep_context:
                        messages.append({
                            "role": "system",
                            "content": f"对话摘要:\n{zep_context}"
                        })

                    # 添加最近消息
                    recent_messages = await self.memory_manager.zep_manager.get_recent_messages()
                    if recent_messages:
                        messages.extend(recent_messages)
                else:
                    logger.warning("Zep服务不可用，使用本地记忆继续对话")
            except Exception as e:
                logger.error(f"获取Zep记忆失败，使用本地记忆: {e}")

        # 添加用户当前输入
        messages.append({"role": "user", "content": user_input})

        # 生成响应
        try:
            response = await self.llm_client.chat.completions.create(
                model=self.config.volcano_endpoint_id,
                messages=messages,
                max_tokens=1024,
                temperature=0.8,
            )

            ai_response = response.choices[0].message.content

            # 异步处理记忆（增强错误处理）
            asyncio.create_task(self._process_conversation_memory_safe(user_input, ai_response))

            return ai_response

        except Exception as e:
            logger.error(f"对话生成失败: {e}")
            return "抱歉，我遇到了一些技术问题，请稍后再试。"

    async def _build_system_prompt(self) -> str:
        """构建系统提示"""
        return f"""你是{self.config.agent_name}，一个拥有完整记忆能力的AI助手。

核心特性：
1. 你拥有完整的记忆系统，能够记住用户的所有重要信息
2. 你会主动关心用户，根据记忆提供个性化服务
3. 你有情感理解能力，能够感知用户的情绪变化
4. 你会学习用户的偏好和习惯，持续优化服务质量

对话原则：
- 用温暖、亲切的语气与用户交流
- 主动使用记忆中的信息来提供个性化回应
- 在合适的时候主动关心用户的生活状态
- 避免重复询问已经记住的信息
- 当用户提到重要信息时，表示会记住这些内容

当前状态：
- 对话次数: {self.conversation_count}
- 运行时间: {datetime.now() - self.start_time}
- 记忆系统: 已启用
"""

    async def _process_conversation_memory_safe(self, user_input: str, ai_response: str) -> None:
        """安全处理对话记忆（带错误处理）"""
        try:
            await self._process_conversation_memory(user_input, ai_response)
        except Exception as e:
            logger.error(f"处理对话记忆时发生错误: {e}")
            # 即使记忆处理失败，也不影响主对话流程

    async def _process_conversation_memory(self, user_input: str, ai_response: str) -> None:
        """处理对话记忆"""
        try:
            # 添加用户输入到记忆
            await self.memory_manager.add_memory(
                content=user_input,
                memory_type=MemoryType.SHORT_TERM
            )

            # 添加AI响应到记忆
            await self.memory_manager.add_memory(
                content=ai_response,
                memory_type=MemoryType.SHORT_TERM
            )

            # 添加到Zep（增强错误处理）
            if self.memory_manager.zep_manager:
                try:
                    await self.memory_manager.zep_manager.add_message("user", user_input)
                    await self.memory_manager.zep_manager.add_message("assistant", ai_response)
                except Exception as zep_error:
                    logger.error(f"Zep记忆同步失败: {zep_error}")
                    # Zep失败不影响本地记忆

        except Exception as e:
            logger.error(f"处理对话记忆失败: {e}")

    async def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        all_memories = await self.memory_manager.storage.get_all_memories()

        stats = {
            "total_memories": len(all_memories),
            "by_type": {},
            "by_importance": {},
            "by_category": {},
            "oldest_memory": None,
            "newest_memory": None
        }

        for memory in all_memories:
            # 按类型统计
            type_name = memory.memory_type.value
            stats["by_type"][type_name] = stats["by_type"].get(type_name, 0) + 1

            # 按重要性统计
            importance_name = memory.importance.value
            stats["by_importance"][importance_name] = stats["by_importance"].get(importance_name, 0) + 1

            # 按分类统计
            category_name = memory.category.value
            stats["by_category"][category_name] = stats["by_category"].get(category_name, 0) + 1

            # 最老和最新记忆
            if stats["oldest_memory"] is None or memory.created_at < stats["oldest_memory"]:
                stats["oldest_memory"] = memory.created_at
            if stats["newest_memory"] is None or memory.created_at > stats["newest_memory"]:
                stats["newest_memory"] = memory.created_at

        return stats

    async def search_memories(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索记忆"""
        memories = await self.memory_manager.search_memories(query, limit=limit)
        return [
            {
                "id": m.id,
                "content": m.content,
                "type": m.memory_type.value,
                "importance": m.importance.value,
                "category": m.category.value,
                "created_at": m.created_at.isoformat(),
                "access_count": m.access_count
            }
            for m in memories
        ]

    async def export_memories(self, file_path: str) -> bool:
        """导出记忆"""
        return await self.memory_manager.backup_memories(file_path)

    async def import_memories(self, file_path: str) -> bool:
        """导入记忆"""
        return await self.memory_manager.restore_memories(file_path)

    async def cleanup(self) -> None:
        """清理资源"""
        # 停止后台任务
        for task in self.memory_manager._background_tasks:
            task.cancel()

        # 刷新缓存
        await self.memory_manager.storage._flush_cache()

        # 清理Zep连接
        if self.memory_manager.zep_manager:
            await self.memory_manager.zep_manager.cleanup()

        logger.info("智能体资源清理完成")

# ============================================================================
# 主程序
# ============================================================================

async def main():
    """主程序"""
    print("🤖 心桥AI智能体 - 高级记忆管理系统")
    print("=" * 50)

    # 创建配置（会自动处理会话持久化）
    config = AgentConfig.from_env()

    # 验证配置
    if not config.volcano_api_key or not config.volcano_endpoint_id:
        print("❌ 错误：请在1.env文件中配置火山引擎相关环境变量")
        print("需要设置：VOLCENGINE_API_KEY, VOLCANO_LLM_ENDPOINT_ID")
        return

    # 创建智能体
    agent = XinqiaoAgent(config)

    # 初始化
    if not await agent.initialize():
        print("❌ 智能体初始化失败")
        return

    print(f"✅ 智能体初始化成功")
    print(f"👤 用户ID: {config.user_id}")
    print(f"💬 会话ID: {config.session_id}")
    print(f"📊 记忆系统: 已启用")
    print(f"🔄 Zep集成: {'已启用' if config.zep_enabled else '已禁用'}")
    print(f"💾 自动备份: {'已启用' if config.backup_enabled else '已禁用'}")
    print(f"🗜️  记忆压缩: {'已启用' if config.compression_enabled else '已禁用'}")
    print("\n开始对话吧！输入 'quit' 退出，输入 'help' 查看命令")
    print("💡 提示：您的会话信息已保存，重启后会恢复之前的记忆")

    try:
        while True:
            user_input = input(f"\n{config.user_id}: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！您的会话信息已保存")
                break

            if user_input.lower() == 'help':
                print("""
🔧 可用命令：
- help: 显示此帮助
- stats: 显示记忆统计信息
- search <关键词>: 搜索记忆
- export <文件路径>: 导出记忆
- import <文件路径>: 导入记忆
- compress: 手动压缩记忆
- backup: 手动备份记忆
- reset_session: 重置会话（清除会话信息）
- quit/exit: 退出程序
                """)
                continue

            if user_input.lower() == 'stats':
                stats = await agent.get_memory_stats()
                print(f"📊 记忆统计:")
                print(f"  总记忆数: {stats['total_memories']}")
                print(f"  按类型: {stats['by_type']}")
                print(f"  按重要性: {stats['by_importance']}")
                print(f"  按分类: {stats['by_category']}")
                continue

            if user_input.lower().startswith('search '):
                query = user_input[7:]
                memories = await agent.search_memories(query)
                print(f"🔍 搜索结果 ({len(memories)} 条):")
                for i, memory in enumerate(memories, 1):
                    print(f"  {i}. [{memory['type']}] {memory['content'][:50]}...")
                continue

            if user_input.lower().startswith('export '):
                file_path = user_input[7:]
                if await agent.export_memories(file_path):
                    print(f"✅ 记忆导出成功: {file_path}")
                else:
                    print(f"❌ 记忆导出失败")
                continue

            if user_input.lower().startswith('import '):
                file_path = user_input[7:]
                if await agent.import_memories(file_path):
                    print(f"✅ 记忆导入成功: {file_path}")
                else:
                    print(f"❌ 记忆导入失败")
                continue

            if user_input.lower() == 'compress':
                count = await agent.memory_manager.compress_memories()
                print(f"🗜️  记忆压缩完成，删除了 {count} 条记忆")
                continue

            if user_input.lower() == 'backup':
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_{timestamp}.json"
                if await agent.export_memories(backup_path):
                    print(f"💾 记忆备份成功: {backup_path}")
                else:
                    print(f"❌ 记忆备份失败")
                continue

            if user_input.lower() == 'reset_session':
                try:
                    session_file = Path("session_info.json")
                    if session_file.exists():
                        session_file.unlink()
                        print("🔄 会话信息已重置，请重启程序以创建新会话")
                    else:
                        print("ℹ️  没有找到会话信息文件")
                except Exception as e:
                    print(f"❌ 重置会话失败: {e}")
                continue

            if not user_input:
                continue

            # 进行对话
            print(f"\n🤖 {config.agent_name}:")
            response = await agent.chat(user_input)
            print(response)

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")

    finally:
        await agent.cleanup()
        print("💾 会话信息已保存，下次启动会恢复记忆")

if __name__ == "__main__":
    asyncio.run(main())
