#!/usr/bin/env python3
"""
认证API测试
测试接口:
- POST /api/v1/auth/anonymous-login
- POST /api/v1/auth/refresh-token
- POST /api/v1/auth/finalize_onboarding
"""

import asyncio
import sys
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class AuthTester(BaseAPITester):
    """认证API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("02_auth", base_url)

    async def test_anonymous_login(self):
        """测试匿名登录API"""
        self.logger.info("🔐 测试匿名登录API")

        data = {
            "device_info": {
                "device_id": self.test_data["device_id"],
                "platform": self.test_data["platform"],
                "app_version": self.test_data["app_version"]
            }
        }

        response = await self.make_request(
            "POST",
            "/api/v1/auth/anonymous-login",
            data=data,
            include_auth=False
        )

        # 验证响应
        if "access_token" in response:
            self.access_token = response["access_token"]
            self.refresh_token = response["refresh_token"]
            self.user_id = response.get("user", {}).get("id")
            self.logger.info(f"✅ 匿名登录成功，用户ID: {self.user_id}")

            # 验证Token格式
            if self.access_token and len(self.access_token) > 20:
                self.logger.info("✅ Access Token格式正确")
            else:
                self.logger.error("❌ Access Token格式错误")

            if self.refresh_token and len(self.refresh_token) > 20:
                self.logger.info("✅ Refresh Token格式正确")
            else:
                self.logger.error("❌ Refresh Token格式错误")
        else:
            self.logger.error("❌ 匿名登录失败，未获取到access_token")

    async def test_refresh_token(self):
        """测试Token刷新API"""
        if not self.refresh_token:
            self.logger.warning("⚠️ 跳过Token刷新测试，因为没有refresh_token")
            return

        self.logger.info("🔄 测试Token刷新API")
        data = {"refresh_token": self.refresh_token}

        response = await self.make_request(
            "POST",
            "/api/v1/auth/refresh-token",
            data=data,
            include_auth=False
        )

        # 验证响应并更新Token
        if "access_token" in response:
            old_access_token = self.access_token
            self.access_token = response["access_token"]
            self.refresh_token = response["refresh_token"]
            self.logger.info("✅ Token刷新成功")

            # 验证新Token与旧Token不同
            if self.access_token != old_access_token:
                self.logger.info("✅ 新Token与旧Token不同")
            else:
                self.logger.warning("⚠️ 新Token与旧Token相同")

            # 保存新的认证信息到共享配置
            self.save_auth_info()
        else:
            self.logger.error("❌ Token刷新失败")

    async def test_finalize_onboarding(self):
        """测试完成引导流程API"""
        if not self.access_token:
            self.logger.warning("⚠️ 跳过引导完成测试，因为没有access_token")
            return

        self.logger.info("🎯 测试完成引导流程API")

        # 先获取一个角色ID
        try:
            character_id = await self.get_character_id()
        except Exception as e:
            self.logger.error(f"❌ 无法获取角色ID: {e}")
            return

        onboarding_data = {
            "nickname": self.test_data["nickname"],
            "core_needs": ["情感陪伴", "健康咨询"],
            "interests": ["音乐", "阅读"],
            "communication_style_preference": "温和",
            "character": {
                "name": "测试角色",
                "role": "朋友",
                "voice_id": "voice_001"
            }
        }

        response = await self.make_request(
            "POST",
            "/api/v1/auth/finalize_onboarding",
            data=onboarding_data
        )

        # 验证响应
        if response and "error" not in response:
            self.logger.info("✅ 引导流程完成")

            # 保存引导完成相关数据到共享配置
            onboarding_result = {
                "onboarding_completed": True,
                "character_id": character_id,
                "nickname": onboarding_data["nickname"],
                "core_needs": onboarding_data["core_needs"],
                "interests": onboarding_data["interests"],
                "communication_style_preference": onboarding_data["communication_style_preference"],
                "character_info": onboarding_data["character"]
            }
            self.save_test_result("onboarding", onboarding_result)
        else:
            self.logger.error("❌ 引导流程完成失败")

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始认证API测试")

        tests = [
            ("匿名登录", self.test_anonymous_login),
            ("Token刷新", self.test_refresh_token),
            ("完成引导", self.test_finalize_onboarding),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(0.5)  # 测试间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='认证API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with AuthTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
