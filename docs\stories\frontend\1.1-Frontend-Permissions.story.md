# 故事 1.1-Frontend-Permissions: 系统权限管理

## 基本信息
- **故事编号**: 1.1-Frontend-Permissions
- **故事标题**: 系统权限管理
- **Epic**: MVP - 隐私安全与合规保障
- **用户角色**: 前端开发者
- **优先级**: 高（P0 - 应用核心功能依赖）
- **工作量估计**: 2-3 个工作日
- **依赖关系**: 1.1-Frontend（项目基础设置）, 1.1-Frontend-ErrorHandling（错误处理）
- **Status**: Approved

## 故事描述

作为前端开发者，我需要实现完善的系统权限管理机制，**以便** 应用能够优雅地请求、管理和处理各种系统权限（麦克风、通知、存储等），并在权限被拒绝时提供适当的降级处理和用户引导。

## 验收标准

### AC1: 麦克风权限管理
- [ ] 使用Audio.usePermissions hook实现麦克风权限的请求和状态检测
- [ ] 提供权限被拒绝时的用户友好提示和引导
- [ ] 支持权限恢复检测和重新请求流程（包含冷却机制）
- [ ] 集成权限状态的持久化存储和时间戳记录

### AC2: 通知权限管理
- [ ] 使用Notifications.requestPermissionsAsync实现推送通知权限的请求和管理
- [ ] 支持iOS和Android平台特定的通知权限配置
- [ ] 提供通知设置页面的系统设置跳转（区分iOS和Android）
- [ ] 处理通知权限变更的应用响应和状态同步

### AC3: 存储权限管理
- [ ] 使用MediaLibrary.usePermissions hook实现文件存储权限的检测和请求
- [ ] 处理Android 13+的细粒度媒体权限（granularPermissions）
- [ ] 支持媒体文件访问权限的管理和权限选择器
- [ ] 实现数据导出和备份的权限处理

### AC4: 权限教育和引导
- [ ] 设计适老化的权限说明和教育界面
- [ ] 提供系统设置页面的精准跳转（支持iOS和Android特定URL）
- [ ] 实现权限状态的可视化展示和冷却时间提示
- [ ] 支持权限问题的故障排除指南和降级处理

## Tasks / Subtasks

### 第一阶段：现代化权限基础设施 (1天)
- [ ] **权限配置和环境设置** (AC1, AC2, AC3)
  - 配置app.json/app.config.js的权限插件和描述
  - 设置iOS Info.plist权限描述信息
  - 配置Android权限清单（包括Android 13+适配）
  - 建立环境变量配置系统

- [ ] **权限状态管理重构** (AC1, AC2, AC3)
  - 创建基于Zustand的现代化权限状态Store
  - 实现权限检测的统一接口（hooks-based）
  - 集成权限状态持久化和时间戳记录
  - 建立权限变更监听机制和冷却时间控制

### 第二阶段：麦克风权限现代化实现 (1天)
- [ ] **音频权限处理升级** (AC1)
  - 使用Audio.usePermissions hook替代废弃API
  - 创建现代化的音频录制权限检测组件
  - 设计权限拒绝的降级UI和重试机制
  - 实现权限恢复检测和智能提醒机制

### 第三阶段：通知和存储权限现代化 (1天)
- [ ] **通知权限管理升级** (AC2)
  - 使用Notifications.requestPermissionsAsync实现现代化权限请求
  - 创建支持iOS/Android差异的通知设置界面
  - 处理通知权限的平台特定设置跳转
  - 实现权限状态同步和变更监听

- [ ] **存储权限处理升级** (AC3)
  - 使用MediaLibrary.usePermissions hook实现媒体库权限管理
  - 创建Android 13+细粒度权限支持
  - 处理媒体权限选择器和限制访问模式
  - 实现数据备份权限流程和存储检测

### 第四阶段：用户教育界面现代化 (半天)
- [ ] **权限引导设计升级** (AC4)
  - 创建适老化的权限说明界面和权益展示
  - 实现系统设置精准跳转（支持iOS/Android特定路径）
  - 设计权限状态可视化和冷却时间提示
  - 集成故障排除指南和智能降级处理

## Dev Notes

### 技术实现要求

基于现代化Expo SDK的权限管理：

#### 环境变量配置
- [ ] `PERMISSION_REQUEST_TIMEOUT` - 权限请求超时时间（默认30秒）
- [ ] `PERMISSION_RETRY_COUNT` - 权限请求重试次数（默认3次）
- [ ] `PERMISSION_EDUCATION_ENABLED` - 是否启用权限教育界面（true/false）
- [ ] `SYSTEM_SETTINGS_REDIRECT_ENABLED` - 是否允许跳转系统设置（true/false）

#### 应用配置要求（app.json/app.config.js）
```javascript
{
  "expo": {
    "plugins": [
      [
        "expo-notifications",
        {
          "icon": "./assets/notification-icon.png",
          "color": "#ffffff",
          "sounds": ["./assets/notification-sound.wav"]
        }
      ],
      [
        "expo-media-library",
        {
          "photosPermission": "心桥需要访问您的照片以保存聊天记录和语音消息。",
          "savePhotosPermission": "心桥需要保存照片到您的相册以备份重要内容。",
          "isAccessMediaLocationEnabled": true
        }
      ]
    ],
    "ios": {
      "infoPlist": {
        "NSMicrophoneUsageDescription": "心桥需要使用麦克风进行语音对话，为您提供更自然的交流体验。",
        "NSCameraUsageDescription": "心桥需要使用相机拍摄照片，帮助您记录和分享重要信息。"
      }
    },
    "android": {
      "permissions": [
        "android.permission.RECORD_AUDIO",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.READ_MEDIA_IMAGES",
        "android.permission.READ_MEDIA_VIDEO",
        "android.permission.ACCESS_MEDIA_LOCATION"
      ]
    }
  }
}
```

#### 权限管理核心实现
```typescript
// lib/permissions/permission-manager.ts
import { Audio } from 'expo-av';
import * as Notifications from 'expo-notifications';
import * as MediaLibrary from 'expo-media-library';

export enum PermissionType {
  MICROPHONE = 'microphone',
  NOTIFICATIONS = 'notifications',
  MEDIA_LIBRARY = 'mediaLibrary'
}

export enum PermissionStatus {
  GRANTED = 'granted',
  DENIED = 'denied',
  UNDETERMINED = 'undetermined'
}

export interface PermissionResponse {
  status: PermissionStatus;
  expires?: 'never' | number;
  granted: boolean;
  canAskAgain: boolean;
}

class ModernPermissionManager {
  
  static async requestNotificationPermission(): Promise<PermissionResponse> {
    try {
      const response = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowDisplayInCarPlay: true,
          allowCriticalAlerts: false,
          provideAppNotificationSettings: true,
          allowProvisional: false,
          allowAnnouncements: true,
        },
        android: {}
      });
      
      return {
        status: response.status as PermissionStatus,
        granted: response.granted,
        canAskAgain: response.canAskAgain,
        expires: response.expires
      };
    } catch (error) {
      console.error('Notification permission error:', error);
      return {
        status: PermissionStatus.DENIED,
        granted: false,
        canAskAgain: false
      };
    }
  }

  static async checkNotificationPermission(): Promise<PermissionResponse> {
    const response = await Notifications.getPermissionsAsync();
    return {
      status: response.status as PermissionStatus,
      granted: response.granted,
      canAskAgain: response.canAskAgain,
      expires: response.expires
    };
  }

  // 这些方法需要在React组件中使用hooks
  static createAudioPermissionHook() {
    return Audio.usePermissions;
  }

  static createMediaLibraryPermissionHook() {
    return MediaLibrary.usePermissions;
  }
}

export { ModernPermissionManager };
```

#### 权限状态管理Store（更新）
```typescript
// store/permissions.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ModernPermissionManager, PermissionType, PermissionStatus, PermissionResponse } from '../lib/permissions/permission-manager';

interface PermissionState {
  microphone: PermissionStatus;
  notifications: PermissionStatus;
  mediaLibrary: PermissionStatus;
  
  // 权限请求时间戳（用于避免频繁请求）
  lastRequestTime: Record<PermissionType, number>;
  
  // 操作方法
  updatePermissionStatus: (type: PermissionType, status: PermissionStatus) => void;
  checkAllPermissions: () => Promise<void>;
  requestNotificationPermission: () => Promise<PermissionResponse>;
  canRequestPermission: (type: PermissionType) => boolean;
}

export const usePermissionStore = create<PermissionState>()(
  persist(
    (set, get) => ({
      microphone: PermissionStatus.UNDETERMINED,
      notifications: PermissionStatus.UNDETERMINED,
      mediaLibrary: PermissionStatus.UNDETERMINED,
      lastRequestTime: {
        [PermissionType.MICROPHONE]: 0,
        [PermissionType.NOTIFICATIONS]: 0,
        [PermissionType.MEDIA_LIBRARY]: 0,
      },

      updatePermissionStatus: (type, status) => {
        set(state => ({ 
          ...state, 
          [type]: status,
          lastRequestTime: {
            ...state.lastRequestTime,
            [type]: Date.now()
          }
        }));
      },

      checkAllPermissions: async () => {
        // 通知权限检查
        const notifStatus = await ModernPermissionManager.checkNotificationPermission();
        
        set(state => ({
          ...state,
          notifications: notifStatus.status
        }));
      },

      requestNotificationPermission: async () => {
        const response = await ModernPermissionManager.requestNotificationPermission();
        
        set(state => ({
          ...state,
          notifications: response.status,
          lastRequestTime: {
            ...state.lastRequestTime,
            [PermissionType.NOTIFICATIONS]: Date.now()
          }
        }));
        
        return response;
      },

      canRequestPermission: (type) => {
        const state = get();
        const lastRequest = state.lastRequestTime[type] || 0;
        const cooldownPeriod = 60000; // 1分钟冷却时间
        
        return Date.now() - lastRequest > cooldownPeriod;
      }
    }),
    {
      name: 'permission-storage',
      // 只持久化权限状态，不持久化时间戳
      partialize: (state) => ({
        microphone: state.microphone,
        notifications: state.notifications,
        mediaLibrary: state.mediaLibrary,
      })
    }
  )
);
```

#### 现代化权限请求组件
```typescript
// components/permission/PermissionRequestCard.tsx
import React from 'react';
import { View, Text, Pressable, Alert } from 'react-native';
import { Linking } from 'expo-linking';
import { Audio } from 'expo-av';
import * as MediaLibrary from 'expo-media-library';
import { usePermissionStore } from '../../store/permissions';
import { PermissionType, PermissionStatus } from '../../lib/permissions/permission-manager';

interface PermissionRequestCardProps {
  type: PermissionType;
  title: string;
  description: string;
  benefits: string[];
  onPermissionGranted: () => void;
  onPermissionDenied?: () => void;
}

export const PermissionRequestCard: React.FC<PermissionRequestCardProps> = ({
  type,
  title,
  description,
  benefits,
  onPermissionGranted,
  onPermissionDenied
}) => {
  const { requestNotificationPermission, canRequestPermission } = usePermissionStore();
  
  // 使用现代化的权限hooks
  const [audioPermission, requestAudioPermission] = Audio.usePermissions();
  const [mediaPermission, requestMediaPermission] = MediaLibrary.usePermissions();

  const handleRequestPermission = async () => {
    if (!canRequestPermission(type)) {
      Alert.alert(
        '请稍后再试',
        '为了避免过度打扰，请稍后再尝试授权。',
        [{ text: '好的', style: 'default' }]
      );
      return;
    }

    let response;
    
    try {
      switch (type) {
        case PermissionType.MICROPHONE:
          response = await requestAudioPermission();
          break;
        case PermissionType.NOTIFICATIONS:
          response = await requestNotificationPermission();
          break;
        case PermissionType.MEDIA_LIBRARY:
          response = await requestMediaPermission();
          break;
        default:
          return;
      }

      if (response.granted) {
        onPermissionGranted();
      } else {
        if (response.canAskAgain) {
          showPermissionDeniedDialog(false);
        } else {
          showPermissionDeniedDialog(true);
        }
        onPermissionDenied?.();
      }
    } catch (error) {
      console.error('Permission request error:', error);
      Alert.alert('权限请求失败', '请稍后重试或手动在设置中开启权限。');
    }
  };

  const showPermissionDeniedDialog = (isPermanentlyDenied: boolean) => {
    const title = isPermanentlyDenied ? '权限被永久拒绝' : '权限被拒绝';
    const message = isPermanentlyDenied 
      ? '您已永久拒绝此权限，请在系统设置中手动开启。'
      : '您可以稍后重新尝试，或在系统设置中开启权限。';
    
    const buttons = isPermanentlyDenied 
      ? [
          { text: '取消', style: 'cancel' as const },
          { text: '去设置', onPress: openSystemSettings }
        ]
      : [
          { text: '稍后再试', style: 'cancel' as const },
          { text: '去设置', onPress: openSystemSettings }
        ];

    Alert.alert(title, message, buttons);
  };

  const openSystemSettings = async () => {
    try {
      await Linking.openSettings();
    } catch (error) {
      console.error('Failed to open settings:', error);
      Alert.alert('无法打开设置', '请手动进入系统设置页面开启权限。');
    }
  };

  return (
    <View className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <Text className="text-xl font-semibold text-gray-900 mb-2">
        {title}
      </Text>
      <Text className="text-gray-600 mb-4 leading-6">
        {description}
      </Text>
      
      {benefits.length > 0 && (
        <View className="mb-6">
          <Text className="text-sm font-medium text-gray-700 mb-2">
            开启后您可以：
          </Text>
          {benefits.map((benefit, index) => (
            <Text key={index} className="text-sm text-gray-600 mb-1 pl-4">
              • {benefit}
            </Text>
          ))}
        </View>
      )}

      <Pressable
        onPress={handleRequestPermission}
        className="bg-blue-500 px-6 py-4 rounded-lg active:bg-blue-600"
        style={{ minHeight: 44 }} // 适老化触摸区域
      >
        <Text className="text-white text-center font-medium text-base">
          授予权限
        </Text>
      </Pressable>
    </View>
  );
};
```

#### 系统设置跳转工具（更新）
```typescript
// utils/system-settings.ts
import { Linking, Platform, Alert } from 'react-native';

export interface SettingsOptions {
  type?: 'app' | 'notifications' | 'privacy' | 'bluetooth' | 'cellular';
  fallbackToGeneral?: boolean;
}

export const openSystemSettings = async (options: SettingsOptions = {}) => {
  const { type = 'app', fallbackToGeneral = true } = options;
  
  try {
    if (Platform.OS === 'ios') {
      let url = 'app-settings:';
      
      // iOS specific settings URLs
      switch (type) {
        case 'notifications':
          url = 'app-settings:NOTIFICATIONS_ID';
          break;
        case 'privacy':
          url = 'app-settings:Privacy';
          break;
        case 'app':
        default:
          url = 'app-settings:';
          break;
      }
      
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        return true;
      }
    } else {
      // Android specific settings
      let url = 'app-settings:';
      
      switch (type) {
        case 'notifications':
          url = 'app-settings:notification_settings';
          break;
        case 'app':
        default:
          url = 'app-settings:';
          break;
      }
      
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        return true;
      }
    }
    
    // 降级处理
    if (fallbackToGeneral) {
      await Linking.openSettings();
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Failed to open system settings:', error);
    
    if (fallbackToGeneral) {
      try {
        await Linking.openSettings();
        return true;
      } catch (fallbackError) {
        console.error('Failed to open general settings:', fallbackError);
        Alert.alert(
          '无法打开设置',
          '请手动进入系统设置 > 应用管理 > 心桥 > 权限管理',
          [{ text: '我知道了', style: 'default' }]
        );
        return false;
      }
    }
    
    return false;
  }
};

// 权限特定的设置跳转
export const openPermissionSettings = async (permissionType: 'microphone' | 'notifications' | 'photos') => {
  let settingsType: SettingsOptions['type'] = 'app';
  
  switch (permissionType) {
    case 'notifications':
      settingsType = 'notifications';
      break;
    case 'microphone':
    case 'photos':
      settingsType = 'privacy';
      break;
  }
  
  return openSystemSettings({ type: settingsType });
};
```

### 适老化权限引导设计（更新）

#### 权限说明文案
```javascript
const PERMISSION_MESSAGES = {
  [PermissionType.MICROPHONE]: {
    title: '语音功能权限',
    description: '开启麦克风权限后，您可以直接和AI助手进行语音对话，就像和朋友聊天一样自然。我们承诺您的语音数据只用于对话，不会被保存或分享。',
    benefits: [
      '语音对话更方便，无需打字',
      '解放双手，操作更简单',
      'AI能更好理解您的需求',
      '适合视力不便时使用'
    ],
    deniedMessage: '没有麦克风权限时，您只能通过文字与AI对话。如需语音功能，请点击"去设置"开启麦克风权限。',
    deniedTitle: '语音功能暂不可用'
  },
  [PermissionType.NOTIFICATIONS]: {
    title: '消息提醒权限',
    description: '开启通知权限后，心桥可以在重要时刻提醒您，比如服药时间、预约提醒、重要消息等，让您不会错过任何重要事情。',
    benefits: [
      '及时收到服药和预约提醒',
      '重要消息不会遗漏',
      '家人关怀信息及时送达',
      '紧急情况能够及时通知'
    ],
    deniedMessage: '没有通知权限时，您将无法收到重要提醒。为了不错过重要事情，建议开启通知权限。',
    deniedTitle: '无法发送提醒'
  },
  [PermissionType.MEDIA_LIBRARY]: {
    title: '相册访问权限',
    description: '开启相册权限后，心桥可以帮您保存重要的聊天记录、照片和语音消息，方便您随时查看和分享给家人。',
    benefits: [
      '保存重要聊天内容',
      '备份珍贵照片和语音',
      '方便分享给家人朋友',
      '避免重要信息丢失'
    ],
    deniedMessage: '没有相册权限时，无法保存照片和语音消息。如需保存功能，请在设置中开启相册权限。',
    deniedTitle: '无法保存内容'
  }
};
```

## Testing

### 现代化权限功能测试
- [ ] **权限请求流程**: 测试各类权限使用hooks的请求和响应
  ```javascript
  // 测试Audio.usePermissions hook
  import { renderHook, act } from '@testing-library/react-native';
  import { Audio } from 'expo-av';
  
  test('音频权限hook请求流程', async () => {
    const { result } = renderHook(() => Audio.usePermissions());
    
    expect(result.current[0]?.status).toBe('undetermined');
    
    await act(async () => {
      await result.current[1](); // requestPermission
    });
    
    expect(result.current[0]?.granted).toBe(true);
  });
  ```

- [ ] **权限拒绝处理**: 验证权限被拒绝时的降级功能和冷却机制
  ```javascript
  // 测试权限冷却机制
  test('权限请求冷却时间控制', async () => {
    const { canRequestPermission } = usePermissionStore.getState();
    
    // 第一次请求
    const canRequest1 = canRequestPermission(PermissionType.MICROPHONE);
    expect(canRequest1).toBe(true);
    
    // 请求后立即再次尝试
    const canRequest2 = canRequestPermission(PermissionType.MICROPHONE);
    expect(canRequest2).toBe(false);
  });
  ```

- [ ] **权限状态同步**: 测试权限状态变更的实时同步和持久化
- [ ] **系统设置跳转**: 验证iOS和Android平台的设置页面跳转准确性
- [ ] **平台差异处理**: 测试Android 13+细粒度权限和iOS特定配置

### 用户体验测试
- [ ] **权限说明清晰度**: 验证老年用户能理解权限用途和权益
- [ ] **权限引导易用性**: 确保权限设置流程简单易懂，符合适老化标准
- [ ] **错误恢复能力**: 测试权限问题的恢复机制和智能提示
- [ ] **跨平台一致性**: 验证iOS和Android平台的一致体验
- [ ] **冷却时间提示**: 测试权限请求频率限制的用户友好提示

### 环境和配置测试
- [ ] **应用配置验证**: 验证app.json/app.config.js的权限配置正确性
- [ ] **权限描述文案**: 测试iOS Info.plist和Android权限描述的合规性
- [ ] **环境变量配置**: 验证不同环境下的权限行为差异

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 前端项目基础设置已完成（故事1.1-Frontend）
- [ ] 错误处理机制已实现（故事1.1-Frontend-ErrorHandling）
- [ ] Expo SDK已升级到最新稳定版本
- [ ] 应用配置文件(app.json)权限部分已准备就绪
- [ ] 各平台权限API文档已研究和更新
- [ ] 隐私政策和权限说明已确定和法务审核

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过功能测试
- [ ] 现代化权限管理在iOS和Android上正常工作
- [ ] 用户体验测试结果满足适老化要求
- [ ] 隐私合规审查通过
- [ ] 权限配置通过应用商店审核标准
- [ ] 跨平台权限行为一致性验证通过

### 交付物 (Deliverables)
- [ ] **现代化权限管理组件库**：基于hooks的可复用权限请求和管理组件
- [ ] **权限状态管理**：基于Zustand的现代化权限状态Store
- [ ] **权限引导界面**：适老化的权限说明和设置界面
- [ ] **权限处理文档**：开发者使用指南和API迁移文档
- [ ] **平台配置文件**：完整的app.json权限配置和平台特定设置

## 风险与缓解措施

### 主要风险
1. **API迁移复杂性**：从废弃API迁移到现代化hooks可能影响现有功能
2. **平台差异性**：iOS和Android权限机制存在差异，特别是Android 13+
3. **用户权限恐惧**：老年用户可能对权限请求感到担忧
4. **权限拒绝影响**：关键权限被拒绝可能严重影响功能
5. **系统设置复杂**：用户可能无法找到正确的设置页面

### 缓解措施
1. **渐进式迁移**：分阶段迁移权限API，确保向后兼容性
2. **平台适配测试**：针对不同平台版本实现特定的权限处理逻辑
3. **透明沟通**：清晰说明权限用途、权益和隐私保护措施
4. **智能降级**：为权限被拒绝情况提供替代方案和重试机制
5. **精准导航**：提供准确的系统设置页面跳转和图文指导

## 后续故事依赖关系

### 🔗 此故事为以下故事提供基础：
- **故事1.4**: 实时语音会话（依赖麦克风权限的现代化实现）
- **故事1.6**: 对话式提醒功能（依赖通知权限的现代化管理）
- **所有涉及媒体和存储的功能**（依赖MediaLibrary权限管理）

### 📋 与其他模块的集成点：
- **设置模块**：权限管理设置界面和状态同步
- **错误处理**：权限错误的友好提示和恢复机制
- **用户引导**：首次使用时的权限教育和适老化引导

## 📚 相关文档引用

- **产品需求 - 隐私与安全** (`@docs/prd/requirements.md#NFR4-隐私安全与合规保障`)
  *详细说明了权限管理的合规要求、用户隐私保护标准和法律法规要求*

- **移动应用技术栈** (`@docs/architecture/mobile-app-tech-stack.md#权限管理架构`)
  *定义了权限管理的技术选型、架构决策和现代化迁移策略*

- **移动应用编码标准** (`@docs/architecture/mobile-app-coding-standards.md#权限处理规范`)
  *规定了权限请求的代码规范、错误处理模式和用户体验标准*

- **UX设计指南** (`@docs/prd/ux-design.md#权限交互设计`)
  *包含权限请求的适老化设计原则、交互流程和用户教育策略*

- **Expo权限管理最佳实践** (https://docs.expo.dev/guides/permissions/)
  *官方权限管理指南，包含最新API用法、平台差异说明和迁移建议*

- **iOS人机界面指南 - 请求权限** (https://developer.apple.com/design/human-interface-guidelines/patterns/requesting-permission/)
  *Apple官方权限请求设计指南，确保符合App Store审核标准*

- **Android权限最佳实践** (https://developer.android.com/training/permissions/requesting)
  *Google官方权限管理指南，特别是Android 13+的细粒度权限处理* 