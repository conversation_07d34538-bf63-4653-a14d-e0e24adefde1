1.  **前端 (React Native App):**
    *   **核心库:** `volcengine-react-native` RTC SDK。
    *   **职责:**
        *   处理UI交互（如通话按钮、角色选择界面）。
        *   管理设备权限（麦克风）。
        *   通过您提供的后端API获取RTC认证信息。
        *   **使用Volcengine SDK**：初始化引擎、加入/离开RTC房间、采集并发送用户语音。
        *   接收并播放由火山引擎服务合成的AI语音。
        *   向后端发送控制指令，如“中断对话”。

2.  **后端 (Python FastAPI - `xinqiao-code`):**
    *   **职责:**
        *   作为**业务和控制中心**。
        *   **用户认证 (`auth_service.py`):** 处理匿名登录，生成和管理JWT，为前端生成加入RTC房间所需的**临时Token**。
        *   **RTC会话管理 (`rtc_session_service.py`):** 调用火山引擎**服务端API**（如`StartVoiceChat`, `StopVoiceChat`, `UpdateVoiceChat`），启动和管理AI语音对话任务。
        *   **Webhook处理 (`rtc_webhook_routes.py`):** 接收火山引擎的回调（如ASR识别结果），并将其转发给**对话编排服务**。
        *   **核心对话逻辑 (`chat_orchestration_service.py`):** 接收识别出的文本，结合记忆、用户画像，调用LLM生成回复。
        *   **工具执行 (`tool_executor_service.py`, `reminder_service.py`):** 当LLM决定使用工具时（如创建提醒），执行相应逻辑。

3.  **火山引擎RTC服务 (PaaS):**
    *   **职责:**
        *   提供高可用的RTC信令和媒体传输网络。
        *   **实时ASR (语音识别):** 将前端用户的语音实时转换成文本。
        *   **实时TTS (语音合成):** 将后端AI生成的文本实时合成为语音。
        *   **核心交互桥梁:** 将ASR结果通过Webhook发送到您的后端，并将后端返回的文本进行TTS后，通过RTC音频流发送回前端。
        *   **Function Calling 流程支持:** 将LLM的工具调用意图通过Webhook发送到后端，并接收后端执行工具后的结果。

#### **核心工作流程图解**

一次完整的语音对话交互流程如下：

```mermaid
sequenceDiagram
    participant App (React Native)
    participant Backend (FastAPI)
    participant Volcengine RTC

    App->>Backend: 1. /auth/anonymous-login (获取App的JWT)
    Backend-->>App: 2. 返回JWT Token

    App->>Backend: 3. /rtc/prepare_session (请求开始语音对话, 携带JWT)
    Backend->>Volcengine RTC: 4. 调用 StartVoiceChat (服务端API)
    Backend->>Backend: 5. 生成RTC Client Token (使用AppID/AppKey)
    Backend-->>App: 6. 返回RTC连接凭证 (含Client Token, RoomID, TaskID)

    App->>Volcengine RTC: 7. 使用凭证初始化SDK, joinRoom()
    Volcengine RTC-->>App: 8. 加入房间成功

    App->>Volcengine RTC: 9. startAudioCapture() (开始发送用户语音流)
    Volcengine RTC->>Volcengine RTC: 10. ASR处理
    Volcengine RTC->>Backend: 11. /rtc_event_handler (Webhook: 发送ASR识别结果)

    Backend->>Backend: 12. ChatOrchestrationService处理文本, 调用LLM/工具
    Backend-->>Volcengine RTC: 13. 返回AI生成的文本回复

    Volcengine RTC->>Volcengine RTC: 14. TTS处理
    Volcengine RTC-->>App: 15. 将AI合成的语音流发送给App

    Note right of App: 用户听到AI回复, 循环步骤9-15

    App->>Backend: 16. /rtc/end_session (用户挂断)
    Backend->>Volcengine RTC: 17. 调用 StopVoiceChat (服务端API)
    Backend-->>App: 18. 确认会话结束
    App->>App: 19. leaveRoom(), destroyEngine() (清理本地资源)
```

---

### **React Native最佳实践与完整代码实现**

我们将通过创建一系列的服务和UI组件来构建一个健壮、可维护的前端应用。

#### **1. 项目设置与依赖**

首先，确保您的React Native项目中已安装火山引擎SDK：

```bash
npm install @volcengine/react-native-rtc
# or
yarn add @volcengine/react-native-rtc
```

同时，安装`axios`用于网络请求，以及一个状态管理库（如`zustand`或`@reduxjs/toolkit`）来管理全局状态。

#### **2. 状态管理**

我们需要一个全局状态来管理认证、会话和通话状态。这里使用`zustand`作为示例：

```typescript
// src/store/useAppStore.ts
import { create } from 'zustand';

interface RtcSessionState {
  token: string | null;
  roomId: string | null;
  userId: string | null;
  taskId: string | null;
}

interface AppState {
  jwt: string | null;
  isLoggedIn: boolean;
  isCalling: boolean;
  isListening: boolean; // AI是否正在说话
  isMuted: boolean;
  activeCharacterId: string | null;
  rtcSession: RtcSessionState | null;

  setJwt: (token: string | null) => void;
  startCall: (session: RtcSessionState) => void;
  endCall: () => void;
  // ... 其他actions
}

export const useAppStore = create<AppState>((set) => ({
  jwt: null,
  isLoggedIn: false,
  isCalling: false,
  isListening: false,
  isMuted: false,
  activeCharacterId: 'default', // 默认角色ID
  rtcSession: null,

  setJwt: (token) => set({ jwt: token, isLoggedIn: !!token }),
  startCall: (session) => set({ isCalling: true, rtcSession: session }),
  endCall: () => set({ isCalling: false, rtcSession: null }),
}));
```

#### **3. API服务**

创建一个中心化的服务来处理与您后端的所有API交互。

```typescript
// src/services/ApiService.ts
import axios from 'axios';
import { useAppStore } from '../store/useAppStore';
import type { PrepareSessionRequest, PrepareSessionResponse, RtcCredentials } from '../../../shared/contracts/schema'; // 引用共享契约

const apiClient = axios.create({
  baseURL: 'YOUR_BACKEND_API_URL', // 替换为您的后端地址
  timeout: 10000,
});

// 请求拦截器，自动附加JWT
apiClient.interceptors.request.use(config => {
  const token = useAppStore.getState().jwt;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

class ApiService {
  async anonymousLogin(deviceInfo: { device_id: string; platform: string; app_version: string; }) {
    const response = await apiClient.post('/api/v1/auth/anonymous-login', { device_info: deviceInfo });
    return response.data;
  }

  async prepareRtcSession(sessionId: string, characterId: string): Promise<RtcCredentials> {
    const request: PrepareSessionRequest = { sessionId, characterId };
    const response = await apiClient.post<PrepareSessionResponse>('/api/v1/rtc/prepare_session', request);
    return response.data;
  }

  async endRtcSession(sessionId: string, taskId: string) {
    const response = await apiClient.post('/api/v1/rtc/end_session', { sessionId, taskId });
    return response.data;
  }

  async sendRtcCommand(sessionId: string, command: string) {
      const response = await apiClient.post(`/api/v1/rtc/sessions/${sessionId}/command`, { command });
      return response.data;
  }

  // ... 其他API调用，如获取角色列表、用户画像等
}

export const apiService = new ApiService();
```

#### **4. RTC服务 (`RTCService.ts`) - 核心！**

这是封装所有`volcengine-react-native` SDK交互的核心服务。

```typescript
// src/services/RTCService.ts
import {
  RTCManager,
  RTCVideoEventHandler,
  RTCRoomEventHandler,
  IEngine,
  IRoom,
  StreamIndex,
  // 导入文档中提到的各种类型
  WarningCode,
  ErrorCode,
  ConnectionState,
  RTCRoomStats,
  LocalAudioStreamState,
  LocalAudioStreamError,
} from '@volcengine/react-native-rtc';
import { useAppStore } from '../store/useAppStore';

class RTCService {
  private engine: IEngine | null = null;
  private room: IRoom | null = null;

  // 初始化RTC引擎
  public async initializeEngine(): Promise<void> {
    if (this.engine) {
      console.log('RTC Engine already initialized.');
      return;
    }
    try {
      this.engine = await RTCManager.createRTCEngine({ appID: 'YOUR_VOLCENGINE_RTC_APP_ID' });
      this.setupEventHandlers();
      console.log('RTC Engine initialized successfully.');
    } catch (e) {
      console.error('Failed to initialize RTC Engine:', e);
      throw e;
    }
  }

  // 设置事件回调
  private setupEventHandlers(): void {
    if (!this.engine) return;

    const engineEventHandler: RTCVideoEventHandler = {
      // 监听关键的引擎事件
      onError: (err: ErrorCode) => {
        console.error('RTC Engine Error:', err);
        // 这里可以根据错误码做全局处理，比如提示用户网络问题
      },
      onWarning: (warn: WarningCode) => {
        console.warn('RTC Engine Warning:', warn);
      },
      onConnectionStateChanged: (state: ConnectionState) => {
        console.log('RTC Connection State Changed:', ConnectionState[state]);
        // 可以根据连接状态更新UI
      },
      onLocalAudioStateChanged: (state: LocalAudioStreamState, error: LocalAudioStreamError) => {
          console.log(`Local audio state changed: ${LocalAudioStreamState[state]}, error: ${LocalAudioStreamError[error]}`);
      }
      // ... 其他你关心的引擎回调
    };

    this.engine.setRtcVideoEventHandler(engineEventHandler);
  }

  // 开始语音通话
  public async startCall(credentials: { token: string; roomId: string; userId: string; taskId: string }): Promise<void> {
    if (!this.engine) {
      await this.initializeEngine();
    }
    if (this.isCalling()) {
      console.warn('Already in a call.');
      return;
    }

    try {
      this.room = this.engine!.createRTCRoom(credentials.roomId);
      this.setupRoomEventHandlers();

      // 加入房间
      const joinResult = this.room.joinRoom({
          token: credentials.token,
          userId: credentials.userId,
          roomConfigs: {
              profile: 0, // Communication profile
              isAutoPublish: true, // 自动发布音频流
              isAutoSubscribeAudio: true, // 自动订阅音频
              isAutoSubscribeVideo: false, // 我们是纯语音，不订阅视频
          },
          extras: {}
      });

      if (joinResult !== 0) {
        throw new Error(`Failed to join room, code: ${joinResult}`);
      }
      
      // onRoomStateChanged 会确认是否成功加入
    } catch (e) {
      console.error('Failed to start call:', e);
      await this.endCall(); // 清理
      throw e;
    }
  }

  // 设置房间事件回调
  private setupRoomEventHandlers(): void {
    if (!this.room) return;

    const roomEventHandler: RTCRoomEventHandler = {
      onRoomStateChanged: (roomId, uid, state, extraInfo) => {
        console.log(`Room state changed: roomId=${roomId}, uid=${uid}, state=${state}, info=${extraInfo}`);
        if (state === 0) { // Join room success
          console.log('Successfully joined the room!');
          // 成功加入房间后，开启音频采集
          this.engine?.startAudioCapture();
          useAppStore.getState().startCall({ ...useAppStore.getState().rtcSession!, token: 'sensitive', roomId, userId: uid, taskId: '' });
        } else {
            console.error(`Failed to join room or error occurred. State: ${state}`);
            useAppStore.getState().endCall();
        }
      },
      onLeaveRoom: (stats: RTCRoomStats) => {
        console.log('Successfully left the room.');
        this.cleanupRoom();
      },
      // ... 其他房间事件，如 onUserJoined, onUserLeave 等
    };

    this.room.setRTCRoomEventHandler(roomEventHandler);
  }

  // 结束通话
  public async endCall(): Promise<void> {
    console.log('Ending call...');
    if (this.room) {
      this.room.leaveRoom();
    }
    if (this.engine) {
      this.engine.stopAudioCapture();
    }
    // UI状态的清理在 onLeaveRoom 回调中或直接在此处触发
    useAppStore.getState().endCall();
  }

  // 发送中断指令
  public async interrupt(): Promise<void> {
      const session = useAppStore.getState().rtcSession;
      if (!session || !session.sessionId) {
          console.error("Cannot interrupt: no active session.");
          return;
      }
      try {
          console.log('Sending interrupt command...');
          await apiService.sendRtcCommand(session.sessionId, 'interrupt');
      } catch (e) {
          console.error('Failed to send interrupt command:', e);
      }
  }

  // 切换静音
  public setMute(muted: boolean): void {
    this.engine?.muteAudioCapture(StreamIndex.STREAM_INDEX_MAIN, muted);
  }

  public isCalling(): boolean {
    return useAppStore.getState().isCalling;
  }

  // 清理房间资源
  private cleanupRoom(): void {
    if (this.room) {
      this.room.destroy();
      this.room = null;
    }
  }

  // 销毁引擎
  public async destroyEngine(): Promise<void> {
    if (this.engine) {
      await this.endCall(); // 确保离开房间
      RTCManager.destroyRTCEngine();
      this.engine = null;
      console.log('RTC Engine destroyed.');
    }
  }
}

export const rtcService = new RTCService();
```

#### **5. UI组件**

这是将所有服务串联起来的React组件。

```tsx
// src/screens/VoiceAssistantScreen.tsx
import React, { useEffect, useState } from 'react';
import { View, Text, Button, StyleSheet, ActivityIndicator } from 'react-native';
import { useAppStore } from '../store/useAppStore';
import { apiService } from '../services/ApiService';
import { rtcService } from '../services/RTCService';
import { v4 as uuidv4 } from 'uuid'; // 用来生成sessionId

const VoiceAssistantScreen: React.FC = () => {
  const { jwt, isLoggedIn, isCalling, rtcSession } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 1. 登录与初始化
  useEffect(() => {
    const initialize = async () => {
      if (!isLoggedIn) {
        try {
          const deviceInfo = { device_id: 'unique-device-id', platform: 'react-native', app_version: '1.0.0' };
          const authData = await apiService.anonymousLogin(deviceInfo);
          useAppStore.getState().setJwt(authData.access_token);
        } catch (e) {
          setError('Login failed.');
        }
      }
      // 初始化RTC引擎
      rtcService.initializeEngine();
    };
    initialize();

    // 组件卸载时销毁引擎
    return () => {
      rtcService.destroyEngine();
    };
  }, [isLoggedIn]);

  // 2. 开始通话处理
  const handleStartCall = async () => {
    if (isCalling || isLoading) return;
    setIsLoading(true);
    setError(null);
    try {
      const sessionId = uuidv4();
      const characterId = useAppStore.getState().activeCharacterId || 'default';
      
      const credentials = await apiService.prepareRtcSession(sessionId, characterId);
      await rtcService.startCall({ ...credentials, sessionId });
      
    } catch (e: any) {
      setError(e.message || 'Failed to start call.');
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  };

  // 3. 结束通话处理
  const handleEndCall = async () => {
    if (!isCalling || !rtcSession) return;
    setIsLoading(true);
    try {
      await apiService.endRtcSession(rtcSession.sessionId!, rtcSession.taskId!);
      await rtcService.endCall();
    } catch (e: any) {
      setError(e.message || 'Failed to end call properly.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 4. 中断处理
  const handleInterrupt = () => {
      if(isCalling) {
          rtcService.interrupt();
      }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>AI语音助手</Text>
      
      {isCalling ? (
        <View>
          <Text style={styles.status}>通话中...</Text>
          <Button title="挂断" onPress={handleEndCall} color="red" />
          <Button title="打断" onPress={handleInterrupt} />
        </View>
      ) : (
        <Button title="开始通话" onPress={handleStartCall} disabled={isLoading || !isLoggedIn} />
      )}

      {isLoading && <ActivityIndicator size="large" color="#0000ff" />}
      {error && <Text style={styles.error}>{error}</Text>}
    </View>
  );
};

// ... styles
```

#### **6. 实现高级功能**

*   **打断对话 (Interruption):**
    *   **前端:** 在用户开始说话时（可以通过音量检测或一个“按住说话”的按钮触发），调用`rtcService.interrupt()`。
    *   **`RTCService.ts`:** `interrupt`方法调用`apiService.sendRtcCommand(sessionId, 'interrupt')`。
    *   **后端 (`rtc_routes.py`):** 接收命令，并调用`volcano_client.update_voice_chat`向火山引擎发送`interrupt`命令。
    *   **火山引擎:** 收到`interrupt`命令后，会立即停止正在进行的TTS播放，并准备接收新的用户语音输入。

*   **更换角色/声音 (Switching Character/Voice):**
    *   这是一个纯粹的服务端配置变更，前端的角色是触发它。
    *   **前端:** UI上提供角色/声音列表（通过调用后端的`/characters`接口获取）。用户选择后，调用后端一个新的API，例如 `PUT /api/v1/rtc/sessions/{session_id}/config`。
    *   **后端:** `rtc_routes.py`中实现该接口，调用`volcano_client.update_voice_chat`，并传入新的`TTSConfig`或`LLMConfig`（包含新的`BotId`）。
    *   **火山引擎:** 接收到`UpdateVoiceChat`后，后续的TTS或LLM调用将使用新的配置。前端SDK无需任何改动。

*   **提醒和预约 (Reminders & Scheduling):**
    *   这是通过**Function Calling**实现的，前端的职责非常简单。
    *   **前端:** 用户正常说话，例如“提醒我明天下午三点吃药”。
    *   **火山引擎:** ASR识别文本后，发送给后端Webhook。
    *   **后端 (`ChatOrchestrationService`):** 调用LLM时，LLM识别出这是一个设置提醒的意图，并返回一个`tool_calls`指令，要求调用`set_reminder`工具，参数为`{"content": "吃药", "time": "明天下午三点"}`。
    *   **后端 (`ToolExecutorService`):** 接收到这个指令，调用`ReminderService`。
    *   **后端 (`ReminderService`):** 解析时间字符串`"明天下午三点"`，并创建数据库记录。
    *   **后端:** 将工具执行结果（例如“好的，已为您设置提醒”）返回给火山引擎。
    *   **火山引擎:** 将这个确认信息通过TTS播放给用户。
    *   **结论:** 前端对此过程**无感知**，它只是一个语音的输入和输出设备。所有复杂逻辑都在您的后端和火山引擎服务之间完成。

---

### **相关文档与代码文件参考**

为了更好地理解这个最佳实践，请重点关注以下您代码库中的文件，它们是本方案的核心后端支撑：

*   **`apps/agent-api/api/routes/rtc_routes.py`**: 前端RTC相关API调用的直接入口。
*   **`apps/agent-api/api/routes/rtc_webhook_routes.py`**: 理解后端如何接收和处理火山引擎事件的关键。
*   **`apps/agent-api/api/services/rtc_session_service.py`**: RTC会话生命周期管理的核心逻辑。
*   **`apps/agent-api/api/services/auth_service.py`**: 尤其是`generate_rtc_token`方法，是客户端能加入RTC房间的钥匙。
*   **`apps/agent-api/api/services/tool_executor_service.py`**: 理解Function Calling如何工作的核心。
*   **`shared/contracts/schema.py` & `schema.ts`**: 前后端数据结构对齐的“真理之源”。
