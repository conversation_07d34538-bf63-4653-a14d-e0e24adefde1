version: 4.20.1
markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture
customTechnicalDocuments: shared/contracts/api-contracts.md
devLoadAlwaysFiles:
  - docs/architecture/agent-api-coding-standards.md
  - docs/architecture/agent-api-tech-stack.md
  - docs/architecture/agent-api-source-tree.md
  - docs/architecture/08-volcengine-rtc-integration-guide.md
devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories/backend
