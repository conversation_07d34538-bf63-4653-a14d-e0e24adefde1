#!/usr/bin/env python3
"""
聊天服务API测试
测试接口:
- POST /api/v1/chat/text_message (SSE)
- GET /api/v1/chat/connections/status
"""

import asyncio
import sys
import uuid
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class ChatTester(BaseAPITester):
    """聊天服务API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("06_chat", base_url)
        self.character_id = None
        self.session_id = None

    async def test_text_chat_sse(self):
        """测试文本聊天SSE接口"""
        self.logger.info("💬 测试文本聊天SSE接口")

        # 确保有角色ID
        if not self.character_id:
            self.character_id = await self.get_character_id()

        # 生成测试会话ID
        test_session_id = f"test-session-{uuid.uuid4()}"

        chat_data = {
            "message": "你好，这是一个测试消息",
            "sessionId": test_session_id,
            "characterId": self.character_id
        }

        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat/text_message",
                json=chat_data,
                headers=self._get_headers()
            ) as response:
                self.logger.info(f"📥 SSE响应状态: {response.status}")

                if response.status == 200:
                    self.logger.info("✅ SSE连接建立成功")
                    self.test_results["passed"] += 1

                    # 读取部分SSE流数据进行验证
                    chunk_count = 0
                    received_data = []

                    try:
                        async for line in response.content:
                            if chunk_count >= 10:  # 只读取前10个chunk进行测试
                                break

                            if line:
                                line_str = line.decode('utf-8').strip()
                                if line_str.startswith('data: '):
                                    data = line_str[6:]
                                    received_data.append(data)
                                    self.logger.debug(f"📥 SSE Chunk {chunk_count}: {data}")
                                    chunk_count += 1

                                    # 检查stream_end事件
                                    if 'stream_end' in data:
                                        self.logger.info("📥 检测到stream_end事件")
                                        break

                                    # 检查text_chunk事件
                                    if 'text_chunk' in data:
                                        self.logger.info("📥 检测到text_chunk事件")

                    except asyncio.TimeoutError:
                        self.logger.warning("⚠️ SSE流读取超时，这可能是正常的")

                    self.logger.info(f"✅ 成功读取 {chunk_count} 个SSE数据块")

                    # 验证是否收到了有效的SSE数据
                    if chunk_count > 0:
                        self.logger.info("✅ SSE数据流验证成功")
                    else:
                        self.logger.warning("⚠️ 未收到SSE数据")

                else:
                    self.logger.error(f"❌ SSE连接失败 - Status: {response.status}")
                    self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ SSE测试异常: {str(e)}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def test_chat_connections_status(self):
        """测试聊天连接状态API"""
        self.logger.info("🔗 测试聊天连接状态API")

        response = await self.make_request("GET", "/api/v1/chat/connections/status")

        # 验证响应结构
        if response and "error" not in response:
            self.logger.info("✅ 成功获取连接状态")

            # 验证可能的状态字段
            status_fields = ["active_connections", "total_connections", "status"]
            for field in status_fields:
                if field in response:
                    self.logger.info(f"✅ 状态响应包含字段: {field}")
                else:
                    self.logger.info(f"ℹ️ 状态响应不包含字段: {field}")

        else:
            self.logger.error("❌ 获取连接状态失败")

    async def test_text_chat_without_auth(self):
        """测试未认证的文本聊天请求"""
        self.logger.info("🔒 测试未认证的文本聊天请求")

        chat_data = {
            "message": "未认证的测试消息",
            "sessionId": f"test-session-{uuid.uuid4()}",
            "characterId": "test-character"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/chat/text_message",
            data=chat_data,
            include_auth=False,
            expected_status=401
        )

        # 验证未认证请求被正确拒绝
        if response:
            self.logger.info("✅ 未认证请求正确返回401")
        else:
            self.logger.error("❌ 未认证请求处理异常")

    async def test_text_chat_invalid_data(self):
        """测试无效数据的文本聊天请求"""
        self.logger.info("❌ 测试无效数据的文本聊天请求")

        # 测试空消息
        invalid_data = {
            "message": "",
            "sessionId": f"test-session-{uuid.uuid4()}",
            "characterId": "test-character"
        }

        response = await self.make_request(
            "POST",
            "/api/v1/chat/text_message",
            data=invalid_data,
            expected_status=422  # FastAPI validation error
        )

        # 验证无效数据请求被正确拒绝
        if response:
            self.logger.info("✅ 无效数据请求正确返回422")
        else:
            self.logger.warning("⚠️ 无效数据请求处理可能有问题")

    async def test_text_chat_invalid_character(self):
        """测试使用无效角色ID的文本聊天请求"""
        self.logger.info("❌ 测试使用无效角色ID的文本聊天请求")

        chat_data = {
            "message": "测试消息",
            "sessionId": f"test-session-{uuid.uuid4()}",
            "characterId": "00000000-0000-4000-8000-000000000000"  # 格式正确但不存在的UUID
        }

        response = await self.make_request(
            "POST",
            "/api/v1/chat/text_message",
            data=chat_data,
            expected_status=200  # SSE接口接受请求，但在流中返回错误
        )

        # 验证无效角色ID在SSE流中返回错误
        if response and "raw_text" in response:
            response_text = response["raw_text"]
            if "error" in response_text or "PROCESSING_ERROR" in response_text:
                self.logger.info("✅ 无效角色ID在SSE流中正确返回错误")
            else:
                self.logger.warning("⚠️ 无效角色ID处理可能有问题")
        else:
            self.logger.warning("⚠️ 无效角色ID请求处理可能有问题")

    async def test_chat_concurrent_connections(self):
        """测试并发聊天连接（简单版本）"""
        self.logger.info("🔄 测试并发聊天连接")

        # 确保有角色ID
        if not self.character_id:
            self.character_id = await self.get_character_id()

        # 创建多个并发请求
        tasks = []
        for i in range(3):  # 创建3个并发连接
            chat_data = {
                "message": f"并发测试消息 {i+1}",
                "sessionId": f"concurrent-session-{i+1}-{uuid.uuid4()}",
                "characterId": self.character_id
            }

            task = self._test_single_concurrent_chat(chat_data, i+1)
            tasks.append(task)

        # 执行并发测试
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            success_count = sum(1 for result in results if result is True)
            self.logger.info(f"✅ 并发测试完成，成功: {success_count}/3")

            if success_count >= 2:  # 至少2个成功认为测试通过
                self.logger.info("✅ 并发连接测试通过")
                self.test_results["passed"] += 1
            else:
                self.logger.error("❌ 并发连接测试失败")
                self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ 并发测试异常: {e}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def _test_single_concurrent_chat(self, chat_data, connection_id):
        """单个并发聊天连接的测试"""
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat/text_message",
                json=chat_data,
                headers=self._get_headers()
            ) as response:
                if response.status == 200:
                    # 读取少量数据即可
                    chunk_count = 0
                    async for line in response.content:
                        chunk_count += 1
                        if chunk_count >= 3:  # 只读取3个chunk
                            break

                    self.logger.info(f"✅ 并发连接 {connection_id} 成功")
                    return True
                else:
                    self.logger.error(f"❌ 并发连接 {connection_id} 失败: {response.status}")
                    return False

        except Exception as e:
            self.logger.error(f"❌ 并发连接 {connection_id} 异常: {e}")
            return False

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始聊天服务API测试")

        # 确保用户已认证
        await self.ensure_authenticated()

        tests = [
            ("聊天连接状态", self.test_chat_connections_status),
            ("文本聊天SSE", self.test_text_chat_sse),
            ("未认证聊天请求", self.test_text_chat_without_auth),
            ("无效数据聊天请求", self.test_text_chat_invalid_data),
            ("无效角色聊天请求", self.test_text_chat_invalid_character),
            ("并发聊天连接", self.test_chat_concurrent_connections),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(1.0)  # 聊天测试需要更长间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='聊天服务API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with ChatTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
