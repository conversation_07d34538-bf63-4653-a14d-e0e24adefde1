Title: 返回结构--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1178322

Markdown Content:
返回结构--实时音视频-火山引擎

===============


返回结构

最近更新时间：2024.07.16 17:04:59 首次发布时间：2023.12.08 15:01:59

[我的收藏](https://www.volcengine.com/docs/favorite)

有用

有用

无用

无用

![Image 1](blob:http://localhost/7511cd80c66a21d3576513d5323b9f98)文档反馈

![Image 2](blob:http://localhost/8791860fec2e7731d895208f5ceb1ced)问问助手

2020-12-01
----------

*   房间管理、云端媒体处理、公共流、应用管理、业务标识管理、实时消息通信模块下：

 当 HTTP 响应状态码 `!=200` 时，表示调用失败。你可以根据返回结构中 `BaseResponse.ResponseMetaData` 的 `Code` 和 `Message` 字段解析错误原因。

 当 HTTP 响应状态码 `==200` 时，你仍需解析返回结构中的 `BaseResponse.ResponseMetaData.Error` 结构体。当 Error 为空时，表示调用成功。

### 返回结构

`BaseResponse` 返回结构如下：

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| ResponseMetadata | [ResponseMetadata](https://www.volcengine.com/docs/6348/1178322#responsemetadata1) |  |
| Result | interface{} | 仅在请求成功时返回, 具体值参考每个 API 的说明。 |

ResponseMetadata

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| RequestId | String | `Your_RequestId` | 请求标识 |
| Action | String | `StartRecord` | 接口名称 |
| Version | String | `2020-12-01` | 接口版本 |
| Service | String | `rtc` | 接口所属服务 |
| Region | String | `cn-north-1` | 地域参数：* `cn-north-1` (华北) * `ap-singapore-1` (新加坡) * `us-east-1` (美东) |
| Error | [ErrorInfo](https://www.volcengine.com/docs/6348/1178322#errorinfo1) | - | 仅在请求失败时返回。 |

ErrorInfo 错误的结构定义。

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| CodeN | Uint32 | 网关的错误码。（请求失败时返回） |
| Code | String | API 的错误码，参看[错误码](https://www.volcengine.com/docs/6348/70426)。 |
| Message | String | 具体的错误信息 |

### 返回结构示例

#### 调用成功：

```json
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
```

json

#### 调用失败：

```json
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
```

json

*   获取数据指标和歌曲查询模块下：

当 HTTP 响应状态码 `!=200` 时，表示调用失败。你可以根据返回结构中 `BaseResponse.ResponseMetaData` 的 `Code` 和 `Message` 和 `BaseResponse.Result` 字段解析错误原因。

 当 HTTP 响应状态码 ==200 时，表示调用成功。

### 返回结构

`BaseResponse` 返回结构如下：

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| ResponseMetadata | [ResponseMetadata](https://www.volcengine.com/docs/6348/1178322#responsemetadata1) |  |
| Result | interface{} | 具体值参考每个 API 的说明。 |

ResponseMetadata

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| RequestId | String | `Your_RequestId` | 请求标识 |
| Action | String | `StartRecord` | 接口名称 |
| Version | String | `2020-12-01` | 接口版本 |
| Service | String | `rtc` | 接口所属服务 |
| Region | String | `cn-north-1` | 地域参数：* `cn-north-1` (华北) * `ap-singapore-1` (新加坡) * `us-east-1` (美东) |
| Error | [ErrorInfo](https://www.volcengine.com/docs/6348/1178322#errorinfo1) | - | 仅在请求失败时返回。 |

ErrorInfo 错误的结构定义。

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| CodeN | Uint32 | 网关的错误码 |
| Code | String | API 的错误码，参看[错误码](https://www.volcengine.com/docs/6348/70426)。 |
| Message | String | 具体的错误信息 |

### 返回结构示例

#### 调用成功：

```json
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
```

json

#### 调用失败：

```json
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
```

json

2022-06-01
----------

当 HTTP 响应状态码 `!=200` 时，表示调用失败。你可以根据返回结构中 `BaseResponse.ResponseMetaData` 的 `Code` 和 `Message` 字段解析错误原因。

 当 HTTP 响应状态码 `==200` 时，你仍需解析返回结构中的 `BaseResponse.ResponseMetaData.Error` 结构体。当 Error 为空时，表示调用成功。

### 返回结构

`BaseResponse` 返回结构如下：

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| ResponseMetadata | [ResponseMetadata](https://www.volcengine.com/docs/6348/1178322#responsemetadata1) |  |
| Result | interface{} | 仅在请求成功时返回, 具体值参考每个 API 的说明。 |

ResponseMetadata

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| RequestId | String | `Your_RequestId` | 请求标识 |
| Action | String | `StartRecord` | 接口名称 |
| Version | String | `2022-06-01` | 接口版本 |
| Service | String | `rtc` | 接口所属服务 |
| Region | String | `cn-north-1` | 地域参数：* `cn-north-1` (华北) * `ap-singapore-1` (新加坡) * `us-east-1` (美东) |
| Error | [ErrorInfo](https://www.volcengine.com/docs/6348/1178322#errorinfo1) | - | 仅在请求失败时返回。 |

ErrorInfo 错误的结构定义。

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| CodeN | Uint32 | 网关的错误码 |
| Code | String | API 的错误码，参看[错误码](https://www.volcengine.com/docs/6348/70426)。 |
| Message | String | 具体的错误信息 |

### 返回结构示例

#### 调用成功：

```json
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2022-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
```

json

#### 调用失败：

```json
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2022-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "CodeN": 10009,
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
```

json

2023-06-01
----------

当 HTTP 响应状态码 `!=200` 时，表示调用失败。你可以根据返回结构中 `BaseResponse.ResponseMetaData` 的 `Code` 和 `Message` 字段解析错误原因。

 当 HTTP 响应状态码 `==200` 时，你仍需解析返回结构中的 `BaseResponse.ResponseMetaData.Error` 结构体。当 Error 为空时，表示调用成功。

### 返回结构

`BaseResponse` 返回结构如下：

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| ResponseMetadata | [ResponseMetadata](https://www.volcengine.com/docs/6348/1178322#responsemetadata1) |  |
| Result | interface{} | 仅在请求成功时返回, 具体值参考每个 API 的说明。 |

ResponseMetadata

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| RequestId | String | `Your_RequestId` | 请求标识 |
| Action | String | `StartRecord` | 接口名称 |
| Version | String | `2023-06-01` | 接口版本 |
| Service | String | `rtc` | 接口所属服务 |
| Region | String | `cn-north-1` | 地域参数：* `cn-north-1` (华北) * `ap-singapore-1` (新加坡) * `us-east-1` (美东) |
| Error | [ErrorInfo](https://www.volcengine.com/docs/6348/1178322#errorinfo1) | - | 仅在请求失败时返回。 |

ErrorInfo 错误的结构定义。

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| CodeN | Uint32 | 网关的错误码 |
| Code | String | API 的错误码，参看[错误码](https://www.volcengine.com/docs/6348/70426)。 |
| Message | String | 具体的错误信息 |

### 返回结构示例

#### 调用成功：

```json
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2023-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
```

json

#### 调用失败：

```json
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2023-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "CodeN": 10009,
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
```

json

2023-07-20
----------

当 HTTP 响应状态码 `!=200` 时，表示调用失败。你可以根据返回结构中 `BaseResponse.ResponseMetaData` 的 `Code` 和 `Message` 字段解析错误原因。

 当 HTTP 响应状态码 ==200 时，表示调用成功。

### 返回结构

`BaseResponse` 返回结构如下：

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| ResponseMetadata | [ResponseMetadata](https://www.volcengine.com/docs/6348/1178322#responsemetadata1) |  |
| Result | interface{} | 具体值参考每个 API 的说明。 |

ResponseMetadata

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| RequestId | String | `Your_RequestId` | 请求标识 |
| Action | String | `StartRecord` | 接口名称 |
| Version | String | `2023-07-20` | 接口版本 |
| Service | String | `rtc` | 接口所属服务 |
| Region | String | `cn-north-1` | 地域参数：* `cn-north-1` (华北) * `ap-singapore-1` (新加坡) * `us-east-1` (美东) |
| Error | [ErrorInfo](https://www.volcengine.com/docs/6348/1178322#errorinfo1) | - | 仅在请求失败时返回。 |

ErrorInfo 错误的结构定义。

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| Code | String | API 的错误码，参看[错误码](https://www.volcengine.com/docs/6348/70426)。 |
| Message | String | 具体的错误信息 |

### 返回结构示例

#### 调用成功：

```json
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2023-07-20",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
```

json

#### 调用失败：

```json
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2023-07-20",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
```

json

2023-11-01 & 2024-06-01
-----------------------

当 HTTP 响应状态码 `!=200` 时，表示调用失败。你可以根据返回结构中 `BaseResponse.ResponseMetaData` 的 `Code` 和 `Message` 字段解析错误原因。

 当 HTTP 响应状态码 ==200 时，表示调用成功。

### 返回结构

`BaseResponse` 返回结构如下：

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| ResponseMetadata | [ResponseMetadata](https://www.volcengine.com/docs/6348/1178322#responsemetadata1) |  |
| Result | interface{} | 具体值参考每个 API 的说明。 |

ResponseMetadata

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| RequestId | String | `Your_RequestId` | 请求标识 |
| Action | String | `StartRecord` | 接口名称 |
| Version | String | `2023-11-01` | 接口版本 |
| Service | String | `rtc` | 接口所属服务 |
| Region | String | `cn-north-1` | 地域参数：* `cn-north-1` (华北) * `ap-singapore-1` (新加坡) * `us-east-1` (美东) |
| Error | [ErrorInfo](https://www.volcengine.com/docs/6348/1178322#errorinfo1) | - | 仅在请求失败时返回。 |

ErrorInfo 错误的结构定义。

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| Code | String | API 的错误码，参看[错误码](https://www.volcengine.com/docs/6348/70426)。 |
| Message | String | 具体的错误信息 |

### 返回结构示例

#### 调用成功：

```json
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2023-11-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
```

json

#### 调用失败：

```json
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2023-11-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
```

json

