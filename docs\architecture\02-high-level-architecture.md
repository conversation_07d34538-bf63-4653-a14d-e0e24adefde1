# 心桥项目高层架构

## 2.1 核心组件

*   **客户端 (React Native/Expo):** 负责UI呈现、用户交互、状态管理，并集成火山引擎RTC前端SDK。
*   **心桥后端服务 (FastAPI):** 项目的核心业务后端和智能中枢。它负责：
    1.  **会话管理：** 启动和结束火山RTC会话。
    2.  **LLM原生编排：** 通过自定义的`LLMProxyService`直连并编排火山引擎LLM，管理上下文和提示。
    3.  **原生工具调用：** 通过`ToolExecutorService`响应LLM的工具调用请求，并执行相应逻辑。
    4.  **记忆管理：** 通过`IMemoryService`抽象层，对接并管理专业的记忆服务（Zep AI或Mem0 AI）。
    5.  **标准API服务：** 提供用户画像、会话历史等常规RESTful API。
*   **火山RTC云服务 (Volcano Engine):** 作为项目的**"通信与感知主干"**，负责所有媒体处理，包括：
    1.  **ASR (语音转文本)**
    2.  **TTS (文本转语音)**
    3.  **实时媒体传输 (RTC)**
    4.  **通过Webhook将ASR结果等事件通知到我方后端服务**
*   **专业记忆服务 (Zep AI / Mem0 AI):** 作为项目的**"可插拔记忆中枢"**，提供先进的、可配置的长期与短期对话记忆存储和检索能力。
*   **Supabase (BaaS):** 作为我们的**"持久化与认证基座"**，提供PostgreSQL数据库、用户认证(Auth)、对象存储(Storage)等服务。

## 2.2 核心交互流程: 原生LLM编排模式

### 交互路径1: RTC实时语音对话
1.  **[前端]** 用户发起语音聊天，调用我方后端的会话准备接口。
2.  **[后端]** `心桥后端服务`调用火山`StartVoiceChat` API以启动云端的AI智能体。**同时，后端服务独立生成一个符合火山安全规范的、用于客户端鉴权的RTC Token**。
3.  **[前端]** 客户端使用从我方后端获取的**自签名Token**，通过火山RTC SDK连接到火山媒体服务器。
4.  **[实时对话循环]**
    -   **[火山云 -> 我方后端]** 用户的语音经火山ASR服务转换成文本后，通过Webhook实时**POST**到我方后端指定的事件接收端点（例如 `/api/v1/chat/rtc_event_handler`）。
    -   **[我方后端 - 智能核心]** 后端服务在收到文本后，执行完整的原生LLM编排逻辑：
        1.  调用`IMemoryService`检索相关记忆。
        2.  构建包含记忆、对话历史和**工具定义**的提示（Prompt）。
        3.  通过`LLMProxyService`将请求发送给火山引擎LLM。
        4.  **(工具调用循环):** 如果LLM决定调用工具，它将返回一个工具调用请求。我方`ToolExecutorService`会执行该工具，并将结果返回给LLM以继续生成。此过程可能重复多次。
        5.  LLM生成最终的文本回复。
        6.  异步调用`IMemoryService`存储新一轮对话。
    -   **[我方后端 -> 火山云]** 后端服务将最终的AI回复文本通过Webhook的响应体返回给火山云。
    -   **[火山云 -> 前端]** 火山云TTS服务将文本转为语音，并通过RTC通道实时传回给前端播放。

### 交互路径2: 纯文本聊天
-   **[客户端 -> 我方后端]** 用户通过`POST /api/v1/chat/text_message` 发送请求。
-   **[我方后端]** 后端执行与RTC路径完全相同的原生LLM编排逻辑（记忆检索 -> LLM调用 -> 工具调用循环 -> 存储记忆）。
-   **[我方后端 -> 客户端]** 后端通过 **SSE (Server-Sent Events)** 将AI的回复**流式**地直接返回给客户端，实现打字机效果。


## 2.3 架构图

```mermaid
sequenceDiagram
    participant Client as 客户端 (React Native)
    participant BackendService as 心桥后端服务 (FastAPI)
    participant VolcanoRTC as 火山引擎RTC云
    participant LLMService as 火山引擎LLM
    participant MemoryService as 可插拔记忆服务

    alt RTC实时语音对话
        Client->>BackendService: 1. /prepare_session
        activate BackendService
        BackendService->>VolcanoRTC: 2. StartVoiceChat (在云端启动AI)
        VolcanoRTC-->>BackendService: 3. 确认AI智能体启动成功
        BackendService->>BackendService: 4. [内部] 生成客户端专用的RTC Token
        BackendService-->>Client: 5. 返回自生成的Token和房间信息
        deactivate BackendService

        Client-->>VolcanoRTC: 6. [RTC SDK] 使用Token加入房间
        
        loop 实时对话
            Client->>VolcanoRTC: 用户语音
            VolcanoRTC->>VolcanoRTC: ASR处理
            VolcanoRTC->>BackendService: 7. Webhook: onMessage(ASR文本)
            activate BackendService
            
            BackendService->>MemoryService: 8. 检索相关记忆
            MemoryService-->>BackendService: 返回记忆
            
            BackendService->>LLMService: 9. 调用LLM(含记忆、工具定义)
            
            alt LLM请求工具调用
                LLMService-->>BackendService: 10. 返回工具调用请求
                BackendService->>BackendService: 11. (内部)执行工具
                BackendService->>LLMService: 12. 提交工具结果，继续生成
            end
            
            LLMService-->>BackendService: 13. 返回最终AI回复
            
            BackendService-->>VolcanoRTC: 14. Webhook Response: (TTS文本)
            
            note right of BackendService: 异步处理
            BackendService->>MemoryService: 15. [async] 存储新一轮对话
            
            deactivate BackendService
            
            VolcanoRTC->>VolcanoRTC: TTS合成
            VolcanoRTC-->>Client: AI语音流
        end
    end

    alt 纯文本对话 (SSE)
        Client->>BackendService: POST /chat/text_message
        activate BackendService
        BackendService->>MemoryService: get_memory_context(...)
        MemoryService-->>BackendService: 返回记忆
        BackendService->>LLMService: 调用LLM(含工具)
        LLMService-->>BackendService: LLM回复流
        
        note right of BackendService: 异步处理
        BackendService->>MemoryService: [async] add_conversation_turn(...)
        
        BackendService-->>Client: [SSE] 流式返回AI回复
        deactivate BackendService
    end
``` 