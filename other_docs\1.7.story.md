# 故事 1.7: 基础危机响应协议

## 基本信息
- **故事编号**: 1.7
- **故事标题**: 基础危机响应协议
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: 开发者
- **优先级**: 高（P1）
- **工作量估计**: 5-8 个工作日
- **依赖关系**: 故事 1.3
- **Status**: Approved

## 故事描述

我需要为系统构建一个安全底线，在用户表达极端负面情绪时能正确应对，**以便** 保护用户安全和产品信任。

## 验收标准

### AC1: 安全守卫模块实现
- [ ] agent-api服务中存在一个"安全守卫"模块，所有用户输入都必须先经过该模块处理
- [ ] 关键词库覆盖常见的危机表达（自杀、自伤、暴力等）
- [ ] 模块的响应速度和准确性符合要求（检测时间< 100ms）
- [ ] 支持关键词和语义级别的双重检测机制

### AC2: 危机检测机制
- [ ] 当输入命中危机关键词库时，该模块能成功阻止后续对通用LLM的调用
- [ ] 支持语义级别的情绪分析和风险评估
- [ ] 降低误报率，避免过度敏感（误报率< 2%）
- [ ] 实现分级风险评估（低、中、高风险）

### AC3: 安全响应流程
- [ ] 此时，模块能从专用的安全知识库中返回一个预设的、安全的安抚与引导脚本给用户
- [ ] 响应内容温和、专业且有帮助
- [ ] 必要时能够提供专业资源的联系方式（心理热线、急救电话等）
- [ ] 支持多轮安全对话，直到用户情绪稳定

### AC4: 安全数据记录与监控
- [ ] 所有危机事件都能被完整记录和追踪
- [ ] 实现实时监控和报警机制
- [ ] 支持危机事件的后续跟进和关怀
- [ ] 确保敏感数据的安全存储和访问控制

## Dev Notes

### 重要开发环境说明 (CRITICAL: Development Environment)
- **环境变量**: 项目所需的所有环境变量均已在 `apps/agent-api/.env` 文件中配置完毕。如果无法读取，可参考内容完全相同的 `apps/agent-api/1.env` 文件。
- **Python依赖**: `requirements.txt` 中列出的所有依赖库均已安装。
- **虚拟环境**: **必须**在执行任何脚本或启动服务前，激活指定的Conda虚拟环境: `conda activate xinqiao-py312`，当前环境为Windows系统。
- **服务端口**: FastAPI服务应在 **8003** 端口启动，例如: `uvicorn api.main:app --reload --host 0.0.0.0 --port 8003`。

CRITICAL: This is a **backend story**. Load the following standards for implementation:
- `@docs/architecture/agent-api-tech-stack.md`
- `@docs/architecture/agent-api-source-tree.md`
- `@docs/architecture/agent-api-coding-standards.md`

### 参考实现
**重要业务逻辑参考**: 除了参考scripts目录下的PoC代码外，开发者在实现本故事的功能时，必须主动参考apps/agent-api/api/目录下已有的相关服务和路由代码。虽然记忆框架已改变，但其中包含的业务流程、错误处理、数据模型验证等模式是宝贵的资产，应当被借鉴和迁移，以确保新旧功能在代码风格和质量上的一致性。

**Technical Guidance from Architecture:**

### Relevant API Spec:
从 `api-design.md` 中的相关接口：
- **Core Integration**: 安全守卫模块必须集成到`/api/v1/chat/rtc_event_handler`和`/api/v1/chat/text_message`这两个核心入口。
- **Processing Flow**: 用户输入 → 安全检测 → 正常处理 or 危机响应
- **Response Format**: 危机响应需要返回特殊的响应格式，明确标识为安全模式
- **Logging**: 所有危机事件必须详细记录，包括触发关键词、用户信息、响应内容

### Data Models Involved:
- **注意**: `crisis_events` 等相关安全数据表均已创建完毕。
- `crisis_keywords`: 危机关键词库表
- `crisis_events`: 危机事件记录表
- `safety_responses`: 安全响应模板表
- `user_safety_status`: 用户安全状态追踪表
- `emergency_contacts`: 紧急联系方式配置表

### 与专业记忆服务交互 (MemoryService Interaction):
- 危机事件应该通过`MemoryService`记录到用户记忆中，但需要添加特殊的安全标记（例如，在元数据中添加`"is_crisis": true`）。
- 安全守卫模块需要通过`MemoryService`访问用户历史记忆，以识别重复的危机模式。
- 危机后的关怀对话也应该被记忆系统正常记录。
- 需要实现记忆的安全访问控制，确保敏感信息保护，这可能需要在`MemoryService`的实现中增加额外的权限检查。

### Key Logic Pattern:
从 `backend-design.md` 中的安全处理模式：
```python
from typing import Optional, List
from pydantic import BaseModel
from enum import Enum

class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SafetyCheckResult(BaseModel):
    is_crisis: bool
    risk_level: RiskLevel
    triggered_keywords: List[str]
    confidence_score: float
    response_template: Optional[str] = None
    emergency_contacts: Optional[List[str]] = None

async def safety_guard_check(user_input: str, user_id: str) -> SafetyCheckResult:
    # 1. 关键词检测
    keyword_risk = await check_crisis_keywords(user_input)
    
    # 2. 语义分析（可选：使用NLP库）
    semantic_risk = await analyze_emotional_state(user_input)
    
    # 3. 历史模式分析
    pattern_risk = await check_user_crisis_history(user_id)
    
    # 4. 综合风险评估
    overall_risk = calculate_risk_level(keyword_risk, semantic_risk, pattern_risk)
    
    if overall_risk.value in ["high", "critical"]:
        return SafetyCheckResult(
            is_crisis=True,
            risk_level=overall_risk,
            triggered_keywords=keyword_risk.keywords,
            confidence_score=max(keyword_risk.score, semantic_risk.score),
            response_template=await get_safety_response_template(overall_risk),
            emergency_contacts=await get_emergency_contacts_for_user(user_id)
        )
    
    return SafetyCheckResult(
        is_crisis=False, 
        risk_level=RiskLevel.LOW,
        triggered_keywords=[],
        confidence_score=0.0
    )
```

**第三方库集成指导**:
根据最新技术文档，安全检测模块需要以下NLP库的具体配置：

### jieba中文分词配置
```python
import jieba
import jieba.posseg as pseg

# 危机关键词词典加载
jieba.load_userdict("crisis_keywords.txt")

def chinese_word_segment(text: str) -> List[str]:
    """中文文本分词和词性标注"""
    return list(jieba.cut(text, cut_all=False))
```

### spaCy多语言NLP处理
```python
from spacy.lang.zh import Chinese
from spacy.lang.en import English
import spacy

# 中文NLP管道配置
def setup_chinese_nlp():
    # 使用jieba分词的中文管道
    cfg = {"segmenter": "jieba"}
    nlp_zh = Chinese.from_config({"nlp": {"tokenizer": cfg}})
    nlp_zh.add_pipe("sentencizer")  # 添加句子边界检测
    return nlp_zh

# 英文NLP管道配置
def setup_english_nlp():
    nlp_en = spacy.load("en_core_web_sm")
    return nlp_en

# 情感分析使用示例
def analyze_emotion_with_spacy(text: str, language: str = "zh"):
    if language == "zh":
        nlp = setup_chinese_nlp()
    else:
        nlp = setup_english_nlp()
    
    doc = nlp(text)
    # 基于依存句法分析的情感倾向检测
    negative_patterns = []
    for token in doc:
        if token.pos_ == "VERB" and any(child.dep_ == "neg" for child in token.children):
            negative_patterns.append(token.text)
    
    return {"negative_indicators": negative_patterns, "sentence_count": len(list(doc.sents))}
```

### transformers预训练模型配置
```python
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification

# 多语言情感分析模型
def setup_multilingual_sentiment():
    return pipeline(
        "sentiment-analysis",
        model="nlptown/bert-base-multilingual-uncased-sentiment",
        tokenizer="nlptown/bert-base-multilingual-uncased-sentiment",
        device=0 if torch.cuda.is_available() else -1
    )

# 中文专用情感分析模型
def setup_chinese_sentiment():
    model_name = "uer/roberta-base-finetuned-chinanews-chinese"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    
    return pipeline(
        "sentiment-analysis",
        model=model,
        tokenizer=tokenizer,
        device=0 if torch.cuda.is_available() else -1
    )

# 危机检测的综合情感分析
async def comprehensive_sentiment_analysis(text: str) -> Dict[str, Any]:
    """
    结合多个模型的情感分析结果
    """
    # 使用多语言模型
    multilingual_result = setup_multilingual_sentiment()(text)
    
    # 如果包含中文，使用中文专用模型
    if any('\u4e00' <= char <= '\u9fff' for char in text):
        chinese_result = setup_chinese_sentiment()(text)
        
        # 综合两个模型的结果
        combined_score = (multilingual_result[0]['score'] + chinese_result[0]['score']) / 2
        return {
            "combined_sentiment": multilingual_result[0]['label'],
            "confidence": combined_score,
            "models_used": ["multilingual", "chinese_specific"]
        }
    
    return {
        "combined_sentiment": multilingual_result[0]['label'],
        "confidence": multilingual_result[0]['score'],
        "models_used": ["multilingual"]
    }
```

### 依赖版本要求
```requirements.txt
jieba>=0.42.1
spacy>=3.7.0
transformers>=4.30.0
torch>=2.0.0
zh-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/zh_core_web_sm-3.7.0/zh_core_web_sm-3.7.0.tar.gz
```

## Tasks / Subtasks

### 第一阶段：安全守卫模块基础 (2-3天)
- [ ] **创建安全检测服务**
  - 在`api/services/SecurityGuardService.py`中实现核心安全检测逻辑
  - 定义SafetyCheckResult和CrisisEvent等数据模型
  - 实现关键词库的加载和匹配算法

- [ ] **数据库表验证**
  - **验证** `crisis_keywords`, `crisis_events`, `safety_responses` 等表已存在。
  - 实现用户安全状态追踪表

- [ ] **关键词库建设**
  
  **危机关键词分类体系**:
  ```python
  CRISIS_KEYWORDS = {
      "CRITICAL": {  # 严重危机 - 立即响应
          "chinese": ["自杀", "想死", "不想活", "结束生命", "跳楼", "上吊", "割腕"],
          "english": ["suicide", "kill myself", "end my life", "want to die", "hang myself"],
          "weight": 1.0
      },
      "HIGH": {  # 高风险 - 优先处理
          "chinese": ["活着没意思", "世界太残酷", "没有希望", "想要离开"],
          "english": ["life is meaningless", "no hope", "want to leave", "can't go on"],
          "weight": 0.8
      },
      "MEDIUM": {  # 中等风险 - 关注跟进
          "chinese": ["很绝望", "很痛苦", "撑不下去", "太累了"],
          "english": ["desperate", "can't handle", "give up", "hopeless"],
          "weight": 0.6
      },
      "VIOLENCE": {  # 暴力倾向 - 特殊处理
          "chinese": ["伤害别人", "报复社会", "想杀人", "都该死"],
          "english": ["hurt others", "kill someone", "revenge", "harm people"],
          "weight": 1.0
      }
  }
  
  # 网络用语和隐晦表达
  INTERNET_SLANG = {
      "chinese": ["想s", "emo了", "社死", "抑郁了", "废了"],
      "english": ["kms", "end it all", "done with life", "over it"],
      "weight": 0.4
  }
  
  # 误报防范白名单（正常表达但包含危机词汇）
  FALSE_POSITIVE_WHITELIST = {
      "game_context": ["游戏", "电影", "小说", "角色", "剧情"],
      "academic_context": ["研究", "论文", "课程", "学习"],
      "metaphor_context": ["比喻", "形容", "就像", "好比"]
  }
  ```
  
  **关键词匹配算法**:
  ```python
  def build_keyword_matcher():
      """构建高效的关键词匹配器"""
      from ahocorasick import Automaton
      
      automaton = Automaton()
      
      for category, data in CRISIS_KEYWORDS.items():
          for lang in ["chinese", "english"]:
              for keyword in data[lang]:
                  automaton.add_word(keyword, (category, keyword, data["weight"]))
      
      automaton.make_automaton()
      return automaton
  
  def detect_keywords(text: str, automaton) -> List[Dict]:
      """检测文本中的危机关键词"""
      matches = []
      for end_index, (category, keyword, weight) in automaton.iter(text.lower()):
          start_index = end_index - len(keyword) + 1
          matches.append({
              "keyword": keyword,
              "category": category,
              "weight": weight,
              "position": (start_index, end_index),
              "context": text[max(0, start_index-10):end_index+10]
          })
      return matches
  ```
  
  **动态更新机制**:
  - [ ] 实现关键词库的版本控制和热更新
  - [ ] 建立关键词效果评估和反馈机制
  - [ ] 支持管理员通过界面添加/删除关键词
  - [ ] 实现A/B测试验证新关键词的有效性

### 第二阶段：语义分析和风险评估 (2天)
- [ ] **情绪分析集成**
  - 集成NLP模型进行情绪和情感分析
  - 实现语义级别的危机检测
  - 配置情绪分析的阈值和评分机制

- [ ] **风险评估算法**
  - 实现多维度风险评估模型
  - 结合关键词、语义和历史数据进行综合评估
  - 配置可调节的风险阈值和分级机制

- [ ] **历史模式分析**
  - 分析用户的历史危机记录
  - 识别重复的危机模式和触发因素
  - 实现基于历史的风险预测

### 第三阶段：安全响应机制 (2天)
- [ ] **专业安全响应模板**
  
  **基于心理危机干预原理的响应模板**:
  ```python
  SAFETY_RESPONSE_TEMPLATES = {
      "CRITICAL": {
          "immediate_response": "我注意到你现在可能在经历非常困难的时期。你的感受是真实和重要的，我想帮助你。现在有专业人士可以立即为你提供支持。",
          "professional_referral": "请立即联系以下专业资源：\n• 全国心理危机干预热线：400-161-9995\n• 当地急救电话：120\n• 心理咨询师24小时热线",
          "safety_plan": "在等待专业帮助的同时，请考虑：\n1. 找一个安全的地方\n2. 联系信任的朋友或家人\n3. 移除可能的危险物品\n4. 专注于此刻的呼吸",
          "follow_up": "我会继续关注你的情况。你不是一个人在面对这些困难。"
      },
      "HIGH": {
          "empathetic_response": "感谢你分享这些困难的感受。经历痛苦需要很大的勇气，你已经迈出了重要的第一步。",
          "validation": "你现在的感受是可以理解的，很多人在人生的某个阶段都会经历类似的困难。",
          "hope_instillation": "虽然现在感觉很困难，但这种状态是暂时的。有很多方法可以帮助你度过这个艰难时期。",
          "resource_guidance": "我建议你考虑以下支持资源：\n• 心理咨询服务\n• 支持小组\n• 可信任的朋友或家人"
      },
      "MEDIUM": {
          "active_listening": "我听到了你表达的困难和痛苦。能告诉我更多关于你现在的感受吗？",
          "coping_strategies": "在面对这些挑战时，有一些策略可能会有帮助：\n• 深呼吸和正念练习\n• 规律的作息和运动\n• 与他人保持联系",
          "gentle_referral": "如果你感觉需要额外的支持，心理健康专业人士可以提供个性化的帮助。"
      }
  }
  
  # 多轮对话状态管理
  CONVERSATION_STATES = {
      "ASSESSMENT": "评估用户当前状态",
      "INTERVENTION": "提供immediate支持和指导", 
      "STABILIZATION": "帮助用户情绪稳定",
      "RESOURCE_CONNECTION": "连接专业资源",
      "FOLLOW_UP": "后续关怀和跟进"
  }
  ```

- [ ] **智能多轮安全对话**
  
  **对话流程管理**:
  ```python
  class SafetyConversationManager:
      def __init__(self):
          self.conversation_state = "ASSESSMENT"
          self.risk_level = None
          self.intervention_count = 0
          self.user_responses = []
      
      async def handle_crisis_conversation(self, user_input: str, session_id: str):
          """处理危机对话的完整流程"""
          
          # 1. 评估当前输入的风险级别
          current_risk = await self.assess_current_risk(user_input)
          
          # 2. 根据对话状态和风险级别选择响应策略
          response_strategy = self.determine_response_strategy(current_risk)
          
          # 3. 生成个性化响应
          response = await self.generate_personalized_response(
              user_input, response_strategy, session_id
          )
          
          # 4. 更新对话状态
          self.update_conversation_state(current_risk, user_input)
          
          # 5. 判断是否需要人工介入
          if self.should_escalate_to_human():
              await self.trigger_human_intervention(session_id)
          
          return response
      
      def should_escalate_to_human(self) -> bool:
          """判断是否需要人工介入"""
          return (
              self.risk_level == "CRITICAL" or 
              self.intervention_count > 3 or
              "求救" in self.user_responses[-1] if self.user_responses else False
          )
  ```

- [ ] **地区化紧急联系系统**
  
  **多地区紧急资源配置**:
  ```python
  EMERGENCY_CONTACTS = {
      "CHINA": {
          "national_hotline": "400-161-9995",
          "emergency": "120",
          "police": "110",
          "regions": {
              "beijing": {
                  "crisis_hotline": "010-12320",
                  "hospitals": ["北京安定医院", "北京大学第六医院"],
                  "counseling_centers": ["北京心理危机研究与干预中心"]
              },
              "shanghai": {
                  "crisis_hotline": "021-12320", 
                  "hospitals": ["上海精神卫生中心"],
                  "counseling_centers": ["上海心理咨询师协会"]
              }
          }
      },
      "INTERNATIONAL": {
          "suicide_prevention": "988",  # US
          "samaritans": "116 123",  # UK
          "lifeline": "13 11 14"  # Australia
      }
  }
  
  async def get_localized_resources(user_location: str) -> Dict[str, Any]:
      """根据用户位置提供本地化的紧急资源"""
      # 实现基于地理位置的资源推荐逻辑
      pass
  ```

### 第四阶段：集成和监控 (1-2天)
- [ ] **API集成**
  - 将安全守卫模块集成到所有用户输入接口
  - 实现安全检测的中间件模式
  - 确保安全检测不影响正常对话性能

- [ ] **监控和报警**
  - 实现危机事件的实时监控dashboard
  - 配置自动报警和通知机制
  - 支持危机事件的统计和分析

- [ ] **数据安全和隐私**
  - 实现敏感数据的加密存储
  - 配置危机数据的访问控制
  - 确保符合隐私保护法规要求

### 第五阶段：测试和优化 (1-2天)
- [ ] **详细功能测试**
  
  **危机检测测试用例集**:
  ```python
  # 直接危机表达测试用例
  direct_crisis_cases = [
      {"text": "我想死", "expected": True, "risk_level": "HIGH"},
      {"text": "I want to kill myself", "expected": True, "risk_level": "CRITICAL"},
      {"text": "想要结束这一切", "expected": True, "risk_level": "HIGH"},
      {"text": "不想活了", "expected": True, "risk_level": "HIGH"},
  ]
  
  # 间接危机表达测试用例
  indirect_crisis_cases = [
      {"text": "活着太累了，没有意义", "expected": True, "risk_level": "MEDIUM"},
      {"text": "感觉世界对我很残酷", "expected": True, "risk_level": "LOW"},
      {"text": "每天都很绝望", "expected": True, "risk_level": "MEDIUM"},
      {"text": "没有人关心我", "expected": False, "risk_level": "LOW"},
  ]
  
  # 暴力倾向测试用例
  violence_cases = [
      {"text": "想要伤害别人", "expected": True, "risk_level": "CRITICAL"},
      {"text": "他们都该死", "expected": True, "risk_level": "HIGH"},
      {"text": "I'm going to hurt someone", "expected": True, "risk_level": "CRITICAL"},
  ]
  
  # 误报防范测试用例（正常负面情绪）
  false_positive_prevention = [
      {"text": "今天心情不好", "expected": False, "risk_level": "LOW"},
      {"text": "工作压力很大", "expected": False, "risk_level": "LOW"},
      {"text": "这个游戏里我死了很多次", "expected": False, "risk_level": "LOW"},
      {"text": "电影里的死亡场景很震撼", "expected": False, "risk_level": "LOW"},
  ]
  
  # 多语言混合测试
  multilingual_cases = [
      {"text": "我feel so hopeless", "expected": True, "risk_level": "MEDIUM"},
      {"text": "想要give up一切", "expected": True, "risk_level": "MEDIUM"},
      {"text": "Life is meaningless 活着没意思", "expected": True, "risk_level": "MEDIUM"},
  ]
  
  # 网络用语和表情符号测试
  internet_slang_cases = [
      {"text": "我要emo了😭😭", "expected": True, "risk_level": "LOW"},
      {"text": "💀💀💀想死", "expected": True, "risk_level": "HIGH"},
      {"text": "真的服了，想s", "expected": True, "risk_level": "MEDIUM"},
      {"text": "社会性死亡", "expected": False, "risk_level": "LOW"},
  ]
  ```
  
  **测试数据准备**:
  - [ ] 建立脱敏的危机文本测试数据库（至少1000个样本）
  - [ ] 准备多语言测试数据集（中英文各500个样本）
  - [ ] 收集网络用语和新兴表达方式的测试用例
  - [ ] 建立边缘情况和上下文相关的测试场景

- [ ] **性能和压力测试**
  ```python
  # 性能测试配置
  PERFORMANCE_TARGETS = {
      "detection_latency": 100,  # ms
      "throughput": 1000,  # requests/second
      "concurrent_users": 100,
      "memory_usage": 500,  # MB
      "cpu_usage": 80,  # %
  }
  
  # 压力测试场景
  stress_test_scenarios = [
      {"concurrent_requests": 50, "duration": "5min"},
      {"concurrent_requests": 100, "duration": "3min"},
      {"concurrent_requests": 200, "duration": "1min"},
  ]
  ```
  
  **国际化和本地化测试**:
  - [ ] 测试繁体中文的危机表达检测
  - [ ] 验证不同方言和地区表达习惯
  - [ ] 测试时区和文化背景相关的危机表达

- [ ] **集成测试和端到端测试**
  - [ ] 测试与可插拔记忆服务的数据同步
  - [ ] 验证危机事件的完整处理流程
  - [ ] 测试与用户界面的交互响应

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.3（实时上下文注入核心后端服务）已完成
- [ ] 核心对话编排服务(ChatOrchestrationService)已建立
- [ ] 数据库基础表结构已建立
- [ ] LLM API集成已验证

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 安全检测准确率> 95%，误报率< 2%
- [ ] 安全响应时间< 100ms
- [ ] 危机事件记录和监控机制正常运行
- [ ] 单元测试覆盖率> 90%
- [ ] 安全测试和渗透测试通过

## 风险与缓解措施

### 主要风险
1. **检测准确性**: 可能出现误报或漏报危机情况
2. **响应适当性**: 安全响应可能不够专业或有效
3. **隐私合规**: 危机数据处理可能涉及隐私合规问题
4. **性能影响**: 安全检测可能影响正常对话的响应速度

### 缓解措施
1. **持续优化关键词库和检测算法，建立反馈机制**
2. **咨询心理健康专家，制定专业的响应方案**
3. **严格遵循数据保护法规，实施最小权限原则**
4. **优化算法性能，实现异步处理**

## 安全要求

### 数据安全
- 所有危机相关数据必须加密存储
- 实施严格的访问控制和审计日志
- 定期进行安全评估和漏洞扫描
- 确保危机数据的备份和恢复机制

### 合规要求
- 符合《个人信息保护法》要求
- 遵循心理健康相关法规
- 实施数据最小化原则
- 建立用户知情同意机制

## 质量标准

### 检测质量标准
- 危机检测准确率> 95%
- 误报率< 2%
- 漏报率< 1%
- 检测响应时间< 100ms

### 响应质量标准
- 安全响应的专业性和适当性
- 用户满意度评分> 4.0/5.0
- 危机处理成功率> 90%
- 后续跟进完成率> 80%

## 相关文档引用
- [高层架构文档 - 安全架构设计](../../architecture/02-high-level-architecture.md#security-architecture) - 了解系统整体安全设计
- [API设计文档 - 安全接口规范](../../architecture/03-api-design.md#safety-interfaces) - 危机响应接口的设计规范
- [可插拔记忆系统 - 安全数据处理](../../architecture/04-pluggable-memory-system.md#security-data-handling) - 危机事件在记忆系统中的处理方式
- [后端详细设计 - 安全模块](../../architecture/05-backend-design.md#security-modules) - 安全守卫模块的架构设计
- [技术栈文档 - NLP库选型](../../architecture/agent-api-tech-stack.md#nlp-libraries) - 确认支持的NLP库和版本
- [编码标准 - 安全编码实践](../../architecture/agent-api-coding-standards.md#security-coding) - 安全相关代码的编写标准
- [需求规格文档 - 安全需求](../../prd/requirements.md#safety-requirements) - 用户安全保护的业务需求
- [技术架构文档 - 安全技术选型](../../prd/technical.md#security-technology) - 安全技术的选型依据 