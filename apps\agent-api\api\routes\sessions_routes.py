from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from typing import List, Optional
import math
from datetime import datetime, timezone

from api.models.session_models import (
    CreateSessionRequest, ChatSessionResponse, SessionListResponse, PaginationInfo,
    MessageHistoryResponse, ChatMessageResponse,
    EndSessionResponse, EndSessionResponseData
)
from api.services.session_service import SessionService, get_session_service
from api.services.content_generation_service import content_generation_service
from api.services.session_analysis_service import get_session_analysis_service, SessionAnalysisService
from api.dependencies.auth import get_current_user
from api.settings import logger

router = APIRouter(
    tags=["Chat Sessions"],
)

@router.post("", response_model=ChatSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_new_session_route(
    request_data: CreateSessionRequest,
    current_user: dict = Depends(get_current_user), # 修复: 添加认证依赖
    session_service: SessionService = Depends(get_session_service)
):
    """
    为已认证的用户创建一个新的聊天会话。
    """
    try:
        # 修复: 使用从安全JWT令牌中获取的用户ID
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的用户令牌")

        # 服务调用现在接收的是安全的用户ID，并忽略请求体中的任何userId
        created_session_dict = await session_service.create_session(user_id, request_data)
        if not created_session_dict:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建会话失败。")

        return created_session_dict

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        user_id_for_log = current_user.get("sub") if 'current_user' in locals() and current_user else "unknown"
        logger.error(f"在 create_new_session_route 中为用户 {user_id_for_log} 发生未处理的错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建会话时发生意外错误: {str(e)}"
        )

@router.get("", response_model=SessionListResponse)
async def get_sessions_for_user_route(
    page: int = Query(1, ge=1, description="Page number, 1-indexed"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: dict = Depends(get_current_user),  # 从JWT Token获取用户信息
    session_service: SessionService = Depends(get_session_service) # 注入服务
):
    """
    Retrieves a paginated list of chat sessions for the authenticated user.
    Sessions are ordered by the most recent activity.
    User ID is extracted from JWT Token for security.
    """
    # 从JWT Token的sub字段获取用户ID
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials: missing user ID"
        )

    logger.info(f"Get sessions request for authenticated user_id: {user_id}")
    try:
        sessions_list_dict, total_items = await session_service.get_user_sessions(
            user_id=user_id,  # 使用从JWT Token提取的用户ID
            page=page,
            limit=limit
        )
        session_responses = [ChatSessionResponse.model_validate(s) for s in sessions_list_dict]
        total_pages = math.ceil(total_items / limit) if limit > 0 and total_items > 0 else 0
        if total_items == 0:
            total_pages = 0
        return SessionListResponse(
            sessions=session_responses,
            pagination=PaginationInfo(
                page=page,
                limit=limit,
                total=total_items,
                pages=total_pages
            )
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Unhandled error in get_sessions_for_user_route for user {user_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while retrieving sessions: {str(e)}"
        )

@router.get("/{session_id}/messages", response_model=MessageHistoryResponse)
async def get_messages_for_session_route(
    session_id: str,
    page: int = Query(1, ge=1, description="Page number, 1-indexed"),
    limit: int = Query(20, ge=1, le=100, description="Number of messages per page"),
    current_user: dict = Depends(get_current_user),  # 从JWT Token获取用户信息
    session_service: SessionService = Depends(get_session_service) # 注入服务
):
    """
    Retrieves a paginated list of messages for the specified session.
    User ID is extracted from JWT Token and used for access control.
    """
    # 从JWT Token的sub字段获取用户ID
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials: missing user ID"
        )

    logger.info(f"Get messages request for session_id: {session_id}, authenticated user_id: {user_id}")
    try:
        # 验证用户是否有权限访问此会话
        has_access = await session_service.verify_session_access(session_id, user_id)
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: Session does not belong to the authenticated user"
            )

        messages_list_dict, total_items = await session_service.get_session_messages(
            session_id=session_id,
            page=page,
            limit=limit
        )
        message_responses = [ChatMessageResponse.model_validate(m) for m in messages_list_dict]
        total_pages = math.ceil(total_items / limit) if limit > 0 and total_items > 0 else 0
        if total_items == 0:
            total_pages = 0
        return MessageHistoryResponse(
            messages=message_responses,
            pagination=PaginationInfo(
                page=page,
                limit=limit,
                total=total_items,
                pages=total_pages
            )
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Unhandled error in get_messages_for_session_route for session {session_id}, user {user_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while retrieving messages: {str(e)}"
        )

@router.put("/{session_id}/end", response_model=EndSessionResponse)
async def end_session_and_summarize_route(
    session_id: str,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),  # 从JWT Token获取用户信息
    session_service: SessionService = Depends(get_session_service),
    session_analysis_service: SessionAnalysisService = Depends(get_session_analysis_service)
):
    """
    Ends a chat session and triggers background analysis.
    User ID is extracted from JWT Token for access control.
    """
    # 从JWT Token的sub字段获取用户ID
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials: missing user ID"
        )

    logger.info(f"End session request for session_id: {session_id}, authenticated user_id: {user_id}")
    try:
        # 验证用户是否有权限操作此会话
        has_access = await session_service.verify_session_access(session_id, user_id)
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: Session does not belong to the authenticated user"
            )

        # 获取会话的所有消息用于分析
        all_messages = await session_service.get_all_messages_for_session(session_id)

        # 生成会话摘要 - 添加降级机制
        conversation_text = "\n".join([
            f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"
            for msg in all_messages
        ])

        try:
            summary = await session_analysis_service.generate_session_summary(conversation_text)
        except Exception as e:
            logger.warning(f"摘要生成失败，使用默认摘要: {e}")
            summary = f"对话于{datetime.now().strftime('%Y年%m月%d日')}结束，包含{len(all_messages)}条消息"

        # 结束会话并保存摘要
        success = await session_service.end_session(
            session_id=session_id,
            summary=summary,
            topic=None,  # 可以后续扩展
            tags=None    # 可以后续扩展
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to end session"
            )

        # 添加后台任务进行深度分析
        background_tasks.add_task(
            session_analysis_service.analyze_session_async,
            session_id,
            all_messages
        )

        return EndSessionResponse(
            success=True,
            data=EndSessionResponseData(
                sessionId=session_id,
                summary=summary,
                endedAt=datetime.now(timezone.utc).isoformat()
            )
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Unhandled error in end_session_and_summarize_route for session {session_id}, user {user_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while ending session: {str(e)}"
        )

# Future session-related endpoints (GET list, GET messages, PUT end) will be added here.
