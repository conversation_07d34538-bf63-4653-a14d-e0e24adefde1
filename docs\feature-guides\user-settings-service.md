# 用户设置服务 API 使用指南

## 功能概述

用户设置服务提供了完整的个性化应用配置管理功能，支持主题切换、字体大小调整、通知偏好管理等功能。该服务特别针对老年用户进行了优化，提供友好的默认设置和简化的配置选项。

### 核心特性
- **个性化设置管理**: 支持主题、字体、语言、对比度等配置
- **通知偏好控制**: 完整的通知开关和静默时间管理
- **老年用户友好**: 提供大字体、高对比度等适老化默认设置
- **实时同步**: 设置更改即时生效，支持跨设备同步
- **并发安全**: 采用UPSERT机制确保多设备同时修改时的数据一致性

## 核心API端点

### 1. 获取用户设置
- **端点**: `GET /api/v1/user/settings`
- **认证**: JWT Bearer Token（必须）
- **参数**: 无
- **响应**: 用户完整设置对象或老年用户友好的默认设置

### 2. 更新用户设置  
- **端点**: `PUT /api/v1/user/settings`
- **认证**: JWT Bearer Token（必须）
- **参数**: 支持部分更新（PATCH语义），只需发送要修改的字段
- **响应**: 更新后的完整设置对象

## 数据契约

### 设置对象结构
```json
{
  "theme": "light|dark|auto",
  "fontSize": "small|medium|large|extra_large", 
  "highContrast": boolean,
  "language": "zh-CN|en-US|...",
  "notifications": {
    "enabled": boolean,
    "quietHours": {
      "enabled": boolean,
      "start": "HH:MM",
      "end": "HH:MM"
    }
  }
}
```

### 完整响应示例
```json
{
  "theme": "auto",
  "fontSize": "large",
  "highContrast": false,
  "language": "zh-CN",
  "notifications": {
    "enabled": true,
    "quietHours": {
      "enabled": false,
      "start": "22:00",
      "end": "08:00"
    }
  }
}
```

### 部分更新请求示例
```json
{
  "theme": "dark",
  "notifications": {
    "quietHours": {
      "enabled": true,
      "start": "23:00"
    }
  }
}
```

## 调用示例与注意事项

### 1. 认证管理
```typescript
// 所有请求必须携带有效的JWT Bearer Token
const headers = {
  'Authorization': `Bearer ${accessToken}`,
  'Content-Type': 'application/json'
};
```

### 2. 获取设置示例
```typescript
// 获取用户设置
const getUserSettings = async () => {
  try {
    const response = await fetch('/api/v1/user/settings', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (response.ok) {
      const settings = await response.json();
      return settings;
    }
  } catch (error) {
    console.error('获取设置失败:', error);
  }
};
```

### 3. 更新设置示例  
```typescript
// 部分更新设置（推荐方式）
const updateSettings = async (partialSettings) => {
  try {
    const response = await fetch('/api/v1/user/settings', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(partialSettings)
    });
    
    if (response.ok) {
      const updatedSettings = await response.json();
      return updatedSettings;
    }
  } catch (error) {
    console.error('更新设置失败:', error);
  }
};

// 使用示例
await updateSettings({ theme: 'dark' }); // 只更新主题
await updateSettings({ 
  fontSize: 'large',
  notifications: { enabled: false }
}); // 更新多个字段
```

### 4. 时间格式处理
```typescript
// 静默时间设置
const setQuietHours = async (startTime, endTime) => {
  await updateSettings({
    notifications: {
      quietHours: {
        enabled: true,
        start: startTime, // 格式: "22:00"
        end: endTime      // 格式: "08:00"
      }
    }
  });
};
```

### 5. 错误处理
```typescript
const handleSettingsRequest = async () => {
  try {
    const response = await fetch('/api/v1/user/settings');
    
    switch (response.status) {
      case 200:
        return await response.json();
      case 401:
        // 认证失败，重新登录
        redirectToLogin();
        break;
      case 422:
        // 验证失败，检查数据格式
        const error = await response.json();
        showValidationError(error.detail);
        break;
      case 500:
        // 服务器错误，稍后重试
        showError('服务暂时不可用，请稍后重试');
        break;
    }
  } catch (error) {
    showError('网络连接失败');
  }
};
```

### 6. 性能优化建议
```typescript
// 实现本地缓存避免频繁请求
const SettingsCache = {
  cache: null,
  lastFetch: 0,
  
  async getSettings() {
    const now = Date.now();
    if (this.cache && (now - this.lastFetch < 300000)) { // 5分钟缓存
      return this.cache;
    }
    
    this.cache = await getUserSettings();
    this.lastFetch = now;
    return this.cache;
  },
  
  invalidate() {
    this.cache = null;
    this.lastFetch = 0;
  }
};
```

### 7. 默认值处理
```typescript
// 新用户将自动获得老年用户友好的默认设置
const getDefaultSettings = () => ({
  theme: 'auto',
  fontSize: 'large',      // 大字体提升可读性
  highContrast: false,    // 高对比度可选
  language: 'zh-CN',
  notifications: {
    enabled: true,        // 简化通知设置
    quietHours: {
      enabled: false,
      start: "22:00",
      end: "08:00"
    }
  }
});
```

### 8. React Hook示例
```typescript
import { useState, useEffect } from 'react';

export const useUserSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const userSettings = await getUserSettings();
        setSettings(userSettings);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };
    
    loadSettings();
  }, []);
  
  const updateUserSettings = async (partialSettings) => {
    try {
      const updated = await updateSettings(partialSettings);
      setSettings(updated);
      return updated;
    } catch (err) {
      setError(err);
      throw err;
    }
  };
  
  return { settings, loading, error, updateUserSettings };
};
```

### 重要注意事项

1. **认证要求**: 所有请求必须携带有效的JWT Bearer Token
2. **默认值**: 新用户将自动获得老年用户友好的默认设置（大字体、简化通知等）
3. **部分更新**: PUT端点支持PATCH语义，只需发送要更新的字段，未指定字段保持不变
4. **时间格式**: 静默时间使用"HH:MM"格式（如"22:00"），24小时制
5. **错误处理**: 遵循标准HTTP状态码（401认证失败、422验证失败、500服务器错误）
6. **性能**: 响应时间P95<200ms，建议实现适当的本地缓存
7. **并发安全**: 后端采用UPSERT机制，支持多设备同时修改设置
8. **数据验证**: 主题只支持"light|dark|auto"，字体大小支持"small|medium|large|extra_large"

### 故障排除

- **401错误**: 检查JWT token是否有效，是否需要刷新
- **422错误**: 检查请求数据格式，特别是时间字段格式
- **500错误**: 后端服务问题，建议实现重试机制
- **网络超时**: 实现指数退避重试策略

### 安全考虑

- 使用HTTPS进行所有API调用
- 不要在前端日志中记录完整的JWT token
- 实现token自动刷新机制
- 设置适当的请求超时时间 