#!/usr/bin/env python3
"""
故事1.17-B: 数据库Schema一致性验证工具

验证数据库Schema与代码模型的一致性，确保：
1. 必需表存在
2. 废弃表已清理
3. 状态约束支持新值
4. RLS策略正确配置

架构师建议：采用"先扩展后收缩"策略进行安全的Schema变更
"""

import asyncio
import logging
import sys
import os
from typing import List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.supabase_init import get_supabase_client
from api.settings import get_settings, logger

# 必需的表列表
REQUIRED_TABLES = [
    "chat_sessions",
    "reminders",
    "user_profiles",
    "characters",
    "chat_messages"
]

# 废弃的表列表（应该已清理）
DEPRECATED_TABLES = [
    "user_memories",
    "chat_conversations"
]

# 会话状态约束值
SESSION_STATUS_VALUES = [
    "active",
    "completed",
    "archived",
    "deleted"
]


async def verify_schema_consistency() -> List[str]:
    """
    验证数据库Schema与代码模型的一致性

    Returns:
        发现的问题列表
    """
    issues = []
    logger.info("开始Schema一致性验证...")

    try:
        supabase = await get_supabase_client()

        # 1. 检查必需表是否存在
        logger.info("检查必需表...")
        table_issues = await _check_required_tables(supabase)
        issues.extend(table_issues)

        # 2. 检查废弃表是否已清理
        logger.info("检查废弃表清理...")
        deprecated_issues = await _check_deprecated_tables(supabase)
        issues.extend(deprecated_issues)

        # 3. 检查状态约束
        logger.info("检查状态约束...")
        constraint_issues = await _check_status_constraints(supabase)
        issues.extend(constraint_issues)

        # 4. 检查RLS策略
        logger.info("检查RLS策略...")
        rls_issues = await _check_rls_policies(supabase)
        issues.extend(rls_issues)

        if not issues:
            logger.info("✅ Schema一致性验证通过，无问题发现")
        else:
            logger.warning(f"❌ 发现 {len(issues)} 个Schema问题")
            for issue in issues:
                logger.warning(f"  - {issue}")

    except Exception as e:
        logger.error(f"Schema验证失败: {e}", exc_info=True)
        issues.append(f"验证过程异常: {str(e)}")

    return issues


async def _check_required_tables(supabase) -> List[str]:
    """检查必需表是否存在"""
    issues = []

    try:
        # --- 修复前 ---
        # response = await supabase.from_("information_schema.tables").select("table_name").eq("table_schema", "public").execute()

        # --- 修复后 ---
        # 使用RPC调用我们创建的 get_all_tables 函数
        response = await supabase.rpc("get_all_tables").execute()

        existing_tables = {row["table_name"] for row in response.data}

        # 检查每个必需表
        for table in REQUIRED_TABLES:
            if table not in existing_tables:
                issues.append(f"缺少必需表: {table}")

    except Exception as e:
        # 捕获可能因为RPC函数不存在而引发的错误
        error_message = str(e)
        if "function public.get_all_tables() does not exist" in error_message:
             issues.append("检查必需表时出错: 'get_all_tables' 数据库函数不存在，请先在Supabase SQL Editor中创建它。")
        else:
             issues.append(f"检查必需表时出错: {error_message}")

    return issues


async def _check_deprecated_tables(supabase) -> List[str]:
    """检查废弃表是否已清理"""
    issues = []

    try:
        # --- 修复前 ---
        # response = await supabase.from_("information_schema.tables").select("table_name").eq("table_schema", "public").execute()

        # --- 修复后 ---
        response = await supabase.rpc("get_all_tables").execute()

        existing_tables = {row["table_name"] for row in response.data}

        # 检查废弃表
        for table in DEPRECATED_TABLES:
            if table in existing_tables:
                issues.append(f"废弃表仍存在: {table}")

    except Exception as e:
        error_message = str(e)
        if "function public.get_all_tables() does not exist" in error_message:
             issues.append("检查废弃表时出错: 'get_all_tables' 数据库函数不存在，请先在Supabase SQL Editor中创建它。")
        else:
             issues.append(f"检查废弃表时出错: {error_message}")

    return issues


async def _check_status_constraints(supabase) -> List[str]:
    """检查状态约束是否支持新值"""
    issues = []

    try:
        # 检查chat_sessions表的状态约束
        # 尝试查询约束信息
        response = await supabase.from_("information_schema.check_constraints").select("*").like("constraint_name", "%status%").execute()

        # 如果能执行成功，说明约束检查可以进行
        # 这里简化处理，实际应该解析约束定义
        logger.info("状态约束检查完成（简化版本）")

        # 尝试验证"completed"状态是否被支持
        # 这将在实际测试中验证

    except Exception as e:
        issues.append(f"检查状态约束时出错: {str(e)}")

    return issues


async def _check_rls_policies(supabase) -> List[str]:
    """检查RLS策略配置"""
    issues = []

    try:
        # 检查主要表的RLS策略
        main_tables = ["chat_sessions", "reminders", "user_profiles"]

        for table in main_tables:
            try:
                # --- 修复前 ---
                # response = await supabase.from_("pg_policies").select("*").eq("tablename", table).execute()

                # --- 修复后 ---
                response = await supabase.rpc("get_rls_policies", {"table_name_param": table}).execute()

                if not response.data:
                    issues.append(f"表 {table} 可能缺少RLS策略")

            except Exception as e:
                error_message = str(e)
                if "function public.get_rls_policies(text) does not exist" in error_message:
                    issues.append(f"无法检查表 {table} 的RLS策略: 'get_rls_policies' 数据库函数不存在，请先创建。")
                    # 避免对每个表都报告一次函数不存在的错误
                    break
                else:
                    logger.debug(f"无法检查表 {table} 的RLS策略: {e}")

    except Exception as e:
        issues.append(f"检查RLS策略时出错: {str(e)}")

    return issues


async def apply_schema_fixes() -> bool:
    """
    应用Schema修复

    架构师建议：采用"先扩展后收缩"策略
    """
    logger.info("开始应用Schema修复...")

    try:
        supabase = await get_supabase_client()

        # 1. 更新状态字段约束，确保支持'completed'状态
        logger.info("更新chat_sessions状态约束...")

        # --- 修复前 ---
        # await supabase.query("""
        #     ALTER TABLE chat_sessions DROP CONSTRAINT IF EXISTS chat_sessions_status_check;
        # """).execute()
        # await supabase.query("""
        #     ALTER TABLE chat_sessions ADD CONSTRAINT chat_sessions_status_check
        #     CHECK (status IN ('active', 'completed', 'archived', 'deleted'));
        # """).execute()

        # --- 修复后 ---
        # 使用 rpc 调用我们刚刚创建的 execute_sql 函数
        await supabase.rpc(
            "execute_sql",
            {"query": "ALTER TABLE chat_sessions DROP CONSTRAINT IF EXISTS chat_sessions_status_check;"}
        ).execute()

        await supabase.rpc(
            "execute_sql",
            {"query": "ALTER TABLE chat_sessions ADD CONSTRAINT chat_sessions_status_check CHECK (status IN ('active', 'completed', 'archived', 'deleted'));"}
        ).execute()

        logger.info("✅ Schema修复完成")
        return True

    except Exception as e:
        logger.error(f"Schema修复失败: {e}", exc_info=True)
        return False


async def main():
    """主入口函数"""
    print("🔍 数据库Schema一致性验证工具")
    print("=" * 50)

    # 验证Schema一致性
    issues = await verify_schema_consistency()

    if issues:
        print(f"\n❌ 发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")

        # 询问是否应用修复
        print(f"\n🔧 是否应用Schema修复? (y/n): ", end="")

        # 在脚本模式下自动应用修复
        response = "y"
        print(response)

        if response.lower() == 'y':
            success = await apply_schema_fixes()
            if success:
                print("✅ Schema修复完成")

                # 重新验证
                print("\n🔍 重新验证Schema...")
                new_issues = await verify_schema_consistency()
                if not new_issues:
                    print("✅ 所有问题已解决")
                else:
                    print(f"⚠️  仍有 {len(new_issues)} 个问题需要手动处理")
            else:
                print("❌ Schema修复失败")
    else:
        print("✅ Schema一致性验证通过")

    print("\n" + "=" * 50)
    print("验证完成")


if __name__ == "__main__":
    asyncio.run(main())
