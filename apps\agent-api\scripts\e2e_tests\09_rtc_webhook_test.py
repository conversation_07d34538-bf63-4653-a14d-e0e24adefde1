#!/usr/bin/env python3
"""
RTC Webhook API测试
测试接口:
- POST /api/v1/chat/rtc_event_handler
"""

import asyncio
import sys
import uuid
import json
import hashlib
import os
from datetime import datetime
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent))

from base_tester import BaseAPITester


class RTCWebhookTester(BaseAPITester):
    """RTC Webhook API测试器"""

    def __init__(self, base_url: str = "http://localhost:8003"):
        super().__init__("09_rtc_webhook", base_url)
        self.character_id = None

    def _calculate_signature(self, webhook_data: dict, secret: str = "73a3489D9278fea1c21") -> str:
        """计算火山引擎标准Webhook签名"""
        try:
            # 按照火山引擎官方算法计算签名
            fields_to_sign = [
                webhook_data["EventType"],
                webhook_data["EventData"],
                webhook_data["EventTime"],
                webhook_data["EventId"],
                webhook_data["Version"],
                webhook_data["AppId"],
                webhook_data["Nonce"],
                secret
            ]

            # 按字母序排序
            fields_to_sign.sort()

            # 直接拼接成字符串
            sign_string = ''.join(str(field) for field in fields_to_sign)

            # SHA256哈希
            signature = hashlib.sha256(sign_string.encode('utf-8')).hexdigest()
            return signature
        except Exception as e:
            self.logger.error(f"签名计算失败: {e}")
            return ""

    def _create_webhook_payload(self, event_type: str = "ASR_SENTENCE_END") -> tuple[dict, dict]:
        """创建标准的Webhook载荷"""
        # 确保有角色ID和用户ID
        character_id = self.character_id or "test-character"
        user_id = self.user_id or "test-user"
        session_id = f"test-session-{uuid.uuid4()}"

        # 构建EventData JSON字符串
        event_data = json.dumps({
            "RequestId": f"rtc-{uuid.uuid4()}",
            "Custom": json.dumps({
                "sessionId": session_id,
                "userId": user_id,
                "characterId": character_id
            }),
            "Payload": {
                "text": "这是一个测试语音识别结果",
                "confidence": 0.95,
                "session_id": session_id
            }
        })

        # 构建火山引擎标准Webhook格式
        webhook_data = {
            "EventType": event_type,
            "EventData": event_data,
            "EventTime": datetime.utcnow().isoformat() + "Z",
            "EventId": f"webhook-{uuid.uuid4()}",
            "Version": "2024-06-01",
            "AppId": "test_app_id",
            "Nonce": "test_nonce"
        }

        # 计算签名
        signature = self._calculate_signature(webhook_data)

        # 创建请求头
        headers = self._get_headers(include_auth=False)
        if signature:
            headers["Signature"] = signature
            self.logger.info(f"🔐 生成Webhook签名: {signature}")
        else:
            self.logger.warning("⚠️ 签名生成失败")

        return webhook_data, headers

    async def test_rtc_webhook_asr_sentence_end(self):
        """测试ASR_SENTENCE_END事件的Webhook"""
        self.logger.info("🎤 测试ASR_SENTENCE_END Webhook")

        webhook_data, headers = self._create_webhook_payload("ASR_SENTENCE_END")

        # 发送Webhook请求
        url = f"{self.base_url}/api/v1/chat/rtc_event_handler"

        try:
            async with self.session.post(url, json=webhook_data, headers=headers) as response:
                response_text = await response.text()

                self.logger.info(f"📥 Webhook响应: {response.status}")
                self.logger.debug(f"📥 响应内容: {response_text}")

                if response.status == 200:
                    self.logger.info("✅ ASR Webhook处理成功")
                    self.test_results["passed"] += 1

                    # 验证响应格式
                    try:
                        response_data = json.loads(response_text)

                        # 验证响应字段
                        if "response" in response_data or "message" in response_data:
                            self.logger.info("✅ 响应包含预期字段")
                        else:
                            self.logger.warning("⚠️ 响应格式可能异常")

                    except json.JSONDecodeError:
                        self.logger.warning("⚠️ 响应不是有效JSON")

                elif response.status == 401:
                    self.logger.warning("⚠️ Webhook签名验证失败 - 这可能是预期的")
                    self.test_results["passed"] += 1  # 401也算测试通过，说明验证机制工作

                else:
                    self.logger.error(f"❌ Webhook处理失败，状态码: {response.status}")
                    self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ Webhook请求异常: {e}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def test_rtc_webhook_without_signature(self):
        """测试没有签名的Webhook请求"""
        self.logger.info("🔒 测试没有签名的Webhook请求")

        webhook_data, _ = self._create_webhook_payload("ASR_SENTENCE_END")
        headers = self._get_headers(include_auth=False)  # 不包含签名

        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat/rtc_event_handler",
                json=webhook_data,
                headers=headers
            ) as response:
                response_text = await response.text()

                self.logger.info(f"📥 无签名Webhook响应: {response.status}")

                if response.status == 401:
                    self.logger.info("✅ 无签名请求正确返回401")
                    self.test_results["passed"] += 1
                elif response.status == 200:
                    self.logger.warning("⚠️ 无签名请求被接受（可能是开发模式）")
                    self.test_results["passed"] += 1
                else:
                    self.logger.error(f"❌ 无签名请求处理异常: {response.status}")
                    self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ 无签名Webhook请求异常: {e}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def test_rtc_webhook_invalid_signature(self):
        """测试错误签名的Webhook请求"""
        self.logger.info("❌ 测试错误签名的Webhook请求")

        webhook_data, headers = self._create_webhook_payload("ASR_SENTENCE_END")
        headers["Signature"] = "invalid_signature_12345"  # 错误的签名

        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat/rtc_event_handler",
                json=webhook_data,
                headers=headers
            ) as response:
                response_text = await response.text()

                self.logger.info(f"📥 错误签名Webhook响应: {response.status}")

                if response.status == 401:
                    self.logger.info("✅ 错误签名请求正确返回401")
                    self.test_results["passed"] += 1
                elif response.status == 200:
                    self.logger.warning("⚠️ 错误签名请求被接受（可能是开发模式）")
                    self.test_results["passed"] += 1
                else:
                    self.logger.error(f"❌ 错误签名请求处理异常: {response.status}")
                    self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ 错误签名Webhook请求异常: {e}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def test_rtc_webhook_invalid_data(self):
        """测试无效数据的Webhook请求"""
        self.logger.info("❌ 测试无效数据的Webhook请求")

        # 创建无效的Webhook数据
        invalid_webhook_data = {
            "EventType": "INVALID_EVENT_TYPE",
            "EventData": "invalid_json_data",  # 无效的JSON
            "EventTime": "invalid_time_format",
            "EventId": "",  # 空ID
            "Version": "1.0.0",
            "AppId": "test_app",
            "Nonce": "test"
        }

        headers = self._get_headers(include_auth=False)

        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat/rtc_event_handler",
                json=invalid_webhook_data,
                headers=headers
            ) as response:
                response_text = await response.text()

                self.logger.info(f"📥 无效数据Webhook响应: {response.status}")

                if response.status in [400, 422]:
                    self.logger.info("✅ 无效数据请求正确返回错误状态码")
                    self.test_results["passed"] += 1
                elif response.status == 401:
                    self.logger.info("✅ 无效数据请求返回401（签名验证）")
                    self.test_results["passed"] += 1
                else:
                    self.logger.warning(f"⚠️ 无效数据请求处理可能有问题: {response.status}")
                    self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ 无效数据Webhook请求异常: {e}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def test_rtc_webhook_different_event_types(self):
        """测试不同事件类型的Webhook"""
        self.logger.info("🎯 测试不同事件类型的Webhook")

        event_types = [
            "ASR_SENTENCE_BEGIN",
            "ASR_SENTENCE_END",
            "ASR_COMPLETE",
            "CUSTOM_EVENT"
        ]

        success_count = 0

        for event_type in event_types:
            self.logger.info(f"测试事件类型: {event_type}")

            webhook_data, headers = self._create_webhook_payload(event_type)

            try:
                async with self.session.post(
                    f"{self.base_url}/api/v1/chat/rtc_event_handler",
                    json=webhook_data,
                    headers=headers
                ) as response:
                    response_text = await response.text()

                    if response.status in [200, 401]:  # 200成功或401签名验证都算正常
                        self.logger.info(f"✅ {event_type} 事件处理正常")
                        success_count += 1
                    else:
                        self.logger.error(f"❌ {event_type} 事件处理失败: {response.status}")

            except Exception as e:
                self.logger.error(f"❌ {event_type} 事件请求异常: {e}")

            await asyncio.sleep(0.2)  # 短暂间隔

        # 统计结果
        if success_count >= len(event_types) // 2:  # 至少一半成功
            self.logger.info(f"✅ 事件类型测试通过: {success_count}/{len(event_types)}")
            self.test_results["passed"] += 1
        else:
            self.logger.error(f"❌ 事件类型测试失败: {success_count}/{len(event_types)}")
            self.test_results["failed"] += 1

        self.test_results["total"] += 1

    async def test_rtc_webhook_malformed_json(self):
        """测试畸形JSON的Webhook请求"""
        self.logger.info("💥 测试畸形JSON的Webhook请求")

        headers = self._get_headers(include_auth=False)
        headers["Content-Type"] = "application/json"

        # 发送畸形JSON
        malformed_json = '{"EventType": "TEST", "EventData": invalid_json}'

        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat/rtc_event_handler",
                data=malformed_json,
                headers=headers
            ) as response:
                response_text = await response.text()

                self.logger.info(f"📥 畸形JSON Webhook响应: {response.status}")

                if response.status in [400, 422]:
                    self.logger.info("✅ 畸形JSON请求正确返回错误状态码")
                    self.test_results["passed"] += 1
                else:
                    self.logger.warning(f"⚠️ 畸形JSON请求处理可能有问题: {response.status}")
                    self.test_results["failed"] += 1

        except Exception as e:
            self.logger.error(f"❌ 畸形JSON Webhook请求异常: {e}")
            self.test_results["failed"] += 1
        finally:
            self.test_results["total"] += 1

    async def run_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始RTC Webhook API测试")

        # 确保用户已认证（用于获取角色ID）
        await self.ensure_authenticated()

        # 获取角色ID
        try:
            self.character_id = await self.get_character_id()
        except Exception as e:
            self.logger.warning(f"⚠️ 无法获取角色ID: {e}")
            self.character_id = "test-character"

        tests = [
            ("ASR_SENTENCE_END Webhook", self.test_rtc_webhook_asr_sentence_end),
            ("无签名Webhook请求", self.test_rtc_webhook_without_signature),
            ("错误签名Webhook请求", self.test_rtc_webhook_invalid_signature),
            ("无效数据Webhook请求", self.test_rtc_webhook_invalid_data),
            ("不同事件类型Webhook", self.test_rtc_webhook_different_event_types),
            ("畸形JSON Webhook请求", self.test_rtc_webhook_malformed_json),
        ]

        for test_name, test_method in tests:
            try:
                await test_method()
                await asyncio.sleep(0.5)  # 测试间隔
            except Exception as e:
                self.logger.error(f"❌ {test_name}测试异常: {e}")
                self.test_results["failed"] += 1
                self.test_results["errors"].append({"test": test_name, "error": str(e)})

        self.print_results()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='RTC Webhook API测试')
    parser.add_argument('--url', '-u', default='http://localhost:8003',
                        help='目标服务器URL (默认: http://localhost:8003)')

    args = parser.parse_args()

    async with RTCWebhookTester(args.url) as tester:
        await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
