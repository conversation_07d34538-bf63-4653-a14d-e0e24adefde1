"""
测试ChatOrchestrationService的危机检测集成 - 故事1.7-B
测试关键架构师风险点：检测时机、LLM调用禁止、流式响应格式一致性
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
import asyncio
import logging

# 这些导入现在会失败，因为我们还没有完全实现
from api.services.chat_orchestration_service import ChatOrchestrationService
from api.services.crisis_detection_service import CrisisDetectionService
from api.services.memory_service import IMemoryService
from api.services.llm_proxy_service import LLMProxyService
from api.services.tool_executor_service import ToolExecutorService
from api.services.prompt_builder_service import PromptBuilderService


class TestChatOrchestrationCrisisIntegration:
    """测试ChatOrchestrationService的危机检测集成"""

    def setup_method(self):
        """为每个测试方法设置测试环境"""
        # Mock所有依赖服务
        self.mock_memory_service = Mock(spec=IMemoryService)
        self.mock_llm_service = Mock(spec=LLMProxyService)
        self.mock_tool_service = Mock(spec=ToolExecutorService)
        self.mock_prompt_service = Mock(spec=PromptBuilderService)

        # 设置异步方法
        self.mock_memory_service.get_memory_context = AsyncMock()
        # 修复：使用正确的方法名 call_llm
        self.mock_llm_service.call_llm = AsyncMock()
        self.mock_prompt_service.build_messages = AsyncMock()

        # 创建危机检测服务
        self.crisis_keywords = ["自杀", "不想活了", "想死"]
        self.mock_crisis_service = Mock(spec=CrisisDetectionService)

        # 创建ChatOrchestrationService实例
        self.chat_service = ChatOrchestrationService(
            memory_service=self.mock_memory_service,
            llm_proxy_service=self.mock_llm_service,
            tool_executor_service=self.mock_tool_service,
            prompt_builder_service=self.mock_prompt_service,
            crisis_detection_service=self.mock_crisis_service  # 需要添加到构造函数
        )

    # 测试场景组2: 对话流程切换 (AC2)

    @pytest.mark.asyncio
    async def test_crisis_detection_stops_llm_call_immediately(self):
        """
        Scenario 2.1: 危机检测时立即中止LLM调用 (架构师关键风险)
        Given ChatOrchestrationService接收到用户消息
        And CrisisDetectionService检测到危机信号
        When ChatOrchestrationService.handle_message()执行
        Then 系统应在调用LLMProxyService之前就中止流程
        And LLMProxyService.call_llm()不应被调用
        """
        # 配置危机检测返回True
        self.mock_crisis_service.detect.return_value = True

        # 准备测试数据
        user_message = "我不想活了"
        context = {
            'userId': 'test_user',
            'sessionId': 'test_session',
            'characterId': 'test_character',
            'requestId': 'test_request'
        }

        # 执行测试
        result = await self.chat_service.handle_message(user_message, context)

        # 验证危机检测被调用
        self.mock_crisis_service.detect.assert_called_once_with(user_message)

        # 验证LLM服务绝不被调用
        self.mock_llm_service.call_llm.assert_not_called()

        # 验证记忆服务也不被调用（因为在危机检测之后）
        self.mock_memory_service.get_memory_context.assert_not_called()

        # 验证返回了危机干预回复
        assert "心理援助热线" in result or "400-161-9995" in result

    @pytest.mark.asyncio
    async def test_scripted_crisis_response_generation(self):
        """
        Scenario 2.2: 脚本化危机回复生成
        Given 用户输入包含危机信号
        And 配置中设置CRISIS_HOTLINE为"400-161-9995"
        When ChatOrchestrationService检测到危机并生成回复
        Then 返回的回复应包含关怀内容和权威帮助热线
        """
        # 配置危机检测
        self.mock_crisis_service.detect.return_value = True

        user_message = "我想自杀"
        context = {'userId': 'test_user', 'requestId': 'test_request'}

        # 执行测试
        result = await self.chat_service.handle_message(user_message, context)

        # 验证回复内容
        assert "400-161-9995" in result, "回复应包含危机热线电话"
        assert any(keyword in result for keyword in ["听到", "难过", "帮助", "不是一个人"]), \
            "回复应包含关怀内容"

        # 验证这是脚本化回复，不是LLM生成
        self.mock_llm_service.call_llm.assert_not_called()

    @pytest.mark.asyncio
    async def test_streaming_response_format_consistency(self):
        """
        Scenario 2.3: 流式响应格式一致性 (架构师风险点)
        Given 用户通过SSE接口发送包含危机信号的消息
        When 系统返回危机干预回复
        Then 回复应使用async generator格式
        And 响应格式应与正常对话流程完全一致
        """
        # 配置危机检测
        self.mock_crisis_service.detect.return_value = True

        user_message = "我不想活了"
        context = {'userId': 'test_user', 'requestId': 'test_request'}

        # 测试流式响应
        response_generator = self.chat_service.handle_message_stream(user_message, context)

        # 验证返回的是async generator
        assert hasattr(response_generator, '__aiter__'), "应该返回async generator"

        # 收集所有响应片段
        response_chunks = []
        async for chunk in response_generator:
            response_chunks.append(chunk)

        # 验证有内容返回
        assert len(response_chunks) > 0, "应该有危机干预回复内容"

        # 验证完整回复包含危机热线
        full_response = ''.join(response_chunks)
        assert "400-161-9995" in full_response, "流式回复应包含危机热线"

        # 验证LLM服务没有被调用
        self.mock_llm_service.call_llm.assert_not_called()

    # 测试场景组3: 安全处理 (AC3)

    @pytest.mark.asyncio
    async def test_crisis_mode_never_calls_llm(self):
        """
        Scenario 3.1: 危机模式下绝不调用LLM
        Given CrisisDetectionService检测到用户输入包含危机信号
        When ChatOrchestrationService进入危机干预模式
        Then 系统绝不能调用任何LLM服务
        """
        # 配置危机检测
        self.mock_crisis_service.detect.return_value = True

        # 测试多种危机消息
        crisis_messages = [
            "我想自杀",
            "不想活了",
            "想要结束生命",
            "我要自残"
        ]

        for message in crisis_messages:
            context = {'userId': f'user_{hash(message)}', 'requestId': f'req_{hash(message)}'}

            # 重置mock调用计数
            self.mock_llm_service.reset_mock()

            # 执行测试
            await self.chat_service.handle_message(message, context)

            # 验证LLM绝不被调用
            self.mock_llm_service.call_llm.assert_not_called()
            assert self.mock_llm_service.call_llm.call_count == 0

    @pytest.mark.asyncio
    async def test_crisis_intervention_high_priority_logging(self):
        """
        Scenario 3.2: 危机干预对话高优记录
        Given 用户发送包含危机信号的消息
        When 系统触发危机干预流程
        Then 所有相关对话应被标记为"CRISIS"级别
        """
        # 配置危机检测
        self.mock_crisis_service.detect.return_value = True

        user_message = "我想自杀"
        context = {
            'userId': 'test_user_crisis',
            'sessionId': 'test_session_crisis',
            'requestId': 'test_request_crisis'
        }

        # 使用日志捕获
        with patch('api.services.chat_orchestration_service.logger') as mock_logger:
            await self.chat_service.handle_message(user_message, context)

            # 验证危机级别日志被记录
            crisis_log_calls = [
                call for call in mock_logger.error.call_args_list + mock_logger.warning.call_args_list + mock_logger.info.call_args_list
                if 'CRISIS' in str(call) or '危机' in str(call)
            ]

            assert len(crisis_log_calls) > 0, "应该记录危机级别日志"

    @pytest.mark.asyncio
    async def test_normal_conversation_when_no_crisis(self):
        """测试无危机信号时的正常对话流程"""
        # 配置无危机检测
        self.mock_crisis_service.detect.return_value = False

        # 配置正常的服务响应
        self.mock_memory_service.get_memory_context.return_value = {"memories": [], "context": ""}
        self.mock_prompt_service.build_messages.return_value = [
            {"role": "user", "content": "正常消息"}
        ]
        # 修复：使用正确的方法名 call_llm，而不是 generate_response
        self.mock_llm_service.call_llm.return_value = "这是正常的AI回复"

        user_message = "今天天气很好"
        context = {'userId': 'test_user', 'requestId': 'test_request'}

        # 执行测试
        result = await self.chat_service.handle_message(user_message, context)

        # 验证正常流程被执行
        self.mock_crisis_service.detect.assert_called_once_with(user_message)
        self.mock_memory_service.get_memory_context.assert_called_once()
        # 修复：验证正确的方法名
        self.mock_llm_service.call_llm.assert_called_once()

        # 验证返回了正常回复
        assert result == "这是正常的AI回复"

    # 测试场景组4: API与集成 (AC4)

    @pytest.mark.asyncio
    async def test_crisis_intervention_transparent_to_api_callers(self):
        """
        Scenario 4.1: 文本对话接口的危机干预透明性
        Given 用户通过API发送危机信号
        When 服务端处理请求
        Then API响应格式应与正常对话完全一致
        """
        # 配置危机检测
        self.mock_crisis_service.detect.return_value = True

        user_message = "我不想活了"
        context = {'userId': 'api_user', 'requestId': 'api_request'}

        # 测试同步接口
        sync_result = await self.chat_service.handle_message(user_message, context)
        assert isinstance(sync_result, str), "同步接口应返回字符串"
        assert len(sync_result) > 0, "应该有回复内容"

        # 测试流式接口
        stream_result = self.chat_service.handle_message_stream(user_message, context)
        assert hasattr(stream_result, '__aiter__'), "流式接口应返回async generator"

        # 收集流式结果
        stream_chunks = [chunk async for chunk in stream_result]
        stream_full = ''.join(stream_chunks)

        # 验证两种接口的内容一致性（都包含危机热线）
        assert "400-161-9995" in sync_result
        assert "400-161-9995" in stream_full

    @pytest.mark.asyncio
    async def test_different_api_endpoints_consistent_behavior(self):
        """
        Scenario 4.3: 不同API入口的行为一致性
        Given 相同的危机信号通过不同API接口输入
        When 分别处理
        Then 两个接口的危机检测行为应完全一致
        """
        # 配置危机检测
        self.mock_crisis_service.detect.return_value = True

        user_message = "我想死"

        # 模拟不同API入口的context
        text_context = {
            'userId': 'user_text',
            'sessionId': 'session_text',
            'source': 'text_message_api'
        }

        rtc_context = {
            'userId': 'user_rtc',
            'sessionId': 'session_rtc',
            'source': 'rtc_webhook_api'
        }

        # 执行两种API调用
        text_result = await self.chat_service.handle_message(user_message, text_context)
        rtc_result = await self.chat_service.handle_message(user_message, rtc_context)

        # 验证两次危机检测都被调用
        assert self.mock_crisis_service.detect.call_count == 2

        # 验证LLM服务都没有被调用
        self.mock_llm_service.call_llm.assert_not_called()

        # 验证回复内容都包含危机热线（行为一致）
        assert "400-161-9995" in text_result
        assert "400-161-9995" in rtc_result
