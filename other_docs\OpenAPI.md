启动智能体 StartVoiceChat
最近更新时间：2025.07.02 17:32:30
首次发布时间：2025.05.15 13:15:32

我的收藏
有用
无用
本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考历史版本。​
在实时音视频场景中，你可以调用此接口在房间内引入一个智能体进行 AI 实时交互。​
RTC 提供语音识别（ASR）、语音合成（TTS）、大模型（LLM） 一站式接入，同时也支持通道服务，即可使用此接口灵活接入第三方大模型/Agent。​
注意事项​
请求频率：单账号下 QPS 不得超过 60。​
请求接入地址：仅支持 rtc.volcengineapi.com。​
调用该接口启动智能体任务后，若真人用户退出房间，180 s 后该智能体任务会自动停止，但该 180s 内仍会计费。为避免不必要的费用，建议真人用户退出房间后，及时调用 StopVoiceChat 接口关闭智能体任务。​
前提条件​
调用该接口前，你需要开通语音识别、语音合成和大模型服务并配置相关权限策略，详情请参看方案集成前置准备。​
调用接口​
发送 HTTP(S) 请求时，你需要符合火山引擎规范。调用接口的请求结构、公共参数、签名机制、返回结构，参看调用方法。​
请求说明​
请求方式：POST​
请求地址：https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01​
调试​
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
去调试
​
请求参数​
下表仅列出该接口特有的请求参数和部分公共参数。完整公共请求参数请见公共参数。​
Query​
Action String 必选 示例值：StartVoiceChat​
接口名称。当前 API 的名称为 StartVoiceChat。​
​
Version String 必选 示例值：2024-12-01​
接口版本。当前 API 的版本为 2024-12-01。​
​
Body​
AppId String 必选 示例值：661e****543cf​
你的音视频应用的唯一标志，参看创建 RTC 应用获取或创建 AppId。​
​
RoomId String 必选 示例值：Room1​
智能体与真人进行通话的房间的 ID，需与真人用户使用客户端 SDK 进房时的使用的 RoomId 保持一致。​
​
TaskId String 必选 示例值：task1​
智能体任务 ID。由你自行定义，用于标识任务，且后续更新或结束此任务也需要使用该 TaskId。参数定义规则参看参数赋值规范。​
一个 AppId 的 RoomId 下 TaskId 是唯一的，AppId + RoomId + TaskId 共同构成一个全局唯一的任务标识，用来标识指定 AppId 下某个房间内正在运行的任务，从而能在此任务运行中进行更新或者停止此任务。​
不同 AppId 或者不同 RoomId 下 TaskId可以重复。​
​
Config Object 必选 示例值：-​
智能体交互服务配置，包括语音识别（ASR）、语音合成（TTS）、大模型(LLM)、字幕和函数调用（Function Calling）配置。​
ASRConfig Object 必选 示例值：-​
语音识别（ASR）相关配置。​
Provider String 必选 示例值：volcano​
语音识别服务的提供商。该参数固定取值：volcano，表示仅支持火山引擎语音识别服务。可使用以下模型：​
火山引擎流式语音识别（识别速度更快）​
火山引擎流式语音识别大模型（识别准确率更高）​
两者详细差异（如可识别语种、支持的能力等），请参见流式语音识别和流式语音识别大模型。​
​
ProviderParams Object 必选 示例值：-​
服务配置参数。​
不同服务，该结构包含字段不同，具体参看：​
ASRConfig.ProviderParams（火山引擎流式语音识别）​
ASRConfig.ProviderParams（火山引擎流式语音识别大模型）​
ASRConfig.ProviderParams（火山引擎流式语音识别）​
使用火山引擎流式语音识别时，识别速度快。你需要在 ASRConfig.ProviderParams里配置以下字段：​
Mode String 必选 示例值：smallmodel​
模型类型。该参数固定取值：smallmodel，表示火山引擎流式语音识别模型。​
​
AppId String 必选 示例值：93****21​
开通流式语音识别服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
Cluster String 必选 示例值：volcengine_streaming_common​
开通的流式语音识别服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​
​
​
ASRConfig-ProviderParams（火山引擎流式语音识别大模型）​
使用火山引擎流式语音识别时，识别速度稍慢，但识别准确度更高，你需要在 ASRConfig.ProviderParams里配置以下字段：​
Mode String 必选 示例值：bigmodel​
模型类型。该参数固定取值：bigmodel，表示火山引擎语音识别大模型。​
​
AppId String 必选 示例值：93****21​
开通火山引擎流式语音识别大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
AccessToken String 必选 示例值：MOaOaa_VQ6****1B34UHA4h5B​
与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。​
Access Token 查找方式，可参看如何获取 Token。​
​
ApiResourceId String 必选 示例值：volc.bigasr.sauc.duration​
火山引擎流式语音识别大模型服务开通类型：​
volc.bigasr.sauc.duration：小时版。​
volc.bigasr.sauc.concurrent：并发版。​
默认值为 volc.bigasr.sauc.duration。​
​
StreamMode Integer 必选 示例值：0​
语音识别结果输出模式：​
0：流式输出。即识别结果会分段、实时地返回。该模式下识别速度更快，适用于实时字幕场景。​
1：非流式输出。即在完整接收并处理完整个语音片段后，一次性返回最终的识别结果。该模式下识别准确率更高，适用于不需要即时反馈的离线转录场景（如会议录音）。​
默认值为 0。​
​
context String 必选 示例值："{\"hotwords\": [{\"word\": \"CO2\"},{\"word\": \"雨伞\"},{\"word\": \"鱼\"}]}"​
热词直传（通过JSON 字符串直接传入）。​
如果某些词汇（比如人名、产品名等）的识别准确率较低，可以将其作为热词传入 ASR 模型，提高输入词汇的识别准确率。例如传入"雨伞"热词，发音相似的词会优先识别为“雨伞”。​
大小限制：热词传入最大值为 200 tokens，超出会自动截断。​
格式要求（JSON 字符串）：​
​
   {​
  "hotwords": [​
    { "word": "热词1" },​
    { "word": "热词2" },​
    { "word": "热词3" }​
    // 更多热词​
  ]​
}​
​
​
boosting_table_id String 必选 示例值：26603****1-adad​
热词词表 ID。​
你需要先在火山语音技术_热词管理创建热词词表，并获取热词 ID。​
说明​
执行优先级：热词词表低于热词直传。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。​
​
boosting_table_name String 必选 示例值：语音打断关键词​
热词词表名称。​
你需要先在火山语音技术_热词管理创建热词词表，并获取热词词表的文件名称。​
说明​
执行优先级：热词词表低于热词直传。即如果同时配置了热词直传和热词词表，系统会先执行热词直传，后执行热词词表。​
​
context_history_length Integer 必选 示例值：0​
上下文轮次。将最近指定轮数会话内容送入流式语音识别大模型，有助于模型理解当前对话的背景，从而提升大模型识别准确性。​
取值范围为 0、[1,21)，0表示不开启该功能。​
​
correct_table_id String 必选 示例值：26603****1-adad​
替换词 ID。将 ASR 识别出的特定词汇替换为预期的标准词汇，可用于纠错、脱敏或别名替换等。比如“智立”替换为“致力”。​
你需要先在火山语音技术_替换词管理创建替换词，并获取替换词 ID。​
说明​
执行优先级：替换词低于热词。即如果一个词同时是热词和替换词的源词，则优先执行热词，再执行替换词。例如，原词为“智立”：​
若热词有“致力”，替换词要求“智立→治理”，那最后结果为 “致力”。​
若热词有“致力”，替换词要求“致力→治理”，那最后结果为 “治理”。​
​
correct_table_name String 必选 示例值：替换词​
替换词文件名称。你需要先在火山语音技术_替换词管理创建替换词，并获取替换词文件名称。​
说明​
执行优先级：替换词低于热词。即如果一个词同时是热词和替换词的源词，则优先执行热词，再执行替换词。例如，原词为“智立”：​
若热词有“致力”，替换词要求“智立→治理”，那最后结果为 “致力”。​
若热词有“致力”，替换词要求“致力→治理”，那最后结果为 “治理”。​
​
​
​
VADConfig Object 必选 示例值：-​
VAD（语音检测） 配置。​
SilenceTime Integer 必选 示例值：600​
判停时间。房间内真人用户停顿时间若高于该值设定时间，则认为一句话结束。​
取值范围为 [500，3000)，单位为 ms，默认值为 600。​
​
​
VolumeGain Float 必选 示例值：0.3​
音量增益值。增益值越低，采集音量越低。适当低增益值可减少噪音引起的 ASR 错误识别。​
默认值为 1.0，推荐值 0.3。​
​
InterruptConfig Object 必选 示例值：-​
语音打断配置。​
注意​
该参数仅当 Config.InterruptMode=0（即开启语音打断）时生效。​
InterruptSpeechDuration Integer 必选 示例值：500​
自动打断触发阈值。房间内真人用户持续说话时间达到该参数设定值后，智能体自动停止输出。​
取值范围为0，[200，3000]，单位为 ms，值越大智能体说话越不容易被打断。​
默认值为 0，表示用户发出声音且包含真实语义时即打断智能体输出。​
​
InterruptKeywords String[] 必选 示例值：["停止", "停下"]​
触发打断的关键词列表。当用户语音中识别到列表中的任意关键词时，智能体将立即停止输出。​
若配置该参数，只有识别到配置的打断词时才会触发打断，以降低背景环境人声无打断的干扰。使用该参数时，建议 InterruptSpeechDuration 设置为 0，避免自动打断触发阈值过高，导致关键词打断不生效。​
​
​
TurnDetectionMode Integer 必选 示例值：0​
新一轮对话的触发方式。​
0：服务端检测到完整的一句话后，自动触发新一轮对话。​
1：收到输入结束信令或说话字幕结果后，你自行决定是否触发新一轮会话。​
默认值为 0。​
该功能使用方法参看配置对话触发模式。​
​
​
TTSConfig Object 必选 示例值：-​
语音合成（TTS）相关配置。​
IgnoreBracketText Integer[] 必选 示例值：[1,2]​
过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。你需要在大模型 Prompt 中自行定义哪些内容放在指定标点符号内。具体使用方法参看过滤指定内容。​
支持取值及含义如下：​
1：中文括号（）​
2：英文括号()​
3：中文方括号【】​
4：英文方括号[]​
5：英文花括号{}​
默认值为空，表示不进行过滤。​
说明​
若大模型返回的内容中，包含标点符号里的内容在最末端，且为独立句子，其后无包含真实语义内容，该标点符号中的内容不会出现在字幕中。​
如大模型返回的内容为：当然可以，尽管问，我知无不言！(自信满满)。​
此时（自信满满）。不会出现在字幕里。​
如大模型返回的内容为：当然可以，尽管问，我知无不言(自信满满)！​
此时（自信满满）会出现在字幕里。​
​
Provider String 必选 示例值：-​
语音合成服务提供商，使用不同语音合成服务时，取值不同。支持使用的语音合成服务及对应取值如下：​
volcano（服务自上而下语音生成速度递减，情感表现力递增）​
火山引擎流式语音合成​
火山引擎语音合成大模型（非流式输入流式输出）​
火山引擎声音复刻大模型（非流式输入流式输出）​
volcano_bidirection（服务自上而下语音生成速度递减，情感表现力递增）​
火山引擎语音合成大模型（流式输入流式输出）​
火山引擎声音复刻大模型（流式输入流式输出）​
minimax​
MiniMax 语音合成​
​
ProviderParams Object 必选 示例值：-​
配置所选的语音合成服务。不同服务下，该结构包含字段不同：​
TTSConfig.ProviderParams（火山引擎语音合成）​
TTSConfig.ProviderParams（火山引擎语音合成大模型非流式输入流式输出）​
TTSConfig.ProviderParams（火山引擎语音合成大模型流式输入流式输出）​
TTSConfig.ProviderParams（火山引擎声音复刻大模型非流式输入流式输出）​
TTSConfig.ProviderParams（火山引擎声音复刻大模型流式输入流式输出）​
TTSConfig.ProviderParams（MiniMax 语音合成）​
TTSConfig.ProviderParams（火山引擎语音合成）​
使用火山引擎语音合成时，合成速度快，你需要在 TTSConfig.ProviderParams里配置以下字段：​
app Object 必选 示例值：-​
火山引擎语音合成服务应用配置。​
appid String 必选 示例值：94****11​
开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
cluster String 必选 示例值：volcano_tts​
已开通语音合成服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​
​
​
audio Object 必选 示例值：-​
火山引擎语音合成服务音频配置。​
voice_type String 必选 示例值：BV001_streaming​
音色。​
填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​
​
speed_ratio Float 必选 示例值：1.0​
语速。取值范围为 [0.2,3]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​
​
volume_ratio Float 必选 示例值：1.0​
音量。取值范围为 [0.1,3]，默认值为 1.0，通常保留一位小数即可。取值越大，音量越高。​
​
pitch_ratio Float 必选 示例值：1.0​
音高。取值范围为 [0.1,3]，默认值为 1.0，通常保留一位小数即可。取值越大，音调越高。​
​
​
​
TTSConfig.ProviderParams（火山引擎语音合成大模型非流式输入流式输出）​
使用火山引擎语音合成大模型服务非流式输入流式输出模式时，速度稍慢，但是更生动、更具情感表现力，你需要在 TTSConfig.ProviderParams里配置以下字段：​
app Object 必选 示例值：-​
火山引擎语音合成大模型服务应用配置。​
appid String 必选 示例值：94****11​
开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
cluster String 必选 示例值：volcano_tts​
已开通语音合成大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​
​
​
audio Object 必选 示例值：-​
火山引擎语音合成大模型服务音频配置。​
voice_type String 必选 示例值：zh_female_meilinvyou_moon_bigtts​
音色。​
填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​
​
pitch_rate Integer 必选 示例值：0​
音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。​
​
speech_rate Integer 必选 示例值：0​
语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。​
​
​
​
TTSConfig.ProviderParams（火山引擎语音合成大模型流式输入流式输出）​
使用火山引擎语音合成大模型服务流式输入流式输出模式时，生动、情感表现力最佳，你需要在 TTSConfig.ProviderParams里配置以下字段：​
app Object 必选 示例值：-​
火山引擎语音合成大模型服务应用配置。​
appid String 必选 示例值：94****11​
开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
token String 必选 示例值：OaO****ws1​
与语音合成大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。Access Token 查找方式，可参看如何获取 Token。​
​
​
audio Object 必选 示例值：-​
火山引擎语音合成大模型服务音频配置。​
voice_type String 必选 示例值：BV001_streaming​
音色。​
填入音色对应的标识 Voice_type，可在语音技术控制台购买音色后获取。​
​
pitch_rate Integer 必选 示例值：0​
音调。取值范围为 [-12,12]。默认值为 0。取值越大，音调越高。​
​
speech_rate Integer 必选 示例值：0​
语速。取值范围为[-50,100]，100 代表 2.0 倍速，-50 代表 0.5 倍速。默认值为 0。取值越大，语速越快。​
​
​
Additions Object 必选 示例值：-​
火山引擎语音合成大模型服务高级配置。​
enable_latex_tn Boolean 必选 示例值：true​
是否播报 Latex 公式。​
true：播报。 为true 时，disable_markdown_filter 也需为 true 才生效。​
false：不播报。​
默认值为 false。​
​
disable_markdown_filter Boolean 必选 示例值：true​
是否对 Markdown 格式内容进行过滤。​
true：过滤；例如，**你好**，会读为“你好”。​
false：不过滤。例如，**你好**，会读为“星星你好星星”。​
默认值为 false。​
​
enable_language_detector Boolean 必选 示例值：false​
是否自动识别语种。支持哪些语种？​
true：自动识别。​
false：不自动识别。​
默认值为 false。​
​
​
ResourceId String 必选 示例值：volc.service_type.10029​
调用服务的资源信息 ID，该参数固定取值：volc.service_type.10029；​
​
​
TTSConfig.ProviderParams（火山引擎声音复刻大模型非流式输入流式输出）​
使用火山引擎语音合成大模型服务非流式输入流式输出模式时，可自定义音色，且生成速度较快，你需要在 TTSConfig.ProviderParams里配置以下字段：​
app Object 必选 示例值：-​
火山引擎声音复刻大模型服务应用配置。​
appid String 必选 示例值：94****11​
开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
cluster String 必选 示例值：volcano_icl​
已开通声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。​
​
​
audio Object 必选 示例值：-​
火山引擎声音复刻大模型服务音频配置。​
voice_type String 必选 示例值：S_N****T7k1​
声音复刻声音 ID。你可登录语音技术控制台获取。​
​
speed_ratio Float 必选 示例值：1.0​
语速。取值范围为 [0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​
​
​
​
TTSConfig.ProviderParams（火山引擎声音复刻大模型流式输入流式输出）​
使用火山引擎语音合成大模型服务非流式输入流式输出模式时，可自定义音色，且更生动、情感表现力更佳，你需要在 TTSConfig.ProviderParams里配置以下字段：​
app Object 必选 示例值：-​
火山引擎声音复刻大模型服务应用配置。​
appid String 必选 示例值：94****11​
开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。​
​
token String 必选 示例值：OaO****ws1​
与开通声音复刻大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。Access Token 查找方式，可参看如何获取 Token。​
​
​
audio Object 必选 示例值：-​
火山引擎声音复刻大模型服务音频配置。​
voice_type String 必选 示例值：S_N****T7k1​
声音复刻声音 ID。你可登录语音技术控制台获取。​
​
speed_ratio Float 必选 示例值：1.0​
语速。取值范围为 [0.8,2]，默认值为 1.0，通常保留一位小数即可。取值越大，语速越快。​
​
​
Additions Object 必选 示例值：-​
火山引擎声音复刻大模型服务高级配置。​
enable_latex_tn Boolean 必选 示例值：true​
是否播报 Latex 公式。​
true：播报。 为true 时，disable_markdown_filter也需为 true才可生效。​
false：不播报。​
默认值为 false。​
​
disable_markdown_filter Boolean 必选 示例值：true​
是否对 Markdown 格式内容进行过滤。​
true：过滤；例如，**你好**，会读为“你好”。​
false：不过滤。例如，**你好**，会读为“星星你好星星”。​
默认值为 false。​
​
enable_language_detector Boolean 必选 示例值：false​
是否自动识别语种。​
true：自动识别。​
false：不自动识别。​
默认值为 false。​
​
​
ResourceId String 必选 示例值：volc.megatts.default​
调用服务的资源信息 ID，该参数固定取值：volc.megatts.default。​
​
​
TTSConfig.ProviderParams（MiniMax 语音合成）​
使用MiniMax语音合成时，你需要在 TTSConfig.ProviderParams里配置以下字段：​
Authorization String 必选 示例值：eyJhbG****SUzI1N​
API 密钥。前往 Minimax 账户管理-接口密钥获取。​
​
Groupid String 必选 示例值：983*****669​
用户所属组 ID。前往 Minimax 账号信息-基本信息获取。​
​
model String 必选 示例值：speech-01-turbo​
发起请求的模型版本：​
speech-01-turbo：最新模型，拥有出色的效果与时延表现。​
speech-01-240228：稳定版本模型，效果出色。​
speech-01-turbo-240228：稳定版本模型，时延更低。​
​
URL String 必选 示例值：https://api.minimax.chat/v1/t2a_v2​
请求语音合成 URL，该参数固定取值：https://api.minimax.chat/v1/t2a_v2。​
​
voice_setting Object 必选 示例值：-​
音频配置。​
speed Float 必选 示例值：1.0​
语速。取值越大，语速越快。​
取值范围为 [0.5,2]，默认值为1.0。​
​
vol Float 必选 示例值：1.0​
音量。取值越大，音调越高。​
取值范围为 (0,10]，默认值为 1.0。​
​
pitch Float 必选 示例值：0​
语调。取值越大，语调越高。​
取值范围为 [-12,12]，且必须为整数。​
默认值为 0，表示原音色输出。​
​
voice_id String 必选 示例值：male-qn-jingying​
系统音色编号/复刻音色编号。​
系统音色可前往 voice_setting.voice_id查询。​
克隆音色参看FAQ。​
注意​
voice_id 与 timber_weights必须设置其中一个。​
​
​
pronunciation_dict Object 必选 示例值：-​
特殊标注配置。可对特殊文字、符号指定发音。​
tone String[] 必选 示例值：["燕少飞/(yan4)(shao3)(fei1)","达菲/(da2)(fei1)"，"omg/oh my god"]​
用于替换需要特殊标注的文字、符号及对应的发音，可用于调整声调或指定其他字符的发音。格式为 "原文字/注音"，注音部分根据语言类型采用不同方式标注：​
英文注音：使用对应发音的英文单词，例如："omg/oh my god"。​
中文注音：使用拼音，并在每个音节后以括号标注声调，音调用数字表示：​
一声（阴平）：1​
二声（阳平）：2​
三声（上声）：3​
四声（去声）：4​
轻声：5​
例如，"燕少飞/(yan4)(shao3)(fei1)"、"达菲/(da2)(fei1)"。​
​
​
timber_weights Object[] 必选 示例值：-​
合成音色权重设置。可通过该参数设置多种音色混合，并调整每个具体音色权重。最多支持 4 种音色混合。​
注意​
timber_weights与 VoiceSetting.voice_id必须设置其中一个。​
voice_id String 必选 示例值：male-qn-jingying​
音色编号。当前仅支持系统音色，可前往 voice_setting.voice_id查询。​
​
weight Integer 必选 示例值：1​
权重。取值为整数，单一音色取值占比越高，合成音色越像。取值范围为[1,100]。​
​
​
stream Boolean 必选 示例值：false​
是否开启流式输出。​
false：不开启流式输出。​
true：开启流式输出。​
默认值为 false。​
​
language_booststring String 必选 示例值：auto​
增强指定小语种/方言场景下的语音表现。不同场景下取值及含义如下：​
不明确小语种类型：auto。取值为auto时，模型将自主判断小语种类型。​
小语种：​
Spanish：西班牙语​
French：法语​
Portuguese：葡萄牙语​
Korean：韩语​
Indonesian：印度尼西亚语​
German：德语​
Japanese：日语​
Italian：意大利语​
auto：自动模式​
方言：​
Chinese,Yue：粤语。Chinese,Yue仅当MiniMaxTTSConfig.model=speech-01-turbo时生效。​
默认值为空。​
​
​
​
​
LLMConfig Object 必选 示例值：-​
大模型相关配置。支持的大模型平台如下：​
LLMConfig（火山方舟平台）​
LLMConfig（Coze平台）​
LLMConfig（第三方大模型/Agent）​
LLMConfig（火山方舟平台）​
使用火山方舟平台时，你需要在 LLMConfig里配置以下字段：​
Mode String 必选 示例值：ArkV3​
大模型平台标识。使用火山方舟平台时，该参数固定取值： ArkV3。​
​
EndPointId String 必选 示例值：ep-22****212​
自定义推理接入点 EndPointId。当需要使用模型推理功能（如直接调用部署的基础模型）时，此参数为必填。​
可前往控制台创建或查询自定义推理接入点。​
注意​
EndPointId 与 BotId 不可同时填写，若同时填写，则 EndPointId 生效。​
当前仅支持自定义推理接入点，不支持预置推理接入点。​
​
BotId String 必选 示例值：botid****212​
应用 ID。需要使用方舟应用实验室功能时，为必填。​
可前往控制台创建或查询应用 ID。​
注意​
请确保你的应用使用的是自定义推理接入点，目前暂不支持预置推理接入点。​
​​​
EndPointId 与 BotId 不可同时填写，若同时填写，则 EndPointId 生效。​
​
Temperature Float 必选 示例值：0.1​
采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。​
取值范围为 (0,1]，默认值为 0.1。​
​
MaxTokens Integer 必选 示例值：1024​
输出文本的最大 token 限制。默认值为 1024。​
​
TopP Float 必选 示例值：0.3​
采样的选择范围，控制输出 token 的多样性。模型将从概率分布中累计概率超过该取值的标记中进行采样，以确保采样的选择范围不会过宽，值越大输出的 token 类型越丰富。​
取值范围为 [0,1]，默认值为 0.3。​
​
SystemMessages String[] 必选 示例值：["你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"]​
系统提示词。用于输入控制大模型行为方式的指令，定义了模型的角色、行为准则，特定的输出格式等。​
​
UserPrompts Object 必选 示例值：[ { "Role": "user", "Content": "你好" }, { "Role": "assistant", "Content": "有什么可以帮到你的？" }, { "Role": "user", "Content": "你是谁？" }, { "Role": "assistant", "Content": "我是你的智能问答助手。" }​
用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。​
UserPrompts 存储的对话轮数受 HistoryLength 控制。例如 UserPrompts 中预先存储了两轮对话，HistoryLength 设置为 3，用户已进行了三轮对话，第四轮会话开始时，UserPrompts 中存储的内容会被全部删除。​
注意​
UserPrompts 中 Role 的取值只包含 user 和assistant，且必须成对出现，否则大模型可能会出现未定义行为。​
​
HistoryLength Integer 必选 示例值：3​
历史问题轮数。默认值为 3。​
在调用该接口时需要确保所有 UserPrompts 和 SystemMessage 消息文本总长度不超过大模型上下文长度。​
例如：历史问题轮数为 3，使用 Skylark2-lite-8k 大模型，长度限制为 8k，UserPrompts 预先存储了两轮对话，用户输入了第一轮会话的问题，此时 SystemMessages+UserPrompts+第一轮会话问题总长度不超过8k。​
​
Tools Object[] 必选 示例值：-​
定义一组可供模型在 Function Calling 功能中调用的工具。​
目前仅支持函数作为工具。你需要提供每个函数的定义，模型会基于定义，在需要时生成调用特定函数的 JSON 指令。该功能使用方法参看 Function Calling。​
注意​
Function calling 功能不支持和联网插件或知识库插件同时使用。​
type String 必选 示例值：function​
工具类型。可取值及含义如下：​
function：函数调用。​
​
function Object 必选 示例值：-​
模型可以调用的工具列表。​
name String 必选 示例值：get_current_weather​
函数的名称。​
​
description String 必选 示例值：获取指定城市的天气信息​
对函数用途的描述，供模型判断何时以及如何调用该工具函数。​
​
parameters JSONMap 必选 示例值：-​
函数请求参数，以 JSON Schema 格式描述。具体格式请参考 JSON Schema 文档。​
​
{​
    "type": "object",​
    "properties": {​
        "location": {​
            "type": "string",​
            "description": "城市，如：北京",​
        },​
    },​
    "required": ["location"],​
}​
​
​
​
​
Prefill Boolean 必选 示例值：false​
将 ASR 中间结果提前送入大模型进行处理。​
true：开启。启用后，允许将 ASR 识别中间结果提前发送给大模型进行处理，以降低延时。​
false：关闭。等待 ASR 模块识别出相对完整的一句话后再送入大模型处理。​
默认值为 false。​
注意​
开启后会产生额外模型消耗。​
​
VisionConfig Object 必选 示例值：-​
视觉理解能力配置。如何使用视觉理解能力？​
注意​
该功能仅在使用 EndPointId（推理点）接入大模型，且推理点选择 vision 系列模型时才生效，如 Doubao-vision-pro-32k。​
Enable Boolean 必选 示例值：true​
是否开启视觉理解功能。​
false：不开启；​
true：开启。​
默认值为 false。​
​
SnapshotConfig Object 必选 示例值：-​
截图相关配置。截图送入大模型以供理解信息。​
StreamType Integer 必选 示例值：0​
截图流类型。​
0：主流。​
1：屏幕流。​
默认值为 0。​
​
ImageDetail String 必选 示例值：auto​
图片处理模式。取值及含义如下：​
high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。​
low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。​
auto：自动模式。根据图片分辨率，自动选择适合的模式。​
默认值为 auto。​
​
Height Integer 必选 示例值：640​
送入大模型截图视频帧高度，取值范围为 [0, 1792]，单位为像素。​
不填或传 0时自动修改为 360。​
传入大模型截图视频帧宽度自动按传入高度进行比例计算。​
​
Interval Integer 必选 示例值：1000​
相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。​
​
ImagesLimit Integer 必选 示例值：2​
单次送大模型截图数。取值范围为 [0, 50]。​
不传或传 0 时自动修改为 2。​
​
​
StorageConfig Object 必选 示例值：-​
截图存储配置。​
Type Integer 必选 示例值：0​
存储类型。​
0：按照 Base 64 编码存入服务端缓存，会话结束后自动删除。​
1：存储至 TOS 平台。使用 TOS 存储前需前往控制台开通该服务。​
​
TosConfig Object 必选 示例值：-​
TOS 存储配置。​
AccountId String 必选 示例值：acc****_id​
火山引擎平台账号 ID，例如：*********。查看路径参看查看和管理账号信息。​
注意​
此账号 ID 为火山引擎主账号 ID。​
若你调用 OpenAPI 鉴权过程中使用的 AK、SK 为子用户 AK、SK，账号 ID 也必须为火山引擎主账号 ID，不能使用子用户账号 ID。​
​
Region Integer 必选 示例值：0​
存储桶区域。不同存储桶区域对应的 Region 不同，具体参看 Region对照表。​
默认值为 0。​
注意​
该字段填入的存储桶区域需要与你在 TOS 平台创建存储桶时选择的区域相同。​
​
Bucket String 必选 示例值：b****t​
存储桶名称。前往控制台创建或查询。​
​
​
​
​
​
LLMConfig（Coze平台）​
使用 Coze 平台时，你需要在 LLMConfig 里配置以下字段。​
使用前确保智能体已发布为 API 服务。详情参考准备工作。​
Mode String 必选 示例值：CozeBot​
大模型平台名称。该参数固定取值：CozeBot。​
​
CozeBotConfig Object 必选 示例值：-​
Coze 智能体配置。​
Url String 必选 示例值：https://api.coze.cn​
请求地址。该参数固定取值：https://api.coze.cn​
​
BotId String 必选 示例值：73****68​
Coze 智能体 ID。​
可前往你需要调用的智能体开发页面获取。开发页面 URL 中 bot 参数后的数字即智能体ID。例如开发页面 URL 为：https://www.coze.cn/space/341/bot/73428668，则 BotId 为 73428668。​
​
APIKey String 必选 示例值：czu_UEE2mJn6****MHxLCVv9uQ7H​
Coze 访问密钥。​
你可以生成个人访问令牌以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考 OAuth 应用管理。​
注意​
创建个人访问令牌或 OAuth 应用时，你需要根据你的 Bot 使用场景勾选对应权限，否则会鉴权失败。​
​
UserId String 必选 示例值：123​
标识当前与智能体对话的用户，由你自行定义、生成与维护。UserId 用于标识对话中的不同用户，不同的 UserId，其对话的上下文消息、数据库等对话记忆数据互相隔离。如果不需要用户数据隔离，可将此参数固定为一个任意字符串，例如 123，abc等。​
​
HistoryLength Integer 必选 示例值：3​
历史问题轮数。默认值为 3。​
在调用该接口时需要确保作为上下文的用户消息和智能体消息文本总长度小于模型上下文长度。​
例如：历史问题轮数为 3，使用 Skylark2-lite-8k大模型，该模型上下文长度限制为 8k，询问第 10 个问题时，需保证第 10 个问题的长度与第八、九轮用户消息和智能体消息文本的总长度之和不得超过 8k。​
​
Prefill Boolean 必选 示例值：false​
将 ASR 中间结果提前送入大模型进行处理。​
开启该功能后可不等待 ASR 模块识别出完整的一句话再送入大模型处理，而是将 ASR 识别中间结果提前送入大模型进行处理以降低延时。​
true：开启。​
false：关闭。​
默认值为 false。​
注意​
开启后会产生额外模型消耗。​
​
EnableConversation Boolean 必选 示例值：false​
是否将上下文存储在 Coze 平台。​
若需要使用 Coze 平台上下文管理相关功能，如将指定内容添加到会话中，可开启此功能。功能开启后 RTC 不再存储上下文内容。​
false：不开启。​
true：开启。​
默认值为 false。​
注意​
EnableConversation 为 true 时会导致HistoryLength设置无效。​
​
​
​
LLMConfig（第三方大模型）​
使用第三方大模型时，你需要在 LLMConfig里配置以下字段：​
Mode String 必选 示例值：CustomLLM​
大模型平台名称。使用第三方大模型/Agent 时，该参数固定取值： CustomLLM。​
​
URL String 必选 示例值：https://test.com/path/to/app​
第三方大模型/Agent 的请求 URL，需要使用 HTTPS 域名，且必须符合火山引擎标准。​
验证是否符合标准：可前往体验 Demo->修改 AI 设定->第三方模型，并填入 URL 进行快速验证。​
若验证失败可前往文档接入第三方大模型/Agent，查看接口标准并通过验证工具查看详细报错。​
说明​
如果需要在每次请求时传递一些简单的、非敏感的参数（如 session_id），可以直接将它们作为查询参数拼接到此 URL 中。​
如需使用 HTTP 域名进行测试，可在下方 Feature 参数中填入 {"Http":true}，但无法保证服务质量。​
​
ModelName String 必选 示例值：name1​
第三方大模型/Agent 的名称。​
​
APIKey String 必选 示例值：pat*****123231​
Bearer Token 认证方式的大模型鉴权 Token。​
​
MaxTokens Integer 必选 示例值：1024​
输出文本的最大 token 限制。默认值为 1024。​
​
Temperature Float 必选 示例值：0.1​
采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为（0,1]，默认值为 0.1。​
​
TopP Float 必选 示例值：0.3​
采样的选择范围，控制输出 token 的多样性。模型将从概率分布中累计概率超过该取值的标记中进行采样，以确保采样的选择范围不会过宽，值越大输出的 token 类型越丰富。​
取值范围为[0,1]，默认值为 0.3。​
​
SystemMessages String[] 必选 示例值：["你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"]​
大模型 System 角色预设指令，可用于控制模型输出。​
​
UserPrompts Object 必选 示例值：[ { "Role": "user", "Content": "你好" }, { "Role": "assistant", "Content": "有什么可以帮到你的？" }, { "Role": "user", "Content": "你是谁？" }, { "Role": "assistant", "Content": "我是你的智能问答助手。" }​
大模型 User 角色预设 Prompt，可用于增强模型的回复质量，模型回复时会优先参考此处内容。​
UserPrompts 存储的对话轮数受 HistoryLength 控制。例如UserPrompts 中预先存储了两轮对话，HistoryLength 设置为 3，用户已进行了三轮对话，第四轮会话开始时，UserPrompts 中存储的内容会被全部删除。​
注意​
UserPrompts 中 Role 的取值只包含 user 和 assistant，且必须成对出现，否则大模型可能会出现未定义行为。​
​
HistoryLength Integer 必选 示例值：3​
历史问题轮数。默认值为 3。​
在调用该接口时需要确保所有 UserPrompts 和 SystemMessage 消息文本总长度小于大模型上下文长度。​
例如：历史问题轮数为 3，使用 Skylark2-lite-8k 大模型，长度限制为 8k，UserPrompts 预先存储了两轮对话，用户输入了第一轮会话的问题，此时 SystemMessages+UserPrompts+第一轮会话问题的总长度不超过 8k。​
​
Feature String 必选 示例值：{\"Http\":true}​
使用 HTTP 域名进行测试，该参数固定取值：{\"Http\":true}。​
​
Prefill Boolean 必选 示例值：false​
将 ASR 中间结果提前送入大模型进行处理：​
true：开启。将 ASR 识别中间结果提前送入大模型进行处理，以降低延时。​
false：关闭。需等待 ASR 模块识别出完整的一句话后，再将其整体送入大模型处理。​
默认值为 false。​
注意​
开启后会产生额外模型消耗。​
​
Custom String 必选 示例值：-​
自定义 JSON 字符串，可传入业务自定义参数。​
​
VisionConfig Object 必选 示例值：-​
视觉理解能力配置。​
该功能使用说明参看视觉理解能力。​
Enable Boolean 必选 示例值：true​
是否开启视觉理解功能。​
false：不开启；​
true：开启。​
默认值为false。​
​
SnapshotConfig Object 必选 示例值：-​
传给大模型截图相关配置。​
StreamType Integer 必选 示例值：0​
截图流类型。​
0：主流。​
1：屏幕流。​
默认值为 0。​
​
ImageDetail String 必选 示例值：auto​
图片处理模式。取值及含义如下：​
high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。​
low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。​
auto：自动模式。根据图片分辨率，自动选择适合的模式。​
默认值为 auto。​
​
Height Integer 必选 示例值：640​
送入大模型视频帧高度，取值范围为 [0, 1792]，单位为像素。​
不填或传 0时自动修改为 360。​
传入大模型视频帧宽度自动按传入高度计算。​
​
Interval Integer 必选 示例值：1000​
相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。​
​
ImagesLimit Integer 必选 示例值：2​
单次送大模型图片数。取值范围为 [0, 50]。​
不传或传 0时自动修改为 2。​
​
​
StorageConfig Object 必选 示例值：-​
截图存储相关配置。​
Type Integer 必选 示例值：0​
存储类型。​
0：Base 64 编码存入本地，会话结束后自动删除。​
1：TOS。使用 TOS 存储前需前往控制台开通该服务。​
​
TosConfig Object 必选 示例值：-​
TOS 存储配置。​
AccountId String 必选 示例值：acc****_id​
火山引擎平台账号 ID，例如：*********。​
火山引擎平台账号 ID 查看路径参看查看和管理账号信息。​
此账号 ID 为火山引擎主账号 ID。​
若你调用 OpenAPI 鉴权过程中使用的 AK、SK 为子用户 AK、SK，账号 ID 也必须为火山引擎主账号 ID，不能使用子用户账号 ID。​
​
Region Integer 必选 示例值：0​
不同存储平台支持的 Region 不同，具体参看 Region对照表​
默认值为0。​
​
Bucket String 必选 示例值：b****t​
存储桶名称。前往控制台创建或查询。​
​
​
​
​
​
​
SubtitleConfig Object 必选 示例值：-​
配置字幕回调。​
可通过客户端或服务端接收回调消息，消息格式为二进制，使用前需解析。详细说明参看实时对话式 AI 字幕。​
DisableRTSSubtitle Boolean 必选 示例值：false​
是否关闭房间内客户端字幕回调。​
true：关闭，即不通过客户端接收字幕回调消息。​
false：开启，通过客户端接收字幕回调消息。开启后，在客户端实现监听 onRoomBinaryMessageReceived（以 Android 为例），即可接收字幕回调消息。​
默认值为 false。​
注意​
如需通过服务端接收字幕回调，请配置 ServerMessageUrl 和 ServerMessageSignature。​
​
ServerMessageUrl String 必选 示例值：https://example-domain.com/vertc/subtitle​
接收字幕结果的 URL 地址。通过服务端接收字幕回调时必填。​
接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​
可通过 curl -v http(s)://yourexample-domain.com/vertc/subtitle 命令对域名进行快速校验：​
若返回 HTTP 状态码为 301 或 302，则说明域名不可用，POST 方法可能会重定向为 GET。​
若返回 307 或 308 则说明域名可用，且始终保持 POST 方法。​
注意​
如果你同时通过该接口接收任务状态变化回调和字幕回调，请确保在 ServerMessageUrl 和 ServerMessageURLForRTS中填入相同的 URL，否则会导致无法接收任务状态回调或字幕回调。​
​
ServerMessageSignature String 必选 示例值：b46ab5f1d8ad6a​
鉴权签名。通过服务端接收字幕回调时必填。​
在接收到字幕结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​
​
SubtitleMode Integer 必选 示例值：0​
字幕回调时是否需要对齐音频时间戳。​
0：对齐音频时间戳。​
1：不对齐音频时间戳。取 1 时可更快回调字幕信息。​
默认值为 0。​
​
​
FunctionCallingConfig Object 必选 示例值：-​
使用 Function calling 功能时，从服务端接受函数工具返回的信息指令配置。​
Function calling 功能使用详情参看功能说明文档。​
注意​
该功能仅在使用火山方舟平台时生效。​
ServerMessageUrl String 必选 示例值：https://example-domain8080/m2​
服务端接收 Function Calling 函数工具返回的信息指令的 URL 地址。功能使用详情参看服务端实现 Function Calling 功能。​
接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​
ServerMessageUrl 和 ServerMessageSignature均填写正确才能开启该功能。​
​
ServerMessageSignature String 必选 示例值：TestSignature​
鉴权签名。​
在接收到函数调用信息结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​
​
​
InterruptMode Integer 必选 示例值：0​
是否启用语音打断：​
0：开启语音打断。开启后，一旦检测到用户发出声音，智能体立刻停止输出。​
1：关闭语音打断。关闭后，智能体说话期间，用户语音输入内容会被忽略不做处理，不会打断智能体讲话。​
默认值为 0。​
​
​
AgentConfig Object 必选 示例值：-​
智能体相关配置，包括欢迎词、任务状态回调等信息。​
TargetUserId String[] 必选 示例值：["user1"]​
真人用户 ID。需使用客户端 SDK 进房的真人用户的 UserId。仅支持传入一个 UserId，即单个房间内，仅支持一个用户与智能体一对一通话。​
​
WelcomeMessage String 必选 示例值：Hello​
智能体启动后的欢迎词。​
​
UserId String 必选 示例值：BotName001​
智能体 ID，用于标识智能体。​
由你自行定义、生成与维护，支持由大小写字母（A-Z、a-z）、数字（0-9）、下划线（_）、短横线（-）、句点（.）和 @ 组成，最大长度为 128 个字符。​
若不填则默认值为 voiceChat_$(TargetUserId)_$(timestamp_now)。​
注意​
同一 AppId 下 UserId 建议全局唯一。若同一 AppId 下不同房间内智能体名称相同，会导致使用服务端回调的功能异常，如字幕、Function Calling 和任务状态回调功能。​
UserId 取值与 TargetUserId不能重复。​
​
EnableConversationStateCallback Boolean 必选 示例值：false​
是否接收智能体状态变化回调，获取智能体关键状态，比如​
“聆听中”、“思考中”、“说话中”、“被打断”等。功能详细说明，参看接收状态变化消息。​
true：接收。可通过客户端或服务端接收智能体状态变化回调。​
通过客户端接收：还需在客户端实现监听回调 onRoomBinaryMessageReceived（以 Android 端为例）。​
通过服务端接收：还需配置字段 ServerMessageURLForRTS 和 ServerMessageSignatureForRTS。​
false：不接收。​
默认值为 false。​
​
ServerMessageSignatureForRTS String 必选 示例值：b46ab5f1d8ad6a​
鉴权签名。通过服务端接受任务状态变化回调时必填。​
你可传入该鉴权参数，在接收到回调结果后，与结果中的 signature字段值进行对比以进行鉴权验证。​
​
ServerMessageURLForRTS String 必选 示例值：https://example-domain.com/vertc/callback​
接收任务状态变化的 URL 地址。通过服务端接受任务状态变化回调时必填。​
接收结果的 URL 必须以域名开头。如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。​
注意​
如果同时通过该接口接收任务状态变化回调和字幕回调，请确保在 ServerMessageURLForRTS 与 SubtitleConfig.ServerMessageUrl中填入相同的 URL，否则会导致无法接收任务状态回调或字幕回调。​
​
UseLicense Boolean 必选 示例值：true​
是否为 License 用户。​
true：是；​
false：否。​
默认值为 false。​
若为 License 用户，你需要：​
联系技术支持开通白名单。​
前往控制台硬件场景服务获取你需要的 ASR、TTS 和 LLM 相关参数值。注意你必须使用在此处获取的 ASR、TTS 和 LLM 参数值，智能体才能正常工作。​
如果你使用大模型流式语音识别和大模型语音合成，在调用 StartVoiceChat 接口时，ASRConfig.ProviderParams.AccessToken 和 TTSConfig.ProviderParams.AccessToken无需填入。​
​
Burst Object 必选 示例值：-​
音频快速发送配置。​
开启该功能后，可通过快速发送音频实现更好的抗弱网能力。​
说明​
该功能仅在嵌入式硬件场景下支持，且嵌入式 Linux SDK 版本不低于 1.57。​
Enable Boolean 必选 示例值：true​
是否开启音频快速发送。​
false：开启。​
true：关闭。​
默认值为 false。​
​
BufferSize Integer 必选 示例值：10​
接收音频快速发送片段时，客户端可缓存的最大音频时长。取值范围为[10,3600000]，单位为 ms，默认值为 10。​
​
Interval Integer 必选 示例值：10​
音频快速发送结束后，其他音频内容发送间隔。取值范围为[10,600]，单位为 ms，默认值为10。​
​
​
​
返回参数​
本接口无特有的返回参数。公共返回参数请见返回结构。​
其中返回值 Result 仅在请求成功时返回 ok，失败时为空。​
完整请求结构
火山引擎流式语音识别
火山引擎流式语音识别大模型
火山引擎语音合成
火山引擎语音合成大模型流式输入流式输出
MiniMax 语音合成
火山方舟大模型
第三方大模型
输入示例

POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01

{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "task1",
    "Config": {
        "ASRConfig": { // 以火山引擎流式语音识别大模型为例，其他语音识别服务配置参看其他示例。
            "Provider": "volcano",
            "ProviderParams": {
                "Mode": "bigmodel",
                "AppId": "93****21",
                "AccessToken": "MOaOa*****HA4h5B",
                "ApiResourceId": "volc.bigasr.sauc.duration",
                "StreamMode": 0
            }
        },
        "TTSConfig": { // 以火山引擎语音合成流式输入流式输出合成为例，其他语音合成服务配置参看其他示例。
            "Provider": "volcano_bidirection",
            "ProviderParams": {
                "app": {
                    "appid": "94****11",
                    "token": "OaO****ws1"
                },
                "audio": {
                    "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
                    "speech_rate": 0,
                    "pitch_rate": 0
                },
                "ResourceId": "volc.service_type.10029"
            }
        },
        "LLMConfig": { // 以火山方舟大模型为例，其他大模型服务配置参看其他示例。
            "Mode": "ArkV3",
            "EndPointId": "epid****212",
            "MaxTokens": 1024,
            "Temperature": 0.1,
            "TopP": 0.3,
            "SystemMessages": [
                "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
            ],
            "UserPrompts": [
                {
                    "Role": "user",
                    "Content": "你好"
                },
                {
                    "Role": "assistant",
                    "Content": "有什么可以帮到你的？"
                }
            ],
            "HistoryLength": 3
        }
    },
    "AgentConfig": {
        "TargetUserId": [
            "user1"
        ],
        "WelcomeMessage": "Hello",
        "UserId": "BotName001"
    }
}
输出示例

{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "20230****10420",
        "Action": "StartVoiceChat",
        "Version": "2024-12-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}

更新智能体 UpdateVoiceChat
最近更新时间：2025.06.11 17:42:16
首次发布时间：2024.12.31 16:11:14

我的收藏
有用
无用
本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考历史版本。

在实时音视频通话场景中，若你需要对智能体进行操作，比如在智能体进行语音输出时进行打断，可以通过调用此接口实现。

使用说明
调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看调用方法。

注意事项
请求频率：单账号下 QPS 不得超过 60。
该接口请求接入地址仅支持 rtc.volcengineapi.com。
请求说明
请求方式：POST
请求地址：https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
去调试
请求参数
下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见公共参数。

Query
参数
类型
是否必选
示例值
描述
Action
String
是
UpdateVoiceChat
接口名称。当前 API 的名称为 UpdateVoiceChat。
Version
String
是
2024-12-01
接口版本。当前 API 的版本为 2024-12-01。
Body
参数
类型
是否必选
示例值
描述
AppId
String
是
661e****543cf
你的音视频应用的唯一标志，参看创建 RTC 应用获取或创建 AppId。
RoomId
String
是
Room1
房间的 ID，是房间的唯一标志，由你自行定义、生成与维护，参数定义规则参看参数赋值规范。
TaskId
String
是
Task1
智能体任务 ID
Command
String
是
interrupt
更新指令

interrupt：打断智能体。
function：传回工具调用信息指令。
ExternalTextToSpeech ： 传入文本信息供 TTS 音频播放。使用方法参看自定义语音播放。
ExternalPromptsForLLM：传入自定义文本与用户问题拼接后送入 LLM。
ExternalTextToLLM：传入外部问题送入 LLM。根据你设定的优先级决定替代用户问题或增加新一轮对话。
FinishSpeechRecognition：触发新一轮对话。
Message
String
否
"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
工具调用信息指令。

注意

Command 取值为 function、ExternalTextToSpeech、ExternalPromptsForLLM和ExternalTextToLLM时，Message必填。
当 Command 取值为 function时，Message 格式需为 Json 转译字符串，例如：
"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
其他取值时格式为普通字符串，例如你刚才的故事讲的真棒。"
当 Command 取值为 ExternalTextToSpeech时，message 传入内容建议不超过 200 个字符。
InterruptMode
Integer
否
1
传入文本信息或外部问题时，处理的优先级。

1：高优先级。传入信息直接打断交互，进行处理。
2：中优先级。等待当前交互结束后，进行处理。
3：低优先级。如当前正在发生交互，直接丢弃 Message 传入的信息。
注意

当 command 为 ExternalTextToSpeech 或 ExternalTextToLLM 时为该参数必填。

返回参数
本接口无特有的返回参数。公共返回参数请见返回结构。
其中返回值 Result 仅在请求成功时返回 ok,失败时为空。

请求示例
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "User1",
    "Command": "interrupt"
}
返回示例
{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "20230****10420",
        "Action": "UpdateAudioBot",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}关闭智能体 StopVoiceChat
最近更新时间：2025.02.12 15:24:08
首次发布时间：2024.12.31 16:11:14

我的收藏
有用
无用
本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考历史版本。

在实时音视频通话场景中，若你需要结束智能体的语音聊天服务，可以通过调用此接口实现。

使用说明
调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看调用方法。

注意事项
请求频率：QPS 不得超过 60。
该接口请求接入地址仅支持 rtc.volcengineapi.com。
请求说明
请求方式：POST
请求地址：https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01
调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
去调试
请求参数
Query
参数
类型
是否必选
示例值
描述
Action
String
是
StopVoiceChat
接口名称。当前 API 的名称为 StopVoiceChat。
Version
String
是
2024-12-01
接口版本。当前 API 的版本为 2024-12-01。
Body
参数
类型
是否必选
示例值
描述
AppId
String
是
661e****543cf
你的音视频应用的唯一标志，参看获取 AppId
RoomId
String
是
Room1
房间的 ID，是房间的唯一标志。赋值规则参看参数赋值规范。
TaskId
String
是
task1
智能体任务 ID
返回参数
本接口无特有的返回参数。公共返回参数请见返回结构。
其中返回值 Result 仅在请求成功时返回 ok,失败时为空。

请求示例
POST https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "task1"
}
返回示例
{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "Your_Re20230****10420questId",
        "Action": "StopVoiceChat",
        "Version": "2024-12-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}对话式 AI 功能咨询
最近更新时间：2025.06.19 13:27:45
首次发布时间：2025.05.22 21:32:08

我的收藏
有用
无用
实时对话 AI 是否支持输入文本来触发AI对话?
目前 AI 方案不支持文字触发 AI 问答，目前仅支持语音麦克风采集问答。

实时对话 AI 支持给用户打电话吗?
不支持传统运营商电话，只支持类似微信语音电话类型的网络电话(SIP)。运营商的电话拨打需要业务自行和运营商沟通集成，RTC 不提供支持。网络电话支持通过 RTC Linux SDK 服务器版 接入业务自有网络电话系统实现音频流中转，但具体方案需要业务自行实现。

实时对话式 AI 如何记忆对话内容?
调用 StartVoiceChat 接口时，配置参数 LLMConfig.HistoryLength，可以记忆对话内容。

实时对话式 AI 可以基于 WebSocket 的接入吗？
实时对话式 AI 目前暂不支持 WebSocket 接入，需使用通过火山引擎 RTC SDK 接入。

实时对话式 AI 支持几人通话？
目前仅支持 1v1，即一个客户端用户、一个智能体。

启动智能体的 AppId、RoomId、UserId 是随机定义的吗？
AppId：每个应用（App）的唯一标识符，AppId 由火山分配，不可自定义。
RoomId：智能体与真人通话的房间 ID，必须与真人用户使用客户端 SDK 进房时使用的 RoomId 保持一致。
UserId：智能体 ID，用于标识智能体。由你自行定义，支持由大小写字母（A-Z、a-z）、数字（0-9）、下划线（_）、短横线（-）、句点（.）和 @ 组成，最大长度为 128 个字符。
实时对话式 AI 是否可以把图片信息传递给多模态的大模型?
暂不不支持传入图片。

房间只有一个用户，没有智能体，会自动关闭房间吗？
如果没有主动退出房间，在 Token 过期前不会自动关闭房间。

启动智能体后，可以远程控制智能体说话吗？
不支持。

实时对话式 AI 互动支持多少并发？
RTC 关于并发目前没有限制。

实时对话式 AI 是否支持使用第三方服务（比如TTS、ASR）？
部分服务支持使用第三方的，具体支持情况如下：

LLM：支持使用第三方大模型或 Agent，但是需要满足火山引擎规范。具体请参见接入第三方大模型或 Agent。
TTS：支持使用 MiniMax 语音合成服务。
ASR：不支持使用第三方服务，仅支持火山引擎的 ASR 服务。
实时对话式 AI 音色是否可以直接集成？
可以。在语音技术控制台可以购买音色，获取 voice_type 值后填入 StartVoiceChat 接口。不同服务下具体支持的音色不同，支持音色详情参看不同服务下的音色列表。

实时对话式 AI 支持自定义音色吗？
支持。可参看火山声音复刻大模型（非流式输入流式输出）自定义音色。

能否获得 ASR、TTS 和 LLM 模块的内容？
可以。可通过字幕功能接收，具体请参见字幕功能。

是否可以实现对话中查询天气等能力？
可以。可参看 Function Calling功能实现。

是否支持获取大模型的推理过程？
不支持。

如何存储真人用户和智能体的音频对话内容？
可使用云端录制功能存储，详情参看关于云端录制。

如何调节房间内智能体和真人用户的说话音量？
可通过 setRemoteAudioPlaybackVolume 接口调节本地播放的所有音频的音量。

不同房间模式下，智能体的声音高低不同？
不同房间模式下，对声音的处理策略不同，会导致不同场景下的声音高低不同。

智能体能否存储上一次对话内容？
可在启动智能体任务前，将上一次对话内容传入 LLMConfig.UserPrompts 作为对话上下文。

智能体启动后，ASR、TTS、LLM 和 Agent 配置能否修改？
无法在任务启动时修改。需要先结束当前任务，再使用新的配置重新启动任务。

QPS 限制是主账号和子账号共用的吗？
是的。单用户 QPS 限制是指火山引擎主账号及其子账号每秒调用特定 API 的频率上限。例如，StartVoiceChat接口的单用户 QPS 限制为 60 次/秒，假设用户有 1 个主账号，该主账号有 4 个子账号，如果这 5 个账号同时调用StartVoiceChat接口，则这 5 个账号的调用频率加起来不能超过 60 次/秒。

是否支持语音唤醒？
不支持。

如何暂时关闭智能体？
可以使用打断功能 组合静音麦克风暂停与智能体的交互。

是否可以设置智能体人设？
可以。可以通过大模型配置中的 SystemMessages 配置。详情可参看大模型配置。

能否设置人说话不自动打断智能体回复？
可以使用配置对话触发模式功能手动判断新一轮对话触发的时机。

对话式 AI 能否接入知识库？
可以。具体操作请参看对话式 AI 如何接入知识库 RAG？

对话过程中能否人工介入修改智能体的回复？
可以。具体操作请参看自定义语音播放。

是否可以多个智能体设备 ASR、TTS、LLM 使用相同账号和配置？
可以。单需要注意并发数，具体 QPS 限制请参考各个服务官网。

调用 StartVoiceChat 智能体接口时，是否支持海外请求地址？
不支持。

Function Calling（流式返回结果） 和 Function Calling（非流式返回结果） 有什么区别？
Function Calling（流式返回结果） 是指在调用 Function Calling 接口时，返回结果是流式返回的。Function Calling（非流式返回结果） 是指在调用 Function Calling 接口时，返回结果是一次性返回的。不同模型下返回的方式不同，只有在使用 doubao-1.5 代系模型和 DeepSeek 模型时，按照流式返回 Function Calling 结果，使用 doubao 非1.5 代系模型时，按照非流式返回 Function Calling 结果。

大模型推理内容能否保存？
不支持。

如何实现等待模型返回内容时，智能体不被新的语音输入打断？
可以使用配置对话触发模式选择手动触发新一轮对话，智能体未接受触发新一轮对话开始信令时，不会被新的语音输入打断。实时对话式 AI 场景，如果你希望智能体具备联网搜索能力，可通过集成火山方舟平台已开启联网插件的应用（Bot）来实现。

注意

联网插件和 Function calling 不能同时使用。
如果通过 StartVoiceChat 接入 Coze 智能体实现对话式 AI，目前暂不支持使用 Coze 平台提供的联网插件。
具体实现方式如下：

前往火山方舟_我的应用，创建应用并启用联网插件。

如果对联网搜索的实时性要求较高，建议关闭高级配置中的智能改写功能，以节省返回耗时。

Image

调用 StartVoiceChat 接口时，将 LLMConfig 部分需要进行如下关键配置：

Mode：设置为 ArkV3，指定使用火山方舟平台。
BotId：填入已配置联网插件的应用 ID。
（推荐）在LLM Config 里面增加透传参数 Metadata，实现在智能体触发联网后输出安抚语句（如“正在检索...”）。
"LLMConfig": {
            "Mode": "ArkV3",
            "BotId": "bot-****212",
            "Metadata": {
            "emit_intention_signal_extra": "true"， /*正在搜索提前返回，metedata参数透传*/
            }，
            "MaxTokens": 1024,
            "Temperature": 0.1,
            "TopP": 0.3,
            "SystemMessages": [
                "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
            ],
            "UserMessages": [
                "user:\"你是谁\"",
                "assistant:\"我是问答助手\"",
                "user:\"你能干什么\"",
                "assistant:\"我能回答问题\""
            ],
            "HistoryLength": 3
        }
    }在实时对话式 AI 场景，你可以通过集成方舟应用或 Coze 智能体来接入知识库（如私有数据、领域专业知识等），从而允许智能体可以利用外部数据源生成更精准、更相关的回复。

方式1：通过方舟应用（Bot）接入知识库
LLM 使用火山方舟平台的应用，并为应用配置知识库插件。

注意

知识库插件和 Function calling 不能同时使用。

具体实现方式如下：

前往火山方舟_我的应用，创建应用并配置知识库插件（需先创建知识库）。
Image
调用 StartVoiceChat 接口时，LLMConfig 部分需要注意如下关键配置：
Mode：设置为 ArkV3，指定使用火山方舟平台。
BotId：填入已配置知识库插件的应用 ID。
方式2：通过 Coze 智能体接入知识库
LLM 使用 Coze 平台的智能体，并为智能体配置知识库。

创建一个 Coze 智能体，并为智能体配置知识库。如何为智能体添加知识？
调用 StartVoiceChat 接口时，LLMConfig 部分需注意如下关键配置：
Mode：设置为 CozeBot，指定使用 Coze 平台。
BotId：填入已配置知识库的 Coze 智能体 ID。
请求示例（LLMConfig 部分）：

"LLMConfig": { // 大模型配置 - 使用Coze平台
            "Mode": "CozeBot",
            "CozeBotConfig": {
                "Url": "https://api.coze.cn", // Coze API 固定地址
                "BotId": "YOUR_COZE_BOT_ID",   // 已配置知识库的 Coze 智能体 ID
                "APIKey": "YOUR_COZE_API_KEY", // 你的Coze API访问密钥 (PAT或OAuth token)
                "UserId": "user_for_coze_conversation_context", // 与智能体对话的用户ID，由你自己定义
                "HistoryLength": 5,          // 可选, 保留的历史对话轮数，默认为3
                "Prefill": false,            // 可选, 是否提前将ASR中间结果送入LLM，默认为false
                "EnableConversation": false  // 可选, 是否将上下文存储在Coze平台，默认为false
            }
        }如何提升语音识别准确性？
最近更新时间：2025.05.19 14:36:21
首次发布时间：2025.05.16 19:18:11

我的收藏
有用
无用
实时对话式 AI 场景中，准确的语音识别结果能够让智能体更好地理解用户意图，从而提供更精准的服务。本文将介绍如何提高 RTC 实时对话式 AI 场景中语音识别的准确性。

选择合适的 ASR 模型
实时对话式 AI 场景，支持使用火山引擎以下模型用于语音识别，你可以按需选择合适的 ASR 模型：

流式语音识别大模型：识别准确性更高。
流式语音识别：识别速度更快。
调整音量增益
在嘈杂环境下，可尝试将 VolumeGain 调低，以减少背景噪音对 ASR 识别的干扰。
增益值越低，采集音量越低，有助于滤除部分环境噪音。但是，过低的增益也可能导致有效语音信号过弱。

调用 StartVoiceChat 时，传入 ASRConfig. VolumeGain。

"ASRConfig": {
    // ... ProviderParams ...
    "VolumeGain": 0.3  // 默认 1.0
}
调整 VAD 配置
VAD 用于判断用户是否开始说话以及何时结束说话。不合理的 VAD 配置可能导致句子被错误地截断或合并，从而影响识别准确率。
调用 StartVoiceChat 时，传入 ASRConfig.VADConfig.SilenceTime。

"VADConfig": {
    "SilenceTime": 800 // 如果用户语速较慢或停顿较多，可适当调大
}
使用热词
对于业务中常见的专有名词、人名、产品名、特定术语等，ASR 可能难以准确识别。通过配置热词，可以显著提高这些词汇的识别准确率。

仅火山引擎流式语音识别大模型支持热词功能。

具体配置如下：

方式 1：热词直传
方式 2：传入热词词表
直接在 API 请求中通过 JSON 字符串传入一个或多个热词。 例如："{"hotwords": [{"word": "火山引擎"},{"word": "实时音视频"},{"word": "豆包"}]}"

限制：最大 200 tokens，超出会自动截断。
配置：调用 StartVoiceChat 时，在 ProviderParams 中传入 context。
配置示例：
"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "context": "{"hotwords": [{"word": "智能客服"},{"word": "数字人"},{"word": "火山大陆"}]}"
}



使用替换词
替换词功能可以将 ASR 识别出的特定词汇替换为预期的标准词汇，常用于纠错、脱敏或别名替换。比如“二零二三年”替换为“2023年”。

仅火山引擎流式语音识别大模型支持替换词功能。
替换词的执行优先级低于热词。即如果一个词同时是热词和替换词的源词，优先执行热词，再执行替换词。例如，原词为“智立”：
- 若热词有“致力”，替换词要求“智立→治理”，最后结果为 “致力”。
- 若热词有“致力”，替换词要求“致力→治理”，最后结果为 “治理”。
具体配置如下：

在 火山语音技术_替换词管理创建替换词表，并获取替换词 ID 或文件名称。

调用 StartVoiceChat 时，在 ProviderParams 中传入 correct_table_id 或 correct_table_name。

correct_table_id 和 correct_table_name 不可同时设置。

"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "correct_table_id": "YOUR_CORRECT_TABLE_ID"
    // 或 "correct_table_name": "YourCorrectTableName"
}
使用上下文轮次
将最近指定轮次的对话内容作为上下文信息送入流式语音识别大模型，有助于模型理解当前对话的背景，从而可能提升对后续输入的识别准确性。

仅火山引擎流式语音识别大模型支持上下文轮次功能。
增加上下文轮数可能会略微增加处理开销，需根据实际效果进行调整。
调用 StartVoiceChat 时，在 ProviderParams 中传入 context_history_length。

"ProviderParams": {
    "Mode": "bigmodel",
    // ... AppId, AccessToken ...
    "context_history_length": 3 // 示例：将最近3轮对话作为上下文传入流式语音识别大模型。取值：0(不开启)，或 [1, 21)之间的整数
}如何提升多语言 AI 交互体验
最近更新时间：2025.06.10 18:35:22
首次发布时间：2025.06.10 18:35:22

我的收藏
有用
无用
本文将介绍在不同语种场景中，启动智能体任务时，如何配置 ASR（语音识别）、LLM（大模型）和 TTS（语音合成）模块，提升智能体的多语言交互体验。

中外混合场景
该场景适用于 AI 陪聊等应用，真人用户自由切换输入语言，智能体自动识别真人用户输入语种并使用相同语种回答。你可按照以下推荐操作配置 StartVoiceChat 接口的 ASR、LLM 和 TTS模块。

配置 ASR 模块
你需要选择流式语音识别大模型服务进行语音识别。
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选流式语音识别大模型。大模型支持自动识别语言，无需额外配置。支持中英及方言识别，详细支持语种列表参看支持语种。
alt服务配置完成后，可前往语音识别配置查看具体的调用参数和示例。

配置 LLM 模块
你可以选择使用火山方舟、Coze 平台和第三方大模型进行用户输入解析。请前往大模型配置查看不同方案下具体的调用参数和示例。
为增强智能体在多语言场景下的表现，可在配置大模型时适当增加多语言处理规则。使用火山方舟平台和第三方大模型时通过SystemMessages参数传入规则，使用 Coze 平台时通过智能体编排页人设与回复逻辑处传入规则。
你可参看以下示例进行传入：

"SystemMessages": [
    "输出语种自动匹配输入语种。",
    "乱码输入检测规则：1. 非中英字符触发乱码提示，2：乱码响应模板："这是乱码"。",
    "输出英文需语法正确，且表达自然。"
]
配置 TTS 模块
在收到 LLM 生成回复后，你需要选择语音合成大模型服务进行语音合成。
1. 前往控制台应用管理，新建应用或编辑你已创建的应用，勾选语音合成大模型。
2. 前往语音合成大模型控制台点击音色购买，购买支持指定语种的音色。支持英、日、西班牙等多国语言，详细列表参看音色列表。
alt服务配置完成后，请前往语音合成配置查看具体的调用参数和示例。
如果存在特定内容无需语音合成或过滤指定标签等场景，可使用控制播放内容功能。

纯外语场景
该场景适用于外语陪练等应用，智能体与真人用户使用外语进行对话。你可按照以下推荐操作配置 StartVoiceChat 接口的 ASR、TTS 和 LLM 模块。

配置 ASR 模块
你可以选择使用流式语音识别或流式语音识别大模型服务进行语音识别。

使用流式语音识别时（识别速度快）：
前往控制台应用管理，新建应用或编辑你已创建的应用，在流式语音识别服务中勾选你需要使用的语种服务。支持英、日、法等多国语言，详细列表参看场景&语种支持。
alt
使用流式语音识别大模型时（识别准确度高）：
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选流式语音识别大模型。大模型支持自动识别语言，无需额外配置。支持中英及方言识别，详细列表参看支持语种。
服务配置完成后，可前往语音识别配置查看不同方案下具体的调用参数和示例。

配置 LLM 模块
你可以选择使用火山方舟、Coze 平台和第三方大模型进行用户输入解析。请前往大模型配置查看不同方案下具体的调用参数和示例。
为增强智能体在多语言场景下的表现，可在配置大模型时增加一些多语言处理规则。使用火山方舟平台和第三方大模型时通过SystemMessages参数传入规则，使用 Coze 平台时通过智能体编排页人设与回复逻辑处传入规则。
你可参看以下示例进行传入：

"SystemMessages": [
    "输入文本无论是中文还是英文,最终回答都必须是英文。",
    "如果输入是中文,请识别出,这是中文,并回复(这是中文)。",
    "如果输入的内容是乱码、既不是中文也不是英文,请识别出,这是乱码,并回复(这是乱码)。",   
    "确保输出的英文表达准确、自然,符合语法规则。",
    "回复尽量贴近真人的表达。"
]
配置 TTS 模块
在收到 LLM 生成回复后，你可以选择语音合成或语音合成大模型服务进行语音合成。

使用语音合成时（合成声音速度快）：
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选语音合成。
前往语音合成控制台点击音色购买，购买指定语种对应的音色。支持英、日、葡等多国语言，详细列表参看音色列表。
alt
使用语音合成大模型时（合成声音情感表现力更佳）：
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选语音合成大模型。
前往语音合成大模型控制台点击音色购买，购买指定语种对应的音色。支持英、日、西班牙等多国语言，详细列表参看音色列表。
alt
服务配置完成后，请前往语音识别配置查看不同方案下具体的调用参数和示例。
如果存在特定内容无需语音合成或过滤指定标签等场景，可使用控制播放内容功能。

中外互译场景
该场景适用于翻译等场景，真人用户输入后，智能体翻译并输出。你可按照以下推荐操作配置 StartVoiceChat 接口的 ASR、TTS 和 LLM 模块。

配置 ASR 模块
你可以选择使用流式语音识别或流式语音识别大模型服务进行语音识别。

使用流式语音识别时（识别速度快）：
前往控制台应用管理，新建应用或编辑你已创建的应用，在流式语音识别服务中勾选你需要使用的语种服务。支持中、英、日、法等多国语言，详细列表参看场景&语种支持。
alt
使用流式语音识别大模型时（识别准确度高）：
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选流式语音识别大模型。大模型支持自动识别语言，无需额外配置。支持中英及方言识别，详细列表参看支持语种。
alt
服务配置完成后，可前往语音识别配置查看不同方案下具体的调用参数和示例。
配置 LLM 模块
你可以选择使用火山方舟、Coze 平台和第三方大模型进行用户输入解析。请前往大模型配置查看不同方案下具体的调用参数和示例。
为增强智能体在多语言场景下的表现，可在配置大模型时增加一些多语言处理规则。使用火山方舟平台和第三方大模型时通过SystemMessages参数传入规则，使用 Coze 平台时通过智能体编排页人设与回复逻辑处传入规则。
你可参看以下示例进行传入：

"SystemMessages": [
    "输入文本为中文,最终回答必须是英文。",
    "如果输入不是中文,请识别出,这不是中文,并回复(这不是中文)。",
    "如果输入的内容是乱码、既不是中文也不是英文,请识别出,这是乱码,并回复(这是乱码)。",   
    "确保输出的英文表达准确、自然,符合语法规则。",
    "回复尽量贴近真人的表达。"
]
配置 TTS 模块
在收到 LLM 生成回复后，你可以选择语音合成或语音合成大模型服务进行语音合成。

使用语音合成时（合成声音速度快）：
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选语音合成。
前往语音合成控制台点击音色购买，指定语种对应的音色。支持英、日、葡等多国语言，详细列表参看音色列表。
alt
使用语音合成大模型时（合成声音情感表现力更佳）：
前往控制台应用管理，新建应用或编辑你已创建的应用，勾选语音合成大模型。
前往语音合成大模型控制台点击音色购买，购买指定语种对应的音色。支持英、日、西班牙等多国语言，详细列表参看音色列表。
alt
服务配置完成后，请前往语音识别配置查看不同方案下具体的调用参数和示例。
如果存在特定内容无需语音合成或过滤指定标签等场景，可使用控制播放内容功能。HTTP Code 返回 200
问题描述
调用 StartVoiceChat 返回 200，但智能体出现以下任一异常情况：

智能体未加入房间。
智能体加入房间后无响应，例如没有欢迎语。
智能体加入房间后有欢迎语，但后续语音沟通无反应。
智能体加入房间后，听不到智能体的声音。
可能原因
若你首次接入遇到该问题：
导致上述现象的可能原因如下：
音频设备问题：比如本地麦克风或扬声器配置不当或故障。
服务配置问题：ASR、TTS、LLM 等服务未开通或参数配置错误。
接口参数设置问题：StartVoiceChat 接口缺少必填参数、填写错误或参数大小写错误。
权限问题：跨服务授权未开启，RTC 没有权限调用其他 AI 服务。
若之前智能体运行正常，你再次调用 StartVoiceChat 接口时遇到该问题：
请查看 ASR、TTS、LLM 等服务试用或购买额度是否已用完，或已达限额。
问题排查
若你首次接入遇到该问题，可以参考以下步骤进行排查：

步骤1：检查音频设备
进入实时对话式 AI Web 体验 Demo进行 AI 对话：

如果无法和智能体进行正常对话，则可能是音频输入设备问题，可按照如下方法排查：
Web 端：请参看 Web 排查无声问题。
Native 端：请参看 Native 排查无声问题。
如果可以和智能体进行正常对话，则可能是智能体接口设置问题，请继续执行后续排查步骤。
步骤2：检查跨服务授权
前往跨服务授权查看是否已开通跨服务授权。若显示未开通可按照提示一键开通。
alt

步骤3：检查智能体配置
请确保账号已开通 ASR、TTS、LLM 服务，正确获取并配置了对应核心参数。
注意

若 ASR、TTS 或 LLM 使用的是火山引擎的服务，请检查是否在使用免费版。如在使用免费版请前往控制台确认免费版额度是否用完。如已开通正式版，请确认是否购买资源包。
详细参数说明，请参考StartVoiceChat。
可使用无代码跑通 Demo 快速检验以下核心参数是否正确填写。如无代码跑通 Demo 无法正常运行，则证明参数填写错误，请对参数值进行逐一排查。
如参数均正确，但智能体仍无法正常运行，请前往使用的在线推理点模型限流是否设置为 0。

服务名称	核心参数
ASR

火山引擎流式语音识别

AppId：开通火山引擎流式语音识别服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。

Cluster：开通的流式语音识别服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。

火山引擎流式语音识别大模型

AppId：开通火山引擎流式语音识别大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。

AccessToken：与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。

TTS

火山引擎语音合成

appid：开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。

cluster：开通的语音合成服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。

voice_type：已开通音色对应的音色种类（Voice_type）。你可登录语音技术控制台购买音色后获取。

火山引擎语音合成大模型流式输入流式输出

appid：开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。

token：与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录语音技术控制台获取。

voice_type：已开通音色对应的音色种类（Voice_type）。你可登录语音技术控制台购买音色后获取。

火山引擎声音复刻大模型非流式输入流式输出

appid：开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。

cluster：开通的声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。

voice_type：声音复刻声音 ID。你可登录语音技术控制台获取。

火山引擎声音复刻大模型流式输入流式输出

appid：开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录语音技术控制台获取。

cluster：开通的声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录语音技术控制台开通服务后获取。

voice_type：声音复刻声音 ID。你可登录语音技术控制台获取。

MiniMax

Authorization：API 密钥。前往 Minimax 账户管理-接口密钥获取。

Groupid：用户所属组 ID。前往 Minimax 账号信息-基本信息获取。

LLM

火山方舟平台

自定义推理点 EndPointID 或智能体应用 ID。前往火山方舟控制台获取。仅支持自定义推理接入点，不支持预置推理接入点。创建应用时仅支持使用自定义推理接入点，不支持预置推理接入点。

Coze 平台

BotId：Coze 智能体 ID。可前往你需要调用的智能体开发页面获取。开发页面 URL 中 bot 参数后的数字即智能体ID。例如开发页面 URL 为：https://www.coze.cn/space/341/bot/73428668，则 BotId 为 73428668。 智能体必须已发布为 API。

APIKey：Coze 访问密钥。你可以生成个人访问令牌以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考OAuth 应用管理。

第三方大模型	URL：第三方大模型 URL。
检查 StartVoiceChat 接口请求参数是否正确并完整填写。
确保所有必填参数是否填写。
检查以下参数的规范性：
RoomId、TaskId、TargetUserId、UserId：是否按照参数赋值规范填写，不得出现中文字符。
RoomId：是否与房间内客户端 SDK 进房时的使用的 RoomId 保持一致
TargetUserId：是否与房间内客户端 SDK 进房时使用的 UserId 保持一致。
若按照以上步骤排查后智能体依然无法正常工作，请联系技术支持提供 OpenAPI 接口请求的 RequestId 进行排查。

HTTP Code 返回 ！200
调用 StartVoiceChat 返回 ！200 状态码时，如 401，请查看对应报错进行进行处理。
例如报错信息提示 InvalidAuthorization，则证明使用 Token 无效。可前往控制台 Token 校验检查 Token 是否有效。API 列表
最近更新时间：2025.07.01 10:42:55
首次发布时间：2024.01.09 19:12:18

我的收藏
有用
无用
房间管理
API	说明
BanUserStream	封禁音视频流 BanUserStream
UnbanUserStream	解封音视频流 UnbanUserStream
BanRoomUser	封禁房间&用户 BanRoomUser
UpdateBanRoomUserRule	更新房间&用户封禁规则 UpdateBanRoomUserRule
GetRoomOnlineUsers	获取实时用户列表 GetRoomOnlineUsers
GetRoomUsersProperty	查询用户状态 GetRoomUsersProperty
LimitTokenPrivilege	限制 Token 发布权限 LimitTokenPrivilege
KickUser	（不推荐使用）移出用户 KickUser
DismissRoom	（不推荐使用）解散房间 DismissRoom
云端录制
API	说明
StartRecord	开始云端录制 StartRecord
UpdateRecord	更新云端录制 UpdateRecord
StopRecord	结束云端录制 StopRecord
GetRecordTask	查询录制任务状态 GetRecordTask
转推直播
API	说明
StartPushMixedStreamToCDN	开始合流转推 StartPushMixedStreamToCDN
StartPushSingleStreamToCDN	开始单流转推 StartPushSingleStreamToCDN
UpdatePushMixedStreamToCDN	更新合流转推 UpdatePushMixedStreamToCDN
StopPushStreamToCDN	结束转推直播 StopPushStreamToCDN
GetPushSingleStreamToCDNTask	查询单流转推任务状态 GetPushSingleStreamToCDNTask
GetPushMixedStreamToCDNTask	查询合流转推任务状态 GetPushMixedStreamToCDNTask
音频切片
API	说明
StartSegment	开启音频切片 StartSegment
UpdateSegment	更新音频切片 UpdateSegment
StopSegment	结束音频切片 StopSegment
GetSegmentTask	查询音频切片任务状态 GetSegmentTask
抽帧截图
API	说明
StartSnapshot	开启抽帧截图 StartSnapshot
UpdateSnapshot	更新抽帧参数 UpdateSnapshot
StopSnapshot	关闭抽帧截图 StopSnapshot
GetSnapshotTask	查询抽帧截图任务状态 GetSnapshotTask
输入在线媒体流
API	说明
StartRelayStream	开始在线媒体流输入 StartRelayStream
UpdateRelayStream	更新在线媒体流输入 UpdateRelayStream
StopRelayStream	停止在线媒体流输入 StopRelayStream
ListRelayStream	查询输入媒体流任务 ListRelayStream
公共流
API	说明
StartPushPublicStream	发布公共流 StartPushPublicStream
UpdatePublicStreamParam	更新公共流 UpdatePublicStreamParam
StopPushPublicStream	停止发布公共流 StopPushPublicStream
云录屏
API	说明
StartWebcast	开启云录屏 StartWebcast
StopWebcast	结束云录屏 StopWebcast
GetWebCastTask	查询云录屏任务状态 GetWebCastTask
实时数据监控
API	说明
ListRealTimeQuality	获取实时质量数据 ListRealTimeQuality
ListRealTimeOperationData	获取实时运营数据 ListRealTimeOperationData
ListRealTimeQualityDistribution	获取实时分布质量数据 ListRealTimeQualityDistribution
ListCallDetail	获取通话质量指标 ListCallDetail
ListRealTimePublicStreamInfo	获取实时公共流订阅数据 ListRealTimePublicStreamInfo
历史数据查询
API	说明
ListQuality	获取离线数据指标 ListQuality
ListOperationData	获取离线运营数据 ListOperationData
ListOperationDistribution	获取离线运营分布数据 ListOperationDistribution
ListQualityDistribution	获取离线分布质量数据 ListQualityDistribution
ListRoomInfo	获取离线通话房间列表 ListRoomInfo
ListUserInfo	获取离线用户通话详情 ListUserInfo
ListUsages	获取通话时长数据 ListUsages
应用管理
API	说明
CreateApp	创建应用 CreateApp
ListApps	查询应用 ListApps
ModifyAppStatus	启用&停用应用 ModifyAppStatus
业务标识管理
API	说明
AddBusinessID	添加业务标识 AddBusinessID
ModifyBusinessRemarks	修改业务标识名称 ModifyBusinessRemarks
GetAllBusinessID	获取业务标识 GetAllBusinessID
DeleteBusinessID	删除业务标识 DeleteBusinessID
实时消息通信
API	说明
SendUnicast	发送房间外点对点消息 SendUnicast
SendRoomUnicast	发送房间内点对点消息 SendRoomUnicast
SendBroadcast	发送房间内广播消息 SendBroadcast
BatchSendRoomUnicast	批量发送房间内点对点消息 BatchSendRoomUnicast
音视频内容安全
API	说明
StartDetection	开启审核 StartDetection
StopDetection	停止审核 StopDetection
ListDetectionTask	任务状态查询 ListDetectionTask
歌曲查询
API	说明
SearchMusics	搜索音乐列表 SearchMusics
ListMusics	查询歌曲列表 ListMusics
ListHotMusic	查询热歌榜单 ListHotMusic
实时通话字幕和翻译
API	说明
StartSubtitle	开启实时字幕 StartSubtitle
UpdateSubtitle	更新实时字幕 UpdateSubtitle
StopSubtitle	停止实时字幕 StopSubtitle您可使用 OpenAPI 向 RTC 的服务端地址发送请求，并按照接口说明在请求中加入相应请求参数。系统会根据请求的处理情况，返回处理结果。本章节为您介绍 RTC OpenAPI 的调用方法，包括但不限于请求结构、公共参数、签名机制和公共错误码

服务接入地址
RTC 在全球多个地域部署，请您参考下表来使用对应的接入地址。如果接口不支持该表中的所有地域，则会在接口文档中单独说明。

地域名称	Region	接入地址
华北	cn-north-1	rtc.volcengineapi.com
亚太东南（柔佛）	ap-southeast-1	open-ap-singapore-1.volcengineapi.com
通信协议
你可以使用 HTTP 和 HTTPS 两种协议进行请求通信。我们强烈推荐你使用安全性更高的 HTTPS 方式发送请求。

字符编码
请求及返回结果使用 UTF-8 字符集进行编码。

接口限制
具体值请参看各个接口描述。

当前以火山引擎账号维度进行限制, 账号下多个 AppId 之间共享限流额度。

请求方法
根据各个接口的具体需求，选择 Get 或 Post 方式发起请求。

请求参数
公共请求参数参看公共参数。

各接口特有请求参数参看各接口描述。


对于 ID 类请求参数（BusinessId，TaskId，RoomId 和 UserId），必须遵循统一的 ID 命名规范：
字符串长度不超过 128 字符；
字符串中仅包含以下的字符：
a~z （小写英文字符）
A~Z （大写英文字符）
0~9 （数字）
@ . _ -

构造 URI
请求 URI 的组成结构：{URI-scheme}://{Endpoint}/?{Query-string}。

alt

参数说明如下表所示。

参数	描述
URI-scheme	表示用于传输请求的协议，支持通过 HTTP 和 HTTPS 2 种方式进行请求通信。
Endpoint	API 的服务接入地址。
中国大陆：rtc.volcengineapi.com。
其他国家/地区：open-ap-singapore-1.volcengineapi.com
Query-string	查询字符串，包含公共参数和 GET 请求中的查询参数。
公共参数：需要包含 Action 和 Version 参数；参数前面需要带一个“?”，公共参数之间用“&”相连。
查询参数(GET)：从具体的 OpenAPI 接口文档中获取；查询参数前面需要带一个“?”，形式为“参数名=参数取值”，参数之间用“&”相连。例如“?limit=5”，表示查询不超过 5 条数据。
请求示例

POST https://rtc.volcengineapi.com?Action=BanRoomUser&Version=2020-12-01

{
    "AppId": "Your_AppId",
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "ForbiddenInterval": 0
}
返回结果

{
    "ResponseMetadata": {
        "RequestId": "Your_RequestId",
        "Action": "BanRoomUser",
        "Version": "2020-12-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    },
    "Result": {
        "message": "success"
    }
}公共参数是每个接口都需要使用的请求参数，开发者每次使用火山引擎 API 发送请求时都需要携带这些公共请求参数，否则会导致请求失败。

请求参数
在发起请求时，需要包含两类参数：公共请求参数和接口特有的请求参数。

公共请求参数
在发起所有 OpenAPI 请求时，请求中必须包含以下公共参数：

名称	类型	取值	位置	说明
Host	string	rtc.volcengineapi.com	header	同服务地址。
Content-Type	string	/	header	资源的 MIME 类型。
Post 请求中，该值为 application/json。
Get 请求中,该值可为空。
X-Date	string	/	header	请求时间，UTC 时间，形如20201230T081805Z 。
X-Content-Sha256	string	/	header	对请求体采用 SHA256 加密后的结果字符串。
Authorization	string	/	header	签名。关于如何获得这一签名，参看对 OpenAPI 请求进行签名。
Action	string	/	Query String	接口名称。参看接口详情文档。
Version	string	/	Query String	接口版本。参看接口详情文档。
对于接口特有请求参数，参看具体 OpenAPI 接口文档。为了保证请求者身份的合法性以及请求在传输过程中不被恶意篡改，火山引擎签名机制要求请求者对请求参数进行哈希值计算，经过加密后同 API 请求一起发送到服务器中，服务器将以同样的机制对收到的请求进行签名计算，并以此与请求者传来的签名进行比对，若签名未通过验证，请求将被拒绝。

如何进行签名计算及签名 SDK 示例，请参见签名方法。返回结构
最近更新时间：2024.07.16 17:04:59
首次发布时间：2023.12.08 15:01:59

我的收藏
有用
无用
2020-12-01

房间管理、云端媒体处理、公共流、应用管理、业务标识管理、实时消息通信模块下：
当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。
当 HTTP 响应状态码 ==200 时，你仍需解析返回结构中的 BaseResponse.ResponseMetaData.Error 结构体。当 Error 为空时，表示调用成功。
返回结构
BaseResponse 返回结构如下：

参数名	类型	描述
ResponseMetadata	ResponseMetadata	
Result	interface{}	仅在请求成功时返回, 具体值参考每个 API 的说明。
ResponseMetadata

参数名	类型	示例值	描述
RequestId	String	Your_RequestId	请求标识
Action	String	StartRecord	接口名称
Version	String	2020-12-01	接口版本
Service	String	rtc	接口所属服务
Region	String	cn-north-1	地域参数：
cn-north-1 (华北)
ap-singapore-1 (新加坡)
us-east-1 (美东)
Error	ErrorInfo	-	仅在请求失败时返回。
ErrorInfo 错误的结构定义。

参数名称	类型	描述
CodeN	Uint32	网关的错误码。（请求失败时返回）
Code	String	API 的错误码，参看错误码。
Message	String	具体的错误信息
返回结构示例
调用成功：
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
调用失败：
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
获取数据指标和歌曲查询模块下：
当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 和 BaseResponse.Result 字段解析错误原因。
当 HTTP 响应状态码 ==200 时，表示调用成功。

返回结构
BaseResponse 返回结构如下：

参数名	类型	描述
ResponseMetadata	ResponseMetadata	
Result	interface{}	具体值参考每个 API 的说明。
ResponseMetadata

参数名	类型	示例值	描述
RequestId	String	Your_RequestId	请求标识
Action	String	StartRecord	接口名称
Version	String	2020-12-01	接口版本
Service	String	rtc	接口所属服务
Region	String	cn-north-1	地域参数：
cn-north-1 (华北)
ap-singapore-1 (新加坡)
us-east-1 (美东)
Error	ErrorInfo	-	仅在请求失败时返回。
ErrorInfo 错误的结构定义。

参数名称	类型	描述
CodeN	Uint32	网关的错误码
Code	String	API 的错误码，参看错误码。
Message	String	具体的错误信息
返回结构示例
调用成功：
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
调用失败：
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2020-12-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
2022-06-01

当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。
当 HTTP 响应状态码 ==200 时，你仍需解析返回结构中的 BaseResponse.ResponseMetaData.Error 结构体。当 Error 为空时，表示调用成功。

返回结构
BaseResponse 返回结构如下：

参数名	类型	描述
ResponseMetadata	ResponseMetadata	
Result	interface{}	仅在请求成功时返回, 具体值参考每个 API 的说明。
ResponseMetadata

参数名	类型	示例值	描述
RequestId	String	Your_RequestId	请求标识
Action	String	StartRecord	接口名称
Version	String	2022-06-01	接口版本
Service	String	rtc	接口所属服务
Region	String	cn-north-1	地域参数：
cn-north-1 (华北)
ap-singapore-1 (新加坡)
us-east-1 (美东)
Error	ErrorInfo	-	仅在请求失败时返回。
ErrorInfo 错误的结构定义。

参数名称	类型	描述
CodeN	Uint32	网关的错误码
Code	String	API 的错误码，参看错误码。
Message	String	具体的错误信息
返回结构示例
调用成功：
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2022-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
调用失败：
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2022-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "CodeN": 10009,
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
2023-06-01

当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。
当 HTTP 响应状态码 ==200 时，你仍需解析返回结构中的 BaseResponse.ResponseMetaData.Error 结构体。当 Error 为空时，表示调用成功。

返回结构
BaseResponse 返回结构如下：

参数名	类型	描述
ResponseMetadata	ResponseMetadata	
Result	interface{}	仅在请求成功时返回, 具体值参考每个 API 的说明。
ResponseMetadata

参数名	类型	示例值	描述
RequestId	String	Your_RequestId	请求标识
Action	String	StartRecord	接口名称
Version	String	2023-06-01	接口版本
Service	String	rtc	接口所属服务
Region	String	cn-north-1	地域参数：
cn-north-1 (华北)
ap-singapore-1 (新加坡)
us-east-1 (美东)
Error	ErrorInfo	-	仅在请求失败时返回。
ErrorInfo 错误的结构定义。

参数名称	类型	描述
CodeN	Uint32	网关的错误码
Code	String	API 的错误码，参看错误码。
Message	String	具体的错误信息
返回结构示例
调用成功：
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2023-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
调用失败：
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2023-06-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "CodeN": 10009,
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
2023-07-20

当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。
当 HTTP 响应状态码 ==200 时，表示调用成功。

返回结构
BaseResponse 返回结构如下：

参数名	类型	描述
ResponseMetadata	ResponseMetadata	
Result	interface{}	具体值参考每个 API 的说明。
ResponseMetadata

参数名	类型	示例值	描述
RequestId	String	Your_RequestId	请求标识
Action	String	StartRecord	接口名称
Version	String	2023-07-20	接口版本
Service	String	rtc	接口所属服务
Region	String	cn-north-1	地域参数：
cn-north-1 (华北)
ap-singapore-1 (新加坡)
us-east-1 (美东)
Error	ErrorInfo	-	仅在请求失败时返回。
ErrorInfo 错误的结构定义。

参数名称	类型	描述
Code	String	API 的错误码，参看错误码。
Message	String	具体的错误信息
返回结构示例
调用成功：
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2023-07-20",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
调用失败：
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2023-07-20",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }
2023-11-01 & 2024-06-01

当 HTTP 响应状态码 !=200 时，表示调用失败。你可以根据返回结构中 BaseResponse.ResponseMetaData 的 Code 和 Message 字段解析错误原因。
当 HTTP 响应状态码 ==200 时，表示调用成功。

返回结构
BaseResponse 返回结构如下：

参数名	类型	描述
ResponseMetadata	ResponseMetadata	
Result	interface{}	具体值参考每个 API 的说明。
ResponseMetadata

参数名	类型	示例值	描述
RequestId	String	Your_RequestId	请求标识
Action	String	StartRecord	接口名称
Version	String	2023-11-01	接口版本
Service	String	rtc	接口所属服务
Region	String	cn-north-1	地域参数：
cn-north-1 (华北)
ap-singapore-1 (新加坡)
us-east-1 (美东)
Error	ErrorInfo	-	仅在请求失败时返回。
ErrorInfo 错误的结构定义。

参数名称	类型	描述
Code	String	API 的错误码，参看错误码。
Message	String	具体的错误信息
返回结构示例
调用成功：
{
        "ResponseMetadata": {
            "RequestId": "Your_RequestId",
             "Action": "DismissRoom",
            "Version": "2023-11-01",
            "Service": "rtc",
            "Region": "cn-north-1"
        },
        "Result":{
            "Message": "success"
        }
    }
调用失败：
{
         "ResponseMetadata": {
            "RequestId": "Your_RequestId",
            "Action": "DismissRoom",
            "Version": "2023-11-01",
            "Service": "rtc",
            "Region": "cn-north-1"
            "Error":{
                "Code": "InvalidParameter.AccountIdMismatch",
                "Message": "Check owner failed"
        }
    }，
        "Result": {}        
    }公共错误码
最近更新时间：2025.03.03 17:06:17
首次发布时间：2021.07.18 14:10:40

我的收藏
有用
无用
如果调用 OpenAPI 失败，你会在相应结果中看到错误码和错误信息。以下是错误码，及对应的说明和处理方法：

错误码	说明	处理方法
InvalidParameter.TaskAlreadyExists	任务已存在	请勿重复创建
InvalidParameter.StartTimeAfterEndTime	查询开始时间晚于结束时间	调整请求参数
InvalidParameter.OutofTimeRange	查询要求的时间范围长度超出限制	缩短查询的时间范围
InvalidParameter.ErrorActiveTaskNotFound	指定任务不存在或已经停止	请输入正确的 TaskId
InvalidParameter.InvalidRtmpAddress	推流地址不合法	请确认推流地址
InvalidParameter.IllegalRoomID	RoomId 不合法	RoomId 格式错误，请填写正确房间 ID
InvalidParameter.BodyIsEmpty	请求内容为空	请填写请求参数
InvalidParameter.Unmarshal	请求解析失败	请填写正确的参数格式
InvalidParameter.EmptyAccountId	AccountId 为空	请填写 AccountId
InvalidParameter.NotExistAppId	AppID 不存在	请检查 AppID
InvalidParameter.IllegalAppId	AppID 不合法	请检查 AppID
InvalidParameter.IllegalTo	接收用户名不合法	请检查接收用户参数
InvalidParameter.IllegalFrom	发送用户名不合法	请检查发送用户名参数
InvalidParameter.SizeOverLimit	消息大小超过限制	请减小消息内容大小
InvalidParameter.UserCountOverLimit	接收用户数超过限制	请减少接收用户数
InvalidParameter	参数错误	请根据具体提示调整参数
如具体错误信息为 page and resolution with height should be even，请将分辨率参数调整为偶数
FlowLimitExceeded.TaskNumberOverLimit	任务数量超过限制	目前没有对任务数量进行限制
FlowLimitExceeded.QPSOverLimit	qps 超过限制	请勿太频繁地发起请求
AccessDenied.RecordNotEnabled	录制功能未开通	请在控制台上开通录制
AccessDenied.NoPermissionForApp	此 AccountId 没有操作该 AppId 的权限	请更换正确的 AccountId
AccessDenied.ServiceNotEnabled	需要服务未开通	请在控制台开通需要服务
InternalError.GenerateTokenFailed	生成token失败	详情请联系技术支持
InternalError.GetAutoTriggerPolicyFailed	获取自动录制配置失败	详情请联系技术支持
InternalError.GetRecordPolicyFailed	获取录制参数配置失败	详情请联系技术支持
InternalError.DBCheckTaskExistFailed	判断数据库中是否存在此任务失败	详情请联系技术支持
InternalError.DBCreateTaskFailed	数据库中创建任务失败	详情请联系技术支持
InternalError.DBReadTaskFailed	数据库中读取任务信息失败	详情请联系技术支持
InternalError.DBUpdateTaskFailed	数据库中更新任务信息失败	详情请联系技术支持
InternalError.UnknownInternalError	未知的内部错误	详情请联系技术支持
InternalError.TaskRepeat	未知的内部错误	请不要重复请求，或更换 taskID
InternalError.Unknown	系统故障，未启动成功	详情请联系技术支持
InternalError.DB	服务端数据库查询失败	详情请联系技术支持
InternalError.Executor	服务端停止任务失败	请重试
InternalError.ReachRateLimit	触发系统限流	请勿太频繁地发起请求
InternalError.Parallel	并发操作同一个任务	请勿并发请求
InternalError.RPC	rpc 调用失败	请重新发起请求
InternalError.NotFound	请重新发起请求	请重新发起请求
InternalError.UserNotFound	找不到接收用户	若确定用户已进房，请重新发起请求
InternalError.RoomNotFound	找不到房间	若确定房间已存在，请重新发起请求
InternalError.Marshal	序列化失败	请重新发起请求
InternalError.SendToEdge	推送给 RTM Edge 失败	请重新发起请求
InternalError.Redis	读写 Redis 失败	请重新发起请求
InternalError.TaskNotReady	任务已提交，还未开始运行	详情请联系技术支持
InternalError.InvalidMeta	任务内部参数不合法	详情请联系技术支持
InternalError.TaskControlError	处理任务出现错误	详情请联系技术支持
InternalError.NotifyConnectionRefused	内部连接被拒绝	详情请联系技术支持
InternalError.LockContention	锁竞争超时	详情请联系技术支持
InternalError.ServerError	服务器错误	详情请联系技术支持
InternalError.RPC	此 AccountId 没有操作该 AppId 的权限	
InternalError.Unmarshal	请求解析失败	请填写正确的参数格式
ErrorParameter.MaxRecordSecond	MaxRecordSecond参数错误	请填写正确的值
ErrorParameter.AppId	AppId 错误	请确认 AppId 是否填写正确
MissingParameter.NoAppId	未填写 AppId	请填写 AppId
MissingParameter.NoAccount	账号 ID 未填写	请填写账号 ID
MissingParameter.NoAction	Action 缺失	Action 未填写，请填写正确接口名称
MissingParameter.NoVersion	Version 缺失	version 未填写，请填写正确接口版本
MissingParameter.NoRoomId	RoomId 缺失	请填写 RoomId
MissingParameter.NoUserId	UserId 缺失	请填写 UserId
MissingParameter.NoToken	Token 缺失	请填写 Token
MissingParameter.NoAppId	AppId 缺失	请填写 AppId
MissingParameter.Forbidden	封禁时间不在有效范围内。	请填写有效封禁时间
Unauthorized	操作的 AppId 与账号 ID 不匹配	请确认 AppId 和账号 ID 是否正确
UnknownVersion	Version未知	请将 version 重置为 2020-12-01
UnknownAction	Action未知	请 检查 Action 是否为 Open API 定义的 Action
PartialSuccess	部分用户收到消息	如确定用户均在房间内，请重新发起请求
UpdateError.UpdateSingleStreamRecordNotSupported	单流录制不支持更新	当前单流录制不支持更新，请勿发起单流录制更新请求
UpdateError.CurrentLayoutNotSupported	不支持更新当前布局模式下的合流录制任务	请检查你设置的布局模式，仅自定义布局模式支持更新合流录制任务
UpdateError.UpdatePushSingleStreamNotSupported	单流转推不支持更新	当前单流转推不支持更新，请勿发起单流转推开启实时字幕 StartSubtitle
最近更新时间：2025.05.13 17:55:06
首次发布时间：2024.12.27 11:43:02

我的收藏
有用
无用
通过 StartSubtitle 接口，将用户语音转为字幕文本，字幕结果可通过客户端或服务端来接收。支持生成以下 2 种类型的字幕：

按源语言生成字幕：通过流式语音识别服务（ASR），将用户的语音直接转为文字（不翻译），适用于会议记录生成和直播字幕生成等场景。
生成指定语言的字幕：通过机器翻译服务，先将用户的语音转为文字，再翻译成其他语种（如英文、日语等），适用于国际会议和多语言直播等场景。
两种类型的字幕所用服务、支持识别的语种、费用等均存在差异。详细说明，请参见 实时通话字幕和翻译。

注意

关于接收字幕：
支持通过客户端和服务端接收字幕结果：
接收地址包含客户端：必须在客户端监听 onRoomBinaryMessageReceived 回调来接收字幕数据。
接收地址包含服务端：字幕会按照每句话返回给该接口设定的 Url 地址。
关于解析字幕：生成的字幕可返回至客户端或服务端，格式为 Base64 编码的二进制消息。使用前（比如，实时显示、存储等），需先解析。如何解析字幕?
注意事项
前提条件
调用该接口生成字幕前，先按照需求开通相关服务：

需求	所需开通服务
按源语言生成字幕

开通流式语音识别：前往语音技术控制台_流式语音识别，创建流式语音识别应用，服务类型选择 流式语音识别，并记录 APP ID，Access Token，和语言的 Cluster ID，以备使用。
开通 RTC 实时字幕服务：前往 RTC 控制台_实时字幕，开启以下服务：
实时字幕： 开启开关。
流式语音识别： 开启开关后，填入已获取的 APP ID，Access Token，和语言的 Cluster ID。
生成指定语言的字幕

开通机器翻译服务：具体操作，请参见开通服务。
开通 RTC 实时字幕服务：前往 RTC 控制台_实时字幕，开启实时字幕和实时语音翻译的开关。
请求频率
单账号下 QPS 不得超过 60。

调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看调用方法。

请求说明
请求方式：POST
请求地址：https://rtc.volcengineapi.com?Action=StartSubtitle&Version=2024-06-01
调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
去调试
请求参数
下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见公共参数。

Query
参数
类型
是否必选
示例值
描述
Action
String
是
StartSubtitle
接口名称。当前 API 的名称为 StartSubtitle。
Version
String
是
2024-06-01
接口版本。当前 API 的版本为 2024-06-01。
Body
参数
类型
是否必选
示例值
描述
AppId
String
是
661e****543cf

音视频应用的 ID。如何获取 AppId？

RoomId
String
是
Room1

需生成字幕的 RTC 房间 ID。

TaskId
String
是
Task1

设置字幕任务 ID，用于唯一标识字幕任务。
命名规则需符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。

说明

每个字幕任务都必须设定 TaskId，在后续更新或结束字幕任务时，也需使用该 TaskId。
在一个 AppId 的 RoomId 下 taskId 是唯一的，不同 AppId 或者不同 RoomId 下 TaskId 可以重复，因此 AppId + RoomId + TaskId 是任务的唯一标识，可以用来标识指定 AppId 下某个房间内正在运行的任务，从而能在此任务运行中进行更新或者停止此任务。
LanguageConfig
Object
是
-

设置用户语音的原始语种；若需翻译字幕，还需设置翻译目标语种。

SourceLanguages
Object[]
是
[
  {
    "user": "A",
    "language": "en"
  },
  {
    "user": "B",
    "language": "ja"
  }
]
设置用户语音的原始语种。
设置后，系统会根据用户的语音生成对应语言的字幕文本。比如 A 说英文，则 A 的语音对应字幕文本为英文；B 说日语，则 B 的语音对应字幕文本为日语。

注意

如果未为房间内某个用户设置源语种，系统会默认将该用户的语音转录为中文字幕，即使用户实际使用的语言不是中文。这可能导致生成的字幕文本与用户的实际语音不匹配，甚至可能出现无法理解的文本。
房间内所有用户的语音都会转为文字，为了确保字幕的准确性和可读性，建议为房间内的所有用户准确设置源语种。
RoomTargetLanguages
String[]
否
["en"]

字幕需翻译为的目标语种，请填写语种代号。支持翻译的语种及代号？

注意

仅使用机器翻译服务时，该字段才生效。
若使用的是流式语音识别服务，该字段不生效，请置为空。
DistributionMode
Integer
是
1

指定字幕结果接收地址，支持的取值及含义如下：

1：通过客户端接收字幕。字幕结果可实时返回，适用于实时显示字幕。
2：通过服务器接收。字幕结果按照每句话返回，适用于存储分析对话数据。
3：同时通过客户端和服务器接收。
注意

接收字幕：当设为 1 或 3 时，必须在客户端监听 onRoomBinaryMessageReceived 回调来接收字幕数据。
解析字幕：生成的字幕会以二进制格式返回至客户端或服务端，使用前需先解析。字幕格式说明及如何解析，请参见实时字幕。
ServerMessage
Object
否
-

接收字幕的服务器的 URL 地址和鉴权签名。

注意

当 DistributionMode 为 2 或 3 时（即字幕结果接收地址包含服务器），该参数为必填。

Signature
String
否
TestSignature

鉴权签名。
你可传入该鉴权参数，在接收到字幕结果后，与结果中的 signature 字段值进行对比以进行鉴权验证。

Url
String
否
http://127.0.0.0:8080/subtitlemsg

接收字幕结果的 URL 地址。需支持 HTTP(S) 协议。

ReceiverList
String[]
否
["user1","user2"]

字幕返回给客户端时，可指定字幕接收用户。

若指定了接收用户：只有指定的用户可以看到字幕。
若不指定：房间内的所有用户都可以看到字幕。
注意

该参数仅在 DistributionMode 为 1 或 3 时生效（即字幕结果接收地包含客户端）。

返回参数
本接口无特有的返回参数，公共返回参数请见返回结构。
其中，返回值 Result 仅在请求成功时返回 ok，请求失败时返回空。

请求示例
以生成字幕并翻译为英文，字幕结果同时通过客户端和服务端接收为例：

房间内有 2 个用户：user1，源语种为英文、user2，源语种为中文。
服务端地址为：http://127.0.0.0:8080/subtitlemsg。
客户端可看到字幕的用户：user1、user2。
POST https://rtc.volcengineapi.com?Action=StartSubtitle&Version=2024-06-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "Task1",
    "LanguageConfig": {
        "SourceLanguages": [
            {
                "UserId": "user1",
                "LanguageCode": [
                    "en"
                ]
            },
            {
                "UserId": "user2",
                "LanguageCode": [
                    "zh"
                ]
            }
        ],
        "RoomTargetLanguages": [
            "zh"
        ]
    },
    "DistributionMode": 3,
    "ServerMessage": {
        "Signature": "TestSignature",
        "Url": "http://127.0.0.0:8080/subtitlemsg"
    },
    "ReceiverList": [
        "user1",
        "user2"
    ]
}
返回示例
{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "20230604110420****100232280032D31",
        "Action": "StartSubtitle",
        "Version": "2024-06-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}更新实时字幕 UpdateSubtitle
最近更新时间：2025.02.12 11:55:47
首次发布时间：2024.12.27 11:43:02

我的收藏
有用
无用
在实时音视频通话场景中，若你需要对开启的字幕任务进行更新，如更新生成字幕目标用户，生成字幕语言，你可以使用该接口实现。

注意事项
请求频率
单账号下 QPS 不得超过 60。

调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看调用方法

请求说明
请求方式：POST
请求地址：https://rtc.volcengineapi.com?Action=UpdateSubtitle&Version=2024-06-01
调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
去调试
请求参数
下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见公共参数。

Query
参数
类型
是否必选
示例值
描述
Action
String
是
UpdateSubtitle
接口名称。当前 API 的名称为 UpdateSubtitle。
Version
String
是
2024-06-01
接口版本。当前 API 的版本为 2024-06-01。
Body
参数
类型
是否必选
示例值
描述
AppId
String
是
661e****543cf
你的音视频应用的唯一标志，参看获取 AppId。
RoomId
String
是
Room1
房间的 ID，是房间的唯一标志。赋值规则参看参数赋值规范。
TaskId
String
是
task1
字幕任务 ID。
VendorConfig
Object
是
-
字幕供应商平台配置。
Type
Integer
是
0
字幕服务供应商。支持取值及含义如下：
0：火山引擎语音技术。
LanguageConfig
Object
是
-
字幕源语种和目标语种配置
SourceLanguages
Object[]
是
-
字幕源语种配置。
UserId
String
是
user1
房间内需生成字幕用户对应的 UserId
LanguageCode
String[]
是
["zh"]
语种代号。 前往 RTC 控制台-实时字幕-流式语音识别-语种模型匹配获取或自定义语种代号。
仅支持配置一种语种代号。
RoomTargetLanguages
String[]
否
["zh"]
字幕目标语种代号，默认源语种相同。
ReceiverList
String[]
否
["user1","user2"]
字幕结果回调给客户端时，接收用户列表。
返回参数
本接口无特有的返回参数。公共返回参数请见返回结构。
其中返回值 Result 仅在请求成功时返回 ok,失败时为空。

请求示例
POST https://rtc.volcengineapi.com?Action=UpdateSubtitle&Version=2024-06-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "task1",
    "VendorConfig": {
        "Type": 0
    },
    "LanguageConfig": {
        "SourceLanguages": [
            {
                "UserId": "user1",
                "LanguageCode": [
                    "zh"
                ]
            }
        ],
        "RoomTargetLanguages": [
            "zh"
        ]
    },
    "ReceiverList": [
        "user1",
        "user2"
    ]
}
返回示例
{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "20230604110420****100232280032D31",
        "Action": "UpdateSubtitle",
        "Version": "2024-06-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}停止实时字幕 StopSubtitle
最近更新时间：2025.02.12 11:55:47
首次发布时间：2024.12.27 11:43:02

我的收藏
有用
无用
在实时音视频通话场景中，若需停止指定字幕任务，你可通过该接口实现。

注意事项
请求频率
单账号下 QPS 不得超过 60。

调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看调用方法。

请求说明
请求方式：POST
请求地址：https://rtc.volcengineapi.com?Action=StopSubtitle&Version=2024-06-01
调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
去调试
请求参数
Query
参数
类型
是否必选
示例值
描述
Action
String
是
StopSubtitle
接口名称。当前 API 的名称为 StopSubtitle。
Version
String
是
2024-06-01
接口版本。当前 API 的版本为 2024-06-01。
Body
参数
类型
是否必选
示例值
描述
AppId
String
是
661e****543cf
你的音视频应用的唯一标志，参看获取 AppId。
RoomId
String
是
Room1
房间的 ID，是房间的唯一标志。赋值规则参看参数赋值规范。
TaskId
String
是
Task1
需要终止的任务对应的任务 ID。
返回参数
请求示例
POST https://rtc.volcengineapi.com?Action=StopSubtitle&Version=2024-06-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "Task1"
}
返回示例
{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "20230604110420****100232280032D31",
        "Action": "StopSubtitle",
        "Version": "2024-06-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}