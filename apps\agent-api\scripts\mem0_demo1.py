import asyncio
import os
from openai import AsyncOpenAI
from mem0 import AsyncMemoryClient
from dotenv import load_dotenv

# --- 1. 配置 ---
# 加载 1.env 文件中的环境变量
# 请确保已安装 python-dotenv: pip install python-dotenv
load_dotenv(dotenv_path="../1.env")

# Mem0 配置
# 从环境变量中读取 MEM0_API_KEY
MEM0_API_KEY = os.environ.get("MEM0_API_KEY")

# 火山引擎 (方舟) LLM 配置
# 从环境变量中读取 VOLCENGINE_API_KEY
ARK_API_KEY = os.environ.get("VOLCENGINE_API_KEY")
VOLCANO_LLM_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
# 从环境变量中读取模型 Endpoint ID
VOLCANO_LLM_ENDPOINT_ID = os.environ.get("VOLCANO_LLM_ENDPOINT_ID")

# 用于演示的唯一用户标识符
USER_ID = "demo_user_volcano_mem0_chat_001"

# 为聊天机器人设计的系统提示
SYSTEM_PROMPT = """你是一个温暖、体贴的情感陪伴AI助手。
你拥有长期记忆，能够记住用户的情感状态、重要事件、偏好和我们的对话历史。
请以温和、共情的语调回应，关注用户的情感需求。
当用户感到难过时给予安慰，开心时分享喜悦，困惑时提供支持。
利用你的记忆为用户提供连续性和个性化的情感支持。"""

# 情感陪伴专用的记忆搜索策略
async def smart_memory_search(mem0_client, user_input, user_id):
    """
    针对情感陪伴优化的智能记忆搜索
    """
    relevant_memories = []

    # 1. 快速情感关键词检测
    emotion_keywords = {
        "难过": ["难过", "伤心", "沮丧", "痛苦", "失落"],
        "开心": ["开心", "高兴", "快乐", "兴奋", "愉快"],
        "焦虑": ["焦虑", "担心", "紧张", "害怕", "不安"],
        "愤怒": ["生气", "愤怒", "气愤", "恼火", "烦躁"],
        "困惑": ["困惑", "迷茫", "不知道", "纠结", "犹豫"]
    }

    # 检测当前情感
    detected_emotion = None
    for emotion, keywords in emotion_keywords.items():
        if any(keyword in user_input for keyword in keywords):
            detected_emotion = emotion
            break

    try:
        # 2. 优先搜索情感相关记忆
        if detected_emotion:
            emotion_memories = await mem0_client.search(
                query=f"{detected_emotion} 情感 {user_input[:30]}",
                user_id=user_id,
                limit=3
            )
            if emotion_memories and hasattr(emotion_memories, 'results'):
                relevant_memories.extend(emotion_memories.results[:3])

        # 3. 搜索直接相关记忆（限制搜索量提升速度）
        direct_memories = await mem0_client.search(
            query=user_input[:50],  # 只取前50字符
            user_id=user_id,
            limit=2
        )
        if direct_memories and hasattr(direct_memories, 'results'):
            relevant_memories.extend(direct_memories.results[:2])

        # 4. 如果记忆少于3条，补充最近记忆
        if len(relevant_memories) < 3:
            recent_memories = await mem0_client.get_all(user_id=user_id, limit=2)
            if recent_memories and hasattr(recent_memories, 'results'):
                relevant_memories.extend(recent_memories.results[:2])

        # 去重
        seen_memories = set()
        unique_memories = []
        for memory in relevant_memories:
            memory_text = memory.get('memory', '') if isinstance(memory, dict) else str(memory)
            if memory_text and memory_text not in seen_memories:
                seen_memories.add(memory_text)
                unique_memories.append(memory)
                if len(unique_memories) >= 5:  # 最多5条记忆
                    break

        return unique_memories, detected_emotion

    except Exception as e:
        print(f">>> 记忆搜索出现错误: {e}")
        return [], None


# 记忆重要性评估和管理
async def manage_memory_importance(mem0_client, user_id):
    """
    评估和管理记忆重要性，确保长短期记忆平衡
    """
    try:
        # 获取最近的记忆
        recent_memories = await mem0_client.get_all(user_id=user_id, limit=20)

        if not recent_memories or not hasattr(recent_memories, 'results'):
            return

        important_indicators = [
            "姓名", "名字", "生日", "家人", "工作", "爱好",
            "难过", "开心", "重要", "特别", "第一次"
        ]

        # 统计情感记忆数量
        emotion_memories = []
        personal_memories = []

        for memory in recent_memories.results:
            memory_text = memory.get('memory', '').lower()
            metadata = memory.get('metadata', {})

            # 情感记忆标记
            if metadata.get('emotional_context') or any(word in memory_text for word in ["难过", "开心", "生气", "焦虑"]):
                emotion_memories.append(memory)

            # 个人信息记忆标记
            if any(word in memory_text for word in important_indicators):
                personal_memories.append(memory)

        print(f">>> 记忆分析: 情感记忆 {len(emotion_memories)} 条, 个人信息记忆 {len(personal_memories)} 条")

    except Exception as e:
        print(f">>> 记忆管理出现问题: {e}")


async def chat_with_ai():
    """
    主聊天循环函数，集成了 Mem0 记忆和火山引擎 LLM。
    """
    # --- 检查 API Keys 是否已配置 ---
    if not MEM0_API_KEY or not ARK_API_KEY:
        print("错误：请在运行脚本前设置 MEM0_API_KEY 和 ARK_API_KEY 环境变量。")
        print("脚本会尝试从 1.env 文件加载，请检查该文件是否存在且包含正确的值。")
        print("获取 MEM0_API_KEY: https://app.mem0.ai/")
        print("获取 ARK_API_KEY: https://www.volcengine.com/product/ark")
        return

    if not VOLCANO_LLM_ENDPOINT_ID:
        print("错误：未设置 VOLCANO_LLM_ENDPOINT_ID")
        print("请在 1.env 文件中设置火山引擎的模型 Endpoint ID")
        return

    # --- 初始化 Mem0 和 LLM 的异步客户端 ---
    print("--- 正在初始化 Mem0 和火山引擎 LLM 客户端 ---")
    mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)
    llm_client = AsyncOpenAI(
        base_url=VOLCANO_LLM_BASE_URL,
        api_key=ARK_API_KEY,
    )

    print(f"--- Mem0 客户端初始化成功，用户 ID: {USER_ID} ---")

    # --- 显示现有记忆状态 ---
    try:
        existing_memories = await mem0_client.get_all(user_id=USER_ID, limit=100)
        if existing_memories and hasattr(existing_memories, 'results') and existing_memories.results:
            memory_count = len(existing_memories.results)
            print(f"📚 发现 {memory_count} 条历史记忆，我会根据这些记忆与你对话")
        else:
            print("📝 这是我们的第一次对话，我会开始学习和记住你的信息")
    except Exception as e:
        print(f"📋 检查历史记忆时出现问题: {e}")

    print("\n--- 聊天开始 ---")
    print(f"你好！我是你的情感陪伴AI助手，拥有持久记忆功能。我会记住我们的对话和你的情感状态。")
    print("输入 'quit' 或 'exit' 来结束对话。")

    conversation_count = 0  # 对话轮次计数

    while True:
        try:
            # 在异步函数中使用 input() 的推荐方式
            user_input = await asyncio.to_thread(input, f"\n你 ({USER_ID}): ")

            if user_input.lower() in ["quit", "exit"]:
                print("--- 聊天结束，下次再见！ ---")
                break

            # --- 1. 从 Mem0 搜索相关记忆 ---
            print(">>> 正在从 Mem0 搜索相关记忆...")
            try:
                # 使用优化后的智能搜索策略
                relevant_memories, detected_emotion = await smart_memory_search(mem0_client, user_input, USER_ID)

                if relevant_memories:
                    print(f">>> 找到 {len(relevant_memories)} 条相关记忆")
                    if detected_emotion:
                        print(f">>> 检测到情感状态: {detected_emotion}")
                    for i, mem_obj in enumerate(relevant_memories, 1):
                        memory_text = mem_obj.get('memory', '') if isinstance(mem_obj, dict) else str(mem_obj)
                        print(f">>> {i}. {memory_text[:80]}...")
                else:
                    print(">>> 未找到相关记忆，这可能是我们的第一次对话")

            except Exception as e:
                print(f">>> 搜索记忆时出现错误: {e}")
                relevant_memories = []

            # --- 2. 为 LLM 构建提示 ---
            llm_messages = [{"role": "system", "content": SYSTEM_PROMPT}]

            # 如果有相关记忆，添加到上下文中
            if relevant_memories:
                memory_texts = []
                for mem_obj in relevant_memories:
                    if isinstance(mem_obj, dict):
                        memory_texts.append(mem_obj.get('memory', ''))
                    else:
                        memory_texts.append(str(mem_obj))

                memory_context = "以下是相关的记忆信息：\n" + "\n".join([f"- {memory}" for memory in memory_texts if memory])
                emotion_hint = ""
                if detected_emotion:
                    emotion_hint = f"\n\n用户当前可能的情感状态：{detected_emotion}，请以温暖、共情的方式回应。"

                llm_messages.append(
                    {
                        "role": "system",
                        "content": f"记忆上下文:\n{memory_context}{emotion_hint}\n\n请根据这些记忆信息和情感状态来回应用户。"
                    }
                )

            # 添加用户当前输入
            llm_messages.append({"role": "user", "content": user_input})

            # --- 3. 调用火山引擎 LLM ---
            print(">>> 正在调用火山引擎 LLM 生成回复...")
            response = await llm_client.chat.completions.create(
                model=VOLCANO_LLM_ENDPOINT_ID,
                messages=llm_messages,
                max_tokens=1024,
                temperature=0.7,
            )
            ai_response_content = response.choices[0].message.content
            print(f"\nAI: {ai_response_content}")

            # --- 4. 将新的对话存入 Mem0 记忆 ---
            print(">>> 正在将新对话存入记忆库...")
            try:
                # 构建对话消息格式
                conversation_messages = [
                    {"role": "user", "content": user_input},
                    {"role": "assistant", "content": ai_response_content}
                ]

                # 准备元数据，包含情感信息
                metadata = {
                    "timestamp": int(asyncio.get_event_loop().time()),
                    "session": "emotional_companion",
                    "conversation_turn": f"turn_{len(user_input)}"
                }

                # 如果检测到情感，添加到元数据
                if detected_emotion:
                    metadata["emotion"] = detected_emotion
                    metadata["emotional_context"] = True

                # 添加到 Mem0，让它自动提取重要信息
                add_result = await mem0_client.add(
                    messages=conversation_messages,
                    user_id=USER_ID,
                    metadata=metadata,
                    output_format="v1.1"
                )

                # 简化的存储反馈
                if add_result and hasattr(add_result, 'results') and add_result.results:
                    print(f">>> ✓ 已保存 {len(add_result.results)} 条新记忆")
                else:
                    print(">>> ✓ 记忆已保存")

            except Exception as e:
                print(f">>> ⚠️ 记忆保存遇到问题: {e}")
                # 不影响主流程，继续对话

            # 增加对话计数，定期进行记忆管理
            conversation_count += 1
            if conversation_count % 5 == 0:  # 每5轮对话进行一次记忆分析
                await manage_memory_importance(mem0_client, USER_ID)

        except Exception as e:
            print(f"\n在聊天过程中发生错误: {e}")
            print("请检查你的 API Keys、网络连接和模型 Endpoint ID 是否正确。")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断。")
            break


async def test_mem0_connection():
    """
    测试 Mem0 连接和基本功能的辅助函数
    """
    print("--- 🔧 Mem0 连接和功能测试 ---")
    try:
        if not MEM0_API_KEY:
            print("❌ 错误：未找到 MEM0_API_KEY")
            return False

        mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)

        # 1. 测试基本连接
        print(">>> 测试1: 基本连接...")
        try:
            memories = await mem0_client.get_all(user_id=USER_ID, limit=1)
            print("✓ 基本连接成功")
        except Exception as e:
            print(f"❌ 基本连接失败: {e}")
            return False

        # 2. 测试添加记忆功能
        print(">>> 测试2: 添加记忆功能...")
        test_user_id = f"test_user_{int(asyncio.get_event_loop().time())}"
        test_messages = [
            {"role": "user", "content": "测试用户: 我的名字是测试用户"},
            {"role": "assistant", "content": "好的，我记住了你的名字是测试用户"}
        ]

        try:
            add_result = await mem0_client.add(
                messages=test_messages,
                user_id=test_user_id,
                metadata={"test": True, "timestamp": int(asyncio.get_event_loop().time())}
            )
            print(f"✓ 添加记忆成功: {add_result}")
        except Exception as e:
            print(f"❌ 添加记忆失败: {e}")
            return False

        # 3. 等待索引完成
        print(">>> 等待3秒让记忆索引完成...")
        await asyncio.sleep(3)

        # 4. 测试搜索功能
        print(">>> 测试3: 搜索记忆功能...")
        try:
            search_result = await mem0_client.search(
                query="测试用户",
                user_id=test_user_id,
                limit=5
            )

            if search_result and (
                (hasattr(search_result, 'results') and search_result.results) or
                (isinstance(search_result, list) and search_result)
            ):
                print("✓ 搜索记忆成功")
                print(f"搜索结果: {search_result}")
            else:
                print("⚠️ 搜索记忆无结果")
                print(f"搜索返回: {search_result}")

        except Exception as e:
            print(f"❌ 搜索记忆失败: {e}")
            return False

        # 5. 测试获取所有记忆
        print(">>> 测试4: 获取所有记忆...")
        try:
            all_memories = await mem0_client.get_all(user_id=test_user_id, limit=10)

            if all_memories and (
                (hasattr(all_memories, 'results') and all_memories.results) or
                (isinstance(all_memories, list) and all_memories)
            ):
                print("✓ 获取所有记忆成功")
                print(f"获取结果: {all_memories}")
            else:
                print("⚠️ 获取所有记忆无结果")
                print(f"获取返回: {all_memories}")

        except Exception as e:
            print(f"❌ 获取所有记忆失败: {e}")
            return False

        # 6. 清理测试数据
        print(">>> 测试5: 清理测试数据...")
        try:
            await mem0_client.delete_all(user_id=test_user_id)
            print("✓ 清理测试数据成功")
        except Exception as e:
            print(f"⚠️ 清理测试数据失败: {e}")

        print("\n🎉 所有测试通过！Mem0 API 工作正常")
        return True

    except Exception as e:
        print(f"❌ 整体测试失败: {e}")
        import traceback
        print(f"完整错误栈: {traceback.format_exc()}")
        return False


async def show_user_memories():
    """
    显示用户的所有记忆（用于调试）
    """
    try:
        if not MEM0_API_KEY:
            print("错误：未找到 MEM0_API_KEY")
            return

        mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)

        print(f"\n--- 📋 用户 {USER_ID} 的记忆诊断 ---")

        # 尝试不同的API格式获取记忆
        print(">>> 尝试使用 v1.1 格式获取记忆...")
        try:
            memories_v11 = await mem0_client.get_all(user_id=USER_ID, limit=50, output_format="v1.1")
            print(f">>> v1.1 API 返回结果: {type(memories_v11)}")
            print(f">>> v1.1 结果内容: {memories_v11}")
        except Exception as e:
            print(f">>> v1.1 格式获取失败: {e}")
            memories_v11 = None

        print(">>> 尝试使用默认格式获取记忆...")
        try:
            memories_default = await mem0_client.get_all(user_id=USER_ID, limit=50)
            print(f">>> 默认 API 返回结果: {type(memories_default)}")
            print(f">>> 默认结果内容: {memories_default}")
        except Exception as e:
            print(f">>> 默认格式获取失败: {e}")
            memories_default = None

        # 处理结果
        memories = memories_v11 or memories_default

        if memories and hasattr(memories, 'results') and memories.results:
            print(f"\n✅ 总共找到 {len(memories.results)} 条记忆:")
            for i, memory in enumerate(memories.results, 1):
                memory_text = memory.get('memory', 'N/A')
                memory_id = memory.get('id', 'N/A')
                created_at = memory.get('created_at', 'N/A')
                updated_at = memory.get('updated_at', 'N/A')
                categories = memory.get('categories', [])
                metadata = memory.get('metadata', {})

                print(f"\n{i}. 记忆 ID: {memory_id}")
                print(f"   内容: {memory_text}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                if categories:
                    print(f"   分类: {', '.join(categories)}")
                if metadata:
                    print(f"   元数据: {metadata}")

        elif memories and isinstance(memories, list):
            print(f"\n✅ 总共找到 {len(memories)} 条记忆:")
            for i, memory in enumerate(memories, 1):
                print(f"{i}. {memory}")

        else:
            print("\n❌ 该用户暂无记忆记录")
            print("🔍 可能的原因:")
            print("   1. 记忆确实没有存储成功")
            print("   2. 记忆正在处理中，需要等待几分钟")
            print("   3. API Key 权限不足")
            print("   4. 用户ID不匹配")

        # 尝试搜索测试
        print(f"\n--- 🔍 搜索功能测试 ---")
        test_queries = ["alex", "hello", "姓名", "name", "用户"]
        for query in test_queries:
            try:
                search_result = await mem0_client.search(query=query, user_id=USER_ID, limit=5)
                if search_result and (
                    (hasattr(search_result, 'results') and search_result.results) or
                    (isinstance(search_result, list) and search_result)
                ):
                    print(f">>> 搜索 '{query}': 找到结果")
                    if hasattr(search_result, 'results'):
                        for res in search_result.results[:2]:
                            print(f"    - {res.get('memory', str(res))[:50]}...")
                    else:
                        for res in search_result[:2]:
                            print(f"    - {res.get('memory', str(res))[:50]}...")
                else:
                    print(f">>> 搜索 '{query}': 无结果")
            except Exception as e:
                print(f">>> 搜索 '{query}' 失败: {e}")

    except Exception as e:
        print(f"❌ 获取记忆时出现错误: {e}")
        import traceback
        print(f"完整错误栈: {traceback.format_exc()}")


async def clear_user_memories():
    """
    清除用户的所有记忆（用于调试和重新开始）
    """
    try:
        if not MEM0_API_KEY:
            print("错误：未找到 MEM0_API_KEY")
            return

        print(f"⚠️  警告：即将删除用户 {USER_ID} 的所有记忆！")
        confirm = input("请输入 'DELETE' 来确认删除所有记忆: ").strip()

        if confirm != "DELETE":
            print("操作已取消。")
            return

        mem0_client = AsyncMemoryClient(api_key=MEM0_API_KEY)

        # 删除用户的所有记忆
        result = await mem0_client.delete_all(user_id=USER_ID)
        print(f"✓ 已成功删除用户 {USER_ID} 的所有记忆")
        print(f"删除结果: {result}")

    except Exception as e:
        print(f"删除记忆时出现错误: {e}")


async def debug_config():
    """
    显示当前配置和环境信息
    """
    print("\n--- 🔍 配置和环境信息诊断 ---")

    print(f">>> 用户ID: {USER_ID}")
    print(f">>> MEM0_API_KEY 状态: {'✓ 已设置' if MEM0_API_KEY else '❌ 未设置'}")
    if MEM0_API_KEY:
        print(f">>> MEM0_API_KEY 长度: {len(MEM0_API_KEY)} 字符")
        print(f">>> MEM0_API_KEY 前缀: {MEM0_API_KEY[:10]}..." if len(MEM0_API_KEY) > 10 else f">>> MEM0_API_KEY: {MEM0_API_KEY}")

    print(f">>> ARK_API_KEY 状态: {'✓ 已设置' if ARK_API_KEY else '❌ 未设置'}")
    print(f">>> VOLCANO_LLM_ENDPOINT_ID 状态: {'✓ 已设置' if VOLCANO_LLM_ENDPOINT_ID else '❌ 未设置'}")

    if VOLCANO_LLM_ENDPOINT_ID:
        print(f">>> VOLCANO_LLM_ENDPOINT_ID: {VOLCANO_LLM_ENDPOINT_ID}")

    # 检查环境文件
    import os
    env_file_path = "1.env"
    if os.path.exists(env_file_path):
        print(f">>> 环境文件 {env_file_path}: ✓ 存在")
        try:
            with open(env_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f">>> 环境文件内容长度: {len(content)} 字符")
                lines = content.strip().split('\n')
                for line in lines:
                    if line.strip() and not line.strip().startswith('#') and '=' in line:
                        key = line.split('=')[0].strip()
                        print(f"    - {key}: {'已设置' if '=' in line and line.split('=')[1].strip() else '未设置'}")
        except Exception as e:
            print(f">>> 读取环境文件失败: {e}")
    else:
        print(f">>> 环境文件 {env_file_path}: ❌ 不存在")

    # Python包版本检查
    try:
        import mem0
        print(f">>> mem0ai 版本: {getattr(mem0, '__version__', '未知')}")
    except ImportError:
        print(">>> mem0ai: ❌ 未安装")

    try:
        import openai
        print(f">>> openai 版本: {getattr(openai, '__version__', '未知')}")
    except ImportError:
        print(">>> openai: ❌ 未安装")

    try:
        import dotenv
        print(f">>> python-dotenv: ✓ 已安装")
    except ImportError:
        print(">>> python-dotenv: ❌ 未安装")


if __name__ == "__main__":
    print("=== Mem0 + 火山引擎 LLM 带记忆的 AI 助手 ===")
    print("选择操作:")
    print("1. 开始聊天")
    print("2. 测试 Mem0 API 功能")
    print("3. 查看用户记忆 (详细诊断)")
    print("4. 清除所有记忆 (⚠️ 危险操作)")
    print("5. 显示配置信息")

    try:
        choice = input("请输入选择 (1-5): ").strip()

        if choice == "1":
            asyncio.run(chat_with_ai())
        elif choice == "2":
            asyncio.run(test_mem0_connection())
        elif choice == "3":
            asyncio.run(show_user_memories())
        elif choice == "4":
            asyncio.run(clear_user_memories())
        elif choice == "5":
            asyncio.run(debug_config())
        else:
            print("无效选择，启动默认聊天模式...")
            asyncio.run(chat_with_ai())

    except KeyboardInterrupt:
        print("\n程序被用户中断。")
