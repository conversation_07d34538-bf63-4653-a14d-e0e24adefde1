"""
测试CrisisDetectionService - 故事1.7-B
基于Pre-development Test Cases中的Gherkin测试场景
"""
import pytest
from unittest.mock import Mock, patch

# 这些导入现在会失败，因为我们还没有实现这些服务
from api.services.crisis_detection_service import CrisisDetectionService
from api.settings import Settings


class TestCrisisDetectionService:
    """测试危机检测服务的核心功能"""

    def setup_method(self):
        """为每个测试方法设置测试环境"""
        # 测试用的关键词列表
        self.test_keywords = ["自杀", "不想活了", "想死", "结束生命", "自残"]
        self.crisis_service = CrisisDetectionService(keywords=self.test_keywords)

    # 测试场景组1: 危机信号检测 (AC1)

    def test_detect_explicit_crisis_keywords(self):
        """
        Scenario 1.1: 检测明确的危机关键词
        Given CrisisDetectionService已配置关键词列表 ["自杀", "不想活了", "想死"]
        When 用户输入消息 "我真的不想活了"
        Then CrisisDetectionService.detect()返回True
        """
        user_message = "我真的不想活了"
        result = self.crisis_service.detect(user_message)
        assert result is True, "应该检测到危机信号"

    def test_case_insensitive_crisis_detection(self):
        """
        Scenario 1.2: 大小写不敏感的危机检测 (架构师风险点)
        Given CrisisDetectionService已配置关键词列表 ["自杀", "想死"]
        When 用户输入消息 "我想自杀" 或 "我想死"
        Then CrisisDetectionService.detect()返回True
        And 检测应不受大小写影响
        """
        test_cases = [
            "我想自杀",
            "我想死",
            "我想自杀",  # 重复测试不同表达
        ]

        for message in test_cases:
            result = self.crisis_service.detect(message)
            assert result is True, f"应该检测到危机信号: {message}"

    def test_partial_matching_and_various_expressions(self):
        """
        Scenario 1.3: 部分匹配和多种表达方式 (架构师风险点)
        Given CrisisDetectionService已配置关键词列表包含自杀相关表达
        When 用户输入包含 "不想活" 或 "结束生命" 或 "活着没意思"
        Then CrisisDetectionService.detect()返回True
        """
        # 扩展关键词列表以支持部分匹配
        extended_keywords = self.test_keywords + ["不想活", "活着没意思"]
        crisis_service = CrisisDetectionService(keywords=extended_keywords)

        test_cases = [
            "我不想活下去了",
            "想要结束生命",
            "感觉活着没意思"
        ]

        for message in test_cases:
            result = crisis_service.detect(message)
            assert result is True, f"应该检测到危机信号: {message}"

    def test_non_crisis_content_not_falsely_detected(self):
        """
        Scenario 1.4: 非危机内容不被误检
        Given CrisisDetectionService已配置危机关键词列表
        When 用户输入正常消息 "今天心情不太好，但是会好起来的"
        Then CrisisDetectionService.detect()返回False
        """
        normal_messages = [
            "今天心情不太好，但是会好起来的",
            "我很累，想要休息一下",
            "生活有点困难，但我会坚持",
            "最近工作压力大"
        ]

        for message in normal_messages:
            result = self.crisis_service.detect(message)
            assert result is False, f"不应该误检为危机信号: {message}"

    def test_empty_or_none_input(self):
        """测试空输入或None输入的处理"""
        assert self.crisis_service.detect("") is False
        assert self.crisis_service.detect(None) is False
        assert self.crisis_service.detect("   ") is False  # 只有空格

    def test_very_long_input_performance(self):
        """
        Scenario 5.2: 极长用户输入的处理
        Given 用户输入超长文本（>10000字符）包含危机关键词
        When CrisisDetectionService处理该输入
        Then 检测应在合理时间内完成（<100ms）
        """
        import time

        # 创建超长文本，中间包含危机关键词
        long_text = "这是一段很长的文本。" * 2000 + "我不想活了" + "这还是很长的文本。" * 2000

        start_time = time.time()
        result = self.crisis_service.detect(long_text)
        end_time = time.time()

        processing_time = end_time - start_time
        assert result is True, "应该检测到危机信号"
        assert processing_time < 0.1, f"处理时间应小于100ms，实际: {processing_time:.3f}s"


class TestCrisisDetectionServiceConfiguration:
    """测试危机检测服务的配置相关功能"""

    def test_keywords_list_configuration_update(self):
        """
        Scenario 1.5: 关键词列表配置更新
        Given 系统配置中的CRISIS_KEYWORDS可以动态更新
        When 管理员更新危机关键词列表
        Then CrisisDetectionService应加载新的关键词配置
        """
        initial_keywords = ["自杀", "想死"]
        crisis_service = CrisisDetectionService(keywords=initial_keywords)

        # 初始检测
        assert crisis_service.detect("我想自残") is False

        # 更新关键词列表
        updated_keywords = initial_keywords + ["自残"]
        crisis_service.update_keywords(updated_keywords)

        # 更新后应该能检测到
        assert crisis_service.detect("我想自残") is True

    def test_configuration_missing_handling(self):
        """
        Scenario 5.1: 配置缺失的错误处理
        Given CRISIS_KEYWORDS配置为空或未设置
        When 系统启动或处理消息
        Then 应使用默认的危机关键词列表
        """
        # 测试空关键词列表
        crisis_service = CrisisDetectionService(keywords=[])

        # 应该使用默认关键词列表
        assert len(crisis_service.get_default_keywords()) > 0

        # 使用默认关键词检测
        crisis_service_with_defaults = CrisisDetectionService()
        assert crisis_service_with_defaults.detect("我想自杀") is True


class TestCrisisDetectionServiceIntegration:
    """测试危机检测服务的集成功能"""

    @patch('api.services.crisis_detection_service.settings')
    def test_load_keywords_from_settings(self, mock_settings):
        """测试从配置文件加载关键词"""
        mock_settings.CRISIS_KEYWORDS_LIST = ["测试关键词", "危机信号"]

        crisis_service = CrisisDetectionService.from_settings()

        assert crisis_service.detect("我有测试关键词问题") is True
        assert crisis_service.detect("发出危机信号") is True
        assert crisis_service.detect("正常消息") is False

    def test_concurrent_crisis_detection(self):
        """
        Scenario 5.3: 并发危机检测处理
        Given 多个用户同时发送包含危机信号的消息
        When 系统并发处理这些请求
        Then 每个请求应独立正确处理
        """
        import asyncio
        from concurrent.futures import ThreadPoolExecutor

        crisis_service = CrisisDetectionService(keywords=["自杀", "想死"])

        def detect_crisis(message):
            return crisis_service.detect(message)

        # 模拟并发请求
        test_messages = [
            "我想自杀",
            "今天很好",
            "我想死",
            "正常消息",
            "我要自杀"
        ]

        with ThreadPoolExecutor(max_workers=5) as executor:
            results = list(executor.map(detect_crisis, test_messages))

        expected_results = [True, False, True, False, True]
        assert results == expected_results, f"并发检测结果不正确: {results}"
