# 心桥项目概述

**版本：** 1.2  
**日期：** 2025年7月1日  
**作者：** <PERSON>, Architect

## 1. 项目简介

本文档是"心桥"项目的技术架构概述，涵盖了后端智能服务和前端移动应用的核心设计理念。

## 1.1 后端架构核心

本方案的核心是设计一个高度模块化、服务化的`agent-api`后端。该后端通过标准的Webhook模式接收火山引擎RTC服务的实时语音(ASR)事件，然后通过内部的**对话编排服务 (`ChatOrchestrationService`)**，自主完成与大语言模型(LLM)的交互、工具调用(Function Calling)和记忆检索。

这种**原生LLM编排（Native LLM Orchestration）**架构，使我们能够完全掌控对话逻辑，实现复杂的、多步骤的AI交互，同时与底层LLM及记忆服务（Zep/Mem0）保持松耦合，从而在保证实时性的前提下，实现最深度、最自然的个性化陪伴体验。

## 1.2 前端架构核心

前端采用React Native + Expo技术栈，基于Obytes脚手架构建高性能、用户友好的移动应用。重点实现双模交互（语音/文本）界面，确保为老年用户提供零负担的使用体验。

## 1.3 技术特色

- **原生LLM编排模式：** 由后端服务自主编排LLM与工具调用。
- **可插拔记忆系统：** 可动态切换Zep/Mem0等专业记忆服务。
- **双模交互界面：** 语音与文本无缝切换的用户体验。
- **高性能优化：** 端到端延迟控制和渲染性能保障 