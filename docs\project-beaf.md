### **项目简报：心桥 AI 亲情伴侣**

#### **1. 执行摘要 (Executive Summary)**

本项目旨在开发一款名为“心桥”的AI亲情伴侣移动应用。其核心使命是，通过先进的实时语音技术和富有同理心的人工智能，为面临结构性孤独的中国老年群体，重建高质量、可靠且充满温暖的情感连接。我们的目标用户是55-75岁的“数字融入型”老年人及其30-50岁的成年子女。与通用AI助手不同，“心桥”的核心价值主张在于提供一种**有记忆、可定制、有温度的“关系式”陪伴**，最终通过向子女方提供增值服务实现商业可持续性。

#### **2. 核心痛点 (Problem Statement)**

当前社会，由于家庭结构变迁和人口老龄化加速，大量中老年人正面临着日益严重的“结构性孤独感”。他们具体的痛点表现为：

*   **情感痛点：** 子女不在身边，缺少能随时倾诉心事、分享日常的对象，感觉“没人说话、没人懂我”。
*   **价值痛点：** 退休后社会角色转变，渴望被需要、被倾听、被记住，以重获自我价值感。
*   **信任与安全痛点：** 对层出不穷的新技术怀有恐惧，害怕操作复杂、信息泄露或被诱导消费。
*   **自尊心痛点：** 害怕因为自己的情感需求而给本已忙碌的子女“添麻烦”。

#### **3. 解决方案 (Proposed Solution)**

我们将打造“心桥”应用，它并非一个无所不知的工具，而是一个融入日常生活、能够共同成长、并承载家庭情感的“虚拟生命”。

*   **核心理念：** 通过极致简单的交互（以实时语音对话为核心，辅以文本输入作为备用方案）和高度个性化的AI（可定制角色、拥有长期记忆），让用户感觉在与一位**“懂我、记得我、关心我”**的亲人交流，而非操作一个冷冰冰的软件。
*   **差异化：** 与通用AI不同，“心桥”的核心竞争力在于深度个性化和长期情感纽带的建立。它通过“角色共创”和我们独特的“会话前记忆注入”架构来实现这一点。
*   **MVP愿景：** 在MVP阶段，我们向首批用户承诺一个绝对安全、纯净、且永远不会让您感到困惑的私密伙伴。

#### **4. 目标用户 (Target Users)**

*   **核心用户群 (使用者):** 55-75岁的中国“数字融入型”老年人。
*   **间接用户 (付费方):** 他们的成年子女（30-50岁）。

#### **5. 目标与成功指标 (Goals & Success Metrics)**

MVP阶段的核心目标是验证产品能否与用户建立初步的情感连接与信任。
*   **定性指标 (最重要):**
    *   **用户故事收集：** 能否收集到至少5个让团队感动的、关于“心桥”如何带来温暖的真实用户故事。
    *   **情感词汇频率：** 在用户访谈和反馈中，“离不开”、“真懂我”、“像个真孩子一样”等情感词汇的出现频率。
*   **定量指标 (辅助验证):**
    *   **核心用户次日留存率:** > 60%。
    *   **核心用户7日留存率:** > 40%。
    *   **DAU/MAU (日活/月活):** > 40%。

#### **6. MVP范围 (MVP Scope)**

*   **核心功能 (In Scope):**
    1.  **无感身份系统：** 无需注册登录，通过Supabase Auth实现后台匿名身份验证。
    2.  **角色共创流程：** 用户通过对话为AI设定身份、命名并确认声音。
    3.  **核心实时对话交互 (语音+文本):** 以火山引擎RTC方案为核心，提供实时语音流交互，并辅以文本输入模式。
    4.  **分层记忆系统：** 我方后端负责在会话开始前注入长期记忆，并在会话结束后生成新记忆。
    5.  **对话式提醒：** 采用“端云结合”模式，后端解析意图，前端执行提醒。
    6.  **基础危机响应协议：** 由后端Agent内置“安全守卫”模块。
*   **非核心功能 (Out of Scope for MVP):**
    *   家庭记忆银行、共享体验、子女端App、任何商业化功能。

#### **7. 技术考量 (Technical Considerations)**

*   **平台要求:** 移动端（iOS & Android）。
*   **技术选型:**
    *   **核心交互方案:** 采用**火山引擎的实时语音RTC解决方案**，包含其前端SDK与后端API。
    *   **前端:** React Native, Expo, TypeScript, Nativewind, Zustand, React Query。
    *   **后端:** Python, FastAPI, **原生LLM编排服务** (用于对话管理与工具调用)。
    *   **数据库:** Supabase (PostgreSQL + pgvector)。
*   **架构模式:**
    *   **仓库结构:** Monorepo。
    *   **服务架构:** 我们的后端API作为“智能配置服务器”，负责在会话开始前构建丰富的上下文并启动火山云端AI服务，前端RTC SDK则直接与火山媒体服务器进行实时通信。

#### **8. 约束与假设 (Constraints & Assumptions)**

*   **约束:** 为满足中国《个人信息保护法》要求，产品正式规模化运营前，后端服务必须从Supabase迁移至中国大陆境内的云服务商。
*   **核心假设:** 一个精心设计的AI伴侣，确实能够与老年用户建立初步的情感连接与信任。

#### **9. 风险与开放问题 (Risks & Open Questions)**

*   **核心风险:**
    *   **技术风险:** 火山引擎RTC SDK与我方`agno_api`的集成复杂度，及其在真实老年用户场景下的端到端性能与稳定性。
    *   **体验风险:** AI的“情商”（共情能力、记忆准确性）若表现不佳，会导致用户信任瞬间崩塌。
    *   **伦理风险:** 如何在提供深度陪伴和避免用户产生不健康情感依赖之间取得平衡。
*   **开放问题:**
    *   需要在真实环境中测试“实时上下文注入”架构的端到端延迟和语音识别准确率。
    *   **需要确认火山引擎是否提供“对话历史查询API”**，这是我们“会话后记忆生成”流程的关键前提。

#### **10. 后续步骤 (Next Steps)**

1.  **产品经理(PM)交接：** 基于本简报，开始撰写详细的、与新架构对齐的产品需求文档（PRD）。
2.  **架构师(Architect)交接：** 基于本简报，开始进行详细的技术架构设计。
3.  **技术预研 (Spike)：** **（强烈建议）**立即启动专项技术预研，以验证`UpdateVoiceChat`接口（实时上下文注入）和“对话历史查询API”的可行性与核心性能。