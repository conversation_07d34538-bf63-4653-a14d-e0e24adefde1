# Agent API 源文件结构文档

## 项目概览

Agent API 是基于模块化设计的 Python 后端服务，采用清晰的分层架构组织代码。项目结构遵循 FastAPI 最佳实践，便于维护和扩展。

## 项目根目录结构

```
apps/agent-api/
├── 📁 api/                    # FastAPI 应用核心模块
├── 📁 agents/                 # AI Agent 实现模块
├── 📁 config/                 # 应用配置文件
├── 📁 db/                     # 数据库连接和模型
├── 📁 migrations/             # Alembic 数据库迁移脚本
├── 📁 scripts/                # 开发和部署脚本
├── 📁 tests/                  # 测试代码
├── 📄 pyproject.toml          # 项目配置和依赖定义
├── 📄 requirements.txt        # 依赖版本锁定文件
├── 📄 README.md               # 项目文档
├── 📄 LICENSE                 # 开源许可证
└── 📄 example.env             # 环境变量示例
```

## 核心模块详解

### 🎯 API 模块 (`api/`)

FastAPI 应用的核心实现，负责 HTTP 服务和路由管理。

```
api/
├── 📄 __init__.py             # 模块初始化文件
├── 📄 main.py                 # FastAPI 应用入口点
├── 📄 settings.py             # 应用配置管理
├── 📁 routes/                 # API 路由定义
│   ├── 📄 __init__.py         # 路由模块初始化
│   ├── 📄 v1_router.py        # v1 版本路由聚合器
│   ├── 📄 agents.py           # Agent 相关 API 端点
│   ├── 📄 auth.py             # 认证相关 API 端点
│   ├── 📄 health.py           # 健康检查端点
│   └── 📄 playground.py       # Agno Playground 集成
├── 📁 services/               # 业务逻辑服务层
│   ├── 📄 auth_service.py     # 认证服务
│   └── 📄 agent_service.py    # Agent 管理服务
├── 📁 middleware/             # 自定义中间件
│   └── 📄 auth_middleware.py  # 认证检查中间件
└── 📁 models/                 # Pydantic 模型定义
    ├── 📄 auth_models.py      # 认证相关的请求/响应模型
    └── 📄 agent_models.py     # Agent 相关的模型
```

#### 核心文件功能

**`main.py`** - FastAPI 应用工厂
- 创建 FastAPI 应用实例，配置中间件 (CORS, Auth)
- 集成各版本路由 (`v1_router`)
- 设置全局异常处理器和 OpenAPI 文档

**`settings.py`** - 应用配置管理
- 基于 Pydantic Settings 的配置类
- 从环境变量加载配置

**`routes/`** - API 路由层
- **`v1_router.py`**: 聚合所有 v1 版本的 API 路由
- **`agents.py`**: Agent 对话、知识库管理等核心端点
- **`auth.py`**: 用户注册、登录、Token 刷新等认证端点
- **`health.py`**: 用于负载均衡和监控的健康检查
- **`playground.py`**: Agno Playground 集成，用于调试

**`services/`** - 业务逻辑层
- 封装核心业务逻辑，保持路由处理函数简洁
- **`auth_service.py`**: 处理用户认证、JWT 生成与验证等
- **`agent_service.py`**: 管理 Agent 的创建、配置和交互

**`middleware/`** - 中间件层
- 实现跨请求的通用逻辑，如认证检查、日志记录等

**`models/`** - 数据模型层
- 定义所有 API 请求和响应的 Pydantic 模型，确保数据类型安全

### 🤖 Agents 模块 (`agents/`)

AI Agent 的具体实现，每个 Agent 都有独特的功能和配置。

```
agents/
├── 📄 __init__.py             # 模块初始化
├── 📄 selector.py             # Agent 选择器和工厂
├── 📄 agno_assist.py          # Agno 技术支持 Agent
├── 📄 finance_agent.py        # 金融数据分析 Agent
└── 📄 web_agent.py            # Web 搜索 Agent
```

#### Agent 实现详解

**`selector.py`** - Agent 工厂模式
- 定义 `AgentType` 枚举，统一管理所有 Agent 类型
- 提供 `get_agent()` 工厂函数，根据类型创建对应的 Agent 实例

**`{agent_name}.py`** - 具体 Agent 实现
- **功能**: 定义 Agent 的具体能力、知识库和工具集
- **配置**: 初始化 Agent 所需的模型、指令、记忆系统等

### 🗄️ Database 模块 (`db/`)

数据库连接、会话管理和数据模型定义。

```
db/
├── 📄 __init__.py             # 模块初始化
├── 📄 url.py                  # 数据库 URL 构建
├── 📄 session.py              # SQLAlchemy 会话管理
└── 📁 models/                 # SQLAlchemy ORM 模型
    ├── 📄 __init__.py         # 模型模块初始化
    ├── 📄 user.py             # 用户表模型
    └── 📄 memory.py           # 记忆表模型
```

**`url.py`** - 数据库连接配置
- 从环境变量读取数据库连接信息

**`session.py`** - 会话管理
- 创建 SQLAlchemy Engine 和 SessionLocal 工厂
- 提供 `get_db()` FastAPI 依赖注入函数

**`models/`** - ORM 模型层
- 定义与数据库表对应的 SQLAlchemy 模型类

### 📂 Migrations 模块 (`migrations/`)

Alembic 数据库迁移脚本，用于版本化管理数据库结构。

```
migrations/
├── 📄 alembic.ini             # Alembic 配置文件
├── 📄 env.py                  # Alembic 运行环境脚本
└── 📁 versions/               # 迁移版本文件
    └── 📄 xxxxx_create_users_table.py
```

### ⚙️ Config 模块 (`config/`)

存放非环境变量的静态配置文件。

```
config/
└── 📄 agno_config.json        # Agno 框架特定配置文件 (如果需要)
```

## 数据流架构

### 请求处理流程

```mermaid
graph TD
    A[HTTP Request] --> B{FastAPI};
    B --> C[Middleware];
    C --> D{Router};
    D --> E[Path Operation Function];
    E --> F[Dependencies (e.g., get_db)];
    E --> G[Service Layer];
    G --> H[DB/External API];
    H --> G;
    G --> E;
    E --> I[Response Model];
    I --> B;
    B --> J[HTTP Response];
```

1. **HTTP Request**: 客户端发起请求
2. **FastAPI**: 接收请求
3. **Middleware**: 执行认证、日志等中间件
4. **Router**: 将请求分发到对应的路由处理函数
5. **Path Operation Function**: 执行端点逻辑
6. **Dependencies**: 注入数据库会话、当前用户等依赖
7. **Service Layer**: 调用服务层处理核心业务逻辑
8. **DB/External API**: 与数据库或外部API交互
9. **Response Model**: 使用Pydantic模型序列化响应数据
10. **HTTP Response**: 返回响应给客户端

---

*本文档描述了 agent-api v0.2.0 (假设版本) 的源文件结构，旨在提供清晰、模块化和可扩展的代码组织方案。* 