# 依赖包
node_modules/
npm-debug.*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
pnpm-error.log*
lerna-debug.log*

# Expo
.expo/
.expo-shared/
dist/
web-build/
expo-env.d.ts

# React Native
.bundle
.metro-health-check*
android/app/build/
ios/build/
ios/Pods/

# 构建产物
build/
dist/
out/

# 测试覆盖率
coverage/
.nyc_output/
*.lcov

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# 日志文件
*.log
logs/

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 调试和分析文件
*.hprof
.flipper/

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
# macOS
.DS_Store
.AppleDouble
.LSOverride
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
# Linux
*~

# 临时文件
tmp/
temp/
.tmp/

# 证书和密钥文件
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.keystore

# Git 相关
*.orig.*
*.rej

# 其他工具生成的文件
.maestro/
.husky/_/

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

# @end expo-cli