#!/usr/bin/env python3
"""
基础API测试工具类
提供通用的认证、日志记录和HTTP请求功能
支持测试间数据共享和配置管理
"""

import asyncio
import json
import logging
import time
import uuid
import os
import shutil
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import aiohttp
import sys
from pathlib import Path

class BaseAPITester:
    """基础API测试器"""

    # 配置文件路径
    CONFIG_FILE = Path("e2e_tests/shared_config.json")

    def __init__(self, test_name: str, base_url: str = "http://localhost:8003"):
        self.test_name = test_name
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.user_id: Optional[str] = None

        # 加载共享配置
        self.shared_config = self._load_shared_config()

        # 设置日志
        self._setup_logging()

        # 测试数据 - 优先使用共享配置中的数据
        self.test_data = {
            "device_id": self.shared_config.get("device_id", f"test-device-{uuid.uuid4()}"),
            "platform": self.shared_config.get("platform", "android"),
            "app_version": self.shared_config.get("app_version", "1.0.0"),
            "nickname": self.shared_config.get("nickname", "测试用户"),
        }

        # 从共享配置中加载认证信息
        if "auth" in self.shared_config:
            auth_data = self.shared_config["auth"]
            self.access_token = auth_data.get("access_token")
            self.refresh_token = auth_data.get("refresh_token")
            self.user_id = auth_data.get("user_id")

        # 测试结果
        self.test_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "errors": []
        }

    def _load_shared_config(self) -> Dict[str, Any]:
        """加载共享配置文件"""
        if self.CONFIG_FILE.exists():
            try:
                with open(self.CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config
            except (json.JSONDecodeError, IOError) as e:
                print(f"⚠️ 读取配置文件失败: {e}")
                return {}
        return {}

    def _save_shared_config(self):
        """保存共享配置文件"""
        try:
            # 确保目录存在
            self.CONFIG_FILE.parent.mkdir(parents=True, exist_ok=True)

            with open(self.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.shared_config, f, indent=2, ensure_ascii=False)
        except IOError as e:
            self.logger.error(f"❌ 保存配置文件失败: {e}")

    def update_shared_config(self, key: str, value: Any):
        """更新共享配置中的单个值"""
        self.shared_config[key] = value
        self._save_shared_config()
        self.logger.info(f"✅ 更新共享配置: {key}")

    def update_shared_config_batch(self, updates: Dict[str, Any]):
        """批量更新共享配置"""
        self.shared_config.update(updates)
        self._save_shared_config()
        self.logger.info(f"✅ 批量更新共享配置: {list(updates.keys())}")

    def get_shared_config(self, key: str, default: Any = None) -> Any:
        """获取共享配置中的值"""
        return self.shared_config.get(key, default)

    @classmethod
    def cleanup_test_data(cls):
        """清理所有测试数据和日志（类方法，供01_health_test使用）"""
        print("🧹 开始清理历史测试数据...")

        # 清理日志目录
        logs_dir = Path("e2e_tests/logs")
        if logs_dir.exists():
            try:
                shutil.rmtree(logs_dir)
                print("✅ 清理日志目录完成")
            except Exception as e:
                print(f"⚠️ 清理日志目录失败: {e}")

        # 清理共享配置文件
        if cls.CONFIG_FILE.exists():
            try:
                cls.CONFIG_FILE.unlink()
                print("✅ 清理共享配置文件完成")
            except Exception as e:
                print(f"⚠️ 清理共享配置文件失败: {e}")

        print("🎯 测试数据清理完成，准备开始新的测试轮次")

    def save_auth_info(self):
        """保存认证信息到共享配置"""
        if self.access_token:
            auth_data = {
                "access_token": self.access_token,
                "refresh_token": self.refresh_token,
                "user_id": self.user_id,
                "updated_at": datetime.now().isoformat()
            }
            self.update_shared_config("auth", auth_data)

            # 同时保存设备信息
            device_data = {
                "device_id": self.test_data["device_id"],
                "platform": self.test_data["platform"],
                "app_version": self.test_data["app_version"],
                "nickname": self.test_data["nickname"]
            }
            self.update_shared_config_batch(device_data)

    def save_test_result(self, test_key: str, result_data: Dict[str, Any]):
        """保存测试结果到共享配置"""
        if "test_results" not in self.shared_config:
            self.shared_config["test_results"] = {}

        self.shared_config["test_results"][test_key] = {
            **result_data,
            "completed_at": datetime.now().isoformat(),
            "test_name": self.test_name
        }
        self._save_shared_config()
        self.logger.info(f"✅ 保存测试结果: {test_key}")

    def _setup_logging(self):
        """设置日志记录"""
        # 创建e2e_tests/logs目录
        log_dir = Path("e2e_tests/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"{self.test_name}_{timestamp}.log"

        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 清除现有handlers
        logger = logging.getLogger()
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # 文件handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # 控制台handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        ))

        # 配置root logger
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        self.logger = logger
        self.logger.info(f"🚀 开始测试: {self.test_name}")
        self.logger.info(f"📍 目标服务器: {self.base_url}")
        self.logger.info(f"📄 日志文件: {log_file}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()

    def _get_headers(self, include_auth: bool = True) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"E2E-Tester-{self.test_name}/1.0"
        }

        if include_auth and self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"

        return headers

    async def make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        include_auth: bool = True,
        expected_status: int = 200
    ) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers(include_auth)
        start_time = time.time()

        self.logger.info(f"🔄 {method} {url}")
        if data:
            self.logger.debug(f"📤 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
        if params:
            self.logger.debug(f"📤 Request Params: {json.dumps(params, indent=2, ensure_ascii=False)}")

        try:
            async with self.session.request(
                method, url,
                json=data,
                params=params,
                headers=headers
            ) as response:
                duration = time.time() - start_time
                response_text = await response.text()

                self.logger.info(f"📥 Response Status: {response.status} ({duration:.3f}s)")

                if response_text:
                    try:
                        response_data = json.loads(response_text)
                        self.logger.debug(f"📥 Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                    except json.JSONDecodeError:
                        self.logger.debug(f"📥 Response Text: {response_text}")
                        response_data = {"raw_text": response_text}
                else:
                    response_data = {}

                if response.status == expected_status:
                    self.logger.info(f"✅ {method} {endpoint} - SUCCESS")
                    self.test_results["passed"] += 1
                    return response_data
                else:
                    self.logger.error(f"❌ {method} {endpoint} - FAILED - Expected {expected_status}, got {response.status}")
                    self.test_results["failed"] += 1
                    error_info = {
                        "endpoint": endpoint,
                        "method": method,
                        "expected_status": expected_status,
                        "actual_status": response.status,
                        "response": response_data,
                        "duration": duration
                    }
                    self.test_results["errors"].append(error_info)
                    return response_data

        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(f"❌ {method} {endpoint} - EXCEPTION: {str(e)} ({duration:.3f}s)")
            self.test_results["failed"] += 1
            error_info = {
                "endpoint": endpoint,
                "method": method,
                "error": str(e),
                "duration": duration
            }
            self.test_results["errors"].append(error_info)
            return {"error": str(e)}
        finally:
            self.test_results["total"] += 1

    async def ensure_authenticated(self):
        """确保用户已认证"""
        if self.access_token:
            self.logger.info("✅ 用户已认证（从共享配置加载）")
            return

        self.logger.info("🔐 开始匿名登录...")
        data = {
            "device_info": {
                "device_id": self.test_data["device_id"],
                "platform": self.test_data["platform"],
                "app_version": self.test_data["app_version"]
            }
        }

        response = await self.make_request(
            "POST",
            "/api/v1/auth/anonymous-login",
            data=data,
            include_auth=False
        )

        if "access_token" in response:
            self.access_token = response["access_token"]
            self.refresh_token = response["refresh_token"]
            self.user_id = response.get("user", {}).get("id")
            self.logger.info(f"✅ 登录成功，用户ID: {self.user_id}")

            # 保存认证信息到共享配置
            self.save_auth_info()
        else:
            self.logger.error("❌ 登录失败")
            raise Exception("认证失败")

    async def get_character_id(self):
        """获取第一个可用角色ID"""
        # 优先从共享配置中获取
        cached_character_id = self.get_shared_config("character_id")
        if cached_character_id:
            self.logger.info(f"✅ 使用缓存的角色ID: {cached_character_id}")
            return cached_character_id

        # 如果没有缓存，则从API获取
        response = await self.make_request("GET", "/api/v1/characters", params={"page": 1, "limit": 10})
        if "data" in response and len(response["data"]) > 0:
            character_id = response["data"][0]["id"]
            self.logger.info(f"✅ 获取角色ID: {character_id}")

            # 保存到共享配置
            self.update_shared_config("character_id", character_id)
            return character_id
        else:
            self.logger.error("❌ 无法获取角色ID")
            raise Exception("无可用角色")

    def print_results(self):
        """打印测试结果"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info(f"📊 {self.test_name} 测试结果")
        self.logger.info("=" * 60)
        self.logger.info(f"总计: {self.test_results['total']}")
        self.logger.info(f"成功: {self.test_results['passed']}")
        self.logger.info(f"失败: {self.test_results['failed']}")

        if self.test_results["total"] > 0:
            success_rate = (self.test_results['passed'] / self.test_results['total'] * 100)
            self.logger.info(f"成功率: {success_rate:.1f}%")

        if self.test_results["errors"]:
            self.logger.info(f"\n❌ 失败详情:")
            for i, error in enumerate(self.test_results["errors"], 1):
                self.logger.error(f"{i}. {error}")

        self.logger.info("=" * 60)
