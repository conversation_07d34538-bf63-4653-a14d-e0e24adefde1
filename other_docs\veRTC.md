好的，我已经仔细阅读并分析了您提供的“心桥”项目架构文档（`architecture.md`）和产品需求文档（`PRD.md`）。基于这两个文档中明确的技术选型和功能需求，我为您从火山引擎官方文档中筛选并整理出了所有相关的、必要的文档内容。

这个定制化的文档集合将作为您开发团队的核心参考，确保您在实现**原生LLM编排架构**、集成**实时对话式AI**、以及使用**React Native SDK**时，拥有准确、完整、无遗漏的官方指导。

以下是为您构建“心桥”项目所需的所有火山引擎RTC文档内容，已按照开发流程和逻辑模块进行组织。

---

### **“心桥”项目火山引擎RTC技术文档开发手册**

本文档根据“心桥”项目的具体需求，从官方文档中提取了所有相关部分，涵盖了从服务开通、后端AI编排、到前端SDK集成的全过程。

#### **第一部分：服务开通与基础配置**

在开始开发前，您需要完成火山引擎账号的注册、服务的开通，并理解其核心的鉴权和应用管理机制。

**1.1 开通服务**
这是所有操作的第一步，您必须先开通RTC服务。

---
<details>
<summary>点击展开：开通服务</summary>

### 开通服务--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/69865

/实时音视频/快速入门/开通服务

开通服务
最近更新时间：2025.05.07 12:09:47首次发布时间：2021.07.21 14:28:57

要为你的应用接入实时音视频服务，你必须先开通实时音视频服务。
要开通实时音视频服务，你必须遵循以下步骤：
## 步骤1：登录火山引擎控制台
登录 。
  * 如果你是首次登录，请先注册账号，参看。
  * 如果你已拥有火山引擎账号，请先登录，参看。


## 步骤2：实名认证
登录成功后，你必须先进行实名认证，参看：
## 步骤3：申请开通 RTC 服务
你必须在总览页选择 ，点击 领取礼包并开通 。
## 步骤4：创建 RTC 应用，获取 AppId
  1. 【可选】在多人团队协作中，你可能希望通过子账户（IAM）功能细化权限管理，参看创建和授权子账号应用管理权限。
  2. 登录 后，你可以在 「应用管理」 中管理应用，包括修改应用名称，查看 AppID、 AppKey、创建时间等。 其中，defaultAppName 为系统默认创建。


> 说明
>   1. AppId 是每个应用的唯一标识符，在调用 volcEngineRTC SDK 的 API 接口实现功能，如创建对象时，你必须填入你获取到的 AppId 。
>   2. AppKey 是每个应用对应的密钥，请妥善保管。AppKey 用于生成 Token 鉴权，一旦你的密钥泄露，可能会被盗用流量。
> 

  1. 如果你需要创建新的应用，可以在 「应用管理」 中点击创建应用 , 提交更多创建应用的申请。
</details>

**1.2 鉴权：Token机制**
理解Token的生成和使用是保障应用安全、连接RTC服务的核心。**您的后端服务必须实现Token生成逻辑。**

---
<details>
<summary>点击展开：使用 Token 完成鉴权</summary>

### 使用 Token 完成鉴权--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/70121

/实时音视频/开发指南/鉴权/使用 Token 完成鉴权

使用 Token 完成鉴权
最近更新时间：2025.06.27 14:28:46首次发布时间：2021.07.21 14:29:15

Token 是 RTC 用于验证客户端身份和权限的安全凭证，由您的应用服务端生成，并下发给应用客户端。客户端在加入 RTC 房间或登录实时消息服务时，必须携带有效的 Token。RTC 服务端在收到进房请求时会校验此 Token，以确保请求的合法性。如果校验不成功，进房会失败。
## 鉴权流程
整个鉴权生命周期分为两个阶段：首次进房和 Token 过期更新。
说明
你需要自行实现步骤 1，2，3，4，11 的代码逻辑。
  1. 客户端向你的应用服务端申请 Token。
  2. 应用服务端根据 AppID、RTC AppKey、RoomID、UserID、时间戳等信息生成 Token。
  3. 应用服务端将生成的 Token 下发给客户端。
  4. 客户端使用获取到的 Token 申请加入房间。 
> 加入房间时设置的 uid 和 roomid 需与用于生成 Token 的 uid 和 roomid 保持一致，否则会加入房间失败，并收到错误提示为 ERROR_CODE_INVALID_TOKEN 的 onRoomStateChanged 回调。
  5. RTC 服务端验证 Token 的合法性。
  6. 应用客户端收到来自 RTC SDK 的回调，获取加入房间的结果（成功/失败）。
  7. 若生成 Token 时设置了有效期，当 SDK 检测到 Token 的进房权限将在 30 秒内过期时，触发 onTokenWillExpire 回调。 
> Token 过期后，用户将被移出房间，并收到 ERROR_CODE_INVALID_TOKEN 回调，错误码是 ERROR_CODE_TOKEN_EXPIRED。需要在申请新的 Token 之后调用 JoinRoom 加入房间。
  8. 此时，如果客户端需要继续进行音视频通话，需要申请新的 Token。
  9. 如步骤 2。
  10. 如步骤 3。
  11. 调用 updateToken 接口，使用新的 Token，更新 Token。


## 生成 Token
在您的应用服务端实现 Token 的生成，可以参考以下示例代码（包含了多种语言）：
RTC_Token.zip
47.53KB
以 Golang 为例：
```
var (
// 确保通话时使用的 appID， roomID 和 userID 与用于生成 Token 的相同，否则会导致进房失败。
  appID = "xxxxx" 
  appKey = "xxxxx" 
  roomID = "room" // 生成用于登录实时消息服务的 Token 时传空值
  userID = "uid"
)
t := AccessToken.New(appID, appKey, roomID, userID)
// 添加此 Token 的有效时间，两小时过期。过期后，你无法使用此 Token 进房。
t.ExpireTime(time.Now().Add(time.Hour * 2))
// 添加订阅流权限
t.AddPrivilege(AccessToken.PrivSubscribeStream, time.Time{})
// 添加发布流权限
t.AddPrivilege(AccessToken.PrivPublishStream, time.Time{})
// 获取最终生成的 token
token,err := t.Serialize()

go

```

## 生成通配 Token
在需要频繁切换房间的场景下，可以使用通配 Token，解决频繁请求 Token 可能造成的进房延误和 Token 服务端压力过大等问题。同一用户使用通配 Token 可以加入不同的 RTC 房间。
## 生成临时 Token
在开发和测试阶段，为快速验证业务逻辑，你可以在 RTC 控制台上生成临时 Token，无需在应用服务端部署 Token 生成服务。
> 临时 Token 有效期仅为 7 天且安全性低， 仅适用于测试阶段，严禁用于生产环境。项目正式上线前，请务必切换为由你应用服务端生成的正式 Token。
获取方式：前往 ，选择您的应用，在操作栏单击临时Token。
  * 测试音视频通话时：当需要多个设备加入同一个房间进行通话时，请为每个设备都生成一个独立的 Token，且房间 ID 需相同、用户 ID 必须不同。
  * 仅测试实时消息时：房间 ID 可填写任意值，用户 ID 需为目标登录用户。


## Token 使用常见问题
常见问题参看。
</details>

**1.3 ID参数规范**
所有ID类参数（如RoomId, UserId）都必须遵循火山引擎的命名规范。

---
<details>
<summary>点击展开：参数赋值规范</summary>

### 参数赋值规范--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/70114

/实时音视频/常见问题/集成相关/参数赋值规范

参数赋值规范
最近更新时间：2025.01.08 15:30:23首次发布时间：2022.05.11 21:28:21

## 问题详述
参数赋值有规范吗？
## 问题解答
对于不同参数，你应遵守不同的赋值规范。
### ID 类参数
对于 ID 类参数，如 userId，roomId，streamId 等，你必须遵守以下规范：
  * 同时使用 RTC SDK 和各子产品 SDK，例如 RTS、白板时，注意 userId、roomId 不能重复，建议加前缀进行区分。
  * 长度不超过 128 个字符；
  * 仅支持以下字符：

类型| 说明  
---|---  
a~z| 小写英文字母  
A~Z| 大写英文字母  
0-9| 数字  
@| /  
_| /  
-| /  
.| /  
> 参数中区分字符的大小写。 隐私保护声明：请勿在此字段填写用户敏感信息，包括但不限于手机号、身份证号、护照编号、真实姓名等。
### 视频参数：分辨率、帧率、码率
参看。
</details>

---
#### **第二部分：后端服务核心能力——实时对话式AI**

"心桥"项目的后端核心是**原生LLM编排**，这意味着您不直接使用火山引擎的完整AI Agent，而是利用其**ASR**和**TTS**能力，并在中间插入自己的对话编排逻辑。这正是火山引擎“实时对话式AI”方案所支持的。

**2.1 方案集成准备**
在调用相关API前，必须完成RTC、ASR、TTS、LLM服务的开通和授权。

---
<details>
<summary>点击展开：方案集成前置准备</summary>

### 方案集成前置准备--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1315561

/实时音视频/实时对话式 AI/方案集成/方案集成前置准备

方案集成前置准备
最近更新时间：2025.06.12 17:48:16首次发布时间：2024.07.16 17:04:59

本文提供集成实时对话式 AI 场景的接入准备指南，涵盖服务开通、权限配置等步骤，确保 RTC 具备基础的资源接入能力与调用权限。
操作流程
集成前准备包括开通服务和配置权限 2 步：
  1. 开通服务：依次开通 RTC、ASR、LLM、TTS 服务。
  2. 配置权限：根据业务需要，配置不同权限的主账号或子账号以调用智能体与真人用户进行对话。


## 一、开通服务
### 步骤 1：开通 RTC 服务
参看，开通 RTC 服务。开通后系统会自动新建一个默认应用，可直接使用且享受赠送额度。如需多个应用可自行创建。
### 步骤 2：开通 ASR 和 TTS 服务
  1. 登录。
  2. 单击创建应用按钮，在弹出的抽屉栏中填写应用名称和应用简介，并选择服务。和本方案相关的服务、选择要求和建议如下表所示。

类别| 服务| 服务选择建议| 要求  
---|---|---|---  
ASR| 流式语音识别| 响应延迟较小，适用于响应速度要求高的语音控制场景，例如，实时问答、语音控制和会议速记| 至少选择 1 项  
流式语音识别大模型| 识别准确率更高，适用于内容复杂或背景噪音较多的场景，例如，嘈杂场景对话、专业术语识别、多语种混合  
TTS| 语音合成| 常规语音播报需求，适合短语或标准回复，例如，提醒、系统反馈、数字播报| 至少选择 1 项  
语音合成大模型| 注重语音自然度与情感表现的交互，适用于对话中需要自然语调与情感表达的场景，例如，客户服务、教育陪练、故事讲述  
声音复刻大模型| 可生成近似真人语音，适用于需要构建定制化语音形象时使用，例如，品牌数字人、虚拟主播、游戏角色  
  1. 点击确定按钮，完成应用创建。


注意
  * 开通后默认为试用版，享有免费使用额度。
  * 为保证线上长期正常使用，建议后续请开通正式版并购买资源包。但注意开通正式版后，试用版赠送的免费使用额度将自动清空。


### 步骤 3：开通 LLM 服务
火山引擎提供 3 种方式调用大模型服务。
调用方法| 服务选择建议  
---|---  
使用火山方舟在线推理| 可快速调用火山方舟大模型。详情可参看在火山方舟平台创建并获取自定义推理接入点进行调用。说明仅支持使用自定义推理接入点接入大模型，不支持使用预置推理接入点。  
使用火山方舟应用| 可通过零代码组合插件、知识库、工作流等功能调用大模型或基于应用 SDK 实现高代码编排方式。详情可参看使用火山方舟创建应用并获取应用 ID 进行调用。  
使用 Coze 应用| 可在零代码或低代码的基础上，组合插件、知识库、工作流等功能调用大模型。详情可参看 创建 Coze 智能体并获取 Coze 智能体 ID进行调用。  
你也可以接入第三方大模型，但是第三方大模型接口需要符合约定标准规范，详情查看。
## 二、配置不同权限账号调用智能体
你可以根据业务需要选择以下方式调用智能体：
主账号| 子账号  
---|---  
服务端(推荐)| 使用主账号在服务端调用操作简单，鉴权仅需获取 AK SK 密钥信息，且密钥信息存储在服务端不易泄漏，但注意主账户具有账户的完全权限。| 使用子账号在服务端调用操作相对复杂，但是密钥信息存储在服务端不易泄漏，且子账号只拥有赋予的调用智能体的权限，实现了权限的隔离和管控，操作安全。  
客户端| 使用主账号在客户端调用操作简单，鉴权仅需获取 AK SK 密钥信息，但注意 AK SK 存储在客户端容易泄漏，且注意主账户具有账户的完全权限，| 使用子账号在客户端调用操作复杂，鉴权需要获取临时AK、SK、临时 Token 密钥信息，避免了密钥信息一经泄漏造成重大损失，且子账号只在临时 Token 生效期间拥有调用智能体的权限，实现了权限最小化管控。  
使用主账号在服务端调用
使用主账号在客户端调用
使用子账号在服务端调用
使用子账号在客户端调用
你可按照以下步骤实现使用主账号在服务端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 调用接口。


此时使用主账号的 AK、SK 即可调用 StartVoiceChat 接口，实现房间内智能体与真人用户互动。
你可按照以下步骤实现使用主账号在客户端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 调用接口。


此时使用主账号的 AK、SK 即可调用 StartVoiceChat 接口，实现房间内智能体与真人用户互动。
你可按照以下步骤实现使用子账号在服务端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 为子账号添加调用实时对话式 AI 接口权限。
    1. 登录主账号 。
    2. 前往，点击**为子账号添加权限，**找到你需要授权的子账号，点击添加权限。


  1. 调用接口。


此时使用子账号的 AK、SK 即可调用 StartVoiceChat 接口，实现房间内智能体与真人用户互动。
你可按照以下步骤实现使用子账号在客户端调用接口。
  1. 在主账号配置VoiceChatRoleForRTC 角色。
你需要在主账号配置 VoiceChatRoleForRTC 角色，以便 RTC 能够调用 ASR 、TTS 和 LLM 服务来实现与智能体实时对话，实现房间内智能体与真人用户互动。
    1. 登录主账号 。
    2. 前往，点击一键开通跨服务授权配置角色。
配置成功后你可以在中找到该角色，该角色拥有SAFullAccess 、 MaaSExperienceAccess 和 RTCFullAccess权限。
  2. 为子账号赋予获取临时密钥权限。 子账号获取临时密钥权限后，即可在密钥有效期内调用实时对话式 AI 接口。
    1. 登录主账号 。
    2. 点击用户头像选择访问控制。
    1. 在左侧导航栏选择角色点击VoiceChatRoleForRTC。
    1. 点击信任关系，填入以下代码为子账号添加信任关系。
将信任关系中的代码修改为如下代码，以便子账号拥有使用VoiceChatRoleForRTC角色的权限。
```
{
  "Statement": [
   {
      "Effect": "Allow",
      "Action": [
        "sts:AssumeRole"
      ],
      "Principal": {
        "Service": [
         "rtc"
        ],
        "IAM": [
         "trn:iam::2100********:user/cody" //格式为：trn:iam::${主账号AccountId}:user/${子账号UserName}
        ]
      }
   }
  ]
}

JSON

```

    1. 在左侧导航栏选择用户，点击待授权用户，即子账号用户名。
    1. 点击权限，选择 添加权限，，添加 STSAssumeRoleAccess 权限。
  3. 调用接口


使用子账号调用 OpenAPI 接口获取临时 AK、SK 及 Token。获取成功后，即可在密钥有效期内通过临时 AK、SK 及 Token 调用 StartVoiceChat 接口实现房间内智能体与真人用户互动。 调用AssumeRole接口时 RoleSessionName 和 RoleTrn为必填项。RoleSessionName可自定义，用来标志此次AssumeRole请求。RoleTrn填写规则为 trn:iam::${AccountId}:role/${RoleName}，例如trn:iam::2100****3232:user/cody。
</details>

**2.2 核心API：启动、更新与停止AI会话**
您的后端服务将围绕这三个核心API来管理整个AI对话生命周期。

---
<details>
<summary>点击展开：启动、更新与停止智能体</summary>

### 启动智能体 StartVoiceChat--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1558163

/实时音视频/实时对话式 AI/服务端 OpenAPI/启动智能体 StartVoiceChat

启动智能体 StartVoiceChat
最近更新时间：2025.07.03 21:59:33首次发布时间：2025.05.15 13:15:32

### 更新智能体 UpdateVoiceChat--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1404671

/实时音视频/实时对话式 AI/服务端 OpenAPI/更新智能体 UpdateVoiceChat

更新智能体 UpdateVoiceChat
最近更新时间：2025.06.11 17:42:16首次发布时间：2024.12.31 16:11:14

> 本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考。
在实时音视频通话场景中，若你需要对智能体进行操作，比如在智能体进行语音输出时进行打断，可以通过调用此接口实现。
## 使用说明
### 调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看。
## 注意事项
  * 请求频率：单账号下 QPS 不得超过 60。
  * 该接口请求接入地址仅支持 rtc.volcengineapi.com。


## 请求说明
  * 请求方式：POST
  * 请求地址：https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01


## 调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
## 请求参数
下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见。
### Query
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
Action| String| 是| UpdateVoiceChat| 接口名称。当前 API 的名称为 UpdateVoiceChat。  
Version| String| 是| 2024-12-01| 接口版本。当前 API 的版本为 2024-12-01。  
### Body
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
AppId| String| 是| 661e****543cf| 你的音视频应用的唯一标志，参看创建 RTC 应用获取或创建 AppId。  
RoomId| String| 是| Room1| 房间的 ID，是房间的唯一标志，由你自行定义、生成与维护，参数定义规则参看参数赋值规范。  
TaskId| String| 是| Task1| 智能体任务 ID  
Command| String| 是| interrupt| 更新指令 interrupt：打断智能体。 function：传回工具调用信息指令。 ExternalTextToSpeech ： 传入文本信息供 TTS 音频播放。使用方法参看自定义语音播放。 ExternalPromptsForLLM：传入自定义文本与用户问题拼接后送入 LLM。 ExternalTextToLLM：传入外部问题送入 LLM。根据你设定的优先级决定替代用户问题或增加新一轮对话。 FinishSpeechRecognition：触发新一轮对话。   
Message| String| 否|  "{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}" | 工具调用信息指令。 注意 Command 取值为 function、ExternalTextToSpeech、ExternalPromptsForLLM和ExternalTextToLLM时，Message必填。 当 Command 取值为 function时，Message 格式需为 Json 转译字符串，例如： "{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}" 其他取值时格式为普通字符串，例如你刚才的故事讲的真棒。" 当 Command 取值为 ExternalTextToSpeech时，message 传入内容建议不超过 200 个字符。   
InterruptMode| Integer| 否| 1| 传入文本信息或外部问题时，处理的优先级。 1：高优先级。传入信息直接打断交互，进行处理。 2：中优先级。等待当前交互结束后，进行处理。 3：低优先级。如当前正在发生交互，直接丢弃 Message 传入的信息。 注意 当 command 为 ExternalTextToSpeech 或 ExternalTextToLLM 时为该参数必填。   
## 返回参数
本接口无特有的返回参数。公共返回参数请见。 其中返回值 Result 仅在请求成功时返回 ok,失败时为空。
## 请求示例
```
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "User1",
  "Command": "interrupt"
}

json

```

## 返回示例
```
{
  "Result": "ok",
  "ResponseMetadata": {
    "RequestId": "20230****10420",
    "Action": "UpdateAudioBot",
    "Service": "rtc",
    "Region": "cn-north-1"
  }
}

json

```

### 关闭智能体 StopVoiceChat--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1404672

/实时音视频/实时对话式 AI/服务端 OpenAPI/关闭智能体 StopVoiceChat

关闭智能体 StopVoiceChat
最近更新时间：2025.02.12 15:24:08首次发布时间：2024.12.31 16:11:14

> 本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考。
在实时音视频通话场景中，若你需要结束智能体的语音聊天服务，可以通过调用此接口实现。
## 使用说明
### 调用接口
关于调用接口的请求结构、公共参数、签名方法、返回结构，参看。
## 注意事项
  * 请求频率：QPS 不得超过 60。
  * 该接口请求接入地址仅支持 rtc.volcengineapi.com。


## 请求说明
  * 请求方式：POST
  * 请求地址：https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01


## 调试
API Explorer
您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。
## 请求参数
### Query
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
Action| String| 是| StopVoiceChat| 接口名称。当前 API 的名称为 StopVoiceChat。  
Version| String| 是| 2024-12-01| 接口版本。当前 API 的版本为 2024-12-01。  
### Body
参数| 类型| 是否必选| 示例值| 描述  
---|---|---|---|---  
AppId| String| 是| 661e****543cf| 你的音视频应用的唯一标志，参看获取 AppId  
RoomId| String| 是| Room1| 房间的 ID，是房间的唯一标志。赋值规则参看参数赋值规范。  
TaskId| String| 是| task1| 智能体任务 ID  
## 返回参数
本接口无特有的返回参数。公共返回参数请见。 其中返回值 Result 仅在请求成功时返回 ok,失败时为空。
## 请求示例
```
POST https://rtc.volcengineapi.com?Action=StopVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1"
}

json

```

## 返回示例
```
{
  "Result": "ok",
  "ResponseMetadata": {
    "RequestId": "Your_Re20230****10420questId",
    "Action": "StopVoiceChat",
    "Version": "2024-12-01",
    "Service": "rtc",
    "Region": "cn-north-1"
  }
}

json

```
</details>

**2.3 关键能力：Function Calling**
PRD明确要求通过对话设置提醒，这需要使用Function Calling能力。您需要通过`StartVoiceChat`来配置工具，并通过Webhook接收和响应工具调用请求。

---
<details>
<summary>点击展开：Function Calling（非流式返回结果）</summary>

### Function Calling（非流式返回结果）--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1359441

/实时音视频/实时对话式 AI/体验进阶/Function Calling（非流式返回结果）

Function Calling（非流式返回结果）
最近更新时间：2025.05.29 11:18:34首次发布时间：2024.11.05 11:56:09

在实时对话式 AI场景下，通过使用 Function Calling 功能允许大模型识别用户对话中的特定需求，智能调用外部函数、API 等工具来执行它自身无法独立完成的任务，如处理实时数据检索、文件处理、数据库查询等。适用于天气查询、股票行情查询、数学计算等场景。
说明
该功能仅在使用火山方舟平台模型时生效。且只有在使用 doubao 非1.5 代系模型时，按照非流式返回 Function Calling 结果。
服务端和客户端均可实现该功能。你可根据业务请求端的类型选择对应的方式。例如你在开发 AI 应用时，选择服务端响应请求，建议使用服务端实现传入大模型上下文，降低请求延迟。
## 时序图
你可参看如下时序图在该场景下使用 Function Calling 功能：
步骤 1：开启 Function Calling 功能。 步骤 2：触发 Function Calling 后接收工具调用指令消息。 步骤 3：执行本地工具获取工具调用结果，并将结果信息传回 RTC 服务端。 步骤 4：收到音频回复。 其中步骤 2 、3 支持多轮 Function calling 调用。当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## 服务端实现
### 步骤 1：开启 Function Calling 功能
你需要调用 接口配置以下字段开启该功能：
  1. 通过 LLMConfig.Tools字段配置一组或多组 Function（函数）工具相关的功能和定义。
  2. 通过LLMConfig.FunctionCallingConfig字段配置接收 Function Calling 功能返回的消息的 URL 和鉴权签名，返回消息包括函数被调用时触发的通知消息和函数调用指令消息。 
    1. ServerMessageUrl：接收 Function Calling 功能返回消息的 URL 地址。你指定的 URL 地址将收到来自 RTC 服务器的 HTTP(S) POST 请求发送的指令消息，格式为 JSON。
    2. ServerMessageSignature：鉴权签名。你可传入该鉴权字段，在收到 Function Calling 功能返回消息时，与步骤 2 返回工具调用指令消息中的 signature 字段的值进行对比，用于鉴权，保证消息的可靠性与安全性。


你可以参考以下示例代码进行请求：
```
POST https: //rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserPrompts": [
        {
          "Role": "user",
          "Content": "你好"
        },
        {
          "Role": "assistant",
          "Content": "有什么可以帮到你的？"
        }
      ],
      "HistoryLength": 3,
      "Tools": [
        {
          "Type": "function",
          "function": {
            "name": "get_current_weather",
            "description": "获取给定地点的天气",
            "parameters": {
              "type": "object",
              "properties": {
                "location": {
                  "type": "string",
                  "description": "地理位置，比如北京市"
                },
                "unit": {
                  "type": "string",
                  "description": "",
                  "enum": [
                    "摄氏度",
                    "华氏度"
                  ]
                }
              },
              "required": [
                "location"
              ]
            }
          }
        }
      ]
    }
    "FunctionCallingConfig": {
      "ServerMessageUrl": "https://example-domain.com/vertc/fc",
      "ServerMessageSignature": "b46a****8ad6a",
    },
    "AgentConfig": {
      "TargetUserId": [
        "user1"
      ],
      "WelcomeMessage": "Hello",
      "UserId": "BotName001"
    }
  }
}

JSON

```

### 步骤 2：接收工具调用指令消息
当用户的问题触发 Function Calling 时，会通过你在步骤 1 配置的 URL 地址，使用 HTTP(S) 请求返回本次函数工具调用的指令消息，返回的格式为 JSON 格式，内容如下：
字段名| 类型| 描述  
---|---|---  
message| Array of | 调用指令消息详情。  
signature| String| StartVoiceChat.Config.FunctionCallingConfig 中设置的 signature的值，用于鉴权。  
message：
字段名| 类型| 描述  
---|---|---  
id| String| 本次 Function Calling 任务的标识 ID。  
type| String| Function Calling 调用工具类型，固定为 function，表示为函数调用。  
function| | 调用函数详情。  
function：
字段名| 类型| 描述  
---|---|---  
name| String| 函数名称。  
arguments| String| 函数调用指令详情。  
### 步骤 3：将工具调用的结果信息传回 RTC 服务端
在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。 你可调用 接口设置以下参数将工具调用的结果信息传回 RTC 服务端：
你可参看以下示例将工具调用的结果信息传回 RTC 服务端：
```
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-06-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Command": "function",
  "Message":"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
}

JSON

```

### 步骤 4：获取 Function Calling 最终答复
当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## 客户端实现
### 步骤 1：开启 Function Calling 功能
你需要调用 接口配置 LLMConfig.Tools 字段，输入你需要的一组或多组 Function（函数）工具相关的功能和定义开启该功能。 你可以参考以下示例代码进行请求：
```
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
  "AppId": "661e****543cf",
  "RoomId": "Room1",
  "TaskId": "task1",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "93****21",
        "AccessToken": "MOaOa*****HA4h5B",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "94****11",
          "token": "OaO****ws1"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "epid****212",
      "MaxTokens": 1024,
      "Temperature": 0.1,
      "TopP": 0.3,
      "SystemMessages": [
        "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
      ],
      "UserMessages": [
        "user:\"你是谁\"",
        "assistant:\"我是问答助手\"",
        "user:\"你能干什么\"",
        "assistant:\"我能回答问题\""
      ],
      "HistoryLength": 3,
      "Tools": [
        {
          "Type": "function",
          "function": {
            "name": "get_current_weather",
            "description": "获取给定地点的天气",
            "parameters": {
              "type": "object",
              "properties": {
                "location": {
                  "type": "string",
                  "description": "地理位置，比如北京市"
                },
                "unit": {
                  "type": "string",
                  "description": "",
                  "enum": [
                    "摄氏度",
                    "华氏度"
                  ]
                }
              },
              "required": [
                "location"
              ]
            }
          }
        }
      ]
    }
  },
  "AgentConfig": {
    "TargetUserId": [
      "user1"
    ],
    "WelcomeMessage": "Hello",
    "UserId": "BotName001"
  }
}

JSON

```

### 步骤 2：接收工具调用指令消息
当用户的问题触发 Function Calling 时，你可通过 回调接收本次函数工具调用的指令消息。该回调中的 message 字段中的内容为函数调用指令消息，格式为二进制，使用前需解析。 message 的格式如下：
参数名| 类型| 描述  
---|---|---  
magic number| String| 消息格式，固定为 tool，表示此次消息为函数工具指令消息。  
length| String| 信息指令长度，单位为 bytes。存放方式为大端序。  
Tool_Calls| String| 信息指令详细信息。格式参看。  
Tool_Calls
参数名| 类型| 是否必填| 描述  
---|---|---|---  
tool_calls| Array of | 是| 工具调用信息列表。  
id| String| 是| 本次工具调用的唯一标识 ID。  
type| String| 是| 工具类型。  
tool_call
字段名| 类型| 描述  
---|---|---  
function| | 调用函数详情。  
function
字段名| 类型| 描述  
---|---|---  
name| String| 函数名称。  
arguments| String| 函数调用指令详情。  
你可参看以下示例代码对工具调用指令消息进行解析。
```
//定义结构体
struct function {
  std::string arguments;
  std::string name;
};
struct ToolCall {
  std::string id;
  std::string type;
  function func;
};
struct ToolCallsMsgData {
  std::string subscribe_user_id;
  std::vector<ToolCall> tool_calls
};
//回调事件
void onRoomBinaryMessageReceived(const char* uid, int size, const uint8_t* message) {
  std::string tool_calls;
  bool ret = Unpack(message, size, tool_calls);
  if(ret) {
    ParseData(tool_calls);
  }
}
//拆包校验
bool Unpack(const uint8_t *message, int size, std::string& tool_calls_msg) {
  int kToolCallsHeaderSize = 8;
  if(size < kToolCallsHeaderSize) { 
    return false;
  }
  // magic number "tool"
  if(static_cast<uint32_t>((static_cast<uint32_t>(message[0]) << 24) 
      | (static_cast<uint32_t>(message[1]) << 16) 
      | (static_cast<uint32_t>(message[2]) << 8) 
      | static_cast<uint32_t>(message[3])) != 0x746F6F6CU) {
    return false;
  }
  
  uint32_t length = static_cast<uint32_t>((static_cast<uint32_t>(message[4]) << 24) 
      | (static_cast<uint32_t>(message[5]) << 16) 
      | (static_cast<uint32_t>(message[6]) << 8) 
      | static_cast<uint32_t>(message[7]));
      
  if(size - kToolCallsHeaderSize != length) {
    return false;
  }
  if(length) {
    tool_calls_msg.assign((char*)message + kToolCallsHeaderSize, length);
  } else {
    tool_calls_msg = "";
  }
  return true;
}
//解析
void ParseData(const std::string& msg) {
  // 解析 JSON 字符串
  nlohmann::json json_data = nlohmann::json::parse(msg);
  ToolCallsMsgData toolcalls_data;
  // 存储解析后的数据
  toolcalls_data.subscribe_user_id = json_data["subscribe_user_id"];
  // 遍历 JSON 数据并填充结构体
  for (const auto& item : json_data["tool_calls"]) {
    ToolCall tool_call;
    tool_call.id = item["id"];
    tool_call.type = item["type"];
    auto fun_json = item["function"];
    tool_call.func.arguments = fun_json["arguments"];
    tool_call.func.name = fun_json["name"];
    toolcalls_data.push_back(tool_call);
  }
}

C++

```

### 步骤 3：将工具调用的结果信息传回 RTC 服务端
在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。 你可调用 接口设置以下参数将函数调用指令消息按照二进制格式传回 RTC 服务端：
  * userId：消息接收用户的 ID
  * buffer：工具调用的结果信息。
  * config：发送消息的可靠有序性。


指令消息格式如下：
参数名| 类型| 描述  
---|---|---  
magic number| String| 消息格式，固定为 func。  
length| String| 工具调用的结果信息长度，单位为 bytes。存放方式为大端序。  
Function_Response| String| 工具调用的结果信息。  
你可参看以下示例代码传回工具调用的结果信息。
TypeScript
Java
```
import VERTC from '@volcengine/rtc';
/**
 * @brief 将字符串包装成 TLV
 */
function stringToTLV(inputString: string) {
 const type = 'func';
 const typeBuffer = new Uint8Array(4);
 for (let i = 0; i < type.length; i++) {
  typeBuffer[i] = type.charCodeAt(i);
 }
 const lengthBuffer = new Uint32Array(1);
 const valueBuffer = new TextEncoder().encode(inputString);
 lengthBuffer[0] = valueBuffer.length;
 const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);
 tlvBuffer.set(typeBuffer, 0);
 tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
 tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
 tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
 tlvBuffer[7] = lengthBuffer[0] & 0xff;
 tlvBuffer.set(valueBuffer, 8);
 return tlvBuffer.buffer;
};
/**
 * @brief TLV 数据格式转换成字符串
 * @note TLV 数据格式
 * | magic number | length(big-endian) | value |
 * @param {ArrayBufferLike} tlvBuffer
 * @returns 
 */
function tlv2String(tlvBuffer: ArrayBufferLike) {
 const typeBuffer = new Uint8Array(tlvBuffer, 0, 4);
 const lengthBuffer = new Uint8Array(tlvBuffer, 4, 4);
 const valueBuffer = new Uint8Array(tlvBuffer, 8);
 let type = '';
 for (let i = 0; i < typeBuffer.length; i++) {
  type += String.fromCharCode(typeBuffer[i]);
 }
 const length =
  (lengthBuffer[0] << 24) | (lengthBuffer[1] << 16) | (lengthBuffer[2] << 8) | lengthBuffer[3];
 const value = new TextDecoder().decode(valueBuffer.subarray(0, length));
 return { type, value };
};
/**
 * @brief 通过 onRoomBinaryMessageReceived 接收 toolcall
 *    通过 sendUserBinaryMessage 发送 response
 */
function handleRoomBinaryMessageReceived(
 event: {
  userId: string;
  message: ArrayBuffer;
 },
) {
 const { message } = event;
 const { type, value } = tlv2String(message);
 const data = JSON.parse(value);
 const { tool_calls } = data || {};
 // 处理逻辑
 console.log(type);
 
 if (tool_calls?.length) {
  const name: string = tool_calls?.[0]?.function?.name;
  const map: Record<string, string> = {
   getcurrentweather: '今天下雪， 最低气温零下10度',
   musicplayer: '查询到李四的歌曲， 名称是千里之内',
   sendmessage: '发送成功',
  };
  this.engine.sendUserBinaryMessage(
   'Your AI Bot Name',
   stringToTLV(
    JSON.stringify({
     ToolCallID: tool_calls?.[0]?.id,
     Content: map[name.toLocaleLowerCase().replaceAll('_', '')],
    })
   )
  );
 }
};
/**
 * @brief 监听房间内二进制消息
 */
this.engine.on(VERTC.events.onRoomBinaryMessageReceived, handleRoomBinaryMessageReceived);

TypeScript

```

```
public void SendFunctionResponse(String ToolCallID, String Content) {
  JSONObject json = new JSONObject();
  try {
    json.put("ToolCallID", ToolCallID);
    json.put("Content", Content);
  } catch (JSONException e) {
    throw new RuntimeException(e);
  }
  String jsonString = json.toString();
  System.out.println(jsonString);
  app.rtcSdkWrap.sendUserBinaryMessage("RobotMan_", stringToTLV(jsonString));
}
public byte[] stringToTLV(String content) {
  String func_type = "func";
  byte[] prefixBytes = func_type.getBytes(StandardCharsets.UTF_8);
  byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
  int contentLength = contentBytes.length;
  ByteBuffer buffer = ByteBuffer.allocate(prefixBytes.length + 4 + contentLength);
  buffer.order(ByteOrder.BIG_ENDIAN);
  buffer.put(prefixBytes);
  buffer.putInt(contentLength);
  buffer.put(contentBytes);
  return buffer.array();
}

Java

```

### 步骤 4：获取 Function Calling 最终答复
当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。
## FAQ
Q1：配置了 Function Calling 后，通过 updateVoiceChat/sendUserBinaryMessage 返回给智能体的工具调用结果信息可以不读出来吗? A1：不可以。
Q2：配置了 Function Calling 后，相关配置如何更新？ A1：需要先调用 StopVoiceChat 接口停止智能体任务，然后再调用 StartVoiceChat 接口重新配置 Function Calling 功能。
</details>

**2.4 关键能力：自定义LLM交互**
“心桥”架构的核心是自主编排LLM。您不需要使用火山引擎的全托管Agent，而是需要一个能接收ASR文本，并能回传TTS文本的Webhook接口。这正是通过`接入第三方大模型或Agent`功能实现的。**您的后端服务在火山RTC看来，就是一个"第三方Agent"**。这份文档定义了您的后端`rtc_event_handler`接口需要遵循的Request和Response规范。

---
<details>
<summary>点击展开：接入第三方大模型或 Agent</summary>

### 接入第三方大模型或 Agent--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1399966

/实时音视频/实时对话式 AI/体验进阶/接入第三方大模型或 Agent

接入第三方大模型或 Agent
最近更新时间：2025.06.19 13:27:45首次发布时间：2024.12.17 17:10:35

在实时对话式 AI 场景中，支持集成第三方大模型或 Agent，以满足特定业务需求。为确保集成顺利，你需要提供第三方大模型或 Agent 的服务请求接口，并确保该接口符合火山引擎 RTC 标准规范，否则需要对其进行改造。
接入第三方大模型或 Agent 时，如 Dify Agent，支持通过以下方式传递自定义参数：
  * 直接在第三方大模型或 Agent 的请求 URL 中通过查询参数的方式拼接业务自定义参数，用于传递非敏感信息（如 session_id）。具体实现，参考。
  * 提供 custom 字段，通过 JSON 格式用于传递业务自定义参数。


接入方法
  1. 准备第三方大模型或 Agent 的请求接口 URL，并满足以下要求：
  2. 调用 接口时，按照规定参数结构配置 LLMConfig。关键配置如下：
     * Mode：固定设置为 "CustomLLM"。
     * URL：填写你的第三方大模型或 Agent 的完整 HTTPS URL。
说明
如果需要在每次请求时传递非敏感的业务信息（如 session_id），可以直接将它们作为查询参数拼接到此 URL 中，格式为：https://<第三方模型或Agent的API地址>?<业务自定义参数名>=<参数值>。
     * custom：可选，可通过该字段传递业务自定义参数。
配置示例：
> 下述示例中的参数需根据实际进行替换和配置。参数详细说明，请参见 。
```
{
  "LLMConfig": {
    "Mode": "CustomLLM", // 固定取值 CustomLLM
    "URL": "https://api.example.com/v1/custom_chat_agent?source=volc_rtc&version=1.2", // 填写你的第三方大模型或 Agent 的完整 HTTPS URL，此处示例中，source=volc_rtc&version=1.2 为自定义参数
    "ModelName": "my_agent_model_v2", // 可选，自定义第三方大模型或 Agent 的名称
    "APIKey": "sk-xxxxxxxxxxxxxxxxx", // 可选，如果需要 Bearer Token 鉴权
    "custom": {
      "session_id": "123456",
      "user_name": "user1"
    } // 可选，业务自定义参数
    // ... 其他 LLM 通用参数如 Temperature, MaxTokens 等可以按需配置 ...
  }
}

JSON

```



接口标准
在对话期间，将按照以下格式通过 POST 请求访问你配置的第三方大模型或 Agent 的接口地址，获取相应结果。接口返回需支持 SSE 协议。
## Request
## Response
注意
  * 协议：必须支持 SSE（Server-Sent Events）协议。
  * Headers：Content-Type 必须为 text/event-stream。
  * 结束符：必须包含 data: [DONE] 结束符。


### StatusCode==200
参数| 类型| 必填| 描述  
---|---|---|---  
id| String| 是| 请求 UUID。不同请求的 ID 需不同，但同一个流式请求的 ID 需相同。  
choices| Array of | 是| 流式回复对象。  
created| Int| 是| UnixTime 时间戳，精确到秒。同一个请求的流式所有块拥有相同的时间戳。  
usage| Object of | 否| 最后一个包可带此参数。  
object| String| 是| 取值：chat.completion.chunk。填错将导致接口行为异常。  
model| String| 是| 模型 ID。  
stream_options| object of | 是| 流接口选项。  
Choices
参数| 类型| 必填| 描述  
---|---|---|---  
finish_reason| String| 是| 
* null：流式请求未结束。最后一个流式片段需填入以下值：
  * stop：正常结束。
  * length：达到 MaxTokens。
  * content_filter：内容不合规。

  
delta| Object of | 是| 流式片段对象。  
index| Int| 是| 数组中位置，从 0 开始。  
delta
参数| 类型| 必填| 描述  
---|---|---|---  
content| String| 是| 对话内容。  
role| String| 是| 可取值及含义如下：
  * user
  * assistant

  
usage
参数| 类型| 必填| 描述  
---|---|---|---  
completion_tokens| Int| 是| generated tokens 长度。  
prompt_tokens| Int| 是| prompt tokens 长度。  
total_tokens| Int| 是| prompt+generated tokens 长度。  
stream_options
参数| 类型| 必填| 描述  
---|---|---|---  
include_usage| Boolean| 是| 固定为 true，表示流式传输。  
成功请求及返回样例如下：
```
curl -v --location 'https://rtc-cloud-rendering.byted.org/voicechat/test-sse' \
--header 'Content-Type: application/json' \
--data '{
  "messages":[{
    "role":"user",
    "content":"今天适合干什么？"
  }],
  "stream": true,
  "temperature": 0.1,
  "max_tokens": 100,
  "top_p": 0.9,
  "model": "doubao-32k",
  "stream_options": {"include_usage":true}
}'
> POST /voicechat/test-sse HTTP/1.1
> Host: rtc-cloud-rendering.byted.org
> User-Agent: curl/8.4.0
> Accept: */*
> Content-Type: application/json
> Content-Length: 254
>
< HTTP/1.1 200 OK
< Date: Thu, 15 Aug 2024 09:36:02 GMT
< Content-Type: text/event-stream
< Transfer-Encoding: chunked
< Connection: keep-alive
< Access-Control-Allow-Origin: *
< Cache-Control: no-cache
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"role":"assistant"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":1,"delta":{"content":"天"}},{"finish_reason":null,"index":0,"delta":{"content":"从明"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"起，"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":1,"delta":{"content":"幸福"}},{"finish_reason":null,"index":0,"delta":{"content":"做一个"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"的"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"人"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"。"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"喂马，"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"劈柴，"}},{"finish_reason":null,"index":1,"delta":{"content":"周游"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"世界"}},{"finish_reason":null,"index":1,"delta":{"content":"。"}}],"model":"doubao-32k-2024-07-25","created":1723714562}
data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":"stop","index":0,"delta":{}}],"model":"doubao-32k-2024-07-25","created":1723714562,"usage":{"prompt_tokens":1,"completion_tokens":2,"total_tokens":3}}
data: [DONE]

bash

```

### StatusCode==4xx、5xx
请求终止，报错返回。RTC 不做任何处理，仅报错。
参数| 类型| 必填| 描述  
---|---|---|---  
Error| | 是| 错误详情。  
Error
参数| 类型| 必填| 描述  
---|---|---|---  
Code| String| 是| 错误码。  
Message| String| 是| 错误原因。  
### StatusCode!==200、4xx、5xx
此时 RTC 不做任何处理，仅报错，直接报错返回 StatusCode。
使用工具验证第三方大模型或 Agent 的请求接口
你可以使用工具验证第三方大模型或 Agent 的请求接口是否符合标准规范。如果符合要求，工具可以返回大模型或 Agent 预期的输出结果，否则将返回错误信息。
注意
目前验证工具仅支持在 Linux 和 macOS 系统上使用。
## 下载和使用工具
  1. 下载验证工具包并解压。
Archive.zip
8.16MB
  2. 通过终端，使用 cd 命令进入工具包所在目录。
  3. 执行验证命令。
注意
如果你使用 zsh 终端，且 URL 中包含特殊字符（如 ?、=、& 等）时，必须对特殊字符进行转义。
     * Linux```
./app-linux <llmAPIUrl> <llmModel> <llmAPIKey> <LLMQuestion>

Bash

```

     * macOS```
./app-mac <llmAPIUrl> <llmModel> <llmAPIKey> <LLMQuestion>

Bash

```

参数说明如下： 参数名| 类型| 是否必填| 描述| 示例  
---|---|---|---|---  
llmAPIUrl| String| 是| 第三方大模型或 Agent 的完整请求 URL。| https://api.cn/v1/chat/completion  
llmModel| String| 否| 第三方大模型或 Agent 的名称。默认值为空。| model-v1-chat  
llmAPIKey| String| 否| APIKey。默认值为空。| sk-0aeQbtlX2reL  
LLMQuestion| String| 否| 发送给第三方大模型或 Agent 的测试问题。| 你好  


## 验证示例
执行结果类似如下所示，表示验证通过：
  * openAI
  * 自定义 URL


FAQ
## Q1：是否支持在本地环境调用和测试验证对话式 AI 第三方大模型或 Agent 接口？
支持。
## Q2：使用第三方大模型时，设置的多轮历史对话 HistoryLength 未生效，每次请求只传递了最新一条对话内容怎么办？
可以尝试以下排查：
  1. 检查你的大模型接口是否支持多轮历史对话。
  2. 确认第三方大模型的响应中是否包含正确的请求结束标识 data: [DONE]。如果第三方服务没有在响应末尾返回此结束符，系统可能无法正确识别一次完整请求的结束，从而无法正确地存储和使用历史对话记录。
</details>

---
#### **第三部分：客户端SDK实现（React Native）**

"心桥"项目的前端采用React Native + Expo技术栈。您需要使用火山引擎为React Native提供的SDK来处理实时音视频流的连接和渲染。

**3.1 SDK集成与API参考**
这部分包含了React Native SDK的概览、API详情、回调处理和类型定义，是前端开发的核心参考。

---
<details>
<summary>点击展开：React Native 3.58 - 概览</summary>

### 概览--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1390574

/实时音视频/客户端 API 参考/React Native 3.58/概览

概览
最近更新时间：2025.04.29 20:34:58首次发布时间：2025.02.26 11:44:49

## 引擎管理
方法| 描述  
---|---  
| 自定义 SDK 日志配置，包括日志输出等级、存储路径、日志文件总大小上限、日志文件名前缀。  
| 创建引擎对象 如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 如果当前进程中已创建了引擎实例，再次调用此方法时，会返回已创建的引擎实例。  
| 获取当前 ReactNative SDK 版本  
| 销毁所创建的引擎实例，并释放所有相关资源。  
| 设置引擎事件回调的接收类，必须继承自 IRTCVideoEventHandler 。  
| 设置业务标识参数 可通过 businessId 区分不同的业务场景。businessId 由客户自定义，相当于一个“标签”，可以分担和细化现在 AppId 的逻辑划分的功能，但不需要鉴权。  
| 设置运行时的参数  
## 设备管理
方法| 描述  
---|---  
| 创建视频设备管理实例  
| 获取当前 SDK 正在使用的视频采集设备信息  
| 获取当前系统内视频采集设备列表。  
| 设置当前视频采集设备  
## 房间管理
方法| 描述  
---|---  
| 给房间内的所有其他用户群发文本消息。  
| 创建房间实例。 调用此方法仅返回一个房间实例，你仍需调用 才能真正地创建/加入房间。 多次调用此方法以创建多个 实例。分别调用各 RTCRoom 实例中的 方法，同时加入多个房间。 多房间模式下，用户可以同时订阅各房间的音视频流。  
| 给房间内的所有其他用户群发二进制消息。  
| 退出并销毁调用 createRTCRoom 所创建的房间实例。  
| 给房间内指定的用户发送点对点文本消息（P2P）。  
| 通过设置 对象的事件句柄，监听此对象对应的回调事件。  
| 给房间内指定的用户发送点对点二进制消息（P2P）。  
| 加入房间。 调用 createRTCRoom 创建房间后，调用此方法加入房间，同房间内其他用户进行音视频通话。  
| 调节某个房间内所有远端用户的音频播放音量。  
| 离开房间。 用户调用此方法离开房间，结束通话过程，释放所有通话相关的资源。 此方法是异步操作，调用返回时并没有真正退出房间。真正退出房间后，本地会收到 onLeaveRoom 回调通知。  
| 设置用户可见性。未调用该接口前，本地用户默认对他人可见。 默认情况下，一个 RTC 房间最多同时容纳 50 名可见用户，最多 30 人可同时上麦。更多信息参看。  
| 暂停接收来自远端的媒体流。  
| 恢复接收来自远端的媒体流  
| 停止跨房间媒体流转发。 通过 startForwardStreamToRooms 发起媒体流转发后，可调用本方法停止向所有目标房间转发媒体流。  
| 暂停跨房间媒体流转发。 通过 startForwardStreamToRooms 发起媒体流转发后，可调用本方法暂停向所有目标房间转发媒体流。 调用本方法暂停向所有目标房间转发后，你可以随时调用 快速恢复转发。  
| 恢复跨房间媒体流转发。 调用 pauseForwardStreamToAllRooms 暂停转发之后，调用本方法恢复向所有目标房间转发媒体流。  
| 设置发流端音画同步。 当同一用户同时使用两个通话设备分别采集发送音频和视频时，有可能会因两个设备所处的网络环境不一致而导致发布的流不同步，此时你可以在视频发送端调用该接口，SDK 会根据音频流的时间戳自动校准视频流，以保证接收端听到音频和看到视频在时间上的同步性。  
## 音频管理
方法| 描述  
---|---  
| 打开/关闭音量闪避功能，适用于在 RTC 通话过程中会同时播放短视频或音乐的场景，如“一起看”、“在线 KTV”等。 开启该功能后，当检测到远端人声时，本地的媒体播放音量会自动减弱，从而保证远端人声的清晰可辨；当远端人声消失时，本地媒体音量会恢复到闪避前的音量水平。  
| 开启/关闭音量均衡功能。 开启音量均衡功能后，人声的响度会调整为 -16lufs。如果已调用 setAudioMixingLoudness 传入了混音音乐的原始响度，此音乐播放时，响度会调整为 -20lufs。  
| 获取当前使用的音频播放路由。  
| 调节本地播放的所有远端用户混音后的音量。 播放音频前或播放音频时，你都可以使用此接口设定播放音量。  
| 打开/关闭耳返功能。  
| 设置耳返音量。  
| 启用音频信息提示。开启提示后，你可以收到 onLocalAudioPropertiesReport，onRemoteAudioPropertiesReport onRemoteAudioPropertiesReport 和 onActiveSpeaker。  
| 开启内部音频采集。默认为关闭状态。 内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 调用该方法开启后，本地用户会收到 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStartAudioCapture 的回调。  
| 关闭本地用户朝向对本地用户发声效果的影响。 调用此接口后，房间内的其他用户收听本地发声时，声源都在收听者正面。  
| 立即关闭内部音频采集。默认为关闭状态。 内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 调用该方法，本地用户会收到 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStopAudioCapture 的回调。  
| 设置音频场景类型。 你可以根据你的应用所在场景，选择合适的音频场景类型。 选择音频场景后，SDK 会自动根据客户端音频采集播放设备和状态，适用通话音量/媒体音量，并同步变更对应音频相关的算法配置和采集配置。  
| 设置音质档位。 当所选的 中的音频参数无法满足你的场景需求时，调用本接口切换的音质档位。  
| 发送音频流同步信息。将消息通过音频流发送到远端，并实现与音频流同步，该接口调用成功后，远端用户会收到 onStreamSyncInfoReceived 回调。  
| 支持根据业务场景，设置通话中的音频降噪模式。  
| 设置本地用户在自建空间直角坐标系中的收听坐标和收听朝向，以实现本地用户预期的空间音频收听效果。  
| 设置房间内某一远端用户在本地用户自建的空间音频坐标系中的发声位置和发声朝向，以实现本地用户预期的空间音频收听效果。  
| 移除调用 updateRemotePosition 为某一远端用户设置的空间音频效果。  
| 移除调用 updateRemotePosition 为所有远端用户设置的空间音频效果。  
## 视频管理
方法| 描述  
---|---  
| 关闭外部采集视频帧的 Alpha 通道编码功能。  
| 开启自定义采集视频帧的 Alpha 通道编码功能。 适用于需要分离推流端视频主体与背景，且在拉流端可自定义渲染背景的场景。  
| 视频发布端设置推送多路流时各路流的参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。  
| 该方法设置视频流发布端是否开启发布多路编码参数不同的视频流的模式。  
| 设置向 SDK 输入的视频源，包括屏幕流 默认使用内部采集。内部采集指：使用 RTC SDK 内置的视频采集机制进行视频采集。  
| 使用 SDK 内部渲染时，修改远端视频帧的渲染设置，包括渲染模式、背景颜色和旋转角度。  
| 设置期望订阅的远端视频流的参数。  
| 为采集到的视频流开启镜像  
| 使用内部渲染时，为远端流开启镜像。  
| 立即开启内部视频采集。默认为关闭状态。 内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 调用该方法后，本地用户会收到 onVideoDeviceStateChanged 的回调。 本地用户在可见状态下调用该方法后，房间中的其他用户会收到 onUserStartVideoCapture 的回调。  
| 注册远端编码后视频数据回调。 完成注册后，当 SDK 监测到远端编码后视频帧时，会触发 onRemoteEncodedVideoFrame 回调  
| 立即关闭内部视频采集。默认为关闭状态。 内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 调用该方法，本地用户会收到 onVideoDeviceStateChanged 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStopVideoCapture 的回调。  
| 在订阅远端视频流之前，设置远端视频数据解码方式  
| 设置 RTC SDK 内部采集时的视频采集参数。 如果你的项目使用了 SDK 内部采集模块，可以通过本接口指定视频采集参数包括模式、分辨率、帧率。  
| 设置采集视频的旋转模式。默认以 App 方向为旋转参考系。 接收端渲染视频时，将按照和发送端相同的方式进行旋转。  
| 设置本地视频渲染时使用的视图，并设置渲染模式。  
| 修改本地视频渲染模式和背景色。  
| 设置本端采集的视频帧的旋转角度。 当摄像头倒置或者倾斜安装时，可调用本接口进行调整。对于手机等普通设备，可调用 setVideoRotationMode 实现旋转。  
| 在订阅远端视频流之后，向远端请求关键帧  
| 切换视频内部采集时使用的前置/后置摄像头 调用此接口后，在本地会触发 onVideoDeviceStateChanged 回调。  
| 检测当前使用的摄像头（前置/后置），是否支持变焦（数码/光学变焦）。  
| 获取当前使用的摄像头（前置/后置）的最大变焦倍数  
| 设置当前使用的摄像头（前置/后置）的光学变焦倍数  
| 检测当前使用的摄像头（前置/后置），是否支持闪光灯。  
| 打开/关闭当前使用的摄像头（前置/后置）的闪光灯  
| 检查当前使用的摄像头是否支持手动对焦。  
| 设置当前使用的摄像头的对焦点。  
| 检查当前使用的摄像头是否支持手动设置曝光点。  
| 视频发布端设置期望发布的最大分辨率视频流参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。 该接口支持设置一路视频流参数，设置多路参数请使用 。  
| 设置当前使用的摄像头的曝光点  
| 设置当前使用的摄像头的曝光补偿。  
| 注册自定义编码帧推送事件回调  
## 音视频传输
方法| 描述  
---|---  
| 摄像头处于关闭状态时，使用静态图片填充本地推送的视频流。 调用 stopVideoCapture 接口时，会开始推静态图片。若要停止发送图片，可传入空字符串或启用内部摄像头采集。可重复调用该接口来更新图片。  
## 屏幕共享
方法| 描述  
---|---  
| 使用 RTC SDK 内部采集模块开始采集屏幕音频流和（或）视频流。  
| 使用 RTC SDK 内部屏幕采集后，更新采集的媒体类型。  
| 在屏幕共享时，停止使用 RTC SDK 内部采集方式采集屏幕音视频。  
| 在当前所在房间内发布本地屏幕共享音视频流  
| 停止将本地屏幕共享音视频流发布到当前所在房间中  
| 在屏幕共享时，设置屏幕音频的采集方式（内部采集/自定义采集）  
| 在屏幕共享时，设置屏幕音频流和麦克风采集到的音频流的混流方式  
| 为发布的屏幕共享视频流设置期望的编码参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。  
## 混音
方法| 描述  
---|---  
| 开启本地语音变调功能，多用于 K 歌场景。 使用该方法，你可以对本地语音的音调进行升调或降调等调整。  
## 消息
方法| 描述  
---|---  
| 通过视频帧发送 SEI 数据。 在视频通话场景下，SEI 数据会随视频帧发送；在语音通话场景下，SDK 会自动生成一路 16px × 16px 的黑帧视频流用来发送 SEI 数据。  
## 字幕翻译服务
方法| 描述  
---|---  
| 识别或翻译房间内所有用户的语音，形成字幕。 调用该方法时，可以在 中选择语音识别或翻译模式。如果选择识别模式，语音识别文本会通过 onSubtitleMessageReceived 事件回调给你； 如果选择翻译模式，你会同时收到两个 onSubtitleMessageReceived 回调，分别包含字幕原文及字幕译文。 调用该方法后，你会收到 onSubtitleStateChanged 回调，通知字幕是否开启。  
| 关闭字幕。 调用该方法后，用户会收到 onSubtitleStateChanged 回调，通知字幕是否关闭。  
## 网络管理
方法| 描述  
---|---  
| 开启音视频回路测试。 在进房前，用户可调用该接口对音视频通话全链路进行检测，包括对音视频设备以及用户上下行网络的检测，从而帮助用户判断是否可以正常发布和接收音视频流。 开始检测后，SDK 会录制你声音或视频。如果你在设置的延时范围内收到了回放，则视为音视频回路测试正常。  
| 停止音视频回路测试。 调用 startEchoTest 开启音视频回路检测后，你必须调用该方法停止检测。  
## 高级功能
方法| 描述  
---|---  
| 注入点播播放器实例  
</details>

<details>
<summary>点击展开：React Native 3.58 - API 详情</summary>

### API 详情--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1390575

/实时音视频/客户端 API 参考/React Native 3.58/API 详情

API 详情
最近更新时间：2025.06.24 17:01:22首次发布时间：2025.02.26 11:44:49

## RTCManager 
类型：class
RTC 核心类, 负责管理创建的引擎实例和房间实例
### engine 
类型：IEngine | undefined
RTC 引擎实例
### room 
类型：IRoom | undefined
RTC 房间实例
### audioEffectPlayer 
类型：I_AudioEffectPlayer | undefined
RTC 音效播放器实例
### mediaPlayer 
类型：I_MediaPlayer | undefined
RTC 音乐播放器实例
### vodPlayer 
类型：unknown
点播播放器实例
### getSDKVersion() 
获取当前 ReactNative SDK 版本
类型
```
() => string | undefined

ts

```

### getBasicSDKVersion() 
获取 ReactNative 依赖的 Android/iOS SDK 版本
类型
```
() => string

ts

```

### createRTCEngine() 
创建引擎对象 如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 如果当前进程中已创建了引擎实例，再次调用此方法时，会返回已创建的引擎实例。
类型
```
(options: ICreateRTCEngineOptions) => Promise<IEngine>

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
options| ICreateRTCEngineOptions| 是| 无| 引擎初始化参数, 详情参考 ICreateRTCEngineOptions   
返回值
  * engine: 实例
  * null: ICreateRTCEngineOptions 无效。详见 ，so 文件加载失败。


### destroyRTCEngine() 
销毁所创建的引擎实例，并释放所有相关资源。
类型
```
() => void

ts

```

### setLogConfig() 
自定义 SDK 日志配置，包括日志输出等级、存储路径、日志文件总大小上限、日志文件名前缀。
类型
```
(config: ILogConfigs) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| ILogConfigs| 是| 无| SDK 日志配置。   
返回值
number
  * 0：成功。
  * –1：失败，本方法必须在创建引擎前调用。
  * –2：失败，参数填写错误。


### setVodPlayer() 
注入点播播放器实例
类型
```
(vodPlayer: unknown) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
vodPlayer| unknown| 是| 无| -  
## IEngine 
基于 Android/iOS SDK 的 类封装的引擎类。
类型
```
Omit<
 RTCVideo,
 | 'setLocalVideoCanvas'
 | 'createRTCRoom'
 | 'setCellularEnhancement'
 | 'enableAudioPropertiesReport'
 | 'setRemoteAudioPlaybackVolume'
 | 'getAudioEffectPlayer'
 | 'getMediaPlayer'
 | 'setScreenVideoEncoderConfig'
 | 'startScreenCapture'
 | 'setRemoteVideoCanvas'
 | 'requestRemoteVideoKeyFrame'
 | 'sendStreamSyncInfo'
> & {
 setLocalVideoCanvas: (
  streamIndex: StreamIndex,
  videoCanvas: IVideoCanvas,
 ) => number | undefined;
 setRemoteVideoCanvas: (
  remoteInfo: IRemoteInfo,
  videoCanvas: IVideoCanvas,
 ) => ReturnStatus;
 createRTCRoom: (roomId: string) => IRoom;
 getAudioEffectPlayer: () => I_AudioEffectPlayer;
 getMediaPlayer: (playerId: number) => I_MediaPlayer;
 setCellularEnhancement: (config: IEnhanceMentConfig) => number;
 enableAudioPropertiesReport: (config: IAudioPropertiesConfig) => number;
 setRemoteAudioPlaybackVolume: (
  remoteStreamInfo: IRemoteStreamInfo,
  volume: number,
 ) => number;
 setScreenVideoEncoderConfig: (config: IScreenSolution) => number;
 startScreenCapture: (
  type: ScreenMediaType,
  bundleId?: string,
 ) => Promise<number>;
 setRuntimeParameters: (params: Record<string, string>) => number;
 feedback(options: ProblemFeedbackOption[], info: IFeedbackProblemInfo): number;
 requestRemoteVideoKeyFrame(remoteInfo: IRemoteInfo): number;
 sendStreamSyncInfo(data: ArrayBuffer, info: IStreamSyncInfoConfig): number;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
setLocalVideoCanvas| (streamIndex: StreamIndex, videoCanvas: IVideoCanvas) => number | undefined| 设置本地视频渲染时使用的视图，并设置渲染模式。  
setRemoteVideoCanvas| (remoteInfo: IRemoteInfo, videoCanvas: IVideoCanvas) => ReturnStatus| 渲染来自指定远端用户的视频流时，设置使用的视图和渲染模式。要解除绑定，将 videoCanvas 设置为空。  
createRTCRoom| (roomId: string) => IRoom| 创建房间实例。 调用此方法仅返回一个房间实例，你仍需调用 joinRoom 才能真正地创建/加入房间。  
getAudioEffectPlayer| () => I_AudioEffectPlayer| 创建音效播放器实例。  
getMediaPlayer| (playerId: number) => I_MediaPlayer| 创建音乐播放器实例。  
setCellularEnhancement| (config: IEnhanceMentConfig) => number| 启用蜂窝网络辅助增强，改善通话质量。  
enableAudioPropertiesReport| (config: IAudioPropertiesConfig) => number| 启用音频信息提示。开启提示后，你可以收到 onLocalAudioPropertiesReportonRemoteAudioPropertiesReport 和 onActiveSpeaker。  
setRemoteAudioPlaybackVolume| (remoteStreamInfo: IRemoteStreamInfo, volume: number) => number| 调节来自指定远端用户的音频播放音量。  
setScreenVideoEncoderConfig| (config: IScreenSolution) => number| 为发布的屏幕共享视频流设置期望的编码参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。  
startScreenCapture| (type: ScreenMediaType, bundleId?: string) => Promise<number>| 使用 RTC SDK 内部采集模块开始采集屏幕音频流和（或）视频流。  
setRuntimeParameters| (params: Record<string, string>) => number| 设置运行时的参数  
## IRoom 
基于 Android/iOS SDK 的 类封装的房间类。
类型
```
Omit<RTCRoom, 'joinRoom'> & {
 joinRoom: (params: IJoinRoomProps) => number;
 setRemoteVideoConfig: (
  userId: string,
  remoteVideoConfig: IRemoteVideoConfig,
 ) => number;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
joinRoom| (params: IJoinRoomProps) => number| 加入房间。调用 createRTCRoom 创建房间后，调用此方法加入房间，同房间内其他用户进行音视频通话。  
setRemoteVideoConfig| (userId: string, remoteVideoConfig: IRemoteVideoConfig) => number| 设置期望订阅的远端视频流的参数。  
## I_AudioEffectPlayer 
基于 Android/iOS SDK 的 类封装的音效播放器类。
类型
```
Omit<AudioEffectPlayer, 'start'> & {
 start: (
  effectId: number,
  path: string,
  config: IAudioEffectPlayerConfig,
 ) => I_AudioEffectPlayer;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
start| (effectId: number, path: string, config: IAudioEffectPlayerConfig) => I_AudioEffectPlayer| 开始播放音效文件。可以通过传入不同的 ID 和 filepath 多次调用本方法，以实现同时播放多个音效文件，实现音效叠加。  
## I_MediaPlayer 
基于 Android/iOS SDK 的 类封装的音乐播放器类
类型
```
Omit<IMediaPlayer, 'open'> & {
 open: (filePath: string, config: IMediaPlayerConfig) => number;
}

ts

```

成员
名称| 类型| 描述  
---|---|---  
open| (filePath: string, config: IMediaPlayerConfig) => number| 打开音乐文件。一个播放器实例仅能够同时打开一个音乐文件。如果需要同时打开多个音乐文件，请创建多个音乐播放器实例。要播放 PCM 格式的音频数据，参看 openWithCustomSource。openWithCustomSource 和此 API 互斥。  
## ISpatialAudio 
类型：class
空间音频接口实例
### enableSpatialAudio() 
开启/关闭空间音频功能。
注意
该方法仅开启空间音频功能，你须调用 updatePosition 设置自身位置坐标后方可收听空间音频效果。空间音频相关 API 和调用时序详见。
类型
```
(enable: boolean) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启空间音频功能： true：开启 false：关闭（默认）   
### disableRemoteOrientation() 
关闭本地用户朝向对本地用户发声效果的影响。 调用此接口后，房间内的其他用户收听本地发声时，声源都在收听者正面。
注意
  * 调用本接口关闭朝向功能后，在当前的空间音频实例的生命周期内无法再次开启。
  * 调用此接口不影响本地用户收听朝向的音频效果。要改变本地用户收听朝向，参看 updateSelfPosition 和 updateRemotePosition 。


类型
```
() => void

ts

```

### updateSelfPosition() 
设置本地用户在自建空间直角坐标系中的收听坐标和收听朝向，以实现本地用户预期的空间音频收听效果。
注意
  * 该方法需在进房后调用。
  * 调用该接口更新坐标前，你需调用 enableSpatialAudio 开启空间音频功能。空间音频相关 API 和调用时序详见。
  * 调用此接口在本地进行的设定对其他用户的空间音频收听效果不会产生任何影响。


类型
```
(positionInfo: PositionInfo) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
positionInfo| PositionInfo| 是| 无| 空间音频位置信息。参看 PositionInfo。   
返回值
number
  * 0：成功。
  * <0：失败。
  * -2: 失败，原因是校验本地用户的三维朝向信息时，三个向量没有两两垂直。


### updateRemotePosition() 
设置房间内某一远端用户在本地用户自建的空间音频坐标系中的发声位置和发声朝向，以实现本地用户预期的空间音频收听效果。
注意
  * 该方法需在创建房间后调用。
  * 调用此接口在本地进行的设定对其他用户的空间音频收听效果不会产生任何影响。


类型
```
(uid: string, positionInfo: PositionInfo) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 用户 ID   
positionInfo| PositionInfo| 是| 无| 远端用户的空间音频位置信息。参看 PositionInfo。   
返回值
number
  * 0：成功。
  * <0：失败。
  * -2: 失败，原因是校验远端用户的三维朝向信息时，三个向量没有两两垂直。


### removeRemotePosition() 
移除调用 updateRemotePosition 为某一远端用户设置的空间音频效果。
类型
```
(uid: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 远端用户 ID。   
返回值
number
  * 0：成功。
  * <0：失败。


### removeAllRemotePosition() 
移除调用 updateRemotePosition 为所有远端用户设置的空间音频效果。
类型
```
() => number

ts

```

返回值
number
  * 0：成功。
  * <0：失败。


## IVideoDeviceManager 
类型：class
主要用于枚举、设置视频采集设备
### android_enumerateVideoCaptureDevices() 
获取当前系统内视频采集设备列表。
类型
```
() => $p_a.List<$p_a.VideoDeviceInfo>

ts

```

返回值
$p_a.List<$p_a.VideoDeviceInfo> 包含系统中所有视频采集设备的列表，参看 VideoDeviceInfo。
### setVideoCaptureDevice() 
设置当前视频采集设备
类型
```
(deviceId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceId| string| 是| 无| 视频设备 ID，可以通过 enumerateVideoCaptureDevices 获取   
返回值
number
  * 0：方法调用成功
  * !0：方法调用失败


### ios_getVideoCaptureDevice() 
获取当前 SDK 正在使用的视频采集设备信息
类型
```
(deviceID: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 视频设备 ID   
返回值
number
  * 0：方法调用成功
  * !0：方法调用失败


## IKTVPlayer 
类型：class
KTV 播放器接口。
### ios_delegate 
类型：$p_i.ByteRTCKTVPlayerDelegate
### android_setPlayerEventHandler() 
设置 KTV 播放器进度及状态回调接口。
类型
```
(playerEventHandler: $p_a.IKTVPlayerEventHandler) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerEventHandler| $p_a.IKTVPlayerEventHandler| 是| 无| KTV 播放器回调类，参看 IKTVPlayerEventHandler。   
### playMusic() 
播放歌曲。
注意
类型
```
(musicId: string, trackType: AudioTrackType, playType: AudioPlayType) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。若同一 musicId 的歌曲正在播放，再次调用接口会从开始位置重新播放。若 musicId 对应的音频文件不存在会触发报错。   
trackType| AudioTrackType| 是| 无| 原唱伴唱类型，参看 AudioTrackType。   
playType| AudioPlayType| 是| 无| 音乐播放类型。参看 AudioPlayType。   
### pauseMusic() 
暂停播放歌曲。
注意
  * 调用接口后，你会收到 onPlayStateChanged 回调歌曲播放状态。
  * 若音乐 ID 错误，会触发 onPlayStateChanged 回调，errorCode 为 –3023，playState 为 4。
  * 若未进房，会触发 onPlayStateChanged 回调，errorCode 为 –3022，playState 为 4。


类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### resumeMusic() 
继续播放歌曲。
注意
  * 调用接口后，你会收到 onPlayStateChanged 回调歌曲播放状态。
  * 若音乐 ID 错误，会触发 onPlayStateChanged 回调，errorCode 为 –3023，playState 为 4。
  * 若未进房，会触发 onPlayStateChanged 回调，errorCode 为 –3022，playState 为 4。


类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### stopMusic() 
停止播放歌曲。
注意
  * 调用接口后，你会收到 onPlayStateChanged 回调歌曲播放状态。
  * 若音乐 ID 错误，会触发 onPlayStateChanged 回调，errorCode 为 –3023，playState 为 4。
  * 若未进房，会触发 onPlayStateChanged 回调，errorCode 为 –3022，playState 为 4。


类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### seekMusic() 
设置音乐文件的起始播放位置。
注意
类型
```
(musicId: string, position: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
position| number| 是| 无| 音乐起始位置，单位为毫秒，取值小于音乐文件总时长。   
### setMusicVolume() 
设置歌曲播放音量，只能在开始播放后进行设置。
注意
类型
```
(musicId: string, volume: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
volume| number| 是| 无| 歌曲播放音量，调节范围：[0,400]。 0：静音。 100：原始音量。 - 400: 原始音量的 4 倍(自带溢出保护)。   
### switchAudioTrackType() 
切换歌曲原唱伴唱。
注意
调用本接口时音乐必须处于播放中状态。
类型
```
(musicId: string) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
### setMusicPitch() 
对播放中的音乐设置升降调信息。
注意
类型
```
(musicId: string, pitch: number) => void

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
musicId| string| 是| 无| 音乐 ID。   
pitch| number| 是| 无| 相对于音乐文件原始音调的升高/降低值，取值范围 [-12，12]，默认值为 0，即不做调整。取值范围内每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调，设置的绝对值越大表示音调升高或降低越多。   
## IVideoEffect 
类型：class
高级视频特效，参看。
### initCVResource() 
检查视频特效证书，设置算法模型路径，并初始化特效模块。
类型
```
(licenseFile: string, algoModelDir: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
licenseFile| string| 是| 无| 证书文件的绝对路径，用于鉴权。   
algoModelDir| string| 是| 无| 算法模型绝对路径，即存放特效 SDK 所有算法模型的目录。   
返回值
number
### enableVideoEffect() 
开启高级美颜、滤镜等视频特效。
注意
  * 调用本方法前，必须先调用 initCVResource 进行初始化。
  * 调用该方法后，特效不直接生效，你还需调用 setEffectNodes 设置视频特效素材包或调用 setColorFilter 设置滤镜。
  * 调用 disableVideoEffect 关闭视频特效。


类型
```
() => number

ts

```

返回值
number
### disableVideoEffect() 
关闭视频特效。
注意
调用 enableVideoEffect 开启视频特效。
类型
```
() => number

ts

```

返回值
number
### setEffectNodes() 
设置视频特效素材包。
注意
调用本方法前，必须先调用 enableVideoEffect。
类型
```
(effectNodes: Array<string>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectNodes| string[]| 是| 无| 特效素材包绝对路径数组。要取消当前视频特效，将此参数设置为 null。   
返回值
number
### updateEffectNode() 
设置特效强度。
类型
```
(effectNode: string, key: string, value: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectNode| string| 是| 无| 特效素材包绝对路径，参考素材包结构说明。   
key| string| 是| 无| 需要设置的素材 key 名称，参考素材 key 对应说明。   
value| number| 是| 无| 特效强度值，取值范围 [0,1]，超出范围时设置无效。   
返回值
number
### setColorFilter() 
设置颜色滤镜。
注意
调用 setColorFilterIntensity 设置已启用颜色滤镜的强度。设置强度为 0 时即关闭颜色滤镜。
类型
```
(filterRes: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
filterRes| string| 是| 无| 滤镜资源包绝对路径。   
返回值
number
### setColorFilterIntensity() 
设置已启用颜色滤镜的强度。
类型
```
(intensity: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
intensity| number| 是| 无| 滤镜强度。取值范围 [0,1]，超出范围时设置无效。当设置滤镜强度为 0 时即关闭颜色滤镜。   
返回值
number
### enableVirtualBackground() 
将摄像头采集画面中的人像背景替换为指定图片或纯色背景。
注意
  * 调用本方法前，必须先调用 initCVResource 进行初始化。
  * 调用 disableVirtualBackground 关闭虚拟背景。


类型
```
(backgroundStickerRes: string, source: VirtualBackgroundSource) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
backgroundStickerRes| string| 是| 无| 背景贴纸特效素材绝对路径。   
source| VirtualBackgroundSource| 是| 无| 背景贴纸对象，参看 VirtualBackgroundSource。   
返回值
number
### disableVirtualBackground() 
关闭虚拟背景。
注意
调用 enableVirtualBackground 开启虚拟背景后，可以调用此接口关闭虚拟背景。
类型
```
() => number

ts

```

返回值
number
### enableFaceDetection() 
开启人脸识别功能，并设置人脸检测结果回调观察者。 此观察者后，你会周期性收到 onFaceDetectResult 回调。
类型
```
(observer: IFaceDetectionObserver, intervalMs: number, faceModelPath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IFaceDetectionObserver| 是| 无| 人脸检测结果回调观察者，参看 IFaceDetectionObserver。   
intervalMs| number| 是| 无| 两次回调之间的最小时间间隔，必须大于 0，单位为毫秒。实际收到回调的时间间隔大于 interval_ms，小于 interval_ms+视频采集帧间隔。   
faceModelPath| string| 是| 无| 人脸检测算法模型文件路径，一般为 ttfacemodel 文件夹中 tt_face_vXXX.model 文件的绝对路径。   
返回值
number
### disableFaceDetection() 
关闭人脸识别功能。
类型
```
() => number

ts

```

返回值
number
## RTCVideo 
类型：class
RTCVideo Class
### new RTCVideo()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### setRtcVideoEventHandler() 
设置引擎事件回调的接收类，必须继承自 IRTCVideoEventHandler 。
注意
请勿直接在回调函数的实现中直接进行操作。
  * 调用方需要自行实现一个继承自 IRTCVideoEventHandler 的类，并重载其中需要关注的事件。
  * 该回调为异步回调
  * 所有的事件回调均会在独立的回调线程内触发，请接收回调事件时注意所有与线程运行环境有关的操作，如需要在 UI 线程内执行的操作等，


类型
```
(engineEventHandler: RTCVideoEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
engineEventHandler| RTCVideoEventHandler| 是| 无| 事件处理器接口类，详见 IRTCVideoEventHandler 。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### android_getAudioDeviceManager() 
获取音频设备管理接口
类型
```
() => $p_a.IRTCAudioDeviceManager

ts

```

返回值
$p_a.IRTCAudioDeviceManager 音频设备管理接口 IRTCAudioDeviceManager
### startVideoCapture() 
立即开启内部视频采集。默认为关闭状态。 内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 调用该方法后，本地用户会收到 onVideoDeviceStateChanged 的回调。 本地用户在可见状态下调用该方法后，房间中的其他用户会收到 onUserStartVideoCapture 的回调。
注意
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopVideoCapture() 
立即关闭内部视频采集。默认为关闭状态。 内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 调用该方法，本地用户会收到 onVideoDeviceStateChanged 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStopVideoCapture 的回调。
注意
  * 调用 startVideoCapture 可以开启内部视频采集。
  * 如果不调用本方法停止内部视频采集，则只有当销毁引擎实例时，内部视频采集才会停止。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startAudioCapture() 
开启内部音频采集。默认为关闭状态。 内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 调用该方法开启后，本地用户会收到 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStartAudioCapture 的回调。
注意
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopAudioCapture() 
立即关闭内部音频采集。默认为关闭状态。 内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 调用该方法，本地用户会收到 的回调。 可见用户进房后调用该方法，房间中的其他用户会收到 onUserStopAudioCapture 的回调。
注意
  * 调用 startAudioCapture 可以开启内部音频采集设备。
  * 如果不调用本方法停止内部音频采集，则只有当销毁引擎实例时，内部音频采集才会停止。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAudioScenario() 
设置音频场景类型。 你可以根据你的应用所在场景，选择合适的音频场景类型。 选择音频场景后，SDK 会自动根据客户端音频采集播放设备和状态，适用通话音量/媒体音量，并同步变更对应音频相关的算法配置和采集配置。
注意
  * 建议在加入房间和调用音频相关接口之前，调用此接口设置音频场景类型。如果在此之后调用此接口，可能会引入音频卡顿。
  * 通话音量更适合通话、会议等对信息准确度更高的场景。通话音量会激活系统硬件信号处理，使通话声音更清晰。同时，音量无法降低到 0。
  * 媒体音量更适合娱乐场景，因其声音的表现力会更强。媒体音量下，最低音量可以为 0。


类型
```
(audioScenario: AudioScenarioType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioScenario| AudioScenarioType| 是| 无| 音频场景类型，参看 AudioScenarioType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAudioProfile() 
设置音质档位。 当所选的 中的音频参数无法满足你的场景需求时，调用本接口切换的音质档位。
注意
  * 该方法在进房前后均可调用；
  * 支持通话过程中动态切换音质档位。


类型
```
(audioProfile: AudioProfileType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioProfile| AudioProfileType| 是| 无| 音质档位，参看 AudioProfileType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setAnsMode() 
支持根据业务场景，设置通话中的音频降噪模式。
注意
类型
```
(ansMode: AnsMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
ansMode| AnsMode| 是| 无| 降噪模式。具体参见 AnsMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVoiceChangerType() 
设置变声特效类型
注意
类型
```
(voiceChanger: VoiceChangerType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
voiceChanger| VoiceChangerType| 是| 无| 变声特效类型，参看 VoiceChangerType   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### setVoiceReverbType() 
设置混响特效类型
注意
类型
```
(voiceReverb: VoiceReverbType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
voiceReverb| VoiceReverbType| 是| 无| 混响特效类型，参看 VoiceReverbType   
返回值
number 方法调用结果：
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### setLocalVoiceEqualization() 
设置本地采集语音的均衡效果。包含内部采集和外部采集，但不包含混音音频文件。
注意
根据奈奎斯特采样率，音频采样率必须大于等于设置的中心频率的两倍，否则，设置不生效。
类型
```
(voiceEqualizationConfig: VoiceEqualizationConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
voiceEqualizationConfig| VoiceEqualizationConfig| 是| 无| 语音均衡效果，参看 VoiceEqualizationConfig   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### setLocalVoiceReverbParam() 
设置本地采集音频的混响效果。包含内部采集和外部采集，但不包含混音音频文件。
注意
调用 enableLocalVoiceReverb 开启混响效果。
类型
```
(config: VoiceReverbConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| VoiceReverbConfig| 是| 无| 混响效果，参看 VoiceReverbConfig   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### enableLocalVoiceReverb() 
开启本地音效混响效果
注意
调用 setLocalVoiceReverbParam 设置混响效果。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### setLocalVideoCanvas() 
设置本地视频渲染时使用的视图，并设置渲染模式。
注意
  * 你应在加入房间前，绑定本地视图。退出房间后，此设置仍然有效。
  * 如果需要解除绑定，你可以调用本方法传入空视图。


类型
```
(streamIndex: StreamIndex, videoCanvas: VideoCanvas) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 视频流属性, 参看 StreamIndex   
videoCanvas| VideoCanvas| 是| 无| 视图信息和渲染模式, 参看 VideoCanvas   
返回值
number
  * 0: 成功
  * -1: videoCanvas 为空


### updateLocalVideoCanvas() 
修改本地视频渲染模式和背景色。
注意
你可以在本地视频渲染过程中，调用此接口。调用结果会实时生效。
类型
```
(streamIndex: StreamIndex, renderMode: RenderMode, backgroundColor: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 视频流属性。参看 StreamIndex   
renderMode| RenderMode| 是| 无| 渲染模式。参看 VideoCanvas.renderMode   
backgroundColor| number| 是| 无| 背景颜色。参看 VideoCanvas.backgroundColor   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### updateRemoteStreamVideoCanvas() 
使用 SDK 内部渲染时，修改远端视频帧的渲染设置，包括渲染模式、背景颜色和旋转角度。
注意
  * 调用 setRemoteVideoCanvas 设置了远端视频渲染模式后，你可以调用此接口更新渲染模式、背景颜色、旋转角度的设置。
  * 该接口可以在远端视频渲染过程中调用，调用结果会实时生效。


类型
```
(streamKey: RemoteStreamKey, remoteVideoRenderConfig: RemoteVideoRenderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息。参看 RemoteStreamKey。   
remoteVideoRenderConfig| RemoteVideoRenderConfig| 是| 无| 视频帧渲染设置。具体参看 RemoteVideoRenderConfig。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoCanvas() 
渲染来自指定远端用户的视频流时，设置使用的视图和渲染模式。 要解除绑定，将 videoCanvas 设置为空。
注意
本地用户离开房间时，会解除调用此 API 建立的绑定关系；远端用户离开房间则不会影响。
类型
```
(streamKey: RemoteStreamKey, videoCanvas: VideoCanvas) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息。参看 RemoteStreamKey   
videoCanvas| VideoCanvas| 是| 无| 视图信息和渲染模式，参看 VideoCanvas。3.56 版本起支持通过 renderRotation 设置远端视频渲染的旋转角度。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoSuperResolution() 
设置远端视频超分模式。
注意
类型
```
(streamKey: RemoteStreamKey, mode: VideoSuperResolutionMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息，用于指定需要设置超分的视频流来源及属性，参看 RemoteStreamKey。   
mode| VideoSuperResolutionMode| 是| 无| 超分模式，参看 VideoSuperResolutionMode。   
返回值
number
### android_setVideoDenoiser() 
设置视频降噪模式。
注意
该功能仅 arm 架构支持。
类型
```
(mode: $p_a.VideoDenoiseMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mode| $p_a.VideoDenoiseMode| 是| 无| 视频降噪模式。参看 VideoDenoiseMode。   
返回值
number
  * 0: API 调用成功。 用户可以根据回调函数 onVideoDenoiseModeChanged 判断视频降噪是否开启。
  * < 0: API 调用失败。


### setLocalVideoMirrorType() 
为采集到的视频流开启镜像
注意
前置摄像头| 后置摄像头| 自定义采集视频源 | 桌面端摄像头  
---|---|---|---  
移动端| 本地预览镜像，编码传输不镜像|  本地预览不镜像，编码传输不镜像 |  本地预览不镜像，编码传输不镜像 | /  
桌面端| /| /|  本地预览不镜像，编码传输不镜像 |  本地预览镜像，编码传输不镜像   
类型
```
(mirrorType: MirrorType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mirrorType| MirrorType| 是| 无| 镜像类型，参看 MirrorType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteVideoMirrorType() 
使用内部渲染时，为远端流开启镜像。
类型
```
(remoteStreamKey: RemoteStreamKey, mirrorType: RemoteMirrorType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端流信息，用于指定需要镜像的视频流来源及属性，参看 RemoteStreamKey。   
mirrorType| RemoteMirrorType| 是| 无| 远端流的镜像类型，参看 RemoteMirrorType。   
返回值
number
  * 0: 调用成功。
  * < 0: 调用失败，参看 获得更多错误说明。


### setVideoRotationMode() 
设置采集视频的旋转模式。默认以 App 方向为旋转参考系。 接收端渲染视频时，将按照和发送端相同的方式进行旋转。
注意
  * 旋转仅对内部视频采集生效，不适用于外部视频源和屏幕源。
  * 调用该接口时已开启视频采集，将立即生效；调用该接口时未开启视频采集，则将在采集开启后生效。
  * 更多信息请参考。


类型
```
(rotationMode: VideoRotationMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
rotationMode| VideoRotationMode| 是| 无| 视频旋转参考系为 App 方向或重力方向，参看 VideoRotationMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### switchCamera() 
切换视频内部采集时使用的前置/后置摄像头 调用此接口后，在本地会触发 onVideoDeviceStateChanged 回调。
注意
  * 默认使用前置摄像头。
  * 如果你正在使用相机进行视频采集，切换操作当即生效；如果相机未启动，后续开启内部采集时，会打开设定的摄像头。


类型
```
(cameraId: CameraId) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
cameraId| CameraId| 是| 无| 摄像头 ID，参看 CameraId   
返回值
number
  * 0：方法调用成功
  * < 0：方法调用失败


### setAudioRoute() 
强制切换当前的音频播放路由。默认使用 setDefaultAudioRoute 中设置的音频路由。 音频播放路由发生变化时，会收到 onAudioRouteChanged 回调。
注意
  * 对于绝大多数音频场景，使用 setDefaultAudioRoute 设置默认音频路由，并借助 RTC SDK 的音频路由自动切换逻辑即可完成。切换逻辑参见。你应仅在例外的场景下，使用此接口，比如在接入外接音频设备时，手动切换音频路由。
  * 本接口仅支持在 AUDIO_SCENARIO_COMMUNICATION 音频场景下使用。你可以通过调用 setAudioScenario 切换音频场景。
  * 不同音频场景中，音频路由和发布订阅状态到音量类型的映射关系详见 。


类型
```
(audioRoute: AudioRoute) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioRoute| AudioRoute| 是| 无| 音频播放路由，参见 AudioRoute。对 Android 设备，不同的音频设备连接状态下，可切换的音频设备情况不同。参见移动端设置音频路由。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getAudioRoute() 
获取当前使用的音频播放路由。
注意
要设置音频路由，详见 setAudioRoute。
类型
```
() => AudioRoute

ts

```

返回值
AudioRoute 详见 
### setDefaultAudioRoute() 
将默认的音频播放设备设置为听筒或扬声器。
注意
对于音频路由切换逻辑，参见。
类型
```
(route: AudioRoute) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
route| AudioRoute| 是| 无| 音频播放设备。参看 AudioRoute。仅支持听筒或扬声器。   
返回值
number
  * 0: 方法调用成功。
  * < 0: 方法调用失败。


### enableExternalSoundCard() 
启用匹配外置声卡的音频处理模式
注意
  * 当采用外接声卡进行音频采集时，建议开启此模式，以获得更好的音质。
  * 开启此模式时，仅支持耳机播放。如果需要使用扬声器或者外置音箱播放，关闭此模式。


类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| true: 开启 - false: 不开启(默认)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### muteAudioCapture() 
设置是否将录音信号静音（不改变本端硬件）。
注意
  * 该方法支持选择静音或取消静音麦克风采集，而不影响 SDK 音频流发布状态。
  * 静音后通过 setCaptureVolume 调整音量不会取消静音状态，音量状态会保存至取消静音。
  * 调用 startAudioCapture 开启音频采集前后，都可以使用此接口设置采集音量。


类型
```
(index: StreamIndex, mute: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 流索引，指定调节主流/屏幕流音量，参看 StreamIndex。   
mute| boolean| 是| 无| 是否静音音频采集。 True：静音（关闭麦克风） False：（默认）开启麦克风   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。具体失败原因参看 。


### setCaptureVolume() 
调节音频采集音量
注意
在开启音频采集前后，你都可以使用此接口设定采集音量。
类型
```
(index: StreamIndex, volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 流索引，指定调节主流还是调节屏幕流的音量，参看 StreamIndex   
volume| number| 是| 无| 采集的音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 只改变音频数据的音量信息，不涉及本端硬件的音量调节。 为保证更好的通话质量，建议将 volume 值设为 [0,100]。 0：静音 100：原始音量 - 400: 最大可为原始音量的 4 倍(自带溢出保护)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setPlaybackVolume() 
调节本地播放的所有远端用户混音后的音量。 播放音频前或播放音频时，你都可以使用此接口设定播放音量。
注意
假设某远端用户 A 始终在被调节的目标用户范围内，当该方法与 setRemoteAudioPlaybackVolume 或 setRemoteRoomAudioPlaybackVolume 共同使用时，本地收听用户 A 的音量将为两次设置的音量效果的叠加。
类型
```
(volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 音频播放音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 为保证更好的通话质量，建议将 volume 值设为 [0,100]。 0：静音 100：原始音量 - 400: 最大可为原始音量的 4 倍(自带溢出保护)   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setLocalVoicePitch() 
开启本地语音变调功能，多用于 K 歌场景。 使用该方法，你可以对本地语音的音调进行升调或降调等调整。
类型
```
(pitch: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
pitch| number| 是| 无| 相对于语音原始音调的升高/降低值，取值范围[-12，12]，默认值为 0，即不做调整。取值范围内每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调，设置的绝对值越大表示音调升高或降低越多。超出取值范围则设置失败，并且会触发 onWarning 回调，提示 WarningCode 错误码为 WARNING_CODE_SET_SCREEN_STREAM_INVALID_VOICE_PITCH 设置语音音调不合法   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enableVocalInstrumentBalance() 
开启/关闭音量均衡功能。 开启音量均衡功能后，人声的响度会调整为 -16lufs。如果已调用 setAudioMixingLoudness 传入了混音音乐的原始响度，此音乐播放时，响度会调整为 -20lufs。
注意
该接口须在调用 startAudioMixing 开始播放音频文件之前调用。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启音量均衡功能： true: 是 false: 否   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enablePlaybackDucking() 
打开/关闭音量闪避功能，适用于在 RTC 通话过程中会同时播放短视频或音乐的场景，如“一起看”、“在线 KTV”等。 开启该功能后，当检测到远端人声时，本地的媒体播放音量会自动减弱，从而保证远端人声的清晰可辨；当远端人声消失时，本地媒体音量会恢复到闪避前的音量水平。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否开启音量闪避： true: 是 false: 否   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### login() 
登陆 RTS 服务器。 必须先登录，才能调用 sendUserMessageOutsideRoom 和 sendServerMessage 发送房间外点对点消息和向应用服务器发送消息 在调用本接口登录后，如果想要登出，需要调用 logout。
注意
本地用户调用此方法登录成功后，会收到 onLoginResult 回调通登录结果，远端用户不会收到通知。
类型
```
(token: string, uid: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
token| string| 是| 无| 用户登录必须携带的 Token，用于鉴权验证。测试时可使用控制台生成临时 Token，roomId 填任意值。正式上线需要使用密钥 SDK 在你的服务端生成并下发 Token，roomId 置空，Token 有效期及生成方式参看使用 Token 完成鉴权。   
uid| string| 是| 无| 用户 ID用户 ID 在 appid 的维度下是唯一的。   
返回值
number
  * 0：成功；
  * <0：失败。具体失败原因参看 。


### logout() 
登出 RTS 服务器。 调用本接口登出后，无法调用房间外消息以及端到服务器消息相关的方法或收到相关回调。
注意
  * 调用本接口登出后，必须先调用 login 登录。
  * 本地用户调用此方法登出后，会收到 onLogout 回调通知结果，远端用户不会收到通知。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### updateLoginToken() 
更新用户用于登录的 Token Token 有一定的有效期，当 Token 过期时，需调用此方法更新登录的 Token 信息。 调用 login 方法登录时，如果使用了过期的 Token 将导致登录失败，并会收到 onLoginResult 回调通知，错误码为 LOGIN_ERROR_CODE_INVALID_TOKEN。此时需要重新获取 Token，并调用此方法更新 Token。
注意
  * 如果 Token 无效导致登录失败，则调用此方法更新 Token 后，SDK 会自动重新登录，而用户不需要自己调用 login 方法。
  * Token 过期时，如果已经成功登录，则不会受到影响。Token 过期的错误会在下一次使用过期 Token 登录时，或因本地网络状况不佳导致断网重新登录时通知给用户。


类型
```
(token: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
token| string| 是| 无| 更新的动态密钥   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setServerParams() 
设置应用服务器参数 客户端调用 sendServerMessage 或 sendServerBinaryMessage 发送消息给应用服务器之前，必须需要设置有效签名和应用服务器地址。
注意
  * 用户必须调用 login 登录后，才能调用本接口。
  * 调用本接口后，SDK 会使用 onServerParamsSetResult 返回相应结果。


类型
```
(signature: string, url: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
signature| string| 是| 无| 动态签名，应用服务器可使用该签名验证消息来源。签名需自行定义，可传入任意非空字符串，建议将 uid 等信息编码为签名。设置的签名会以 post 形式发送至通过本方法中 url 参数设置的应用服务器地址。   
url| string| 是| 无| 应用服务器的地址   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getPeerOnlineStatus() 
查询对端用户或本端用户的登录状态
注意
  * 必须调用 login 登录后，才能调用本接口。
  * 调用本接口后，SDK 会使用 onGetPeerOnlineStatus 回调通知查询结果。
  * 在发送房间外消息之前，用户可以通过本接口了解对端用户是否登录，从而决定是否发送消息。也可以通过本接口查询自己查看自己的登录状态。


类型
```
(peerUserID: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
peerUserID| string| 是| 无| 需要查询的用户 ID   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### sendUserMessageOutsideRoom() 
给房间外指定的用户发送文本消息（P2P）
注意
  * 在发送房间外文本消息前，必须先调用 login 完成登录。
  * 用户调用本接口发送文本信息后，会收到一次 onUserMessageSendResultOutsideRoom 回调，得知消息是否成功发送。
  * 若文本消息发送成功，则 uid 所指定的用户会通过 onUserMessageReceivedOutsideRoom 回调收到该消息。


类型
```
(uid: string, message: string, config: MessageConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息接收用户的 ID   
message| string| 是| 无| 发送的文本消息内容。消息不超过 64 KB。   
config| MessageConfig| 是| 无| 消息类型，参看 MessageConfig。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建
  * -2：发送失败，uid 为空


### sendUserBinaryMessageOutsideRoom() 
给房间外指定的用户发送二进制消息（P2P）
注意
  * 在发送房间外二进制消息前，必须先调用 login 完成登录。
  * 用户调用本接口发送二进制消息后，会收到一次 onUserMessageSendResultOutsideRoom 回调，通知消息是否发送成功；
  * 若消息发送成功，则 userId 所指定的用户会通过 onUserBinaryMessageReceivedOutsideRoom 回调收到该条消息。


类型
```
(uid: string, buffer: ArrayBuffer, config: MessageConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息接收用户的 ID   
buffer| ArrayBuffer| 是| 无| 发送的二进制消息内容。消息不超过 46KB。   
config| MessageConfig| 是| 无| 消息类型，参看 MessageConfig。   
返回值
number
### sendServerMessage() 
客户端给应用服务器发送文本消息（P2Server）
注意
  * 在向应用服务器发送文本消息前，必须先调用 login 完成登录，随后调用 setServerParams 设置应用服务器。
  * 调用本接口后会收到一次 onServerMessageSendResult 回调，通知消息发送方是否发送成功。
  * 若文本消息发送成功，则之前调用 setServerParams 设置的应用服务器会收到该条消息。


类型
```
(message: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
message| string| 是| 无| 发送的文本消息内容消息不超过 64 KB。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建


### sendServerBinaryMessage() 
客户端给应用服务器发送二进制消息（P2Server）
注意
  * 在向应用服务器发送二进制消息前，必须先调用 login 完成登录，随后调用 setServerParams 设置应用服务器。
  * 调用本接口后，会收到一次 onServerMessageSendResult 回调，通知消息发送方发送成功或失败；
  * 若二进制消息发送成功，则之前调用 setServerParams 设置的应用服务器会收到该条消息。


类型
```
(buffer: ArrayBuffer) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
buffer| ArrayBuffer| 是| 无| 发送的二进制消息内容消息不超过 46KB。   
返回值
number
  * >0：发送成功，返回这次发送消息的编号，从 1 开始递增
  * -1：发送失败，RTCVideo 实例未创建


### startNetworkDetection() 
开启通话前网络探测
注意
  * 成功调用本接口后，会在 3s 内收到一次 onNetworkDetectionResult 回调，此后每 2s 收到一次该回调，通知探测结果；
  * 若探测停止，则会收到一次 onNetworkDetectionStopped 通知探测停止。


类型
```
(isTestUplink: boolean, expectedUplinkBitrate: number, isTestDownlink: boolean, expectedDownlinkBitrate: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
isTestUplink| boolean| 是| 无| 是否探测上行带宽   
expectedUplinkBitrate| number| 是| 无| 期望上行带宽，单位：kbps范围为 {0, [100-10000]}，其中， 0 表示由 SDK 指定最高码率。   
isTestDownlink| boolean| 是| 无| 是否探测下行带宽   
expectedDownlinkBitrate| number| 是| 无| 期望下行带宽，单位：kbps范围为 {0, [100-10000]}，其中， 0 表示由 SDK 指定最高码率。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopNetworkDetection() 
停止通话前网络探测
注意
  * 调用本接口后，会收到一次 onNetworkDetectionStopped 回调通知探测停止。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enableAudioFrameCallback() 
设置并开启指定的音频数据帧回调
注意
开启音频回调并调用 registerAudioFrameObserver 后 会收到对应的音频回调。两者调用顺序没有限制且相互独立。
类型
```
(method: AudioFrameCallbackMethod, format: AudioFormat) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
method| AudioFrameCallbackMethod| 是| 无| 音频回调方法，参看 AudioFrameCallbackMethod。当音频回调方法设置为 AUDIO_FRAME_CALLBACK_RECORD(0)、AUDIO_FRAME_CALLBACK_PLAYBACK(1)、AUDIO_FRAME_CALLBACK_MIXED(2)时，你需要在参数 format 中指定准确的采样率和声道，暂不支持设置为自动。当音频回调方法设置为 AUDIO_FRAME_CALLBACK_REMOTE_USER(3)时，将 format 中的各个字段设置为默认值。   
format| AudioFormat| 是| 无| 音频参数格式，参看 AudioFormat。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### disableAudioFrameCallback() 
关闭音频回调
注意
该方法需要在调用 enableAudioFrameCallback 之后调用。
类型
```
(method: AudioFrameCallbackMethod) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
method| AudioFrameCallbackMethod| 是| 无| 音频回调方法，参看 AudioFrameCallbackMethod。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerAudioFrameObserver() 
注册音频数据回调观察者。
注意
注册音频数据回调观察者并调用 enableAudioFrameCallback 后 会收到对应的音频回调。对回调中收到的音频数据进行处理，不会影响 RTC 的编码发送或渲染。
类型
```
(observer: IAudioFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IAudioFrameObserver| 是| 无| 音频数据观察者，参看 IAudioFrameObserver。如果传入 null，则取消注册。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerAudioProcessor() 
注册自定义音频处理器。 注册完成后，你可以调用 enableAudioProcessor，对本地采集到的音频进行处理，RTC SDK 将对处理后的音频进行编码和发送。也可以对接收到的远端音频进行自定义处理，RTC SDK 将对处理后的音频进行渲染。
注意
  * 重复调用此接口时，仅最后一次调用生效。
  * 更多相关信息，详见。


类型
```
(processor: IAudioFrameProcessor) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
processor| IAudioFrameProcessor| 是| 无| 自定义音频处理器，详见 IAudioFrameProcessor。SDK 只持有 processor 的弱引用，你应保证其生命周期。需要取消注册时，设置此参数为 nullptr。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoDigitalZoomConfig() 
设置本地摄像头数码变焦参数，包括缩放倍数，移动步长。
注意
  * 每次调用本接口只能设置一种参数。如果缩放系数和移动步长都需要设置，分别调用本接口传入相应参数。
  * 由于移动步长的默认值为 0 ，在调用 setVideoDigitalZoomControl 或 startVideoDigitalZoomControl 进行数码变焦操作前，应先调用本接口。


类型
```
(type: ZoomConfigType, size: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| ZoomConfigType| 是| 无| 数码变焦参数类型，缩放系数或移动步长。参看 ZoomConfigType。必填。   
size| number| 是| 无| 缩放系数或移动步长，保留到小数点后三位。默认值为 0。必填。 选择不同 type 时有不同的取值范围。当计算后的结果超过缩放和移动边界时，取临界值。 ZOOM_FOCUS_OFFSET(0)：缩放系数增量，范围为 [0, 7]。例如，设置为 0.5 时，如果调用 setVideoDigitalZoomControl 选择 Zoom in，则缩放系数增加 0.5。缩放系数范围 [1，8]，默认为 1，原始大小。 - ZOOM_MOVE_OFFSET(1)：移动百分比，范围为 [0, 0.5]，默认为 0，不移动。如果调用 setVideoDigitalZoomControl 选择的是左右移动，则移动距离为 size x 原始视频宽度；如果选择的是上下移动，则移动距离为 size x 原始视频高度。例如，视频帧边长为 1080 px，设置为 0.5 时，实际移动距离为 0.5 x 1080 px = 540 px。  
返回值
number
  * 0：成功。
  * !0：失败。


### setVideoDigitalZoomControl() 
控制本地摄像头数码变焦，缩放或移动一次。设置对本地预览画面和发布到远端的视频都生效。
注意
类型
```
(direction: ZoomDirectionType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
direction| ZoomDirectionType| 是| 无| 数码变焦操作类型，参看 ZoomDirectionType。   
返回值
number
  * 0：成功。
  * !0：失败。


### startVideoDigitalZoomControl() 
开启本地摄像头持续数码变焦，缩放或移动。设置对本地预览画面和发布到远端的视频都生效。
注意
类型
```
(direction: ZoomDirectionType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
direction| ZoomDirectionType| 是| 无| 数码变焦操作类型，参看 ZoomDirectionType。   
返回值
number
  * 0：成功。
  * !0：失败。


### stopVideoDigitalZoomControl() 
停止本地摄像头持续数码变焦。
注意
关于开始数码变焦，参看 startVideoDigitalZoomControl。
类型
```
() => number

ts

```

返回值
number
  * 0：成功。
  * !0：失败。


### registerLocalEncodedVideoFrameObserver() 
注册本地视频帧监测器。 无论使用内部采集还是自定义采集，调用该方法后，SDK 每监测到一帧本地视频帧时，都会将视频帧信息通过 onLocalEncodedVideoFrame 回调给用户。
注意
该方法可在进房前后的任意时间调用，在进房前调用可保证尽可能早地监测视频帧并触发回调
类型
```
(observer: ILocalEncodedVideoFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| ILocalEncodedVideoFrameObserver| 是| 无| 本地频帧监测器，参看 ILocalEncodedVideoFrameObserver 。将参数设置为 null 则取消注册。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### registerRemoteEncodedVideoFrameObserver() 
注册远端编码后视频数据回调。 完成注册后，当 SDK 监测到远端编码后视频帧时，会触发 onRemoteEncodedVideoFrame 回调
注意
  * 更多自定义解码功能说明参看 。
  * 该方法适用于手动订阅，并且进房前后均可调用，建议在进房前调用。
  * 引擎销毁前需取消注册，调用该方法将参数设置为 "null" 即可。


类型
```
(observer: IRemoteEncodedVideoFrameObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
observer| IRemoteEncodedVideoFrameObserver| 是| 无| 远端编码后视频数据监测器，参看 IRemoteEncodedVideoFrameObserver   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoSourceType() 
设置向 SDK 输入的视频源，包括屏幕流 默认使用内部采集。内部采集指：使用 RTC SDK 内置的视频采集机制进行视频采集。
注意
类型
```
(index: StreamIndex, type: VideoSourceType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 视频流的属性，参看 StreamIndex   
type| VideoSourceType| 是| 无| 视频输入源类型，参看 VideoSourceType   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startPushMixedStreamToCDN() 
新增合流转推直播任务，并设置合流的图片、视频视图布局和音频属性。 同一个任务中转推多路直播流时，SDK 会先将多路流合成一路流，然后再进行转推。
注意
类型
```
(taskId: string, mixedConfig: MixedStreamConfig, observer: IMixedStreamObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 转推直播任务 ID，长度不超过 126 字节。你可以在同一房间内发起多个转推直播任务，并用不同的任务 ID 加以区分。当你需要发起多个转推直播任务时，应使用多个 ID；当你仅需发起一个转推直播任务时，建议使用空字符串。   
mixedConfig| MixedStreamConfig| 是| 无| 转推直播配置参数。详见 MixedStreamConfig。   
observer| IMixedStreamObserver| 是| 无| 无效参数。请忽略。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### updatePushMixedStreamToCDN() 
更新合流转推直播参数，会收到 onMixingEvent 回调。 使用 startPushMixedStreamToCDN 启用转推直播功能后，使用此方法更新功能配置参数。
类型
```
(taskId: string, mixedConfig: MixedStreamConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 转推直播任务 ID。指定想要更新参数设置的转推直播任务。   
mixedConfig| MixedStreamConfig| 是| 无| 转推直播配置参数，详见 MixedStreamConfig。除特殊说明外，均支持过程中更新。调用时，结构体中没有传入值的属性，会被更新为默认值。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### startPushSingleStreamToCDN() 
新增单流转推直播任务。
注意
类型
```
(taskId: string, param: PushSingleStreamParam, observer: IPushSingleStreamToCDNObserver) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 任务 ID。你可以发起多个转推直播任务，并用不同的任务 ID 加以区分。当你需要发起多个转推直播任务时，应使用多个 ID；当你仅需发起一个转推直播任务时，建议使用空字符串。   
param| PushSingleStreamParam| 是| 无| 转推直播配置参数。详见 PushSingleStreamParam。   
observer| IPushSingleStreamToCDNObserver| 是| 无| 单流转推直播观察者。详见 IPushSingleStreamToCDNObserver。通过注册 observer 接收单流转推直播相关的回调。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopPushStreamToCDN() 
停止转推直播。 该方法可用于停止单流转推直播或停止合流转推直播，通过 taskId 区分需要停止的任务。
注意
  * 关于启动单流转推直播，参看 startPushSingleStreamToCDN。
  * 关于启动合流转推直播，参看 startPushMixedStreamToCDN。


类型
```
(taskId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| string| 是| 无| 任务 ID。可以指定想要停止的单流转推直播或合流转推直播任务。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoCaptureConfig() 
设置 RTC SDK 内部采集时的视频采集参数。 如果你的项目使用了 SDK 内部采集模块，可以通过本接口指定视频采集参数包括模式、分辨率、帧率。
注意
  * 本接口在引擎创建后即可调用，建议在调用 startVideoCapture 前调用本接口。
  * 建议同一设备上的不同引擎使用相同的视频采集参数。
  * 如果调用本接口前使用内部模块开始视频采集，采集参数默认为 Auto 模式。


类型
```
(videoCaptureConfig: VideoCaptureConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
videoCaptureConfig| VideoCaptureConfig| 是| 无| 视频采集参数。参看: VideoCaptureConfig。   
返回值
number
  * 0: 成功
  * < 0: 失败


### enableSimulcastMode() 
该方法设置视频流发布端是否开启发布多路编码参数不同的视频流的模式。
注意
类型
```
(enabled: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enabled| boolean| 是| 无| 是否开启推送多路视频流模式： true：开启 false：关闭（默认）   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoEncoderConfig() 
视频发布端设置推送多路流时各路流的参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。
注意
类型
```
(channelSolutions: Array<VideoEncoderConfig>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
channelSolutions| VideoEncoderConfig[]| 是| 无| 要推送的多路视频流的参数，最多支持设置 3 路参数，超过 3 路时默认取前 3 路的值。当设置了多路参数时，分辨率和帧率必须是从大到小排列。需注意，所设置的分辨率是各路流的最大分辨率。参看 VideoEncoderConfig。   
返回值
number 方法调用结果：
  * 0：成功
  * !0：失败


### setScreenVideoEncoderConfig() 
为发布的屏幕共享视频流设置期望的编码参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。
注意
建议在采集视频前设置编码参数。若采集前未设置编码参数，则使用默认编码参数: 分辨率 1920px × 1080px，帧率 15fps。
类型
```
(screenSolution: ScreenVideoEncoderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
screenSolution| ScreenVideoEncoderConfig| 是| 无| 屏幕共享视频流参数。参看 ScreenVideoEncoderConfig。   
返回值
number
  * 0：成功。
  * !0：失败。


### enableAlphaChannelVideoEncode() 
开启自定义采集视频帧的 Alpha 通道编码功能。 适用于需要分离推流端视频主体与背景，且在拉流端可自定义渲染背景的场景。
注意
  * 该接口仅作用于自定义采集的、并且使用 RGBA 色彩模型的视频帧，包括 VideoPixelFormat.TEXTURE_2D、VideoPixelFormat.TEXTURE_OES、VideoPixelFormat.RGBA。
  * 该接口须在发布视频流之前调用。
  * 调用本接口开启 Alpha 通道编码后，你需调用 pushExternalVideoFrame 把自定义采集的视频帧推送至 RTC SDK。若推送了不支持的视频帧格式，则调用 pushExternalVideoFrame 时会返回错误码 ReturnStatus.RETURN_STATUS_PARAMETER_ERR。


类型
```
(streamIndex: StreamIndex, alphaLayout: AlphaLayout) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 需开启该功能的视频流类型，当前仅支持对 StreamIndex.STREAM_INDEX_MAIN 即主流开启。   
alphaLayout| AlphaLayout| 是| 无| 分离后的 Alpha 通道相对于 RGB 通道信息的排列位置。当前仅支持 AlphaLayout.TOP，即置于 RGB 通道信息上方。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### disableAlphaChannelVideoEncode() 
关闭外部采集视频帧的 Alpha 通道编码功能。
注意
该接口须在停止发布视频流之后调用。
类型
```
(streamIndex: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 需关闭该功能的视频流类型，当前仅支持设置为 StreamIndex.STREAM_INDEX_MAIN 即主流。   
返回值
number 方法调用结果：
  * 0：成功；
  * !0：失败。


### setAudioSourceType() 
切换音频采集方式
注意
  * 进房前后调用此方法均有效。
  * 如果你调用此方法由内部采集切换至自定义采集，SDK 会自动关闭内部采集。然后，调用 pushExternalAudioFrame 推送自定义采集的音频数据到 RTC SDK 用于传输。
  * 如果你调用此方法由自定义采集切换至内部采集，你必须再调用 startAudioCapture 手动开启内部采集。


类型
```
(type: AudioSourceType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| AudioSourceType| 是| 无| 音频数据源，详见 AudioSourceType。默认使用内部音频采集。音频采集和渲染方式无需对应。   
返回值
number 方法调用结果：
  * ≥0: 切换成功。
  * -1：切换失败。


### setAudioRenderType() 
切换音频渲染方式
注意
  * 进房前后调用此方法均有效。
  * 如果你调用此方法切换至自定义渲染，调用 pullExternalAudioFrame 获取音频数据。


类型
```
(type: AudioRenderType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| AudioRenderType| 是| 无| 音频输出类型，详见 AudioRenderType默认使用内部音频渲染。音频采集和渲染方式无需对应。   
返回值
number 方法调用结果：
  * >0: 切换成功。
  * -1：切换失败。


### pullExternalAudioFrame() 
拉取下行音频数据用于自定义音频渲染。 调用该方法后，SDK 会主动拉取待播放的音频数据，包括远端已解码和混音后的音频数据，用于外部播放。
注意
  * 拉取外部音频数据前，必须先调用 setAudioRenderType 启用自定义音频渲染。
  * 由于 RTC SDK 的帧长为 10 毫秒，你应当每隔 10 毫秒拉取一次音频数据。确保音频采样点数（sample）x 拉取频率等于 audioFrame 的采样率 （sampleRate）。如设置采样率为 48000 时，每 10 毫秒调用本接口拉取数据，每次应拉取 480 个采样点。
  * 音频采样格式为 S16。音频缓冲区内的数据格式为 PCM 数据，其容量大小为 audioFrame.samples × audioFrame.channel × 2。


类型
```
(audioFrame: AudioFrame) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioFrame| AudioFrame| 是| 无| 音频数据帧，详见 AudioFrame   
返回值
number 方法调用结果
  * 0: 设置成功
  * < 0: 设置失败


### createRTCRoom() 
创建房间实例。 调用此方法仅返回一个房间实例，你仍需调用 才能真正地创建/加入房间。 多次调用此方法以创建多个 实例。分别调用各 RTCRoom 实例中的 方法，同时加入多个房间。 多房间模式下，用户可以同时订阅各房间的音视频流。
注意
  * 如果需要加入的房间已存在，你仍需先调用本方法来获取 RTCRoom 实例，再调用 加入房间。
  * 请勿使用同样的 roomId 创建多个房间，否则后创建的房间实例会替换先创建的房间实例。
  * 如果你需要在多个房间发布音视频流，无须创建多房间，直接调用 startForwardStreamToRooms 开始跨房间转发媒体流。


类型
```
(roomId: string) => RTCRoom

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 标识通话房间的房间 ID。该字符串符合正则表达式：[a-zA-Z0-9_@\\-\\.]{1,128}。   
返回值
RTCRoom 创建的 房间实例。
### setPublishFallbackOption() 
设置发布的音视频流的回退选项。 你可以调用该接口设置网络不佳或设备性能不足时从大流起进行降级处理，以保证通话质量。
注意
类型
```
(option: PublishFallbackOption) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
option| PublishFallbackOption| 是| 无| 本地发布的音视频流回退选项，参看 PublishFallbackOption。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRemoteUserPriority() 
设置用户优先级。
注意
  * 该方法与 setSubscribeFallbackOption 搭配使用。
  * 如果开启了订阅流回退选项，弱网或性能不足时会优先保证收到的高优先级用户的流的质量。
  * 该方法在进房前后都可以使用，可以修改远端用户的优先级。


类型
```
(roomid: RemoteUserPriority, uid: string, priority: RemoteUserPriority) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomid| RemoteUserPriority| 是| 无| 房间 ID   
uid| string| 是| 无| 远端用户的 ID 。   
priority| RemoteUserPriority| 是| 无| 远端用户的优先级，详见枚举类型 RemoteUserPriority 。   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### sendSEIMessage() 
通过视频帧发送 SEI 数据。 在视频通话场景下，SEI 数据会随视频帧发送；在语音通话场景下，SDK 会自动生成一路 16px × 16px 的黑帧视频流用来发送 SEI 数据。
注意
类型
```
(streamIndex: StreamIndex, message: ArrayBuffer, repeatCount: number, mode: SEICountPerFrame) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 指定携带 SEI 数据的媒体流类型，参看 StreamIndex。语音通话场景下，该值需设为 STREAM_INDEX_MAIN，否则 SEI 数据会被丢弃从而无法送达远端。   
message| ArrayBuffer| 是| 无| SEI 消息，建议每帧 SEI 数据总长度不超过 4 KB。超过长度限制的消息会被丢弃。   
repeatCount| number| 是| 无| 消息发送重复次数。取值范围是 [0, max{29, %{视频帧率}-1}]。推荐范围 [2,4]。调用此接口后，这些 SEI 数据会添加到从当前视频帧开始的连续 %{repeatCount}+1 个视频帧中。   
mode| SEICountPerFrame| 是| 无| SEI 发送模式，参看 SEICountPerFrame。   
返回值
number
  * >= 0: 将被添加到视频帧中的 SEI 的数量。
  * < 0: 发送失败。


### setBusinessId() 
设置业务标识参数 可通过 businessId 区分不同的业务场景。businessId 由客户自定义，相当于一个“标签”，可以分担和细化现在 AppId 的逻辑划分的功能，但不需要鉴权。
注意
  * 需要在进房前调用，进房后调用该方法无效。


类型
```
(businessId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
businessId| string| 是| 无| 用户设置的自己的 businessId 值businessId 只是一个标签，颗粒度需要用户自定义。   
返回值
number
### getVideoEffectInterface() 
获取视频特效接口。
类型
```
() => IVideoEffect

ts

```

返回值
IVideoEffect 视频特效接口，参看 。
### enableEffectBeauty() 
开启/关闭基础美颜。
注意
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 基础美颜开关 true: 开启基础美颜 false: 关闭基础美颜（默认）   
返回值
number
### setBeautyIntensity() 
调整基础美颜强度
注意
  * 若在调用 enableEffectBeauty 前设置美颜强度，则对应美颜功能的强度初始值会根据设置更新。
  * 销毁引擎后，美颜功能强度恢复默认值。


类型
```
(beautyMode: EffectBeautyMode, intensity: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
beautyMode| EffectBeautyMode| 是| 无| 基础美颜模式，参看 EffectBeautyMode。   
intensity| number| 是| 无| 美颜强度，取值范围为 [0,1]。强度为 0 表示关闭。各基础美颜模式的强度默认值分别为：美白 0.7，磨皮 0.8，锐化 0.5，清晰 0.7。   
返回值
number
### setVideoOrientation() 
在自定义视频前处理及编码前，设置 RTC 链路中的视频帧朝向，默认为 Adaptive 模式。 移动端开启视频特效贴纸，或使用自定义视频前处理时，建议固定视频帧朝向为 Portrait 模式。单流转推场景下，建议根据业务需要固定视频帧朝向为 Portrait 或 Landscape 模式。不同模式的具体显示效果参看。
注意
  * 视频帧朝向设置仅适用于内部采集视频源。对于自定义采集视频源，设置视频帧朝向可能会导致错误，例如宽高对调。屏幕源不支持设置视频帧朝向。
  * 编码分辨率的更新与视频帧处理是异步操作，进房后切换视频帧朝向可能导致画面出现短暂的裁切异常，因此建议在进房前设置视频帧朝向，且不在进房后进行切换。


类型
```
(orientation: VideoOrientation) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
orientation| VideoOrientation| 是| 无| 视频帧朝向，参看 VideoOrientation。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setRuntimeParameters() 
设置运行时的参数
注意
该接口需在 和 startAudioCapture 之前调用。
类型
```
{ (params: JSONObject): number; (params: Record<string, string>): number; }

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
params| JSONObject| 是| 无| 保留参数   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getNativeHandle() 
获取 C++ 层 。
注意
在一些场景下，获取 C++ 层 IRTCVideo，并通过其完成操作，相较于通过 Java 封装层完成有显著更高的执行效率。典型的场景有：视频/音频帧自定义处理，音视频通话加密等。
类型
```
() => number

ts

```

返回值
number
  * >0：方法调用成功, 返回 C++ 层 IRTCVideo 的地址。
  * -1：方法调用失败


### stopASR() 
关闭语音识别服务
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startFileRecording() 
该方法将通话过程中的音视频数据录制到本地的文件中。
注意
  * 调用该方法后，你会收到 onRecordingStateUpdate 回调。
  * 如果录制正常，系统每秒钟会通过 onRecordingProgressUpdate 回调通知录制进度。


类型
```
(type: StreamIndex, config: RecordingConfig, recordingType: RecordingType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| StreamIndex| 是| 无| 流属性，指定录制主流还是屏幕流，参看 StreamIndex   
config| RecordingConfig| 是| 无| 本地录制参数配置，参看 RecordingConfig   
recordingType| RecordingType| 是| 无| 本地录制的媒体类型，参看 RecordingType   
返回值
number 0: 正常 -1: 参数设置异常 -2: 当前版本 SDK 不支持该特性，请联系技术支持人员
### stopFileRecording() 
停止本地录制
注意
  * 调用 startFileRecording 开启本地录制后，你必须调用该方法停止录制。
  * 调用该方法后，你会收到 onRecordingStateUpdate 回调提示录制结果。


类型
```
(type: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| StreamIndex| 是| 无| 流属性，指定停止主流或者屏幕流录制，参看 StreamIndex   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startAudioRecording() 
开启录制语音通话，生成本地文件。 在进房前后开启录制，如果未打开麦克风采集，录制任务正常进行，只是不会将数据写入生成的本地文件；只有调用 startAudioCapture 接口打开麦克风采集后，才会将录制数据写入本地文件。
注意
类型
```
(config: AudioRecordingConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| AudioRecordingConfig| 是| 无| 参看 AudioRecordingConfig   
返回值
number
  * 0: 正常
  * -2: 参数设置异常
  * -3: 当前版本 SDK 不支持该特性，请联系技术支持人员


### stopAudioRecording() 
停止音频文件录制
注意
调用 startAudioRecording 开启本地录制后，你必须调用该方法停止录制。
类型
```
() => number

ts

```

返回值
number
  * 0: 正常
  * -3: 当前版本 SDK 不支持该特性，请联系技术支持人员


### getAudioEffectPlayer() 
创建音效播放器实例。
类型
```
() => IAudioEffectPlayer

ts

```

返回值
IAudioEffectPlayer 音效播放器。详见 。
### getMediaPlayer() 
创建音乐播放器实例。
类型
```
(playerId: number) => IMediaPlayer

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerId| number| 是| 无| 音乐播放器实例 id。取值范围为 [0, 3]。最多同时存在 4 个实例，超出取值范围时返回 nullptr。   
返回值
IMediaPlayer 音乐播放器实例，详见 
### setScreenAudioSourceType() 
在屏幕共享时，设置屏幕音频的采集方式（内部采集/自定义采集）
注意
类型
```
(sourceType: AudioSourceType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
sourceType| AudioSourceType| 是| 无| 屏幕音频输入源类型, 参看 AudioSourceType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setScreenAudioStreamIndex() 
在屏幕共享时，设置屏幕音频流和麦克风采集到的音频流的混流方式
注意
你应该在 publishScreen 之前，调用此方法。否则，你将收到 onWarning 的报错：WARNING_CODE_SET_SCREEN_STREAM_INDEX_FAILED
类型
```
(index: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 混流方式，参看 StreamIndex STREAM_INDEX_MAIN: 将屏幕音频流和麦克风采集到的音频流混流 STREAM_INDEX_SCREEN: 默认值，将屏幕音频流和麦克风采集到的音频流分为两路音频流   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### startScreenCapture() 
使用 RTC SDK 内部采集模块开始采集屏幕音频流和（或）视频流。
注意
类型
```
(type: ScreenMediaType, mediaProjectionResultData: Intent) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| ScreenMediaType| 是| 无| 媒体类型，参看 ScreenMediaType。   
mediaProjectionResultData| unknown| 是| 无| 向 Android 设备申请屏幕共享权限后，拿到的 Intent 数据，参看 getMediaProjection。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### updateScreenCapture() 
使用 RTC SDK 内部屏幕采集后，更新采集的媒体类型。
注意
在 startScreenCapture 后调用该方法。
类型
```
(type: ScreenMediaType) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| ScreenMediaType| 是| 无| 媒体类型，指定屏幕采集媒体类型，参看 ScreenMediaType。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopScreenCapture() 
在屏幕共享时，停止使用 RTC SDK 内部采集方式采集屏幕音视频。
注意
  * 调用本接口时，采集模式应为内部模式。在外部采集模式下调用无效，并将触发 onVideoDeviceWarning 或 onAudioDeviceWarning 回调。
  * 要开始屏幕音视频内部采集，调用 startScreenCapture。


类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setExternalVideoEncoderEventHandler() 
注册自定义编码帧推送事件回调
注意
  * 该方法需在进房前调用。
  * 引擎销毁前需取消注册，调用该方法将参数设置为 "null" 即可。


类型
```
(handler: IExternalVideoEncoderEventHandler) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
handler| IExternalVideoEncoderEventHandler| 是| 无| 自定义编码帧回调类，参看 IExternalVideoEncoderEventHandler   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setVideoDecoderConfig() 
在订阅远端视频流之前，设置远端视频数据解码方式
注意
  * 当你想要对远端流进行自定义解码时，你需要先调用 registerRemoteEncodedVideoFrameObserver 注册远端视频流监测器，然后再调用该接口将解码方式设置为自定义解码。监测到的视频数据会通过 onRemoteEncodedVideoFrame 回调出来。
  * 自 3.56 起，要用于自动订阅场景下，你可以设置 key 中的 RoomId 和 UserId 为 nullptr，此时，通过此接口设置的解码方式根据 key 中的 StreamIndex 值，适用于所有的远端主流或屏幕流的解码方式。


类型
```
(key: RemoteStreamKey, config: VideoDecoderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
key| RemoteStreamKey| 是| 无| 远端流信息，指定对哪一路视频流进行解码方式设置，参看 RemoteStreamKey。   
config| VideoDecoderConfig| 是| 无| 视频解码方式，参看 VideoDecoderConfig。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### requestRemoteVideoKeyFrame() 
在订阅远端视频流之后，向远端请求关键帧
注意
  * 该方法仅适用于手动订阅模式，并且在成功订阅远端流之后使用。
  * 该方法适用于调用 setVideoDecoderConfig 开启自定义解码功能后，并且自定义解码失败的情况下使用


类型
```
(streamKey: RemoteStreamKey) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setEarMonitorMode() 
打开/关闭耳返功能。
注意
  * 耳返功能仅适用于由 RTC SDK 内部采集的音频。
  * 使用耳返功能必须佩戴耳机。为保证低延时耳返最佳体验，建议佩戴有线耳机。蓝牙耳机不支持硬件耳返。
  * RTC SDK 支持硬件耳返和软件耳返。一般来说，硬件耳返延时低且音质好。如果 App 在手机厂商的硬件耳返白名单内，且运行环境存在支持硬件耳返的 SDK，RTC SDK 默认启用硬件耳返。使用华为手机硬件耳返功能时，请添加。


类型
```
(mode: EarMonitorMode) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mode| EarMonitorMode| 是| 无| 耳返功能是否开启，详见 EarMonitorMode。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setEarMonitorVolume() 
设置耳返音量。
注意
  * 设置耳返音量前，你必须先调用 setEarMonitorMode 打开耳返功能。


类型
```
(volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
volume| number| 是| 无| 耳返音量，调节范围：[0,100]，单位：%   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### enableAudioPropertiesReport() 
启用音频信息提示。开启提示后，你可以收到 onLocalAudioPropertiesReport，onRemoteAudioPropertiesReport onRemoteAudioPropertiesReport 和 onActiveSpeaker。
类型
```
(config: AudioPropertiesConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| AudioPropertiesConfig| 是| 无| 详见 AudioPropertiesConfig   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### sendStreamSyncInfo() 
发送音频流同步信息。将消息通过音频流发送到远端，并实现与音频流同步，该接口调用成功后，远端用户会收到 onStreamSyncInfoReceived 回调。
注意
  * 调用本接口的频率建议不超过 50 次每秒。
  * 在 CHANNEL_PROFILE_INTERACTIVE_PODCAST 房间模式下，此消息一定会送达。在其他房间模式下，如果本地用户未说话，此消息不一定会送达。


类型
```
(data: ArrayBuffer, config: StreamSycnInfoConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
data| ArrayBuffer| 是| 无| 消息内容。   
config| StreamSycnInfoConfig| 是| 无| 音频流同步信息的相关配置。详见 StreamSycnInfoConfig 。   
返回值
number
### isCameraTorchSupported() 
检测当前使用的摄像头（前置/后置），是否支持闪光灯。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检测闪光能力。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持
  * false: 不支持


### isCameraZoomSupported() 
检测当前使用的摄像头（前置/后置），是否支持变焦（数码/光学变焦）。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检测摄像头变焦能力。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持
  * false: 不支持


### setCameraZoomRatio() 
设置当前使用的摄像头（前置/后置）的光学变焦倍数
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能设置摄像头变焦倍数。
  * 设置结果在调用 stopVideoCapture 关闭内部采集后失效。
  * 你可以调用 setVideoDigitalZoomConfig 设置数码变焦参数， 调用 setVideoDigitalZoomControl 进行数码变焦。


类型
```
(zoom: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
zoom| number| 是| 无| 变焦倍数。取值范围是 [1, <最大变焦倍数>]。最大变焦倍数可以通过调用 getCameraZoomMaxRatio 获取。   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### getCameraZoomMaxRatio() 
获取当前使用的摄像头（前置/后置）的最大变焦倍数
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检测摄像头最大变焦倍数。
类型
```
() => number

ts

```

返回值
number 最大变焦倍数
### setCameraTorch() 
打开/关闭当前使用的摄像头（前置/后置）的闪光灯
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能设置闪光灯。
  * 设置结果在调用 stopVideoCapture 关闭内部采集后失效。


类型
```
(torchState: TorchState) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
torchState| TorchState| 是| 无| 闪光灯状态。参考 TorchState   
返回值
number
  * 0： 成功。
  * < 0： 失败。


### isCameraFocusPositionSupported() 
检查当前使用的摄像头是否支持手动对焦。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集，才能检查摄像头是否支持手动对焦。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持。
  * false: 不支持。


### setCameraFocusPosition() 
设置当前使用的摄像头的对焦点。
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集，并且使用 SDK 内部渲染时，才能设置对焦点。
  * 移动设备时，自动取消对焦点设置。
  * 调用 stopVideoCapture 关闭内部采集后，设置的对焦点失效。


类型
```
(x: number, y: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
x| number| 是| 无| 对焦点水平方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最左边，1 表示最右边。   
y| number| 是| 无| 对焦点垂直方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最上边，1 表示最下边。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### isCameraExposurePositionSupported() 
检查当前使用的摄像头是否支持手动设置曝光点。
注意
必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能检查曝光点设置能力。
类型
```
() => boolean

ts

```

返回值
boolean
  * true: 支持。
  * false: 不支持。


### setCameraExposurePosition() 
设置当前使用的摄像头的曝光点
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集，并且使用 SDK 内部渲染时，才能设置曝光点。
  * 移动设备时，自动取消曝光点设置。
  * 调用 stopVideoCapture 关闭内部采集后，设置的曝光点失效。


类型
```
(x: number, y: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
x| number| 是| 无| 曝光点水平方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最左边，1 表示最右边。   
y| number| 是| 无| 曝光点垂直方向归一化坐标。以本地预览画布的左上为原点，取值范围为 [0, 1]，0 表示最上边，1 表示最下边。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### setCameraExposureCompensation() 
设置当前使用的摄像头的曝光补偿。
注意
  * 必须已调用 startVideoCapture 使用 SDK 内部采集模块进行视频采集时，才能设置曝光补偿。
  * 调用 stopVideoCapture 关闭内部采集后，设置的曝光补偿失效。


类型
```
(val: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
val| number| 是| 无| 曝光补偿值，取值范围 [-1, 1]，0 为系统默认值(没有曝光补偿)。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### enableCameraAutoExposureFaceMode() 
启用或禁用内部采集时人脸自动曝光模式。此模式会改善强逆光下，脸部过暗的问题；但也会导致 ROI 以外区域过亮/过暗的问题。
注意
你必须在调用 startVideoCapture 开启内部采集前，调用此接口方可生效。
类型
```
(enable: boolean) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
enable| boolean| 是| 无| 是否启用。默认开启。   
返回值
number
  * 0: 成功。
  * < 0: 失败。


### startPushPublicStream() 
发布一路公共流 用户可以指定房间内多个用户发布的媒体流合成一路公共流。使用同一 appID 的用户，可以调用 startPlayPublicStream 获取和播放指定的公共流。
注意
类型
```
(publicStreamId: string, publicStream: PublicStreaming) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
publicStream| PublicStreaming| 是| 无| 公共流参数。详见 PublicStreaming。一路公共流可以包含多路房间内的媒体流，按照指定的布局方式进行聚合。如果指定的媒体流还未发布，则公共流将在指定流开始发布后实时更新。   
返回值
number 0: 成功。同时将收到 onPushPublicStreamResult 回调。
  * !0: 失败。当参数不合法或参数为空，调用失败。


### stopPushPublicStream() 
停止发布当前用户发布的公共流 关于发布公共流，查看 startPushPublicStream
类型
```
(publicStreamId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID指定的流必须为当前用户所发布。   
返回值
number
  * 0: 成功
  * !0: 失败


### updatePublicStreamParam() 
更新公共流参数 关于发布公共流，查看 startPushPublicStream。
注意
调用本接口前需要通过 onPushPublicStreamResult 确认公共流是否已经成功启动。
类型
```
(publicStreamId: string, transcoding: PublicStreaming) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID指定的流必须为当前用户所发布。   
transcoding| PublicStreaming| 是| 无| 配置参数，详见 PublicStreaming。   
返回值
number
  * 0: 成功
  * !0: 失败


### startPlayPublicStream() 
订阅指定公共流 无论用户是否在房间内，都可以调用本接口获取和播放指定的公共流。
注意
类型
```
(publicStreamId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID，如果指定流暂未发布，则本地客户端将在其开始发布后接收到流数据。   
返回值
number
  * 0: 成功。同时将收到 onPlayPublicStreamResult 回调。
  * !0: 失败。当参数不合法或参数为空，调用失败。


### stopPlayPublicStream() 
取消订阅指定公共流 关于订阅公共流，查看 startPlayPublicStream。
类型
```
(publicStreamId: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
返回值
number
  * 0：成功
  * !0：失败


### setPublicStreamVideoCanvas() 
为指定公共流绑定内部渲染视图。
类型
```
(publicStreamId: string, canvas: VideoCanvas) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
canvas| VideoCanvas| 是| 无| 内部渲染视图，如果需要解除视频的绑定视图，把 videoCanvas 设置为空。详见 VideoCanvas   
返回值
number
  * 0：成功
  * <0：失败


### setPublicStreamAudioPlaybackVolume() 
调节公共流的音频播放音量。
类型
```
(publicStreamId: string, volume: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
volume| number| 是| 无| 音频播放音量值和原始音量值的比值，该比值的范围是 [0, 400]，单位为 %，且自带溢出保护。为保证更好的音频质量，建议设定在 [0, 100] 之间，其中 100 为系统默认值。   
返回值
number
  * 0: 成功调用。
  * -2: 参数错误。


### startEchoTest() 
开启音视频回路测试。 在进房前，用户可调用该接口对音视频通话全链路进行检测，包括对音视频设备以及用户上下行网络的检测，从而帮助用户判断是否可以正常发布和接收音视频流。 开始检测后，SDK 会录制你声音或视频。如果你在设置的延时范围内收到了回放，则视为音视频回路测试正常。
注意
类型
```
(config: EchoTestConfig, delayTime: number) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| EchoTestConfig| 是| 无| 回路测试参数设置，参看 EchoTestConfig。   
delayTime| number| 是| 无| 音视频延迟播放的时间间隔，用于指定在开始检测多长时间后期望收到回放。取值范围为 [2,10]，单位为秒，默认为 2 秒。   
返回值
number 方法调用结果：
### stopEchoTest() 
停止音视频回路测试。 调用 startEchoTest 开启音视频回路检测后，你必须调用该方法停止检测。
注意
音视频回路检测结束后，所有对系统设备及音视频流的控制均会恢复到开始检测前的状态。
类型
```
() => number

ts

```

返回值
number 方法调用结果：
  * 0：成功
  * -1：失败，未开启回路检测


### clearVideoWatermark() 
移除指定视频流的水印。
类型
```
(streamIndex: StreamIndex) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 需要移除水印的视频流属性，参看 StreamIndex。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### setDummyCaptureImagePath() 
摄像头处于关闭状态时，使用静态图片填充本地推送的视频流。 调用 stopVideoCapture 接口时，会开始推静态图片。若要停止发送图片，可传入空字符串或启用内部摄像头采集。 可重复调用该接口来更新图片。
注意
类型
```
(filePath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
filePath| string| 是| 无| 设置静态图片的路径。支持本地文件绝对路径和 Asset 资源路径(/assets/xx.png)，不支持网络链接，长度限制为 512 字节。静态图片支持类型为 JPEG/JPG、PNG、BMP。若图片宽高比与设置的编码宽高比不一致，图片会被等比缩放，黑边填充空白区域。推流帧率与码率与设置的编码参数一致。   
返回值
number
  * 0: 成功。
  * -1: 失败。


### startCloudProxy() 
开启云代理
注意
类型
```
(cloudProxiesInfo: Array<CloudProxyInfo>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
cloudProxiesInfo| CloudProxyInfo[]| 是| 无| 云代理服务器信息列表。参看 CloudProxyInfo。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### stopCloudProxy() 
关闭云代理
注意
要开启云代理，调用 startCloudProxy。
类型
```
() => number

ts

```

返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### getSingScoringManager() 
创建 K 歌评分管理接口。
注意
如需使用 K 歌评分功能，即调用该方法以及 ISingScoringManager 类下全部方法，需集成 SAMI 动态库，详情参看文档。
类型
```
() => ISingScoringManager

ts

```

返回值
ISingScoringManager K 歌评分管理接口，参看 。
### getNetworkTimeInfo() 
通过 NTP 协议，获取网络时间。
注意
  * 第一次调用此接口会启动网络时间同步功能，并返回 0。同步完成后，会收到 onNetworkTimeSynchronized，此后，再次调用此 API，即可获取准确的网络时间。
  * 在合唱场景下，合唱参与者应在相同的网络时间播放背景音乐。


类型
```
() => NetworkTimeInfo

ts

```

返回值
NetworkTimeInfo 网络时间。参看 。
### getKTVManager() 
创建 KTV 管理接口。
类型
```
() => IKTVManager

ts

```

返回值
IKTVManager KTV 管理接口，参看 。
### startHardwareEchoDetection() 
开启通话前回声检测
注意
类型
```
(testAudioFilePath: string) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
testAudioFilePath| string| 是| 无| 用于回声检测的音频文件的绝对路径，路径字符串使用 UTF-8 编码格式，支持以下音频格式: mp3，aac，m4a，3gp，wav。音频文件不为静音文件，推荐时长为 10 ～ 20 秒。   
返回值
number 方法调用结果：
  * 0: 成功。
  * -1：失败。上一次检测未结束，请先调用 stopHardwareEchoDetection 停止检测 后重新调用本接口。
  * -2：失败。路径不合法或音频文件格式不支持。


### stopHardwareEchoDetection() 
停止通话前回声检测
注意
  * 关于开启通话前回声检测，参看 startHardwareEchoDetection 。
  * 建议在收到 onHardwareEchoDetectionResult 通知的检测结果后，调用本接口停止检测。
  * 在用户进入房间前结束回声检测，释放对音频设备的占用，以免影响正常通话。


类型
```
() => number

ts

```

返回值
number 方法调用结果：
  * 0: 成功。
  * -1：失败。


### setCellularEnhancement() 
启用蜂窝网络辅助增强，改善通话质量。
注意
此功能默认不开启。
类型
```
(config: MediaTypeEnhancementConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
config| MediaTypeEnhancementConfig| 是| 无| 参看 MediaTypeEnhancementConfig。   
返回值
number 方法调用结果：
  * 0: 成功。
  * -1：失败，内部错误。
  * -2: 失败，输入参数错误。


### android_getVideoDeviceManager() 
创建视频设备管理实例
类型
```
() => $p_a.IVideoDeviceManager

ts

```

返回值
$p_a.IVideoDeviceManager 视频设备管理实例，详见 
### setVideoCaptureRotation() 
设置本端采集的视频帧的旋转角度。 当摄像头倒置或者倾斜安装时，可调用本接口进行调整。对于手机等普通设备，可调用 setVideoRotationMode 实现旋转。
注意
类型
```
(rotation: VideoRotation) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
rotation| VideoRotation| 是| 无| 相机朝向角度，默认为 VIDEO_ROTATION_0(0)，无旋转角度。详见 VideoRotation。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 获得更多错误说明


### ios_setMaxVideoEncoderConfig() 
视频发布端设置期望发布的最大分辨率视频流参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。 该接口支持设置一路视频流参数，设置多路参数请使用 。
注意
类型
```
(encoderConfig: $p_i.ByteRTCVideoEncoderConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
encoderConfig| $p_i.ByteRTCVideoEncoderConfig| 是| 无| 期望发布的最大分辨率视频流参数。参看 ByteRTCVideoEncoderConfig。   
返回值
number 方法调用结果：
  * 0：成功
  * !0：失败


### ios_setCustomizeEncryptHandler() 
设置自定义加密和解密方式。
注意
类型
```
(handler: $p_i.id<$p_i.ByteRTCEncryptHandler>) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
handler| $p_i.ByteRTCEncryptHandler| 是| 无| 自定义加密 handler，需要实现里面的加密和解密方法。参看 ByteRTCEncryptHandler。   
返回值
number
  * 0: 调用成功。
  * < 0 : 调用失败。查看 ByteRTCReturnStatus 获得更多错误说明


### static android_createRTCVideo_context$appId$handler$eglContext$parameters() 
创建引擎对象 如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 如果当前进程中已创建了引擎实例，再次调用此方法时，会返回已创建的引擎实例。
注意
你应注意保持 handler 的生命周期必须大于 的生命周期，即 handler 必须在调用 destroyRTCVideo 之后销毁。
类型
```
(context: $p_a.Context, appId: string, handler: $p_a.IRTCVideoEventHandler, eglContext: Object, parameters: $p_a.JSONObject) => $p_a.RTCVideo

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
context| unknown| 是| 无| Android Application Context   
appId| string| 是| 无| 每个应用的唯一标识符，由 RTC 控制台随机生成的。不同的 AppId 生成的实例在 RTC 中进行音视频通话完全独立，无法互通。   
handler| $p_a.IRTCVideoEventHandler| 是| 无| SDK 回调给应用层的 Handler，详见 IRTCVideoEventHandler   
eglContext| Object| 是| 无| 如果需要支持外部纹理硬编码，则需要以 JObject 方式传入 eglContext。   
parameters| $p_a.JSONObject| 是| 无| 私有参数。如需使用请联系技术支持人员。   
返回值
$p_a.RTCVideo
  * RTCVideo：创建成功。返回一个可用的 实例
  * Null：appId 或者 context 参数为空，.so 文件加载失败。


### static destroyRTCVideo() 
销毁由 createRTCVideo 所创建的引擎实例，并释放所有相关资源。
注意
  * 请确保和需要销毁的 实例相关的业务场景全部结束后，才调用此方法
  * 该方法在调用之后，会销毁所有和此 实例相关的内存，并且停止与媒体服务器的任何交互
  * 调用本方法会启动 SDK 退出逻辑。引擎线程会保留，直到退出逻辑完成。因此，不要在回调线程中直接调用此 API，会导致死锁。同时此方法是耗时操作，不建议在主线程调用本方法，避免主线程阻塞。


类型
```
() => void

ts

```

### static getSDKVersion() 
获取 SDK 当前的版本号。
类型
```
() => string

ts

```

返回值
string SDK 当前的版本号。
### static setLogConfig() 
自定义 SDK 日志配置，包括日志输出等级、存储路径、日志文件总大小上限、日志文件名前缀。
注意
本方法必须在调用 createRTCVideo 之前调用。
类型
```
(logConfig: RTCLogConfig) => number

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
logConfig| RTCLogConfig| 是| 无| 日志配置，参看 RTCLogConfig。   
返回值
number
  * 0：成功。
  * –1：失败，本方法必须在创建引擎前调用。
  * –2：失败，参数填写错误。


### static getErrorDescription() 
获取 SDK 内各种错误码、警告码的描述文字。
类型
```
(code: number) => string

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
code| number| 是| 无| 通过 onWarning 和 onError 回调获得的值，具体可以参考 ErrorCode 和 WarningCode   
返回值
string String 描述文字
</details>

<details>
<summary>点击展开：React Native 3.58 - 回调</summary>

### 回调--实时音视频-火山引擎
Source: https://www.volcengine.com/docs/6348/1390576

/实时音视频/客户端 API 参考/React Native 3.58/回调

回调
最近更新时间：2025.03.28 17:04:43首次发布时间：2025.02.26 11:44:49

## IFaceDetectionObserver 
类型：interface
人脸检测结果回调观察者 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onFaceDetectResult 
特效 SDK 进行人脸检测结果的回调。 调用 enableFaceDetection 注册了 ，并使用 RTC SDK 中包含的特效 SDK 进行视频特效处理时，你会收到此回调。
类型
```
((result: FaceDetectionResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
result| FaceDetectionResult| 是| 无| 人脸检测结果, 参看 FaceDetectionResult。   
## RTCVideoEventHandler 
类型：interface
音视频引擎事件回调接口 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onWarning 
发生警告回调。 SDK 运行时出现了警告。SDK 通常会自动恢复，警告信息可以忽略。
类型
```
((warn: WarningCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
warn| WarningCode| 是| 无| 警告代码，参见 WarningCode   
### onError 
发生错误回调。 SDK 运行时出现了网络或媒体相关的错误，且无法自动恢复时触发此回调。 你可能需要干预.
类型
```
((err: ErrorCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
err| ErrorCode| 是| 无| 错误代码，详情定义见: ErrorCode   
### onExtensionAccessError 
当访问插件失败时，收到此回调。 RTC SDK 将一些功能封装成插件。当使用这些功能时，如果插件不存在，功能将无法使用。
类型
```
((extensionName: string, msg: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
extensionName| string| 是| 无| 插件名字   
msg| string| 是| 无| 失败说明   
### onSysStats 
每 2 秒发生回调，通知当前 cpu，内存使用的信息。
类型
```
((stats: SysStats) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stats| SysStats| 是| 无| cpu，内存信息。详见 SysStats 数据类型。   
### onNetworkTypeChanged 
SDK 当前网络连接类型改变回调。当 SDK 的当前网络连接类型发生改变时回调该事件。
类型
```
((type: RTCNetworkType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| RTCNetworkType| 是| 无| 网络类型。 -1： 网络连接类型未知。 0： 网络连接已断开。 1： LAN 2： Wi-Fi,包含热点 3： 2G 移动网络 4： 3G 移动网络 5： 4G 移动网络 6： 5G 移动网络   
### onUserStartVideoCapture 
房间内的可见用户调用 startVideoCapture 开启内部视频采集时，房间内其他用户会收到此回调。
类型
```
((roomId: string, uid: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 开启视频采集的远端用户所在的房间 ID   
uid| string| 是| 无| 开启视频采集的远端用户 ID   
### onUserStopVideoCapture 
房间内的可见用户调用 stopVideoCapture 关闭内部视频采集时，房间内其他用户会收到此回调。 若发布视频数据前未开启采集，房间内所有可见用户会收到此回调。
类型
```
((roomId: string, uid: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 关闭视频采集的远端用户所在的房间 ID   
uid| string| 是| 无| 关闭视频采集的远端用户 ID   
### onCreateRoomStateChanged 
创建房间失败回调。
类型
```
((roomId: string, errorCode: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 房间 ID。   
errorCode| number| 是| 无| 创建房间错误码，具体原因参看 ErrorCode。   
### onUserStartAudioCapture 
房间内的用户调用 startAudioCapture 开启音频采集时，房间内其他用户会收到此回调。
类型
```
((roomId: string, uid: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 开启音频采集的远端用户所在的房间 ID   
uid| string| 是| 无| 开启音频采集的远端用户 ID   
### onUserStopAudioCapture 
房间内的用户调用 stopAudioCapture 关闭音频采集时，房间内其他用户会收到此回调。
类型
```
((roomId: string, uid: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 关闭音频采集的远端用户所在的房间 ID   
uid| string| 是| 无| 关闭音频采集的远端用户 ID   
### onLocalAudioStateChanged 
本地音频流的状态发生改变时，收到此回调。
类型
```
((state: LocalAudioStreamState, error: LocalAudioStreamError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| LocalAudioStreamState| 是| 无| 本地音频设备的状态，详见 LocalAudioStreamState   
error| LocalAudioStreamError| 是| 无| 本地音频流状态改变时的错误码，详见 LocalAudioStreamError   
### onRemoteAudioStateChanged 
用户订阅来自远端的音频流状态发生改变时，会收到此回调，了解当前的远端音频流状态。
类型
```
((key: RemoteStreamKey, state: RemoteAudioState, reason: RemoteAudioStateChangeReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
key| RemoteStreamKey| 是| 无| 远端流信息, 详见 RemoteStreamKey   
state| RemoteAudioState| 是| 无| 远端音频流状态，详见 RemoteAudioState   
reason| RemoteAudioStateChangeReason| 是| 无| 远端音频流状态改变的原因，详见 RemoteAudioStateChangeReason   
### onLocalVideoStateChanged 
本地视频流的状态发生改变时，收到该事件。
类型
```
((streamIndex: StreamIndex, state: LocalVideoStreamState, error: LocalVideoStreamError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 流属性，参看 StreamIndex   
state| LocalVideoStreamState| 是| 无| 本地视频流状态，参看 LocalVideoStreamState   
error| LocalVideoStreamError| 是| 无| 本地视频状态改变时的错误码，参看 LocalVideoStreamError   
### onRemoteVideoStateChanged 
远端视频流的状态发生改变时，房间内订阅此流的用户会收到该事件。
类型
```
((streamKey: RemoteStreamKey, videoState: RemoteVideoState, videoStateReason: RemoteVideoStateChangeReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端视频流信息，房间、用户 ID、流属性等，参看 RemoteStreamKey   
videoState| RemoteVideoState| 是| 无| 远端视频流状态，参看 RemoteVideoState   
videoStateReason| RemoteVideoStateChangeReason| 是| 无| 远端视频流状态改变原因，参看 RemoteVideoStateChangeReason   
### onFirstRemoteVideoFrameRendered 
SDK 内部渲染成功远端视频流首帧后，收到此回调。
类型
```
((remoteStreamKey: RemoteStreamKey, frameInfo: VideoFrameInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey   
frameInfo| VideoFrameInfo| 是| 无| 视频帧信息，参看 VideoFrameInfo   
### onFirstRemoteVideoFrameDecoded 
SDK 接收并解码远端视频流首帧后，收到此回调。
类型
```
((remoteStreamKey: RemoteStreamKey, frameInfo: VideoFrameInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey   
frameInfo| VideoFrameInfo| 是| 无| 视频帧信息，参看 VideoFrameInfo   
### onFirstLocalVideoFrameCaptured 
RTC SDK 在本地完成第一帧视频帧或屏幕视频帧采集时，收到此回调。
类型
```
((streamIndex: StreamIndex, frameInfo: VideoFrameInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 流属性，参看 StreamIndex   
frameInfo| VideoFrameInfo| 是| 无| 视频信息，参看 VideoFrameInfo   
### onLocalVideoSizeChanged 
本地预览视频大小或旋转信息发生改变时，收到此回调。
类型
```
((streamIndex: StreamIndex, frameInfo: VideoFrameInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 流属性，参看 StreamIndex   
frameInfo| VideoFrameInfo| 是| 无| 视频帧信息，参看 VideoFrameInfo   
### onRemoteVideoSizeChanged 
远端视频大小或旋转信息发生改变时，房间内订阅此视频流的用户会收到此回调。
类型
```
((remoteStreamKey: RemoteStreamKey, frameInfo: VideoFrameInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey。   
frameInfo| VideoFrameInfo| 是| 无| 视频帧信息，参看 VideoFrameInfo   
### onConnectionStateChanged 
回调 SDK 与信令服务器连接状态相关事件。当 SDK 与信令服务器的网络连接状态改变时回调该事件。
类型
```
((state: ConnectionState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| ConnectionState| 是| 无| 当前 SDK 与信令服务器连接状态。 详细定义参见 ConnectionState   
### onAudioRouteChanged 
音频播放路由变化时，收到该回调。
类型
```
((route: AudioRoute) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
route| AudioRoute| 是| 无| 新的音频播放路由，详见 AudioRoute   
### onFirstLocalAudioFrame 
发布音频流时，采集到第一帧音频帧，收到该回调。
类型
```
((streamIndex: StreamIndex) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 音频流属性, 详见 StreamIndex   
### onFirstRemoteAudioFrame 
接收到来自远端某音频流的第一帧时，收到该回调。
类型
```
((remoteStreamKey: RemoteStreamKey) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端音频流信息, 详见 RemoteStreamKey   
### onSEIMessageReceived 
收到通过调用 sendSEIMessage 发送带有 SEI 消息的视频帧时，收到此回调。
类型
```
((remoteStreamKey: RemoteStreamKey, message: ByteBuffer) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 包含 SEI 发送者的用户名，所在的房间名和媒体流，详见 RemoteStreamKey   
message| ArrayBuffer| 是| 无| 收到的 SEI 消息内容   
### onSEIStreamUpdate 
黑帧视频流发布状态回调。 在语音通话场景下，本地用户调用 sendSEIMessage 通过黑帧视频流发送 SEI 数据时，流的发送状态会通过该回调通知远端用户。 你可以通过此回调判断携带 SEI 数据的视频帧为黑帧，从而不对该视频帧进行渲染。
类型
```
((remoteStreamKey: RemoteStreamKey, event: SEIStreamUpdateEventType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
remoteStreamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey。   
event| SEIStreamUpdateEventType| 是| 无| 黑帧视频流状态，参看 SEIStreamUpdateEvent   
### onLoginResult 
登录结果回调
类型
```
((uid: string, errorCode: LoginErrorCode, elapsed: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 登录用户 ID   
errorCode| LoginErrorCode| 是| 无| 登录结果详见 LoginErrorCode。   
elapsed| number| 是| 无| 从调用 login 接口开始到返回结果所用时长。单位为 ms。   
### onLogout 
登出结果回调
类型
```
((reason: LogoutReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
reason| LogoutReason| 是| 无| 用户登出的原因，参看 LogoutReason   
### onServerParamsSetResult 
设置应用服务器参数的返回结果
类型
```
((error: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
error| number| 是| 无| 设置结果 返回 200，设置成功 返回其他，设置失败，详见 UserMessageSendResult   
### onGetPeerOnlineStatus 
查询对端或本端用户登录状态的返回结果
类型
```
((peerUserId: string, status: UserOnlineStatus) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
peerUserId| string| 是| 无| 需要查询的用户 ID   
status| UserOnlineStatus| 是| 无| 查询的用户登录状态详见 UserOnlineStatus.   
### onUserMessageReceivedOutsideRoom 
收到房间外用户调用 sendUserMessageOutsideRoom 发来的文本消息时，会收到此回调
类型
```
((uid: string, message: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息发送者 ID   
message| string| 是| 无| 收到的文本消息内容   
### onUserBinaryMessageReceivedOutsideRoom 
收到房间外用户调用 sendUserBinaryMessageOutsideRoom 发来的二进制消息时，会收到此回调
类型
```
((uid: string, message: ByteBuffer) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息发送者 ID   
message| ArrayBuffer| 是| 无| 收到的二进制消息内容   
### onUserMessageSendResultOutsideRoom 
给房间外指定的用户发送消息的回调
类型
```
((msgid: number, error: UserMessageSendResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
msgid| number| 是| 无| 本条消息的 ID所有的 P2P 和 P2Server 消息共用一个 ID 序列。   
error| UserMessageSendResult| 是| 无| 消息发送结果详见 UserMessageSendResult。   
### onServerMessageSendResult 
给应用服务器发送消息的回调。
类型
```
((msgid: number, error: UserMessageSendResult, message: ByteBuffer) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
msgid| number| 是| 无| 本条消息的 ID所有的 P2P 和 P2Server 消息共用一个 ID 序列。   
error| UserMessageSendResult| 是| 无| 消息发送结果，详见 UserMessageSendResult。   
message| ArrayBuffer| 是| 无| 应用服务器收到 HTTP 请求后，在 ACK 中返回的信息。消息不超过 64 KB。   
### onNetworkDetectionResult 
通话前网络探测结果。 成功调用 startNetworkDetection 接口开始探测后，会在 3s 内首次收到该回调，之后每 2s 收到一次该回调，通知探测结果。
类型
```
((type: NetworkDetectionLinkType, quality: NetworkQuality, rtt: number, lostRate: number, bitrate: number, jitter: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| NetworkDetectionLinkType| 是| 无| 探测网络类型为上行/下行   
quality| NetworkQuality| 是| 无| 探测网络的质量，参看 NetworkQuality。   
rtt| number| 是| 无| 探测网络的 RTT，单位：ms   
lostRate| number| 是| 无| 探测网络的丢包率   
bitrate| number| 是| 无| 探测网络的带宽，单位：kbps   
jitter| number| 是| 无| 探测网络的抖动,单位：ms   
### onNetworkDetectionStopped 
通话前网络探测结束 以下情况将停止探测并收到本一次本回调：
  1. 当调用 stopNetworkDetection 接口停止探测后，会收到一次该回调；
  2. 当收到远端/本端音频首帧后，停止探测；
  3. 当探测超过 3 分钟后，停止探测；
  4. 当探测链路断开一定时间之后，停止探测。


类型
```
((reason: NetworkDetectionStopReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
reason| NetworkDetectionStopReason| 是| 无| 停止探测的原因类型,参考 NetworkDetectionStopReason   
### onSimulcastSubscribeFallback 
音视频流因网络环境变化等原因发生回退，或从回退中恢复时，触发该回调。
类型
```
((event: RemoteStreamSwitchEvent) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
event| RemoteStreamSwitchEvent| 是| 无| 音视频流发生变化的信息。参看 RemoteStreamSwitch。   
### onPerformanceAlarms 
本地未通过 setPublishFallbackOption 开启发布性能回退，检测到设备性能不足时，收到此回调。 本地通过 setPublishFallbackOption 开启发布性能回退，因设备性能/网络原因，造成发布性能回退/恢复时，收到此回调。
类型
```
((mode: PerformanceAlarmMode, roomId: string, reason: PerformanceAlarmReason, data: SourceWantedData) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mode| PerformanceAlarmMode| 是| 无| 指示本地是否开启发布回退功能。参看 PerformanceAlarmMode当发布端未开启发布性能回退时，mode 值为 NORMAL。 当发布端开启发布性能回退时，mode 值为 SIMULCAST。   
roomId| string| 是| 无| 未开启发布性能回退时，roomId 为空 开启发布性能回退时，roomId 是告警影响的房间 ID。   
reason| PerformanceAlarmReason| 是| 无| 告警原因，参看 PerformanceAlarmReason   
data| SourceWantedData| 是| 无| 性能回退相关数据，详见 SourceWantedData。   
### onAudioFrameSendStateChanged 
本地音频首帧发送状态发生改变时，收到此回调。
类型
```
((roomId: string, user: RTCUser, state: FirstFrameSendState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 音频发布用户所在的房间 ID   
user| RTCUser| 是| 无| 本地用户信息，详见 RtcUser   
state| FirstFrameSendState| 是| 无| 首帧发送状态，详见 FirstFrameSendState   
### onVideoFrameSendStateChanged 
本地视频首帧发送状态发生改变时，收到此回调。
类型
```
((roomId: string, user: RTCUser, state: FirstFrameSendState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 视频发布用户所在的房间 ID   
user| RTCUser| 是| 无| 本地用户信息，详见 RtcUser   
state| FirstFrameSendState| 是| 无| 首帧发送状态，详见 FirstFrameSendState   
### onScreenVideoFrameSendStateChanged 
屏幕共享流的视频首帧发送状态发生改变时，收到此回调。
类型
```
((roomId: string, user: RTCUser, state: FirstFrameSendState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 屏幕视频发布用户所在的房间 ID   
user| RTCUser| 是| 无| 本地用户信息，详见 RtcUser   
state| FirstFrameSendState| 是| 无| 首帧发送状态，详见 FirstFrameSendState   
### onAudioFramePlayStateChanged 
音频首帧播放状态发生改变时，收到此回调。
类型
```
((roomId: string, user: RTCUser, state: FirstFramePlayState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 音频发布用户所在的房间 ID   
user| RTCUser| 是| 无| 远端用户信息，详见 RtcUser   
state| FirstFramePlayState| 是| 无| 首帧播放状态，详见 FirstFramePlayState   
### onVideoFramePlayStateChanged 
视频首帧播放状态发生改变时，收到此回调。
类型
```
((roomId: string, user: RTCUser, state: FirstFramePlayState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 视频发布用户所在的房间 ID   
user| RTCUser| 是| 无| 远端用户信息，详见 RtcUser   
state| FirstFramePlayState| 是| 无| 首帧播放状态，详见 FirstFramePlayState   
### onScreenVideoFramePlayStateChanged 
屏幕共享流视频首帧播放状态发生改变时，收到此回调。
类型
```
((roomId: string, user: RTCUser, state: FirstFramePlayState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 屏幕视频发布用户所在的房间 ID   
user| RTCUser| 是| 无| 远端用户信息，详见 RtcUser   
state| FirstFramePlayState| 是| 无| 首帧播放状态，详见 FirstFramePlayState   
### onAudioDeviceStateChanged 
音频设备状态回调。提示音频采集、音频播放等媒体设备的状态。
类型
```
((deviceID: string, deviceType: AudioDeviceType, deviceState: MediaDeviceState, deviceError: MediaDeviceError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 设备 ID   
deviceType| AudioDeviceType| 是| 无| 设备类型，详见 AudioDeviceType。   
deviceState| MediaDeviceState| 是| 无| 设备状态，详见 MediaDeviceState。   
deviceError| MediaDeviceError| 是| 无| 设备错误类型，详见 MediaDeviceError。   
### onVideoDeviceStateChanged 
视频设备状态回调。提示摄像头视频采集、屏幕视频采集等媒体设备的状态。
类型
```
((deviceID: string, deviceType: VideoDeviceType, deviceState: MediaDeviceState, deviceError: MediaDeviceError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 设备 ID   
deviceType| VideoDeviceType| 是| 无| 设备类型，详见 VideoDeviceType。   
deviceState| MediaDeviceState| 是| 无| 设备状态，详见 MediaDeviceState。   
deviceError| MediaDeviceError| 是| 无| 设备错误类型，详见 MediaDeviceError。   
### onAudioDeviceWarning 
音频设备警告回调。音频设备包括音频采集设备、音频渲染设备等。
类型
```
((deviceID: string, deviceType: AudioDeviceType, deviceWarning: MediaDeviceWarning) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 设备 ID   
deviceType| AudioDeviceType| 是| 无| 参看 AudioDeviceType   
deviceWarning| MediaDeviceWarning| 是| 无| 参看 MediaDeviceWarning   
### onVideoDeviceWarning 
视频设备警告回调，包括视频采集等设备。
类型
```
((deviceID: string, deviceType: VideoDeviceType, deviceWarning: MediaDeviceWarning) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 设备 ID   
deviceType| VideoDeviceType| 是| 无| 参看 VideoDeviceType   
deviceWarning| MediaDeviceWarning| 是| 无| 参看 MediaDeviceWarning   
### onRecordingStateUpdate 
获取本地录制状态回调。 该回调由 startFileRecording 或 stopFileRecording 触发。
类型
```
((type: StreamIndex, state: RecordingState, errorCode: RecordingErrorCode, info: RecordingInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| StreamIndex| 是| 无| 录制流的流属性，参看 StreamIndex   
state| RecordingState| 是| 无| 录制状态，参看 RecordingState   
errorCode| RecordingErrorCode| 是| 无| 录制错误码，参看 RecordingErrorCode   
info| RecordingInfo| 是| 无| 录制文件的详细信息，参看 RecordingInfo   
### onRecordingProgressUpdate 
本地录制进度回调。 该回调由 startFileRecording 触发，录制状态正常时，系统每秒钟都会通过该回调提示录制进度。
类型
```
((type: StreamIndex, progress: RecordingProgress, info: RecordingInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
type| StreamIndex| 是| 无| 录制流的流属性，参看 StreamIndex   
progress| RecordingProgress| 是| 无| 录制进度，参看 RecordingProgress   
info| RecordingInfo| 是| 无| 录制文件的详细信息，参看 RecordingInfo   
### onAudioRecordingStateUpdate 
调用 startAudioRecording 或 stopAudioRecording 改变音频文件录制状态时，收到此回调。
类型
```
((state: AudioRecordingState, errorCode: AudioRecordingErrorCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| AudioRecordingState| 是| 无| 录制状态，参看 AudioRecordingState   
errorCode| AudioRecordingErrorCode| 是| 无| 录制错误码，参看 AudioRecordingErrorCode   
### onAudioMixingPlayingProgress 
混音音频文件播放进度回调
类型
```
((mixId: number, progress: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mixId| number| 是| 无| 混音 ID   
progress| number| 是| 无| 当前混音音频文件播放进度，单位毫秒   
### onLocalAudioPropertiesReport 
调用 enableAudioPropertiesReport 后，你会周期性地收到此回调，了解本地音频的相关信息。 本地音频包括使用 RTC SDK 内部机制采集的麦克风音频，屏幕音频和本地混音音频信息。
类型
```
((audioPropertiesInfos: Array<LocalAudioPropertiesInfo>) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioPropertiesInfos| LocalAudioPropertiesInfo[]| 是| 无| 本地音频信息，详见 LocalAudioPropertiesInfo 。   
### onRemoteAudioPropertiesReport 
远端用户进房后，本地调用 enableAudioPropertiesReport ，根据设置的 interval 值，本地会周期性地收到此回调，了解订阅的远端用户的音频信息。 远端用户的音频包括使用 RTC SDK 内部机制/自定义机制采集的麦克风音频和屏幕音频。
类型
```
((audioPropertiesInfos: Array<RemoteAudioPropertiesInfo>, totalRemoteVolume: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioPropertiesInfos| RemoteAudioPropertiesInfo[]| 是| 无| 远端音频信息，其中包含音频流属性、房间 ID、用户 ID ，详见 RemoteAudioPropertiesInfo。   
totalRemoteVolume| number| 是| 无| 订阅的所有远端流的总音量。   
### onActiveSpeaker 
调用 enableAudioPropertiesReport 后，根据设置的 AudioPropertiesConfig.interval，你会周期性地收到此回调，获取房间内的最活跃用户信息。
类型
```
((roomId: string, uid: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 房间 ID   
uid| string| 是| 无| 最活跃用户（ActiveSpeaker）的用户 ID   
### onStreamSyncInfoReceived 
音频流同步信息回调。可以通过此回调，在远端用户调用 sendStreamSyncInfo 发送音频流同步消息后，收到远端发送的音频流同步信息。
类型
```
((streamKey: RemoteStreamKey, streamType: SyncInfoStreamType, data: ByteBuffer) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息，详见 RemoteStreamKey 。   
streamType| SyncInfoStreamType| 是| 无| 媒体流类型，详见 SyncInfoStreamType 。   
data| ArrayBuffer| 是| 无| 消息内容。   
### onPushPublicStreamResult 
公共流发布结果回调。 调用 startPushPublicStream 接口发布公共流后，启动结果通过此回调方法通知用户。
类型
```
((roomId: string, publicStreamId: string, error: PublicStreamErrorCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 发布公共流的房间 ID   
publicStreamId| string| 是| 无| 公共流 ID   
error| PublicStreamErrorCode| 是| 无| 公共流发布结果状态码。详见 PublicStreamErrorCode。   
### onPlayPublicStreamResult 
订阅公共流的结果回调 调用 startPlayPublicStream 接口启动拉公共流功能后，通过此回调收到启动结果和拉流过程中的错误。
类型
```
((publicStreamId: string, error: PublicStreamErrorCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID。   
error| PublicStreamErrorCode| 是| 无| 公共流订阅结果状态码。详见 PublicStreamErrorCode。   
### onPublicStreamSEIMessageReceived 
回调公共流中包含的 SEI 信息。 调用 startPlayPublicStream 接口启动拉公共流功能后，通过此回调收到公共流中的 SEI 消息。
类型
```
((publicStreamId: string, message: ByteBuffer, sourceType: DataMessageSourceType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID。   
message| ArrayBuffer| 是| 无| 收到的 SEI 消息内容。通过调用客户端 sendSEIMessage 插入的 SEI 信息。当公共流中的多路视频流均包含有 SEI 信息：SEI 不互相冲突时，将通过多次回调分别发送；SEI 在同一帧有冲突时，则只有一条流中的 SEI 信息被透传并融合到公共流中。   
sourceType| DataMessageSourceType| 是| 无| SEI 消息类型，自 3.52.1 版本后固定为 0，自定义消息。参看 DataMessageSourceType。   
### onPublicStreamDataMessageReceived 
回调公共流中包含的数据信息。 调用 startPlayPublicStream 订阅公共流后，通过监听本回调获取公共流中的数据消息，包括调用 Open API 发送的 SEI 消息和音量回调。
类型
```
((publicStreamId: string, message: ByteBuffer, sourceType: DataMessageSourceType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID。   
message| ArrayBuffer| 是| 无| 收到的数据消息内容，如下： 调用公共流 OpenAPI 发送的自定义消息。 媒体流音量变化，需要通过公共流 OpenAPI 开启回调。JSON 格式说明如下： { "Type" : "VolumeIndication", //具体业务类型 "VolumeInfos"[ // 业务类型对应信息 { "RoomId":"1000001", // 房间 ID "UserId":"1000001", // 用户 ID "StreamType":0, // 0:摄像头流；1:屏幕流 "LinearVolume":1 // 线性音量大小 } ] }   
sourceType| DataMessageSourceType| 是| 无| 数据消息来源，参看 DataMessageSourceType。   
### onFirstPublicStreamVideoFrameDecoded 
公共流的首帧视频解码成功 关于订阅公共流，详见 startPlayPublicStream。
类型
```
((publicStreamId: string, videoFrameInfo: VideoFrameInfo) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
videoFrameInfo| VideoFrameInfo| 是| 无| 视频帧信息，参看 VideoFrameInfo   
### onFirstPublicStreamAudioFrame 
公共流的音频首帧解码成功 关于订阅公共流，详见 startPlayPublicStream。
类型
```
((publicStreamId: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
publicStreamId| string| 是| 无| 公共流 ID   
### onEchoTestResult 
关于音视频回路测试结果的回调。
类型
```
((result: EchoTestResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
result| EchoTestResult| 是| 无| 测试结果，参看 EchoTestResult   
### onCloudProxyConnected 
调用 startCloudProxy 开启云代理，SDK 首次成功连接云代理服务器时，回调此事件。
类型
```
((interval: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
interval| number| 是| 无| 从开启云代理到连接成功经过的时间，单位为 ms   
### onHardwareEchoDetectionResult 
通话前回声检测结果回调。
类型
```
((hardwareEchoDetectionResult: HardwareEchoDetectionResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
hardwareEchoDetectionResult| HardwareEchoDetectionResult| 是| 无| 参见 HardwareEchoDetectionResult   
### onLocalProxyStateChanged 
本地代理状态发生改变回调。调用 setLocalProxy 设置本地代理后，SDK 会触发此回调，通知代理连接的状态。
类型
```
((localProxyType: LocalProxyType, localProxyState: LocalProxyState, localProxyError: LocalProxyError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
localProxyType| LocalProxyType| 是| 无| 本地代理类型。参看 LocalProxyType 。   
localProxyState| LocalProxyState| 是| 无| 本地代理状态。参看 LocalProxyState。   
localProxyError| LocalProxyError| 是| 无| 本地代理错误。参看 LocalProxyError。   
### onUserMuteAudio 
房间内用户暂停/恢复发送音频流时，房间内其他用户会收到此回调。
已弃用
since 3.36 and will be deleted in 3.51, use onUserPublishStream, onUserPublishScreen, onUserUnpublishStream and onUserUnpublishScreen instead.
类型
```
((roomId: string, uid: string, muteState: MuteState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 房间 ID   
uid| string| 是| 无| 改变本地音频发送状态的用户 ID   
muteState| MuteState| 是| 无| 发送状态，参看 MuteState   
### onUserMuteVideo 
房间内用户暂停/恢复发送视频流时，房间内其他用户会收到此回调。
已弃用
since 3.36 and will be deleted in 3.51, use onUserPublishStream, onUserPublishScreen, onUserUnpublishStream and onUserUnpublishScreen instead.
类型
```
((roomId: string, uid: string, muteState: MuteState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 房间 ID   
uid| string| 是| 无| 暂停/恢复发送视频流的用户 ID。   
muteState| MuteState| 是| 无| 视频流的发送状态。参看 MuteState。   
### onAudioPlaybackDeviceChanged 
音频播放设备变化时，收到该回调。
已弃用
since 3.38 and will be deleted in 3.51, use onAudioRouteChanged instead.
类型
```
((device: AudioPlaybackDevice) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
device| AudioPlaybackDevice| 是| 无| 新的音频设备，详见 AudioPlaybackDevice   
### onMediaDeviceStateChanged 
媒体设备状态回调。提示音频采集、音频播放、摄像头视频采集、屏幕视频采集四种媒体设备的状态。
已弃用
since 3.37 and will be deleted in 3.51, using onAudioDeviceStateChanged and onVideoDeviceStateChanged instead.
类型
```
((deviceID: string, deviceType: MediaDeviceType, deviceState: MediaDeviceState, deviceError: MediaDeviceError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 设备 ID   
deviceType| MediaDeviceType| 是| 无| 设备类型，详见 MediaDeviceType。   
deviceState| MediaDeviceState| 是| 无| 设备状态，详见 MediaDeviceState。   
deviceError| MediaDeviceError| 是| 无| 设备错误类型，详见 MediaDeviceError。   
### onMediaDeviceWarning 
媒体设备警告回调。媒体设备包括：音频采集设备、音频渲染设备、和视频采集设备。
已弃用
since 3.37 and will be deleted in 3.51, using onAudioDeviceWarning and onVideoDeviceWarning instead.
类型
```
((deviceID: string, deviceType: MediaDeviceType, deviceWarning: MediaDeviceWarning) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
deviceID| string| 是| 无| 设备 ID   
deviceType| MediaDeviceType| 是| 无| 参看 MediaDeviceType   
deviceWarning| MediaDeviceWarning| 是| 无| 参看 MediaDeviceWarning   
### onHttpProxyState 
HTTP 代理连接状态改变时，收到该回调。
已弃用
since 3.52, will be deleted at 3.57, use onLocalProxyStateChanged instead
类型
```
((state: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| number| 是| 无| 当前 HTTP 代理连接状态   
### onHttpsProxyState 
HTTPS 代理连接状态改变时，收到该回调。
已弃用
since 3.52, will be deleted at 3.57, use onLocalProxyStateChanged instead
类型
```
((state: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| number| 是| 无| 当前 HTTPS 代理连接状态   
### onSocks5ProxyState 
SOCKS5 代理状态改变时，收到该回调。
已弃用
since 3.52, will be deleted at 3.57, use onLocalProxyStateChanged instead
类型
```
((state: number, cmd: string, proxyAddress: string, localAddress: string, remoteAddress: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| number| 是| 无| SOCKS5 代理连接状态   
cmd| string| 是| 无| 代理连接的每一步操作命令   
proxyAddress| string| 是| 无| 代理地址信息   
localAddress| string| 是| 无| 当前连接使用的本地地址   
remoteAddress| string| 是| 无| 远端的连接地址   
### onAudioMixingStateChanged 
音频混音文件播放状态改变时回调
已弃用
since 353. Use and instead.
类型
```
((mixId: number, state: AudioMixingState, error: AudioMixingError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mixId| number| 是| 无| 混音 ID使用 IAudioMixingManager 相关接口时传入的唯一 ID。   
state| AudioMixingState| 是| 无| 混音状态其混音状态可参考。   
error| AudioMixingError| 是| 无| 错误码详见 AudioMixingError。   
## IAudioFrameObserver 
类型：interface
音频数据回调观察者 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。 本接口类中的回调周期均为 20 ms。
### onRecordAudioFrame 
返回麦克风录制的音频数据
类型
```
((audioFrame: IAudioFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioFrame| IAudioFrame| 是| 无| 音频数据, 参看 IAudioFrame。   
### onPlaybackAudioFrame 
返回订阅的所有远端用户混音后的音频数据。
类型
```
((audioFrame: IAudioFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioFrame| IAudioFrame| 是| 无| 音频数据, 参看 IAudioFrame。   
### onRemoteUserAudioFrame 
返回远端单个用户的音频数据
类型
```
((streamKey: RemoteStreamKey, audioFrame: IAudioFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 远端流信息，参看 RemoteStreamKey。   
audioFrame| IAudioFrame| 是| 无| 音频数据，参看 IAudioFrame。   
### onMixedAudioFrame 
返回本地麦克风录制和订阅的所有远端用户混音后的音频数据
类型
```
((audioFrame: IAudioFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
audioFrame| IAudioFrame| 是| 无| 音频数据, 参看 IAudioFrame。   
## IAudioFrameProcessor 
类型：interface
自定义音频处理器。 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
## ILocalEncodedVideoFrameObserver 
类型：interface
本地视频帧监测器 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onLocalEncodedVideoFrame 
调用 registerLocalEncodedVideoFrameObserver 后，SDK 每次使用内部采集，采集到一帧视频帧，或收到一帧外部视频帧时，都会回调该事件。
类型
```
((streamIndex: StreamIndex, encodedVideoFrame: RTCEncodedVideoFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 本地视频帧类型，参看 StreamIndex   
encodedVideoFrame| RTCEncodedVideoFrame| 是| 无| 本地视频帧信息，参看 RTCEncodedVideoFrame   
## IRemoteEncodedVideoFrameObserver 
类型：interface
远端编码后视频数据监测器 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onRemoteEncodedVideoFrame 
调用 registerRemoteEncodedVideoFrameObserver 后，SDK 监测到远端编码后视频数据时，触发该回调
类型
```
((streamKey: RemoteStreamKey, encodedVideoFrame: RTCEncodedVideoFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamKey| RemoteStreamKey| 是| 无| 收到的远端流信息，参看 RemoteStreamKey   
encodedVideoFrame| RTCEncodedVideoFrame| 是| 无| 收到的远端视频帧信息，参看 RTCEncodedVideoFrame   
## IMixedStreamObserver 
类型：interface
转推直播观察者。
### onMixingEvent 
转推直播状态回调
类型
```
((eventType: ByteRTCStreamMixingEvent, taskId: string, error: RTCStreamMixingErrorCode, mixType: MixedStreamType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
eventType| ByteRTCStreamMixingEvent| 是| 无| 转推直播任务状态, 参看 ByteRTCStreamMixingEvent   
taskId| string| 是| 无| 转推直播任务 ID   
error| RTCStreamMixingErrorCode| 是| 无| 转推直播错误码，参看 ByteRTCTranscoderErrorCode   
mixType| MixedStreamType| 是| 无| 转推直播类型，参看 MixedStreamType   
## IPushSingleStreamToCDNObserver 
类型：interface
单流转推直播观察者。 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onStreamPushEvent 
单流转推直播状态回调
类型
```
((eventType: RTCStreamSinglePushEvent, taskId: string, error: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
eventType| RTCStreamSinglePushEvent| 是| 无| 任务状态, 参看 ByteRTCStreamSinglePushEvent   
taskId| string| 是| 无| 任务 ID   
error| number| 是| 无| 错误码，参看 ByteRTCTranscoderErrorCode   
## RTCRoomEventHandler 
类型：interface
房间事件回调接口 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onLeaveRoom 
离开房间成功回调。 用户调用 leaveRoom 方法后，SDK 会停止所有的发布订阅流，并在释放所有通话相关的音视频资源后，通过此回调通知用户离开房间成功。
类型
```
((stats: RTCRoomStats) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stats| RTCRoomStats| 是| 无| 保留参数，目前为空。   
### onRoomStateChanged 
房间状态改变回调，加入房间、异常退出房间、发生房间相关的警告或错误时会收到此回调。
类型
```
((roomId: string, uid: string, state: number, extraInfo: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 房间 ID。   
uid| string| 是| 无| 用户 ID。   
state| number| 是| 无| 房间状态码。 0: 加入房间成功。 !0: 加入房间失败、异常退房、发生房间相关的警告或错误。具体原因参看 ErrorCode 及 WarningCode。   
extraInfo| string| 是| 无| 额外信息，如 {"elapsed":1187,"join_type":0}。join_type 表示加入房间的类型，0为首次进房，1为重连进房。elapsed 表示加入房间耗时，即本地用户从调用 joinRoom 到加入房间成功所经历的时间间隔，单位为 ms。   
### onStreamStateChanged 
流状态改变回调，发生流相关的警告或错误时会收到此回调。
类型
```
((roomId: string, uid: string, state: number, extraInfo: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
roomId| string| 是| 无| 房间 ID。   
uid| string| 是| 无| 用户 ID。   
state| number| 是| 无| 流状态码，参看 ErrorCode 及 WarningCode。   
extraInfo| string| 是| 无| 附加信息，目前为空。   
### onAVSyncStateChange 
发布端调用 setMultiDeviceAVSync 后音视频同步状态发生改变时，会收到此回调。
类型
```
((state: AVSyncState) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| AVSyncState| 是| 无| 音视频同步状态，参看 AVSyncState。   
### onRoomStats 
房间内通话统计信息回调。 用户进房开始通话后，每 2s 收到一次本回调。
类型
```
((stats: RTCRoomStats) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stats| RTCRoomStats| 是| 无| 房间内的汇总统计数据。详见 RTCRoomStats。   
### onUserJoined 
远端可见用户加入房间，或房内不可见用户切换为可见的回调。 1.远端用户调用 setUserVisibility 方法将自身设为可见后加入房间时，房间内其他用户将收到该事件。 2.远端可见用户断网后重新连入房间时，房间内其他用户将收到该事件。 3.房间内隐身远端用户调用 setUserVisibility 方法切换至可见时，房间内其他用户将收到该事件。 4.新进房用户也会收到进房前已在房内的可见用户的进房回调通知。
类型
```
((userInfo: UserInfo, elapsed: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
userInfo| UserInfo| 是| 无| 用户信息。参看 UserInfo。   
elapsed| number| 是| 无| 保留字段，无意义。   
### onUserLeave 
远端用户离开房间，或切至不可见时，房间内其他用户会收到此事件
类型
```
((uid: string, reason: RTCUserOfflineReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 离开房间，或切至不可见的的远端用户 ID。   
reason| RTCUserOfflineReason| 是| 无| 用户离开房间的原因： 0: 远端用户调用 leaveRoom 主动退出房间。 - 1: 远端用户因 Token 过期或网络原因等掉线。详细信息请参看[连接状态提示](https://www.volcengine.com/docs/6348/95376) 2: 远端用户调用 setUserVisibility 切换至不可见状态。 3: 服务端调用 OpenAPI 将该远端用户踢出房间。   
### onTokenWillExpire 
Token 进房权限过期前 30 秒将触发该回调。 收到该回调后，你需调用 updateToken 更新 Token 进房权限。
类型
```
(() => void) | undefined

ts

```

### onPublishPrivilegeTokenWillExpire 
Token 发布权限过期前 30 秒将触发该回调。 收到该回调后，你需调用 updateToken 更新 Token 发布权限。
类型
```
(() => void) | undefined

ts

```

### onSubscribePrivilegeTokenWillExpire 
Token 订阅权限过期前 30 秒将触发该回调。 收到该回调后，你需调用 updateToken 更新 Token 订阅权限有效期。
类型
```
(() => void) | undefined

ts

```

### onUserPublishStream 
房间内新增远端摄像头/麦克风采集的媒体流的回调。
类型
```
((uid: string, type: MediaStreamType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 远端流发布用户的用户 ID。   
type| MediaStreamType| 是| 无| 远端媒体流的类型，参看 MediaStreamType。   
### onUserUnpublishStream 
房间内远端摄像头/麦克风采集的媒体流移除的回调。
类型
```
((uid: string, type: MediaStreamType, reason: StreamRemoveReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 移除的远端流发布用户的用户 ID。   
type| MediaStreamType| 是| 无| 移除的远端流类型，参看 MediaStreamType。   
reason| StreamRemoveReason| 是| 无| 远端流移除的原因，参看 StreamRemoveReason。   
### onUserPublishScreen 
房间内新增远端屏幕共享音视频流的回调。
类型
```
((uid: string, type: MediaStreamType) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 远端流发布用户的用户 ID。   
type| MediaStreamType| 是| 无| 远端媒体流的类型，参看 MediaStreamType。   
### onUserUnpublishScreen 
房间内远端屏幕共享音视频流移除的回调。
类型
```
((uid: string, type: MediaStreamType, reason: StreamRemoveReason) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 移除的远端流发布用户的用户 ID。   
type| MediaStreamType| 是| 无| 移除的远端流类型，参看 MediaStreamType。   
reason| StreamRemoveReason| 是| 无| 远端流移除的原因，参看 StreamRemoveReason。   
### onLocalStreamStats 
本地流数据统计以及网络质量回调。 本地用户发布流成功后，SDK 会周期性（2s）的通过此回调事件通知用户发布的流在此次统计周期内的质量统计信息。 统计信息通过 类型的回调参数传递给用户，其中包括发送音视频比特率、发送帧率、编码帧率，网络质量等。
类型
```
((stats: LocalStreamStats) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stats| LocalStreamStats| 是| 无| 音视频流以及网络状况统计信息。参见 LocalStreamStats。   
### onRemoteStreamStats 
本地订阅的远端音/视频流数据统计以及网络质量回调。 本地用户订阅流成功后，SDK 会周期性（2s）的通过此回调事件通知用户订阅的流在此次统计周期内的质量统计信息，包括：发送音视频比特率、发送帧率、编码帧率，网络质量等。
类型
```
((stats: RemoteStreamStats) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stats| RemoteStreamStats| 是| 无| 音视频流以及网络状况统计信息。参见 RemoteStreamStats。   
### onStreamSubscribed 
关于订阅媒体流状态改变的回调
类型
```
((stateCode: SubscribeState, userId: string, info: SubscribeConfig) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stateCode| SubscribeState| 是| 无| 订阅媒体流状态，参看 SubscribeState   
userId| string| 是| 无| 流发布用户的用户 ID   
info| SubscribeConfig| 是| 无| 流的属性，参看 SubscribeConfig   
### onRoomMessageReceived 
接收到房间内广播消息的回调。 房间内其他用户调用 sendRoomMessage 发送广播消息时，收到此回调。
类型
```
((uid: string, message: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息发送者 ID   
message| string| 是| 无| 收到的消息内容   
### onRoomBinaryMessageReceived 
收到房间内广播二进制消息的回调。 房间内其他用户调用 sendRoomBinaryMessage 发送广播二进制消息时，收到此回调。
类型
```
((uid: string, message: ByteBuffer) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息发送者 ID   
message| ArrayBuffer| 是| 无| 收到的二进制消息内容   
### onUserMessageReceived 
收到来自房间中其他用户通过 sendUserMessage 发来的点对点文本消息时，会收到此回调。
类型
```
((uid: string, message: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息发送者的用户 ID 。   
message| string| 是| 无| 收到的文本消息内容。   
### onUserBinaryMessageReceived 
收到来自房间中其他用户通过 sendUserBinaryMessage 发来的点对点二进制消息时，会收到此回调。
类型
```
((uid: string, message: ByteBuffer) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 消息发送者的用户 ID 。   
message| ArrayBuffer| 是| 无| 收到的二进制消息内容。   
### onUserMessageSendResult 
向房间内单个用户发送文本或二进制消息后（P2P），消息发送方会收到该消息发送结果回调。
类型
```
((msgid: number, error: UserMessageSendResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
msgid| number| 是| 无| 本条消息的 ID。   
error| UserMessageSendResult| 是| 无| 文本或二进制消息发送结果，详见 UserMessageSendResult   
### onRoomMessageSendResult 
调用 sendRoomMessage 或 sendRoomBinaryMessage 向房间内群发文本或二进制消息后，消息发送方会收到该消息发送结果回调。
类型
```
((msgid: number, error: RoomMessageSendResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
msgid| number| 是| 无| 本条消息的 ID。   
error| RoomMessageSendResult| 是| 无| 消息发送结果，详见 RoomMessageSendResult   
### onVideoStreamBanned 
通过调用服务端 BanUserStream/UnbanUserStream 方法禁用/解禁指定房间内指定用户视频流的发送时，触发此回调。
类型
```
((uid: string, banned: boolean) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 被禁用/解禁的视频流用户 ID   
banned| boolean|好的，这是从您上次中断的位置（`onVideoStreamBanned` 回调之后）开始的、完整的火山引擎RTC技术文档内容。

---

### onAudioStreamBanned 
通过调用服务端 BanUserStream/UnbanUserStream 方法禁用/解禁指定房间内指定用户音频流的发送时，触发此回调。
类型
```
((uid: string, banned: boolean) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
uid| string| 是| 无| 被禁用/解禁的音频流用户 ID   
banned| boolean| 是| 无| 音频流发送状态 true: 音频流发送被禁用 false: 音频流发送被解禁   
### onForwardStreamStateChanged 
跨房间媒体流转发状态和错误回调
类型
```
((stateInfos: Array<ForwardStreamStateInfo>) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stateInfos| ForwardStreamStateInfo[]| 是| 无| 跨房间媒体流转发目标房间信息数组，详见 ForwardStreamStateInfo   
### onForwardStreamEvent 
跨房间媒体流转发事件回调
类型
```
((eventInfos: Array<ForwardStreamEventInfo>) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
eventInfos| ForwardStreamEventInfo[]| 是| 无| 跨房间媒体流转发目标房间事件数组，详见 ForwardStreamEventInfo   
### onNetworkQuality 
加入房间并发布或订阅流后， 以每 2 秒一次的频率，报告本地用户和已订阅的远端用户的上下行网络质量信息。
类型
```
((localQuality: NetworkQualityStats, remoteQualities: Array<NetworkQualityStats>) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
localQuality| NetworkQualityStats| 是| 无| 本地网络质量，详见 NetworkQualityStats。   
remoteQualities| NetworkQualityStats[]| 是| 无| 已订阅用户的网络质量，详见 NetworkQualityStats。   
### onSetRoomExtraInfoResult 
调用 setRoomExtraInfo 设置房间附加信息结果的回调。
类型
```
((taskId: number, error: SetRoomExtraInfoResult) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
taskId| number| 是| 无| 调用 setRoomExtraInfo 的任务编号。   
error| SetRoomExtraInfoResult| 是| 无| 设置房间附加信息的结果，详见 SetRoomExtraInfoResult   
### onRoomExtraInfoUpdate 
接收同一房间内，其他用户调用 setRoomExtraInfo 设置的房间附加信息的回调。
类型
```
((key: string, value: string, lastUpdateUserId: string, lastUpdateTimeMs: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
key| string| 是| 无| 房间附加信息的键值   
value| string| 是| 无| 房间附加信息的内容   
lastUpdateUserId| string| 是| 无| 最后更新本条信息的用户 ID。   
lastUpdateTimeMs| number| 是| 无| 最后更新本条信息的 Unix 时间，单位：毫秒。   
### onUserVisibilityChanged 
用户调用 setUserVisibility 设置用户可见性的回调。
类型
```
((currentUserVisibility: boolean, errorCode: UserVisibilityChangeError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
currentUserVisibility| boolean| 是| 无| 当前用户的可见性。 true: 可见，用户可以在房间内发布音视频流，房间中的其他用户将收到用户的行为通知，例如进房、开启视频采集和退房。 false: 不可见，用户不可以在房间内发布音视频流，房间中的其他用户不会收到用户的行为通知，例如进房、开启视频采集和退房。   
errorCode| UserVisibilityChangeError| 是| 无| 设置用户可见性错误码，参看 UserVisibilityChangeError。   
### onSubtitleStateChanged 
字幕状态发生改变回调。 当用户调用 startSubtitle 和 stopSubtitle 使字幕状态发生改变或字幕任务出现错误时，触发该回调。
类型
```
((state: SubtitleState, errorCode: SubtitleErrorCode, errorMessage: string) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
state| SubtitleState| 是| 无| 字幕状态。参看 SubtitleState。   
errorCode| SubtitleErrorCode| 是| 无| 字幕任务错误码。参看 SubtitleErrorCode。   
errorMessage| string| 是| 无| 与第三方服务有关的错误信息。   
### onSubtitleMessageReceived 
字幕相关内容回调。 当用户成功调用 startSubtitle 后会收到此回调，通知字幕的相关信息。
类型
```
((subtitles: Array<SubtitleMessage>) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
subtitles| SubtitleMessage[]| 是| 无| 字幕消息内容。参看 SubtitleMessage。   
### onRoomWarning 
发生警告回调。
已弃用
since 3.41 and will be deleted in 3.51, use onRoomStateChanged and onStreamStateChanged instead.
类型
```
((warn: WarningCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
warn| WarningCode| 是| 无| 警告代码，参见 WarningCode   
### onRoomError 
发生错误回调。
已弃用
since 3.41 and will be deleted in 3.51, use onRoomStateChanged and onStreamStateChanged instead.
类型
```
((err: ErrorCode) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
err| ErrorCode| 是| 无| 错误代码，详情定义见: ErrorCode   
### onStreamAdd 
房间内新流发布回调。 房间内的远端用户发布新的音视频流时，本地用户会收到此回调通知。
已弃用
since 3.36 and will be deleted in 3.51, use onUserPublishStream and onUserPublishScreen instead.
类型
```
((stream: RTCStream) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
stream| RTCStream| 是| 无| 流的属性，详见 RTCStream 数据类型。   
## IAudioEffectPlayerEventHandler 
类型：interface
对应的回调句柄。你必须调用 setEventHandler 完成设置后，才能收到对应回调。
### onAudioEffectPlayerStateChanged 
播放状态改变时回调。
类型
```
((effectId: number, state: PlayerState, error: PlayerError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
effectId| number| 是| 无| IAudioEffectPlayer 的 ID。通过 getAudioEffectPlayer 设置。   
state| PlayerState| 是| 无| 混音状态。参考 PlayerState。   
error| PlayerError| 是| 无| 错误码。参考 PlayerError。   
## IMediaPlayerAudioFrameObserver 
类型：interface
本地音频文件混音的音频帧观察者。
### onFrame 
当本地音频文件混音时，回调播放的音频帧。
类型
```
((playerId: number, frame: IAudioFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerId| number| 是| 无| -  
frame| IAudioFrame| 是| 无| 参看 IAudioFrame。   
## IMediaPlayerEventHandler 
类型：interface
对应的回调句柄。你必须调用 setEventHandler 完成设置后，才能收到对应回调。
### onMediaPlayerStateChanged 
播放状态改变时回调。
类型
```
((playerId: number, state: PlayerState, error: PlayerError) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerId| number| 是| 无| IMediaPlayer 的 ID。通过 getMediaPlayer 设置。   
state| PlayerState| 是| 无| 混音状态。参考 PlayerState。   
error| PlayerError| 是| 无| 错误码。参考 PlayerError。   
### onMediaPlayerPlayingProgress 
播放进度周期性回调。回调周期通过 setProgressInterval 设置。
类型
```
((playerId: number, progress: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
playerId| number| 是| 无| IMediaPlayer 的 ID。通过 getMediaPlayer 设置。   
progress| number| 是| 无| 进度。单位 ms。   
## IExternalVideoEncoderEventHandler 
类型：interface
自定义编码帧回调类 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onStart 
提示自定义编码帧可以开始推送的回调。 收到该回调后，你即可调用 pushExternalEncodedVideoFrame 向 SDK 推送自定义编码视频帧
类型
```
((index: StreamIndex) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 可以推送的编码流的属性，参看 StreamIndex   
### onStop 
当收到该回调时，你需停止向 SDK 推送自定义编码视频帧
类型
```
((index: StreamIndex) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
index| StreamIndex| 是| 无| 需停止推送的编码流的属性，参看 StreamIndex   
### onRateUpdate 
当自定义编码流的帧率或码率发生变化时，触发该回调
类型
```
((streamIndex: StreamIndex, videoIndex: number, fps: number, bitrateKbps: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 发生变化的编码流的属性，参看 StreamIndex   
videoIndex| number| 是| 无| 对应编码流的下标   
fps| number| 是| 无| 变化后的帧率，单位：fps   
bitrateKbps| number| 是| 无| 变化后的码率，单位：kbps   
### onRequestKeyFrame 
提示流发布端需重新生成关键帧的回调
类型
```
((streamIndex: StreamIndex, videoIndex: number) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 远端编码流的属性，参看 StreamIndex   
videoIndex| number| 是| 无| 对应编码流的下标   
### onActiveVideoLayer 
作为自定义编码视频流的发送端，你会在视频流可发送状态发生变化时，收到此回调。 你可以根据此回调的提示，仅对可发送的视频流进行编码，以降低本端视频编码性能消耗。此回调会根据多个因素综合判断触发，包括：本端设备性能和本端网络性能，以及按需订阅场景下，远端用户是否订阅。
类型
```
((streamIndex: StreamIndex, videoIndex: number, active: boolean) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
streamIndex| StreamIndex| 是| 无| 远端编码流的属性，参看 StreamIndex   
videoIndex| number| 是| 无| 对应编码流的下标   
active| boolean| 是| 无| 该路流可发送状态   
## IAudioFileFrameObserver 
类型：interface
本地音频文件混音的音频帧观察者。 注意：回调函数是在 SDK 内部线程（非 UI 线程）同步抛出来的，请不要做耗时操作或直接操作 UI，否则可能导致 app 崩溃。
### onAudioFileFrame 
当本地音频文件混音时，回调播放的音频帧。
类型
```
((mixID: number, audioFrame: IAudioFrame) => void) | undefined

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
mixID| number| 是| 无| 混音 ID。   
audioFrame| IAudioFrame| 是| 无| 参看 IAudioFrame。   
## IMediaPlayerCustomSourceProvider 
类型：interface
内存播放数据源回调

---
## 错误码--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1390577

---
## 类型详情--实时音视频-火山引擎

Source: https://www.volcengine.com/docs/6348/1390578

/实时音视频/客户端 API 参考/React Native 3.58/类型详情

类型详情
最近更新时间：2025.04.29 20:34:58首次发布时间：2025.02.26 11:44:49

## StreamIndex 
类型：enum
流属性
属性| 值| 描述  
---|---|---  
STREAM_INDEX_MAIN| 0| 主流。包括：
  * 由摄像头/麦克风通过内部采集机制，采集到的视频/音频;
  * 通过自定义采集，采集到的视频/音频。

  
STREAM_INDEX_SCREEN| 1| 屏幕流。屏幕共享时共享的视频流，或来自声卡的本地播放音频流。  
## IVideoCanvas 
类型：interface
视图信息和渲染模式
### viewId 
类型：string
组件 View id
### renderMode 
类型：RenderMode | undefined
渲染模式。
## RenderMode 
类型：enum
图片或视频流的缩放模式。
属性| 值| 描述  
---|---|---  
ByteRTCRenderModeHidden| 0| 视窗填满优先，默认值。视频尺寸等比缩放，直至视窗被填满。当视频尺寸与显示窗口尺寸不一致时，多出的视频将被截掉。  
ByteRTCRenderModeFit| 1| 视频帧内容全部显示优先。视频尺寸等比缩放，优先保证视频内容全部显示。当视频尺寸与显示窗口尺寸不一致时，会把窗口未被填满的区域填充成背景颜色。  
ByteRTCRenderModeFill| 2| 视频帧自适应画布。视频尺寸非等比例缩放，把窗口充满。在此过程中，视频帧的长宽比例可能会发生变化。  
## IRemoteInfo 
类型：interface
远端用户信息。
### userId 
类型：string
用户 ID。
### streamIndex 
类型：StreamIndex
流属性。
### roomId 
类型：string
房间 ID。
## ReturnStatus 
类型：enum
方法调用结果。
属性| 值| 描述  
---|---|---  
RETURN_STATUS_SUCCESS| 0| 成功。  
RETURN_STATUS_FAILURE| 1| 失败。  
RETURN_STATUS_PARAMETER_ERR| 2| 参数错误。  
RETURN_STATUS_WRONG_STATE| 3| 接口状态错误。  
RETURN_STATUS_HAS_IN_ROOM| 4| 失败，用户已在房间内。  
RETURN_STATUS_HAS_IN_LOGIN| 5| 失败，用户已登录。  
RETURN_STATUS_HAS_IN_ECHO_TEST| 6| 失败，用户已经在进行通话回路测试中。  
RETURN_STATUS_NEITHER_VIDEO_NOR_AUDIO| 7| 失败，音视频均未采集。  
RETURN_STATUS_ROOMID_IN_USE| 8| 失败，该 roomId 已被使用。  
RETURN_STATUS_SCREEN_NOT_SUPPORT| 9| 失败，屏幕流不支持。  
RETURN_STATUS_NOT_SUPPORT| 10| 失败，不支持该操作。  
RETURN_STATUS_RESOURCE_OVERFLOW| 11| 失败，资源已占用。  
RETURN_STATUS_AUDIO_NO_FRAME| 12| 失败，没有音频帧。  
RETURN_STATUS_AUDIO_NOT_IMPLEMENTED| 13| 失败，未实现。  
RETURN_STATUS_AUDIO_NO_PERMISSION| 14| 失败，采集设备无麦克风权限，尝试初始化设备失败。  
RETURN_STATUS_AUDIO_DEVICE_NOT_EXISTS| 15| 失败，设备不存在。当前没有设备或设备被移除时返回该值。  
RETURN_STATUS_AUDIO_DEVICE_FORMAT_NOT_SUPPORT| 16| 失败，设备音频格式不支持。  
RETURN_STATUS_AUDIO_DEVICE_NO_DEVICE| 17| 失败，系统无可用设备。  
RETURN_STATUS_AUDIO_DEVICE_CAN_NOT_USE| 18| 失败，当前设备不可用，需更换设备。  
RETURN_STATUS_AUDIO_DEVICE_INIT_FAILED| 19| 系统错误，设备初始化失败。  
RETURN_STATUS_AUDIO_DEVICE_START_FAILED| 20| 系统错误，设备开启失败。  
RETURN_STATUS_NATIVE_IN_VALID| 21| 失败。底层未初始化，engine 无效。  
RETURN_STATUS_VIDEO_NOT_SUPPORT| 22| 失败，不支持视频接口调用。  
RETURN_STATUS_VIDEO_TIMESTAMP_WARNING| 23| 警告。推送视频帧到 RTC SDK 时，相邻视频帧的时间戳差异应当和推帧操作的间隔相同。如果不同，会收到此警告。  
## IJoinRoomProps 
类型：interface
加房配置。
### token 
类型：string | undefined
动态密钥。用于对进房用户进行鉴权验证。 进入房间需要携带 Token。测试时可使用控制台生成临时 Token，正式上线需要使用密钥 SDK 在你的服务端生成并下发 Token。Token 有效期及生成方式参看。 使用不同 AppID 的 App 是不能互通的。 请务必保证生成 Token 使用的 AppID 和创建引擎时使用的 AppID 相同，否则会导致加入房间失败。
### userId 
类型：string
用户 ID。该字符串符合正则表达式：[a-zA-Z0-9_@\\-\\.]{1,128}。 你需要自行设置或管理 uid，并保证同一房间内每个 uid 的唯一性。
### extras 
类型：Record<string, any> | undefined
用户的额外信息，最大长度为 200 字节。会在 onUserJoined 中回调给远端用户。
### roomConfigs 
类型：{ profile: ChannelProfile; isAutoPublish: boolean; isAutoSubscribeAudio: boolean; isAutoSubscribeVideo: boolean; } | undefined
房间参数配置，设置房间模式以及是否自动发布或订阅流
成员
名称| 类型| 描述  
---|---|---  
profile| ChannelProfile| 房间模式，参看 ChannelProfile，默认为 CHANNEL_PROFILE_COMMUNICATION，进房后不可更改。  
isAutoPublish| boolean| 是否自动发布音视频流，默认为自动发布。创建和加入多房间时，只能将其中一个房间设置为自动发布。若每个房间均不做设置，则默认在第一个加入的房间内自动发布流。若调用 setUserVisibility 将自身可见性设为 false，无论是默认的自动发布流还是手动设置的自动发布流都不会进行发布，你需要将自身可见性设为 true 后方可发布。  
isAutoSubscribeAudio| boolean| 是否自动订阅音频流，默认为自动订阅, 包含主流和屏幕流。  
isAutoSubscribeVideo| boolean| 是否自动订阅视频流，默认为自动订阅, 包含主流和屏幕流。  
## ChannelProfile 
类型：enum
房间模式 根据所需场景，选择合适的房间模式，应用不同的音视频算法、视频参数和网络配置 调用 setAudioProfile 改变音频参数配置
属性| 值| 描述  
---|---|---  
CHANNEL_PROFILE_COMMUNICATION| 0| 普通音频通话，默认模式与 CHANNEL_PROFIEL_MEETING(16) 配置相同。你可以联系技术支持更换默认配置参数。  
CHANNEL_PROFILE_GAME| 1| 游戏语音模式，低功耗、低流量消耗。低端机在此模式下运行时，进行了额外的性能优化： - 部分低端机型配置编码帧长 40/60 - 部分低端机型关闭软件 3A 音频处理增强对 iOS 其他屏幕录制进行的兼容性，避免音频录制被 RTC 打断。  
CHANNEL_PROFILE_CLOUD_GAME| 2| 云游戏模式。如果你的游戏场景需要低延迟的配置，使用此设置。此设置下，弱网抗性较差。  
CHANNEL_PROFILE_LOW_LATENCY| 3| 云渲染模式。超低延时配置。如果你的应用并非游戏但又对延时要求较高时，选用此模式该模式下，音视频通话延时会明显降低，但同时弱网抗性、通话音质等均会受到一定影响。  
CHANNEL_PROFILE_CHAT| 4| 适用于 1 vs 1 音视频通话  
CHANNEL_PROFILE_CHAT_ROOM| 5| 适用于 3 人及以上纯语音通话。通话中，闭麦时为是媒体模式，上麦后切换为通话模式。  
CHANNEL_PROFILE_LW_TOGETHER| 6| 实现多端同步播放音视频，适用于 “一起看” 或 “一起听” 场景。该场景中，使用 RTC 信令同步播放进度，共享的音频内容不通过 RTC 进行传输。  
CHANNEL_PROFILE_GAME_HD| 7| 适用于对音质要求较高的游戏场景，优化音频 3A 策略，只通过媒体模式播放音频  
CHANNEL_PROFILE_CO_HOST| 8| 适用于直播中主播之间连麦的业务场景。该场景中，直播时通过 CDN，发起连麦 PK 时使用 RTC。  
CHANNEL_PROFILE_KTV| 9| 线上 KTV 场景，音乐音质，低延迟使用 RTC 传输伴奏音乐，混音后的歌声，适合独唱或单通合唱  
CHANNEL_PROFILE_CHORUS| 10| 适合在线实时合唱场景，高音质，超低延迟。使用本配置前请联系技术支持进行协助完成其他配置。  
CHANNEL_PROFILE_GAME_STREAMING| 11| 适用于 1 vs 1 游戏串流，支持公网或局域网。  
CHANNEL_PROFILE_LAN_LIVE_STREAMING| 12| 适用于局域网的 1 对多视频直播，最高支持 8K， 60 帧/秒， 100 Mbps 码率需要在局域网配置私有化部署媒体服务器。  
CHANNEL_PROFILE_MEETING_ROOM| 13| 适用于云端会议中的会议室终端设备，例如 Rooms，投屏盒子等。  
CHANNEL_PROFILE_CLASSROOM| 14| 适用于课堂互动，房间内所有成员都可以进行音视频互动当你的场景中需要同时互动的成员超过 10 人时使用此模式  
CHANNEL_PROFILE_INTERACTIVE_PODCAST| 15| 适用于单主播和观众进行音视频互动的直播。通话模式，上下麦不会有模式切换，避免音量突变现象  
CHANNEL_PROFIEL_VR_CHAT| 16| 适用于 VR 场景。支持最高 192 KHz 音频采样率，可开启球形立体声。  
CHANNEL_PROFIEL_MEETING| 17| 适用于云端会议中的个人设备。  
CHANNEL_PROFILE_LIVE_BROADCASTING| 18| 直播模式。当你对音视频通话的音质和画质要求较高时，应使用此设置。此设置下，当用户使用蓝牙耳机收听时，蓝牙耳机使用媒体模式。  
## IRemoteVideoConfig 
类型：interface
远端视频帧信息。
### width 
类型：number
视频宽度，单位：px
### height 
类型：number
视频高度，单位：px
### framerate 
类型：number
期望订阅的最高帧率，单位：fps，默认值为 0 即满帧订阅，设为大于 0 的值时开始生效。 如果发布端发布帧率 > 订阅端订阅的帧率，下行媒体服务器 SVC 丢帧，订阅端收到通过此接口设置的帧率；如果发布端发布帧率 < 订阅端订阅的帧率，则订阅端只能收到发布的帧率。 仅码流支持 SVC 分级编码特性时方可生效。
## IAudioEffectPlayerConfig 
类型：interface
音效播放器配置。
### type 
类型：AudioMixingType
混音播放类型
### playCount 
类型：number
混音播放次数
  * play_count <= 0: 无限循环
  * play_count == 1: 播放一次（默认）
  * play_count > 1: 播放 play_count 次


### startPos 
类型：number
混音起始位置。默认值为 0，单位为毫秒。
### pitch 
类型：number
与音乐文件原始音调相比的升高/降低值，取值范围为 [-12，12]，默认值为 0。每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调。
## AudioMixingType 
类型：enum
混音播放类型
属性| 值| 描述  
---|---|---  
AUDIO_MIXING_TYPE_PLAYOUT| 0| 仅本地播放  
AUDIO_MIXING_TYPE_PUBLISH| 1| 仅远端播放  
AUDIO_MIXING_TYPE_PLAYOUT_AND_PUBLISH| 2| 本地和远端同时播放  
## IMediaPlayerConfig 
类型：interface
音乐播放器配置。
### type 
类型：AudioMixingType
音乐播放类型
### playCount 
类型：number
音乐播放次数
  * play_count <= 0: 无限循环
  * play_count == 1: 播放一次（默认）
  * play_count > 1: 播放 play_count 次


### startPos 
类型：number
音乐起始位置。默认值为 0，单位为毫秒。
### autoPlay 
类型：boolean
是否自动播放。如果不自动播放，调用 start 播放音乐文件。
### progressInterval 
类型：number
设置音频文件时，收到 onMediaPlayerPlayingProgress 的间隔。单位毫秒。
  * interval > 0 时，触发回调。实际间隔为 10 的倍数。如果输入数值不能被 10 整除，将自动向上取整。例如传入 52，实际间隔为 60 ms。
  * interval <= 0 时，不会触发回调。


### syncProgressToRecordFrame 
类型：boolean
在采集音频数据时，附带本地混音文件播放进度的时间戳。启用此功能会提升远端人声和音频文件混音播放时的同步效果。
  * 仅在单个音频文件混音时使用有效。
  * true 时开启此功能，false 时关闭此功能，默认为关闭。


## IEnhanceMentConfig 
类型：interface
蜂窝网络辅助增强应用的媒体模式。
### enhanceSignaling 
类型：boolean
对信令消息，是否启用蜂窝网络辅助增强。默认不启用。
### enhanceAudio 
类型：boolean
对屏幕共享以外的其他音频，是否启用蜂窝网络辅助增强。默认不启用。
### enhanceVideo 
类型：boolean
对屏幕共享视频以外的其他视频，是否启用蜂窝网络辅助增强。默认不启用。
### enhanceScreenAudio 
类型：boolean
对屏幕共享音频，是否启用蜂窝网络辅助增强。默认不启用。
### enhanceScreenVideo 
类型：boolean
对屏幕共享视频，是否启用蜂窝网络辅助增强。默认不启用。
## IAudioPropertiesConfig 
类型：interface
音频属性信息提示的相关配置。
### interval 
类型：number
信息提示间隔，单位：ms
  * <= 0: 关闭信息提示
  * (0,100]: 开启信息提示，不合法的 interval 值，SDK 自动设置为 100ms
  * > 100: 开启信息提示，并将信息提示间隔设置为此值


### enableSpectrum 
类型：boolean
是否开启音频频谱检测
### enableVad 
类型：boolean
是否开启人声检测 (VAD)
### localMainReportMode 
类型：AudioReportMode
音量回调模式。详见 。
### audioReportMode 
类型：AudioPropertiesMode
onLocalAudioPropertiesReport 中包含音频数据的范围。参看 。 默认仅包含本地麦克风采集的音频数据和本地屏幕音频采集数据，不包含本地混音音频数据。
### smooth 
类型：number
适用于音频属性信息提示的平滑系数。取值范围是 (0.0, 1.0]。 默认值为 1.0，不开启平滑效果；值越小，提示音量平滑效果越明显。如果要开启平滑效果，可以设置为 0.3。
### enableVoicePitch 
类型：boolean
是否回调本地用户的人声基频。
## AudioReportMode 
类型：enum
音量回调模式。
属性| 值| 描述  
---|---|---  
AUDIO_REPORT_MODE_NORMAL| 0| 默认始终开启音量回调。  
AUDIO_REPORT_MODE_DISCONNECT| 1| 可见用户进房并停止推流后，关闭音量回调。  
AUDIO_REPORT_MODE_RESET| 2| 可见用户进房并停止推流后，开启音量回调，回调值重置为 0。  
## AudioPropertiesMode 
类型：enum
onLocalAudioPropertiesReport 中包含的音频信息的范围。
属性| 值| 描述  
---|---|---  
AUDIO_PROPERTIES_MODE_MICROPHONE| 0| 仅包含本地麦克风采集的音频数据和本地屏幕音频采集数据。  
AUDIO_PROPERTIES_MODE_AUDIOMIXING| 1| 包含以下信息：
  * 本地麦克风采集的音频数据和本地屏幕音频采集数据；
  * 本地混音的音频数据。

  
## IRemoteStreamInfo 
类型：interface
远端流信息。
### roomId 
类型：string
远端用户所在的房间 ID。
### userId 
类型：string
远端用户的 ID。
### streamIndex 
类型：StreamIndex
流属性
## IScreenSolution 
类型：interface
屏幕流编码配置。
### width 
类型：number
视频最大宽度，单位：像素。
### height 
类型：number
视频最大高度，单位：像素。
### frameRate 
类型：number
视频采集和编码帧率，单位：fps。
### maxBitrate 
类型：number
最大编码码率，使用 SDK 内部采集时可选设置，自定义采集时必须设置，单位：kbps。 设为 -1 即适配码率模式，系统将根据输入的分辨率和帧率自动计算适用的码率。 设为 0 则不对视频流进行编码发送。 3.44 及以上版本，内部采集时默认值为 -1，3.44 以前版本无默认值，需手动设置。
### minBitrate 
类型：number
最小编码码率，使用 SDK 内部采集时可选设置，自定义采集时必须设置，单位：kbps。 最小编码码率必须小于或等于最大编码，否则不对视频流进行编码发送。
### encodePrefer 
类型：RTCScreenVideoEncoderPreference
屏幕流编码模式。默认为流畅模式。参看 。
## RTCScreenVideoEncoderPreference 
类型：enum
编码策略偏好。
属性| 值| 描述  
---|---|---  
MAINTAIN_FRAMERATE| 0| （默认值）帧率优先。分辨率不变。  
MAINTAIN_QUALITY| 1| 分辨率优先。  
DISABLED| 2| 无偏好。不降低帧率和分辨率。  
BALANCE| 3| 平衡帧率与分辨率。  
## ScreenMediaType 
类型：enum
屏幕采集媒体类型
属性| 值| 描述  
---|---|---  
SCREEN_MEDIA_TYPE_VIDEO_ONLY| 0| 仅采集视频  
SCREEN_MEDIA_TYPE_AUDIO_ONLY| 1| 仅采集音频  
SCREEN_MEDIA_TYPE_VIDEO_AND_AUDIO| 2| 采集音频和视频  
## ICreateRTCEngineOptions 
类型：interface
引擎初始化参数
### appID 
类型：string
必填，每个应用的唯一标识符，由 RTC 控制台随机生成的。不同的 AppId 生成的实例在 RTC 中进行音视频通话完全独立，无法互通。
### parameters 
类型：Record<string, string | string[]> | undefined
选填，私有参数。如需使用请联系技术支持人员。
## ILogConfigs 
类型：interface
SDK 日志配置。
### logLevel 
类型：LocalLogLevel
日志输出等级，参看 ，默认为警告级别，选填。
### logPath 
类型：string
日志存储路径，必填。
### logFileSize 
类型：number
设置日志文件总大小上限（即所有日志文件大小之和），选填。单位为 MB，取值范围为 1～100 MB。系统处理机制如下：
### logFilenamePrefix 
类型：string
日志文件名前缀，选填。该字符串必须符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。 最终的日志文件名为前缀 + "_" + 文件创建时间 + "_rtclog".log，如 logPrefix_2023-05-25_172324_rtclog.log。
## LocalLogLevel 
类型：enum
日志输出等级。
属性| 值| 描述  
---|---|---  
INFO| 0| 输出 INFO、WARNING、ERROR 级别的日志。  
WARNING| 1| （默认值）仅输出 WARNING、ERROR 级别的日志。  
ERROR| 2| 仅输出 ERROR 级别的日志。  
NONE| 3| 不输出日志。  
## IStreamSyncInfoConfig 
类型：interface
流同步配置。
### streamIndex 
类型：StreamIndex
流属性。
### repeatCount 
类型：number
重复次数。
### streamType 
类型：SyncInfoStreamType
值：0
媒体流信息同步的流类型
## EncryptTypes 
类型：enum
加密类型。
属性| 值| 描述  
---|---|---  
NONE| 0| 不设置。  
AES128CBC| 1| AES128CBC。  
AES256CBC| 2| AES256CBC。  
AES128ECB| 3| AES128ECB。  
AES256ECB| 4| AES256ECB。  
## IFeedbackProblemInfo 
类型：interface
通话质量反馈信息。
### desc 
类型：string
文字描述。
### roomId 
类型：string
房间 ID。
### userId 
类型：string
用户 ID。
## PositionInfo 
类型：class
用户在空间音频坐标系里的位置信息。
### new PositionInfo()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### position 
类型：Position
用户在空间音频坐标系里的位置，需自行建立空间直角坐标系。参看 。
### orientation 
类型：HumanOrientation
用户在空间音频坐标系里的三维朝向信息。三个向量需要两两垂直。参看 。
## AudioTrackType 
类型：enum
原唱伴唱类型。
属性| 值| 描述  
---|---|---  
ORIGINAL| 0| 播放原唱。  
ACCOMPANY| 1| 播放伴唱。  
ByteRTCAudioTrackTypeAccompy| 2| 播放伴唱。  
## AudioPlayType 
类型：enum
音乐播放类型。
属性| 值| 描述  
---|---|---  
LOCAL| 0| 仅本地播放。  
REMOTE| 1| 仅远端播放。  
LOCAL_AND_REMOTE| 2| 本地、远端同时播放。  
## VirtualBackgroundSource 
类型：class
背景贴纸对象。
### sourceType 
类型：VirtualBackgroundSourceType
虚拟背景类型，详见 。
### sourceColor 
类型：number
纯色背景使用的颜色。 格式为 0xAARRGGBB 。
### sourcePath 
类型：string
自定义背景图片的绝对路径。
## FaceDetectionResult 
类型：class
人脸检测结果
### detectResult 
类型：number
人脸检测结果
  * 0：检测成功
  * !0：检测失败。详见。


### imageWidth 
类型：number
原始图片宽度(px)
### imageHeight 
类型：number
原始图片高度(px)
### android_frameTimestampUs 
类型：number
进行人脸识别的视频帧的时间戳。
### ios_frameTimestamp 
类型：number
进行人脸识别的视频帧的时间戳。
### faces() 
识别到人脸的矩形框。数组的长度和检测到的人脸数量一致。参看 。
类型
```
($item: $p_a.Rectangle) => Rectangle

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
$item| $p_a.Rectangle| 是| 无| -  
## SysStats 
类型：class
CPU 和内存统计信息
### new SysStats()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### cpuCores 
类型：number
设备的 CPU 核数
### cpuAppUsage 
类型：number
应用的 CPU 使用率，取值范围为 [0, 1]。
### android_cpuTotalUsage 
类型：number
系统的 CPU 使用率，取值范围为 [0, 1]。
### memoryUsage 
类型：number
应用的内存占用大小（单位 MB）
### fullMemory 
类型：number
设备的内存大小 单位：MB
### totalMemoryUsage 
类型：number
系统已使用内存 MB
### freeMemory 
类型：number
系统当前空闲内存（MB）
### memoryRatio 
类型：number
当前应用的内存使用率（单位 %）
### totalMemoryRatio 
类型：number
系统内存使用率（单位 %）
## RTCNetworkType 
类型：enum
网络连接类型。
属性| 值| 描述  
---|---|---  
UNKNOWN| 0| -  
LAN| 1| -  
MOBILE_2G| 2| -  
MOBILE_3G| 3| -  
WIFI| 4| -  
MOBILE_4G| 5| -  
MOBILE_5G| 6| -  
NONE| 7| -  
ByteRTCNetworkTypeDisconnected| 8| 网络连接已断开。  
## LocalAudioStreamState 
类型：enum
本地音频流状态。 SDK 通过 onLocalAudioStateChanged 回调该状态。
属性| 值| 描述  
---|---|---  
LOCAL_AUDIO_STREAM_STATE_STOPPED| 0| 本地音频默认初始状态。麦克风停止工作时回调该状态，对应错误码 中的 kLocalAudioStreamErrorOk 。  
LOCAL_AUDIO_STREAM_STATE_RECORDING| 1| 本地音频录制设备启动成功。采集到音频首帧时回调该状态，对应错误码 中的 kLocalAudioStreamErrorOk 。  
LOCAL_AUDIO_STREAM_STATE_ENCODING| 2| 本地音频首帧编码成功。音频首帧编码成功时回调该状态，对应错误码 中的 kLocalAudioStreamErrorOk 。  
LOCAL_AUDIO_STREAM_STATE_FAILED| 3| 本地音频启动失败，在以下时机回调该状态：
  * 本地录音设备启动失败，对应错误码 中的 kLocalAudioStreamErrorRecordFailure 。
  * 检测到没有录音设备权限，对应错误码 中的 kLocalAudioStreamErrorDeviceNoPermission。
  * 音频编码失败，对应错误码 中的 kLocalAudioStreamErrorEncodeFailure。

  
## LocalAudioStreamError 
类型：enum
本地音频流状态改变时的错误码。
属性| 值| 描述  
---|---|---  
LOCAL_AUDIO_STREAM_ERROR_OK| 0| 本地音频状态正常  
LOCAL_AUDIO_STREAM_ERROR_FAILURE| 1| 本地音频出错原因未知  
LOCAL_AUDIO_STREAM_ERROR_DEVICE_NO_PERMISSION| 2| 没有权限启动本地音频录制设备  
LOCAL_AUDIO_STREAM_ERROR_RECORD_FAILURE| 3| 本地音频录制失败，建议你检查录制设备是否正常工作  
LOCAL_AUDIO_STREAM_ERROR_ENCODE_FAILURE| 4| 本地音频编码失败  
LOCAL_AUDIO_STREAM_ERROR_NO_RECORDING_DEVICE| 5| brief 没有可用的音频录制设备  
## RemoteStreamKey 
类型：class
远端流信息
### new RemoteStreamKey()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### roomId 
类型：string
房间 ID
### userId 
类型：string
用户 ID
### streamIndex 
类型：StreamIndex
流属性，包括主流、屏幕流。参看 
### android_getRoomId() 
获取房间 ID
类型
```
() => string

ts

```

返回值
string
### android_getUserId() 
获取用户 ID
类型
```
() => string

ts

```

返回值
string
### android_getStreamIndex() 
获取流属性，包括主流、屏幕流。参看 
类型
```
() => $p_a.StreamIndex

ts

```

返回值
$p_a.StreamIndex
### android_hasNullProperty() 
检查当前类中是否有为空的字段
类型
```
() => boolean

ts

```

返回值
boolean
## RemoteAudioState 
类型：enum
用户订阅的远端音频流状态。
属性| 值| 描述  
---|---|---  
REMOTE_AUDIO_STATE_STOPPED| 0| 远端音频流默认初始状态，在以下时机回调该状态：
  * 本地用户停止接收远端音频流，对应原因是 中的 kRemoteAudioReasonLocalMuted
  * 远端用户停止发送音频流，对应原因是 中的 kRemoteAudioReasonRemoteMuted
  * 远端用户离开房间，对应原因是 中的 kRemoteAudioReasonRemoteOffline

  
REMOTE_AUDIO_STATE_STARTING| 1| 开始接收远端音频流首包。  
REMOTE_AUDIO_STATE_DECODING| 2| 远端音频流正在解码，正常播放，在以下时机回调该状态：  
REMOTE_AUDIO_STATE_FROZEN| 3| 远端音频流卡顿。网络阻塞导致丢包率大于 40% 时回调该状态，对应原因是 中的 kRemoteAudioReasonNetworkCongestion  
## RemoteAudioStateChangeReason 
类型：enum
远端音频流状态改变的原因。
属性| 值| 描述  
---|---|---  
REMOTE_AUDIO_STATE_CHANGE_REASON_NETWORK_CONGESTION| 0| 网络阻塞  
REMOTE_AUDIO_STATE_CHANGE_REASON_NETWORK_RECOVERY| 1| 网络恢复正常  
REMOTE_AUDIO_STATE_CHANGE_REASON_LOCAL_MUTED| 2| 本地用户停止接收远端音频流  
REMOTE_AUDIO_STATE_CHANGE_REASON_LOCAL_UNMUTED| 3| 本地用户恢复接收远端音频流  
REMOTE_AUDIO_STATE_CHANGE_REASON_REMOTE_MUTED| 4| 远端用户停止发送音频流  
REMOTE_AUDIO_STATE_CHANGE_REASON_REMOTE_UNMUTED| 5| 远端用户恢复发送音频流  
REMOTE_AUDIO_STATE_CHANGE_REASON_REMOTE_OFFLINE| 6| 远端用户离开房间  
REMOTE_AUDIO_STATE_CHANGE_REASON_INTERNAL| 7| 内部原因  
ByteRTCRemoteAudioStateChangeReasonInternal| 8| 内部原因  
## LocalVideoStreamState 
类型：enum
本地视频流状态
属性| 值| 描述  
---|---|---  
LOCAL_VIDEO_STREAM_STATE_STOPPED| 0| 本地视频采集停止状态（默认初始状态）本地视频采集关闭时回调该状态，对应错误码 中的 LOCAL_VIDEO_STREAM_ERROR_OK  
LOCAL_VIDEO_STREAM_STATE_RECORDING| 1| 本地视频采集设备启动成功本地视频采集开启时回调该状态，对应错误码 中的 LOCAL_VIDEO_STREAM_ERROR_OK  
LOCAL_VIDEO_STREAM_STATE_ENCODING| 2| 本地视频采集后，首帧编码成功本地视频首帧编码成功时回调该状态，对应错误码 中的 LOCAL_VIDEO_STREAM_ERROR_OK  
LOCAL_VIDEO_STREAM_STATE_FAILED| 3| 本地视频启动失败
  * 本地视频采集设备启动失败，对应错误码 中的 LOCAL_VIDEO_STREAM_ERROR_FAILURE
  * 检测到没有视频采集设备权限，对应错误码 中的 LOCAL_VIDEO_STREAM_ERROR_DEVICE_NO_PERMISSION
  * 视频编码失败，对应错误码 中的 LOCAL_VIDEO_STREAM_ERROR_ENCODE_FAILURE

  
## LocalVideoStreamError 
类型：enum
本地视频状态改变时的错误码
属性| 值| 描述  
---|---|---  
LOCAL_VIDEO_STREAM_ERROR_OK| 0| 状态正常（本地视频状态改变正常时默认返回值）  
LOCAL_VIDEO_STREAM_ERROR_FAILURE| 1| 本地视频流发布失败  
LOCAL_VIDEO_STREAM_ERROR_DEVICE_NO_PERMISSION| 2| 没有权限启动本地视频采集设备  
LOCAL_VIDEO_STREAM_ERROR_DEVICE_BUSY| 3| 本地视频采集设备已被占用  
LOCAL_VIDEO_STREAM_ERROR_DEVICE_NOT_FOUND| 4| 本地视频采集设备不存在或已移除  
LOCAL_VIDEO_STREAM_ERROR_CAPTURE_FAILURE| 5| 本地视频采集失败，建议检查采集设备是否正常工作  
LOCAL_VIDEO_STREAM_ERROR_ENCODE_FAILURE| 6| 本地视频编码失败  
LOCAL_VIDEO_STREAM_ERROR_DEVICE_DISCONNECTED| 7| 通话过程中本地视频采集设备被其他程序抢占，导致设备连接中断  
## RemoteVideoState 
类型：enum
远端视频流状态。
属性| 值| 描述  
---|---|---  
REMOTE_VIDEO_STATE_STOPPED| 0| 远端视频流默认初始状态在以下时机回调该状态：
  * 本地用户停止接收远端视频流，对应错误码 中的 REMOTE_VIDEO_STATE_CHANGE_REASON_LOCAL_MUTED。
  * 远端用户停止发送视频流，对应错误码 中的 REMOTE_VIDEO_STATE_CHANGE_REASON_REMOTE_MUTED。
  * 远端用户离开房间，对应错误码 中的 REMOTE_VIDEO_STATE_CHANGE_REASON_REMOTE_OFFLINE。

  
REMOTE_VIDEO_STATE_STARTING| 1| 本地用户已接收远端视频首包收到远端视频首包时回调该状态，对应错误码 中的 REMOTE_VIDEO_STATE_CHANGE_REASON_LOCAL_UNMUTED。  
REMOTE_VIDEO_STATE_DECODING| 2| 远端视频流正在解码，正常播放在以下时机回调该状态：  
REMOTE_VIDEO_STATE_FROZEN| 3| 远端视频流卡顿网络阻塞、丢包率大于 40%时回调该状态，对应错误码 中的 REMOTE_VIDEO_REASON_NETWORK_CONGESTION 。  
ByteRTCRemoteVideoStateFailed| 4| 远端视频流播放失败如果内部处理远端视频流失败，则会回调该方法， 对应错误码 ByteRTCRemoteVideoStateChangeReason 中的 ByteRTCRemoteVideoStateChangeReasonInternal  
## RemoteVideoStateChangeReason 
类型：enum
远端视频流状态改变的原因
属性| 值| 描述  
---|---|---  
REMOTE_VIDEO_STATE_CHANGE_REASON_NETWORK_CONGESTION| 0| 网络阻塞  
REMOTE_VIDEO_STATE_CHANGE_REASON_NETWORK_RECOVERY| 1| 网络恢复正常  
REMOTE_VIDEO_STATE_CHANGE_REASON_LOCAL_MUTED| 2| 本地用户停止接收远端视频流或本地用户禁用视频模块  
REMOTE_VIDEO_STATE_CHANGE_REASON_LOCAL_UNMUTED| 3| 本地用户恢复接收远端视频流或本地用户启用视频模块  
REMOTE_VIDEO_STATE_CHANGE_REASON_REMOTE_MUTED| 4| 远端用户停止发送视频流或远端用户禁用视频模块  
REMOTE_VIDEO_STATE_CHANGE_REASON_REMOTE_UNMUTED| 5| 远端用户恢复发送视频流或远端用户启用视频模块  
REMOTE_VIDEO_STATE_CHANGE_REASON_REMOTE_OFFLINE| 6| 远端用户离开房间状态转换参看 onUserUnpublishStream/onUserUnpublishScreen onUserUnpublishScreen。  
REMOTE_VIDEO_STATE_CHANGE_REASON_INTERNAL| 7| 内部原因  
ByteRTCRemoteVideoStateChangeReasonInternal| 8| 内部原因  
## VideoFrameInfo 
类型：class
视频帧信息
### new VideoFrameInfo()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### width 
类型：number
宽（像素）
### height 
类型：number
高（像素）
### rotation 
类型：VideoRotation
视频帧顺时针旋转角度。参看 。
## ConnectionState 
类型：enum
SDK 与 RTC 服务器连接状态。
属性| 值| 描述  
---|---|---  
CONNECTION_STATE_DISCONNECTED| 0| 连接断开超过 12s，此时 SDK 会尝试自动重连。  
CONNECTION_STATE_CONNECTING| 1| 首次请求建立连接，正在连接中。  
CONNECTION_STATE_CONNECTED| 2| 首次连接成功。  
CONNECTION_STATE_RECONNECTING| 3| 涵盖了以下情况：
  * 首次连接时，10 秒内未连接成功;
  * 连接成功后，断连 10 秒。自动重连中。

  
CONNECTION_STATE_RECONNECTED| 4| 连接断开后，重连成功。  
CONNECTION_STATE_LOST| 5| 处于 CONNECTION_STATE_DISCONNECTED 状态超过 10 秒，且期间重连未成功。SDK 仍将继续尝试重连。  
CONNECTION_STATE_FAILED| 6| 连接失败，服务端状态异常。SDK 不会自动重连，请重新进房，或联系技术支持。  
## AudioRoute 
类型：enum
音频播放路由
属性| 值| 描述  
---|---|---  
AUDIO_ROUTE_DEFAULT| 0| 默认设备。通过 setDefaultAudioRoute 设置的音频路由。  
AUDIO_ROUTE_HEADSET| 1| 有线耳机  
AUDIO_ROUTE_EARPIECE| 2| 听筒。设备自带的，一般用于通话的播放硬件。  
AUDIO_ROUTE_SPEAKERPHONE| 3| 扬声器。设备自带的，一般用于免提播放的硬件。  
AUDIO_ROUTE_HEADSET_BLUETOOTH| 4| 蓝牙耳机  
AUDIO_ROUTE_HEADSET_USB| 5| USB 设备  
## SEIStreamUpdateEventType 
类型：enum
黑帧视频流状态
属性| 值| 描述  
---|---|---  
STREAM_ADD| 0| 远端用户发布黑帧视频流。纯语音通话场景下，远端用户调用 sendSEIMessage 发送 SEI 数据时，SDK 会自动发布一路黑帧视频流，并触发该回调。  
STREAM_REMOVE| 1| 远端黑帧视频流移除。该回调的触发时机包括：
  * 远端用户开启摄像头采集，由语音通话切换至视频通话，黑帧视频流停止发布；
  * 远端用户调用 sendSEIMessage 后 1min 内未有 SEI 数据发送，黑帧视频流停止发布；
  * 远端用户调用 setVideoSourceType 切换至自定义视频采集时，黑帧视频流停止发布。

  
ByteRTCSEIStreamEventTypeStreamAdd| 2| 远端用户发布黑帧视频流。纯语音通话场景下，远端用户调用 sendSEIMessage 发送 SEI 数据时，SDK 会自动发布一路黑帧视频流，并触发该回调。  
ByteRTCSEIStreamEventTypeStreamRemove| 3| 远端黑帧视频流移除。该回调的触发时机包括：
  * 远端用户开启摄像头采集，由语音通话切换至视频通话，黑帧视频流停止发布；
  * 远端用户调用 sendSEIMessage 后 1min 内未有 SEI 数据发送，黑帧视频流停止发布；
  * 远端用户调用 setVideoSourceType 切换至自定义视频采集时，黑帧视频流停止发布。

  
## LogoutReason 
类型：enum
用户登出的原因
属性| 值| 描述  
---|---|---  
LOGOUT_REASON_LOGOUT| 0| 用户主动退出用户调用 logout 接口登出，或者销毁引擎登出。  
LOGOUT_REASON_DUPLICATE_LOGIN| 1| 用户被动退出另一个用户以相同 UserId 进行了 login，导致本端用户被踢出。  
ByteRTCLogoutReasonLogout| 2| 用户主动退出用户调用 logout 接口登出，或者销毁引擎登出。  
ByteRTCLogoutReasonDuplicateLogin| 3| 用户被动退出另一个用户以相同 UserId 进行了 login，导致本端用户被踢出。  
## UserOnlineStatus 
类型：enum
用户在线状态
属性| 值| 描述  
---|---|---  
USER_ONLINE_STATUS_OFFLINE| 0| 对端用户离线对端用户已经调用 logout，或者没有调用 login 进行登录  
USER_ONLINE_STATUS_ONLINE| 1| 对端用户在线对端用户调用 login 登录，并且连接状态正常。  
USER_ONLINE_STATUS_UNREACHABLE| 2| 无法获取对端用户在线状态发生级联错误、对端用户在线状态异常时返回  
ByteRTCUserOnlineStatusOffline| 3| 对端用户离线对端用户已经调用 logout，或者没有调用 login:uid: 进行登录  
ByteRTCUserOnlineStatusOnline| 4| 对端用户在线对端用户调用 login:uid: 登录，并且连接状态正常  
ByteRTCUserOnlineStatusUnreachable| 5| 无法获取对端用户在线状态发生级联错误、对端用户在线状态异常时返回  
## NetworkDetectionLinkType 
类型：enum
通话前探测链接的类型。onNetworkDetectionResult 回调的参数类型
属性| 值| 描述  
---|---|---  
UP| 0| 上行网络探测  
DOWN| 1| 下行网络探测  
## NetworkQuality 
类型：enum
所属用户的媒体流网络质量。
属性| 值| 描述  
---|---|---  
NETWORK_QUALITY_UNKNOWN| 0| 媒体流网络质量未知。  
NETWORK_QUALITY_EXCELLENT| 1| 媒体流网络质量极好。  
NETWORK_QUALITY_GOOD| 2| 媒体流网络质量好。  
NETWORK_QUALITY_POOR| 3| 媒体流网络质量较差但不影响沟通。  
NETWORK_QUALITY_BAD| 4| 媒体流网络质量差沟通不顺畅。  
NETWORK_QUALITY_VERY_BAD| 5| 媒体流网络质量非常差。  
NETWORK_QUALITY_DOWN| 6| 网络连接断开，无法通话。网络可能由于 12s 内无应答、开启飞行模式、拔掉网线等原因断开。更多网络状态信息参见 。  
## NetworkDetectionStopReason 
类型：enum
通话前探测的停止原因。onNetworkDetectionStopped 回调的参数类型
属性| 值| 描述  
---|---|---  
USER| 0| 用户主动停止  
TIMEOUT| 1| 探测超过三分钟  
CONNECTION_LOST| 2| 探测网络连接断开。当超过 12s 没有收到回复，SDK 将断开网络连接，并且不再尝试重连。  
STREAMING| 3| 本地开始推拉流，停止探测  
INNER_ERR| 4| 网络探测失败，内部异常  
ByteRTCNetworkDetectionStopReasonInnerErr| 5| 网络探测失败，内部异常  
## RemoteStreamSwitchEvent 
类型：class
流切换信息。本地用户订阅的远端流触发回退策略时的流切换信息。
### new RemoteStreamSwitchEvent()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### uid 
类型：string
订阅的音视频流的发布者的用户 ID
### isScreen 
类型：boolean
是否是屏幕共享流
### beforeVideoIndex 
类型：number
流切换前订阅视频流的分辨率对应的索引
### afterVideoIndex 
类型：number
流切换后订阅视频流的分辨率对应的索引
### android_beforeEnable 
类型：boolean
流切换前是否有视频流
### android_afterEnable 
类型：boolean
流切换后是否有视频流
### reason 
类型：FallbackOrRecoverReason
流切换原因，详见类型 。
### ios_beforeVideoEnabled 
类型：boolean
流切换前是否有视频流
### ios_afterVideoEnabled 
类型：boolean
流切换后是否有视频流
## PerformanceAlarmMode 
类型：enum
性能回退的模式
属性| 值| 描述  
---|---|---  
NORMAL| 0| 未开启发布性能回退  
SIMULCAST| 1| 已开启发布性能回退  
## PerformanceAlarmReason 
类型：enum
性能相关告警的原因
属性| 值| 描述  
---|---|---  
BANDWIDTH_RESUMED| 0| 网络性能恢复，发送性能回退恢复。仅在开启发送性能回退时，会收到此原因。  
BANDWIDTH_FALLBACKED| 1| 网络原因差，造成了发送性能回退。仅在开启发送性能回退时，会收到此原因。  
PERFORMANCE_FALLBACKED| 2| 如果未开启发送性能回退，收到此告警时，意味着性能不足；如果开启了发送性能回退，收到此告警时，意味着性能不足，且已发生发送性能回退。  
PERFORMANCE_RESUMED| 3| 如果未开启发送性能回退，收到此告警时，意味着性能不足已恢复；如果开启了发送性能回退，收到此告警时，意味着性能不足已恢复，且已发生发送性能回退恢复。  
ByteRTCPerformanceAlarmReasonBandwidthFallback| 4| 网络原因差，造成了发送性能回退。仅在开启发送性能回退时，会收到此原因。  
ByteRTCPerformanceAlarmReasonFallback| 5| 如果未开启发送性能回退，收到此告警时，意味着性能不足；如果开启了发送性能回退，收到此告警时，意味着性能不足，且已发生发送性能回退。  
ByteRTCPerformanceAlarmReasonResumed| 6| 如果未开启发送性能回退，收到此告警时，意味着性能不足已恢复；如果开启了发送性能回退，收到此告警时，意味着性能不足已恢复，且已发生发送性能回退恢复。  
## SourceWantedData 
类型：class
性能回退相关数据
### new SourceWantedData()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### width 
类型：number
如果未开启发送性能回退，此值表示推荐的视频输入宽度； 如果开启了发送性能回退，此值表示当前推流的最大宽度。
### height 
类型：number
如果未开启发送性能回退，此值表示推荐的视频输入高度； 如果开启了发送性能回退，此值表示当前推流的最大高度。
### frameRate 
类型：number
如果未开启发送性能回退，此值表示推荐的视频输入帧率，单位 fps； 如果开启了发送性能回退，此值表示当前推流的最大帧率，单位 fps。
## RTCUser 
类型：class
用户信息
### new RTCUser()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### userId 
类型：string
用户 id
### metaData 
类型：string
元数据
## FirstFrameSendState 
类型：enum
首帧发送状态
属性| 值| 描述  
---|---|---  
FIRST_FRAME_SEND_STATE_SENDING| 0| 发送中  
FIRST_FRAME_SEND_STATE_SENT| 1| 发送成功  
FIRST_FRAME_SEND_STAT_END| 2| 发送失败  
## FirstFramePlayState 
类型：enum
首帧播放状态
属性| 值| 描述  
---|---|---  
FIRST_FRAME_PLAY_STATE_PLAYING| 0| 播放中  
FIRST_FRAME_PLAY_STATE_END| 1| 播放失败  
FIRST_FRAME_PLAY_STATE_PLAYED| 2| 播放成功  
ByteRTCFirstFramePlayStatePlay| 3| 播放成功  
## AudioDeviceType 
类型：enum
音频设备类型
属性| 值| 描述  
---|---|---  
AUDIO_DEVICE_TYPE_UNKNOWN| 0| 未知设备  
AUDIO_DEVICE_TYPE_RENDER_DEVICE| 1| 音频渲染设备  
AUDIO_DEVICE_TYPE_CAPTURE_DEVICE| 2| 音频采集设备类型  
AUDIO_DEVICE_TYPE_SCREEN_CAPTURE_DEVICE| 3| 屏幕流音频设备  
## MediaDeviceState 
类型：enum
媒体设备状态 通过 onAudioDeviceStateChanged/onVideoDeviceStateChanged 回调设备状态。
属性| 值| 描述  
---|---|---  
MEDIA_DEVICE_STATE_STARTED| 0| 设备开启采集  
MEDIA_DEVICE_STATE_STOPPED| 1| 设备停止采集  
MEDIA_DEVICE_STATE_RUNTIMEERROR| 2| 设备运行时错误例如，当媒体设备的预期行为是正常采集，但没有收到采集数据时，将回调该状态。  
MEDIA_DEVICE_STATE_ADDED| 3| 设备已插入  
MEDIA_DEVICE_STATE_REMOVED| 4| 设备被移除  
MEDIA_DEVICE_STATE_INTERRUPTION_BEGAN| 5| 系统通话打断了音视频通话。将在通话结束后自动恢复。  
MEDIA_DEVICE_STATE_INTERRUPTION_ENDED| 6| 音视频通话已从系统电话中恢复  
ByteRTCMediaDeviceStateStarted| 7| 设备已开启  
ByteRTCMediaDeviceStateStopped| 8| 设备已停止  
ByteRTCMediaDeviceStateRuntimeError| 9| 设备运行时错误例如，当媒体设备的预期行为是正常采集，但没有收到采集数据时，将回调该状态。  
ByteRTCMediaDeviceStateAdded| 10| 设备已插入你可以调用获取设备接口更新设备列表。  
ByteRTCMediaDeviceStateRemoved| 11| 设备被移除你可以调用获取设备接口更新设备列表。  
ByteRTCMediaDeviceStateInterruptionBegan| 12| 系统通话，锁屏或第三方应用打断了音视频通话。将在通话结束或第三方应用结束占用后自动恢复。  
ByteRTCMediaDeviceStateInterruptionEnded| 13| 音视频通话已从系统电话或第三方应用打断中恢复  
## MediaDeviceError 
类型：enum
媒体设备错误类型
属性| 值| 描述  
---|---|---  
MEDIA_DEVICE_ERROR_OK| 0| -  
MEDIA_DEVICE_ERROR_NOPERMISSION| 1| -  
MEDIA_DEVICE_ERROR_DEVICEBUSY| 2| -  
MEDIA_DEVICE_ERROR_DEVICEFAILURE| 3| -  
MEDIA_DEVICE_ERROR_DEVICENOTFOUND| 4| -  
MEDIA_DEVICE_ERROR_DEVICEDISCONNECTED| 5| -  
MEDIA_DEVICE_ERROR_DEVICENOCALLBACK| 6| 无采集数据。当媒体设备的预期行为是正常采集，但没有收到采集数据时，将收到该错误。  
MEDIA_DEVICE_ERROR_UNSUPPORTFORMAT| 7| 设备采样率不支持  
ByteRTCMediaDeviceErrorOK| 8| -  
ByteRTCMediaDeviceErrorDeviceNoPermission| 9| -  
ByteRTCMediaDeviceErrorDeviceBusy| 10| -  
ByteRTCMediaDeviceErrorDeviceFailure| 11| -  
ByteRTCMediaDeviceErrorDeviceNotFound| 12| -  
ByteRTCMediaDeviceErrorDeviceDisconnected| 13| -  
ByteRTCMediaDeviceErrorDeviceNoCallback| 14| 无采集数据。当媒体设备的预期行为是正常采集，但没有收到采集数据时，将收到该错误。  
ByteRTCMediaDeviceErrorUNSupportFormat| 15| 设备采样率不支持  
## VideoDeviceType 
类型：enum
当前视频设备类型
属性| 值| 描述  
---|---|---  
VIDEO_DEVICE_TYPE_UNKNOWN| 0| 未知设备类型  
VIDEO_DEVICE_TYPE_RENDER_DEVICE| 1| 视频渲染设备类型  
VIDEO_DEVICE_TYPE_CAPTURE_DEVICE| 2| 视频采集设备类型  
VIDEO_DEVICE_TYPE_SCREEN_CAPTURE_DEVICE| 3| 屏幕流视频设备  
## MediaDeviceWarning 
类型：enum
媒体设备警告
属性| 值| 描述  
---|---|---  
MEDIA_DEVICE_WARNING_OK| 0| 无警告  
MEDIA_DEVICE_WARNING_OPERATION_DENIED| 1| 非法设备操作。在使用外部设备时，调用了 SDK 内部设备 API。  
MEDIA_DEVICE_WARNING_CAPTURE_SILENCE| 2| 采集到的数据为静音帧。  
MEDIA_DEVICE_WARNING_ANDROID_SYS_SILENCE| 3| Android 特有的静音，系统层面的静音上报  
MEDIA_DEVICE_WARNING_ANDROID_SYS_SILENCE_DISAPPEAR| 4| Android 特有的静音消失  
MEDIA_DEVICE_WARNING_DETECT_LEAK_ECHO| 6| 通话中出现回声现象。当 为 CHANNEL_PROFIEL_MEETING 和 CHANNEL_PROFILE_MEETING_ROOM，且 AEC 关闭时，SDK 自动启动回声检测，如果检测到回声问题，将通过 onAudioDeviceWarning 返回本枚举值。  
MEDIA_DEVICE_WARNING_CAPTURE_DETECT_HOWLING| 11| 啸叫。触发该回调的情况如下：1）不支持啸叫抑制的房间模式下，检测到啸叫；2）支持啸叫抑制的房间模式下，检测到未被抑制的啸叫。仅 CHANNEL_PROFILE_COMMUNICATION(0)、CHANNEL_PROFIEL_MEETING(16)、CHANNEL_PROFILE_MEETING_ROOM(17) 三种房间模式支持啸叫抑制。建议提醒用户检查客户端的距离或将麦克风和扬声器调至静音。  
MEDIA_DEVICE_WARNING_SET_AUDIO_ROUTE_INVALID_SCENARIO| 12| 当前 AudioScenario 不支持更改音频路由，设置音频路由失败  
MEDIA_DEVICE_WARNING_SET_AUDIO_ROUTE_NOT_EXISTS| 13| 音频设备不存在，设置音频路由失败  
MEDIA_DEVICE_WARNING_SET_AUDIO_ROUTE_FAILED_BY_PRIORITY| 14| 音频路由被系统或其他应用占用，设置音频路由失败  
MEDIA_DEVICE_WARNING_SET_AUDIO_ROUTE_NOT_VOIP_MODE| 15| 当前非通话模式 AUDIO_SCENARIO_COMMUNICATION(2)，不支持设置音频路由  
MEDIA_DEVICE_WARNING_SET_AUDIO_ROUTE_DEVICE_NOT_START| 16| 音频设备未启动，设置音频路由失败  
ByteRTCMediaDeviceWarningOK| 17| 无警告  
ByteRTCMediaDeviceWarningOperationDenied| 18| 非法设备操作。在使用外部设备时，调用了 SDK 内部设备 API。  
ByteRTCMediaDeviceWarningCaptureSilence| 19| 采集到的数据为静音帧。  
ByteRTCMediaDeviceWarningDetectLeakEcho| 21| 通话中出现回声现象。当 ByteRTCRoomProfile 为 ByteRTCRoomProfileMeeting 和 ByteRTCRoomProfileMeetingRoom ，且 AEC 关闭时，SDK 自动启动回声检测，如果检测到回声问题，将通过 rtcEngine:onAudioDeviceWarning:deviceType:deviceWarning: 返回本枚举值。  
ByteRTCMediaDeviceWarningCaptureDetectHowling| 26| 啸叫。触发该回调的情况如下：1）不支持啸叫抑制的房间模式下，检测到啸叫；2）支持啸叫抑制的房间模式下，检测到未被抑制的啸叫。仅 ByteRTCRoomProfileCommunication、ByteRTCRoomProfileMeeting、ByteRTCRoomProfileMeetingRoom 三种房间模式支持啸叫抑制。建议提醒用户检查客户端的距离或将麦克风和扬声器调至静音。  
## RecordingState 
类型：enum
本地录制的状态
属性| 值| 描述  
---|---|---  
RECORDING_STATE_PROCESSING| 0| 录制进行中  
RECORDING_STATE_SUCCESS| 1| 录制文件保存成功，调用 stopFileRecording 结束录制之后才会收到该状态码。  
RECORDING_STATE_ERROE| 2| 录制异常  
ByteRTCRecordingStateError| 3| 录制异常  
## RecordingErrorCode 
类型：enum
本地录制的错误码
属性| 值| 描述  
---|---|---  
RECORDING_ERROR_CODE_OK| 0| 录制正常  
RECORDING_ERROR_CODE_NO_PERMISSION| 1| 没有文件写权限  
RECORDING_ERROR_CODE_NOT_SUPPORT| 2| 当前版本 SDK 不支持本地录制功能，请联系技术支持人员  
RECORDING_ERROR_CODE_NO_OTHER| 3| 其他异常  
ByteRTCRecordingErrorCodeOk| 4| 录制正常  
ByteRTCRecordingErrorCodeNoPermission| 5| 没有文件写权限  
ByteRTCRecordingErrorCodeNotSupport| 6| 当前版本 SDK 不支持本地录制功能，请联系技术支持人员  
ByteRTCRecordingErrorCodeOther| 7| 其他异常  
## RecordingInfo 
类型：class
本地录制的详细信息
### filePath 
类型：string
录制文件的绝对路径，包含文件名和文件后缀
### width 
类型：number
录制视频的宽，单位：像素。纯音频录制请忽略该字段
### height 
类型：number
录制视频的高，单位：像素。纯音频录制请忽略该字段
### ios_codecType 
类型：$p_i.ByteRTCVideoCodecType
录制文件的视频编码类型，参看 ByteRTCVideoCodecType
## RecordingProgress 
类型：class
本地录制进度
### new RecordingProgress()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### duration 
类型：number
当前文件的累计录制时长，单位：毫秒
### fileSize 
类型：number
当前录制文件的大小，单位：byte
## AudioRecordingState 
类型：enum
录音配置
属性| 值| 描述  
---|---|---  
AUDIO_RECORDING_STATE_ERROR| 0| 录制异常  
AUDIO_RECORDING_STATE_PROCESSING| 1| 录制进行中  
AUDIO_RECORDING_STATE_SUCCESS| 2| 已结束录制，并且录制文件保存成功。  
## LocalAudioPropertiesInfo 
类型：class
本地音频属性信息
### new LocalAudioPropertiesInfo()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### streamIndex 
类型：StreamIndex
流属性，主流或屏幕流。参看 
### audioPropertiesInfo 
类型：AudioPropertiesInfo
音频属性信息，详见 。
## RemoteAudioPropertiesInfo 
类型：class
远端音频属性信息
### new RemoteAudioPropertiesInfo()
类型
```
constructor(...args: any[])

ts

```

参数
名称| 类型| 必选| 默认值| 描述  
---|---|---|---|---  
...args| any[]| 是| 无| -  
### streamKey 
类型：RemoteStreamKey
远端流信息，详见 
### audioPropertiesInfo 
类型：AudioPropertiesInfo
音频属性信息，详见 
## DataMessageSourceType 
类型：enum
数据消息来源
属性| 值| 描述  
---|---|---  
DATA_MESSAGE_SOURCE_TYPE_DEFAULT| 0| 通过客户端或服务端的插入的自定义消息。  
DATA_MESSAGE_SOURCE_TYPE_SYSTEM| 1| 系统数据，包含音量指示信息。  
## EchoTestResult 
类型：enum
音视频回路测试结果
属性| 值| 描述  
---|---|---  
ECHO_TEST_SUCCESS| 0| 接收到采集的音视频的回放，通话回路检测成功  
ECHO_TEST_TIMEOUT| 1| 测试超过 60s 仍未完成，已自动停止  
ECHO_TEST_INTERVAL_SHORT| 2| 上一次测试结束和下一次测试开始之间的时间间隔少于 5s  
ECHO_TEST_AUDIO_DEVICE_ERROR| 3| 音频采集异常  
ECHO_TEST_VIDEO_DEVICE_ERROR| 4| 视频采集异常  
ECHO_TEST_AUDIO_RECEIVE_ERROR| 5| 音频接收异常  
ECHO_TEST_VIDEO_RECEIVE_ERROR| 6| 视频接收异常  
ECHO_TEST_INTERNAL_ERROR| 7| 内部错误，不可恢复  
ByteRTCEchoTestResultSuccess| 8| 接收到采集的音视频的回放，通话回路检测成功  
ByteRTCEchoTestResultTimeout| 9| 测试超过 60s 仍未完成，已自动停止  
ByteRTCEchoTestResultIntervalShort| 10| 上一次测试结束和下一次测试开始之间的时间间隔少于 5s  
ByteRTCEchoTestResultAudioDeviceError| 11| 音频采集异常  
ByteRTCEchoTestResultVideoDeviceError| 12| 视频采集异常  
ByteRTCEchoTestResultAudioReceiveError| 13| 音频接收异常  
ByteRTCEchoTestResultVideoReceiveError| 14| 视频接收异常  
ByteRTCEchoTestResultInternalError| 15| 内部错误，不可恢复  
## HardwareEchoDetectionResult 
类型：enum
通话前回声检测结果
属性| 值| 描述  
---|---|---  
HARDWARE_ECHO_RESULT_CANCELED| 0| 主动调用 stopHardwareEchoDetection 结束流程时，未有回声检测结果。  
HARDWARE_ECHO_RESULT_UNKNOWN| 1| 未检测出结果。建议重试，如果仍然失败请联系技术支持协助排查。  
HARDWARE_ECHO_RESULT_NORMAL| 2| 无回声  
HARDWARE_ECHO_RESULT_POOR| 3| 有回声。可通过 UI 建议用户使用耳机设备入会。  
ByteRTCHardwareEchoDetectionCanceled| 4| 主动调用 stopHardwareEchoDetection 结束流程时，未有回声检测结果。  
ByteRTCHardwareEchoDetectionUnknown| 5| 未检测出结果。建议重试，如果仍然失败请联系技术支持协助排查。  
ByteRTCHardwareEchoDetectionNormal| 6| 无回声  
ByteRTCHardwareEchoDetectionPoor| 7| 有回声。可通过 UI 建议用户使用耳机设备入会。  
## LocalProxyType 
类型：enum
本地代理的类型。
属性| 值| 描述  
---|---|---  
SOCKS5| 0| Socks5 代理。选用此代理服务器，需满足 Socks5 协议标准文档的要求。  
HTTP_TUNNEL| 1| Http 隧道代理。  
## LocalProxyState 
类型：enum
本地代理连接状态。
属性| 值| 描述  
---|---|---  
CONNECTED| 0| 本地代理连接成功。  
ERROR| 1| 本地代理连接出现错误。  
INITED| 2| TCP 代理服务器连接成功。  
ByteRTCLocalProxyStateInited| 3| TCP 代理服务器连接成功。  
## LocalProxyError 
类型：enum
本地代理错误。
属性| 值| 描述  
---|---|---  
OK| 0| 本地代理服务器无错误。  
SOCKS5_VERSION_ERROR| 1| 代理服务器回复的版本号不符合 Socks5 协议标准文档的规定，导致 Socks5 代理连接失败。请检查代理服务器是否存在异常。  
SOCKS5_FORMAT_ERROR| 2| 代理服务器回复的格式错误不符合 Socks5 协议标准文档的规定，导致 Socks5 代理连接失败。请检查代理服务器是否存在异常。  
SOCKS5_INVALID_VALUE| 3| 代理服务器回复的字段值不符合 Socks5 协议标准文档的规定，导致 Socks5 代理连接失败。请检查代理服务器是否存在异常。  
SOCKS5_USER_PASS_NOT_GIVEN| 4| 未提供代理服务器的用户名及密码，导致 Socks5 代理连接失败。请重新调用 setLocalProxy，在设置本地代理时填入用户名和密码。  
SOCKS5_TCP_CLOSED| 5| TCP 关闭，导致 Socks5 代理连接失败。请检查网络或者代理服务器是否存在异常。  
HTTP_TUNNEL_FAILED| 6| Http 隧道代理错误。请检查 Http 隧道代理服务器或者网络是否存在异常。  
## MuteState 
类型：enum
音视频流的发送/播放状态
属性| 值| 描述  
---|---|---  
MUTE_STATE_OFF| 0| 发送  
MUTE_STATE_ON| 1| 停止发送  
## AudioPlaybackDevice 
类型：enum
音频播放设备 音频设备变化时 SDK 通过 onAudioPlaybackDeviceChanged 回调当前音频播放设备。
属性| 值| 描述  
---|---|---  
AUDIO_PLAYBACK_DEVICE_HEADSET| 0| 有线耳机  
AUDIO_PLAYBACK_DEVICE_EARPIECE| 1| 听筒  
AUDIO_PLAYBACK_DEVICE_SPEAKERPHONE| 2| 扬声器  
AUDIO_PLAYBACK_DEVICE_HEADSET_BLUETOOTH| 3| 蓝牙耳机  
AUDIO_PLAYBACK_DEVICE_HEADSET_USB| 4| USB 设备  
## MediaDeviceType 
类型：enum
媒体设备类型
属性| 值| 描述  
---|---|---  
MEDIA_DEVICE_TYPE_AUDIO_UNKNOWN| 0| 未知设备  
MEDIA_DEVICE_TYPE_AUDIO_RENDER_DEVICE| 1| 音频渲染设备  
MEDIA_DEVICE_TYPE_AUDIO_CAPTURE_DEVICE| 2| 音频采集设备  
MEDIA_DEVICE_TYPE_VIDEO_CAPTURE_DEVICE| 4| 视频采集设备  
MEDIA_DEVICE_TYPE_SCREEN_VIDEO_CAPTURE_DEVICE| 5| 屏幕视频设备  
MEDIA_DEVICE_TYPE_SCREEN_AUDIO_CAPTURE_DEVICE| 6| 屏幕音频设备  
ByteRTCMediaDeviceTypeAudioUnknown| 7| 未知音频设备  
ByteRTCMediaDeviceTypeAudioRenderDevice| 8| 音频渲染设备类型  
ByteRTCMediaDeviceTypeAudioCaptureDevice| 9| 音频采集设备类型  
ByteRTCMediaDeviceTypeVideoCaptureDevice| 11| -  
ByteRTCMediaDeviceTypeScreenVideoCaptureDevice| 12| 屏幕流视频设备  
ByteRTCMediaDeviceTypeScreenAudioCaptureDevice| 13| 屏幕流音频设备  
## AudioMixingState 
类型：enum
音频混音文件播放状态。
属性| 值| 描述  
---|---|---  
AUDIO_MIXING_STATE_PRELOADED| 0| 混音已加载  
AUDIO_MIXING_STATE_PLAYING| 1| 混音正在播放  
AUDIO_MIXING_STATE_PAUSED| 2| 混音暂停  
AUDIO_MIXING_STATE_STOPPED| 3| 混音停止  
AUDIO_MIXING_STATE_FAILED| 4| 混音播放失败  
AUDIO_MIXING_STATE_FINISHED| 5| 混音播放结束  
AUDIO_MIXING_STATE_PCM_ENABLED| 6| 准备 PCM 混音  
AUDIO_MIXING_STATE_PCM_DISABLED| 7| PCM 混音播放结束  
## AudioMixingError 
类型：enum
混音错误码。
属性| 值| 描述  
---|---|---  
AUDIO_MIXING_ERROR_OK| 0| 正常  
AUDIO_MIXING_ERROR_PRELOAD_FAILED| 1| 预加载失败。找不到混音文件或者文件长度超出 20s  
AUDIO_MIXING_ERROR_START_FAILED| 2| 混音开启失败。找不到混音文件或者混音文件打开失败  
AUDIO_MIXING_ERROR_SET_POSITION_FAILED| 3| 设置混音文件的播放位置出错  
AUDIO_MIXING_ERROR_LOAD_CONFLICT| 4| 播放的文件与预加载的文件不一致。请先使用 unloadAudioMixing 卸载此前的文件。  
AUDIO_MIXING_ERROR_ID_NOT_FOUND| 5| 混音 ID 异常  
AUDIO_MIXING_ERROR_INVALID_VOLUME| 6| 音量参数不合法，仅支持设置的音量值为[0, 400]  
AUDIO_MIXING_ERROR_ID_TYPE_NOT_MATCH| 7| 不支持此混音类型。  
AUDIO_MIXING_ERROR_ID_TYPE_INVALID_PITCH| 8| 设置混音文件的音调不合法  
AUDIO_MIXING_ERROR_INVALID_AUDIO_TRACK| 9| 设置混音文件的音轨不合法  
AUDIO_MIXING_ERROR_IS_STARTING| 10| 混音文件正在启动中  
AUDIO_MIXING_ERROR_INVALID_PLAYBACK_SPEED| 11| 设置混音文件的播放速度不合法  
ByteRTCAudioMixingErrorIdNotFound| 12| 混音 ID 异常  
ByteRTCAudioMixingErrorInValidVolume| 13| 音量参数不合法，仅支持设置的音量值为[0, 400]  
ByteRTCAudioMixingErrorIdTypeNotMatch| 14| 不支持此混音类型。  
ByteRTCAudioMixingErrorInValidPitch| 15| 设置混音文件的音调不合法  
ByteRTCAudioMixingErrorInValidAudioTrack| 16| 设置混音文件的音轨不合法  
ByteRTCAudioMixingErrorIsStarting| 17| 混音文件正在启动中  
ByteRTCAudioMixingErrorInValidPlaybackSpeed| 18| 设置混音文件的播放速度不合法  
</details>

---
#### **第四部分：附录与法律合规**

这部分内容虽然不直接涉及代码编写，但对于应用的发布和长期运营至关重要。

**4.1 开源软件合规声明**
您的应用如果需要在全球发布，必须了解并遵循veRTC SDK所使用的开源软件的合规要求。

---
<details>