实时音视频
    产品动态
        发版说明
            客户端 SDK (https://www.volcengine.com/docs/6348/110078)
            服务端 OpenAPI (https://www.volcengine.com/docs/6348/116363)
            其他 (https://www.volcengine.com/docs/6348/1185640)
    产品简介
        什么是实时音视频 (https://www.volcengine.com/docs/6348/66812)
        功能特性 (https://www.volcengine.com/docs/6348/1392574)
        基础概念 (https://www.volcengine.com/docs/6348/70120)
        使用限制 (https://www.volcengine.com/docs/6348/257549)
        SDK 概览 (https://www.volcengine.com/docs/6348/1162272)
    快速入门
        开通服务 (https://www.volcengine.com/docs/6348/69865)
        跑通示例项目
            Android (https://www.volcengine.com/docs/6348/70129)
            iOS (https://www.volcengine.com/docs/6348/70128)
        实现音视频通话
            Android (https://www.volcengine.com/docs/6348/70134)
            iOS (Swift) (https://www.volcengine.com/docs/6348/1181844)
            React Native (https://www.volcengine.com/docs/6348/1455709)
    实时对话式 AI
        方案集成
            方案集成前置准备 (https://www.volcengine.com/docs/6348/1315561)
       体验进阶
            实时对话式 AI 字幕 (https://www.volcengine.com/docs/6348/1337284)
            控制播放内容 (https://www.volcengine.com/docs/6348/1350596)
            Function Calling（流式返回结果） (https://www.volcengine.com/docs/6348/1554654)
            Function Calling（非流式返回结果） (https://www.volcengine.com/docs/6348/1359441)
            接入第三方大模型或 Agent (https://www.volcengine.com/docs/6348/1399966)
            视觉理解能力 (https://www.volcengine.com/docs/6348/1408245)
            接收状态变化消息 (https://www.volcengine.com/docs/6348/1415216)
            自定义语音播放 (https://www.volcengine.com/docs/6348/1449206)
            自定义大模型上下文 (https://www.volcengine.com/docs/6348/1511926)
            打断智能体 (https://www.volcengine.com/docs/6348/1511927)
            配置对话触发模式 (https://www.volcengine.com/docs/6348/1544164)
            大模型上下文管理 (https://www.volcengine.com/docs/6348/1581711)
            语音识别配置 (https://www.volcengine.com/docs/6348/1581712)
            语音合成配置 (https://www.volcengine.com/docs/6348/1581713)
            大模型配置 (https://www.volcengine.com/docs/6348/1581714)
        服务端 OpenAPI
            启动智能体 StartVoiceChat (https://www.volcengine.com/docs/6348/1558163)
            更新智能体 UpdateVoiceChat (https://www.volcengine.com/docs/6348/1404671)
            关闭智能体 StopVoiceChat (https://www.volcengine.com/docs/6348/1404672)
        对话式 AI FAQ
            对话式 AI 功能咨询 (https://www.volcengine.com/docs/6348/1568689)
            对话式 AI 如何实现联网能力？ (https://www.volcengine.com/docs/6348/1557770)
            对话式 AI 如何接入知识库 RAG？ (https://www.volcengine.com/docs/6348/1557771)
            如何提升语音识别准确性？ (https://www.volcengine.com/docs/6348/1563620)
            如何提升多语言 AI 交互体验 (https://www.volcengine.com/docs/6348/1593348)
            智能体未进房或未正常工作？ (https://www.volcengine.com/docs/6348/1557772)
    开发指南
        应用管理
            创建并管理应用 (https://www.volcengine.com/docs/6348/70064)
            通过 BusinessId 细分配置下发 (https://www.volcengine.com/docs/6348/70135)
        鉴权
            管理 AppKey (https://www.volcengine.com/docs/6348/128824)
            使用 Token 完成鉴权 (https://www.volcengine.com/docs/6348/70121)
        音频管理
            设置音频属性 (https://www.volcengine.com/docs/6348/97706)
            播放音效文件 (https://www.volcengine.com/docs/6348/1178326)
            使用混音功能 (https://www.volcengine.com/docs/6348/70141)
            美声和音频降噪 (https://www.volcengine.com/docs/6348/1178327)
            音频音量 (https://www.volcengine.com/docs/6348/81268)
            空间音频 (https://www.volcengine.com/docs/6348/93903)
            范围语音 (https://www.volcengine.com/docs/6348/114727)
            Web 端音频降噪 (https://www.volcengine.com/docs/6348/148647)
            获取原始音频数据 (https://www.volcengine.com/docs/6348/1178324)
            自定义音频采集和渲染
                Native 端自定义音频采集和渲染 (https://www.volcengine.com/docs/6348/96197)
                Web 端自定义音视频采集 (https://www.volcengine.com/docs/6348/128914)
            自定义音频处理 (https://www.volcengine.com/docs/6348/80635)
        音视频设备管理
            移动端设置音频路由 (https://www.volcengine.com/docs/6348/117836)
            设备检测
                摄像头检测 (https://www.volcengine.com/docs/6348/1157759)
                麦克风检测 (https://www.volcengine.com/docs/6348/1157760)
                扬声器检测 (https://www.volcengine.com/docs/6348/1157761)
            设备权限处理 (https://www.volcengine.com/docs/6348/1157762)
            设备异常处理 (https://www.volcengine.com/docs/6348/1157763)
        通话前网络检测
            Native 端通话前网络质量检测 (https://www.volcengine.com/docs/6348/97200)
            Web 端通话前网络质量检测 (https://www.volcengine.com/docs/6348/79792)
        使用 Simulcast 功能
            音视频流自动回退和恢复【V3.58 及此前版本】 (https://www.volcengine.com/docs/6348/70137)
            推送多分辨率视频流【V3.58 及此前版本】 (https://www.volcengine.com/docs/6348/70139)
        订阅模式设置 (https://www.volcengine.com/docs/6348/129241)
        同一用户加入多个房间 (https://www.volcengine.com/docs/6348/196844)
        跨房间转发媒体流 (https://www.volcengine.com/docs/6348/104398)
        开启音频选路 (https://www.volcengine.com/docs/6348/113547)
        发布和订阅公共流 (https://www.volcengine.com/docs/6348/108930)
        转推直播
            关于转推直播 (https://www.volcengine.com/docs/6348/69821)
            客户端发起转推直播 (https://www.volcengine.com/docs/6348/69822)
            集成最佳实践 (https://www.volcengine.com/docs/6348/1163740)
        在防火墙限制下进行通话 (https://www.volcengine.com/docs/6348/146420)
        通过媒体内容发送补充信息 (https://www.volcengine.com/docs/6348/70140)
        实时通话字幕和翻译 (https://www.volcengine.com/docs/6348/1402679)
        音视频内容安全 (https://www.volcengine.com/docs/6348/1140990)
        输入在线媒体流 (https://www.volcengine.com/docs/6348/1256374)
        Web 端视频截图处理 (https://www.volcengine.com/docs/6348/1323361)
        SDK 日志文件配置 (https://www.volcengine.com/docs/6348/114721)
        连接状态提示 (https://www.volcengine.com/docs/6348/95376)
        通话质量监测 (https://www.volcengine.com/docs/6348/106866)
        监控台
            数据和统计 (https://www.volcengine.com/docs/6348/70063)
            查看通话数据 (https://www.volcengine.com/docs/6348/70136)
            诊断通话
                根据异常类型诊断（用户体验诊断） (https://www.volcengine.com/docs/6348/1407558)
                通过诊断工具深入诊断 (https://www.volcengine.com/docs/6348/125643)
            实时监控 (https://www.volcengine.com/docs/6348/160649)
            告警通知 (https://www.volcengine.com/docs/6348/135773)
            嵌入 RTC 监控台 (https://www.volcengine.com/docs/6348/173931)
        访问控制 (https://www.volcengine.com/docs/6348/1134900)
    最佳实践
        按需集成插件以缩小应用体积 (https://www.volcengine.com/docs/6348/1108726)
        通话打断和恢复 (https://www.volcengine.com/docs/6348/111590)
    客户端 API 参考
        Android 3.58
            概览 (https://www.volcengine.com/docs/6348/70079)
            API 详情 (https://www.volcengine.com/docs/6348/70080)
            回调 (https://www.volcengine.com/docs/6348/70081)
            错误码 (https://www.volcengine.com/docs/6348/70082)
            类型详情 (https://www.volcengine.com/docs/6348/70083)
        iOS 3.58
            概览 (https://www.volcengine.com/docs/6348/70084)
            API 详情 (https://www.volcengine.com/docs/6348/70086)
            回调 (https://www.volcengine.com/docs/6348/70087)
            错误码 (https://www.volcengine.com/docs/6348/70085)
            类型详情 (https://www.volcengine.com/docs/6348/70088)
        React Native 3.58
            概览 (https://www.volcengine.com/docs/6348/1390574)
            API 详情 (https://www.volcengine.com/docs/6348/1390575)
            回调 (https://www.volcengine.com/docs/6348/1390576)
            错误码 (https://www.volcengine.com/docs/6348/1390577)
            类型详情 (https://www.volcengine.com/docs/6348/1390578)
    API 参考 (服务端)
        服务端 OpenAPI 简介 (https://www.volcengine.com/docs/6348/69827)
        API 列表 (https://www.volcengine.com/docs/6348/1197615)
        调用方法
            请求结构 (https://www.volcengine.com/docs/6348/69828)
            公共参数 (https://www.volcengine.com/docs/6348/1178321)
            签名方法 (https://www.volcengine.com/docs/6348/69859)
            返回结构 (https://www.volcengine.com/docs/6348/1178322)
            公共错误码 (https://www.volcengine.com/docs/6348/70426)
        服务端回调
            开通消息通知服务 (https://www.volcengine.com/docs/6348/75110)
            回调格式参考 (https://www.volcengine.com/docs/6348/75124)
            消息事件参考 (https://www.volcengine.com/docs/6348/75125)
            接收消息通知回调 (https://www.volcengine.com/docs/6348/69820)
        房间管理
            封禁音视频流 BanUserStream (https://www.volcengine.com/docs/6348/1188354)
            解封音视频流 UnbanUserStream (https://www.volcengine.com/docs/6348/1188356)
            封禁房间&用户 BanRoomUser (https://www.volcengine.com/docs/6348/1188353)
            更新房间&用户封禁规则 UpdateBanRoomUserRule (https://www.volcengine.com/docs/6348/1188351)
            获取实时用户列表 GetRoomOnlineUsers (https://www.volcengine.com/docs/6348/1188355)
            查询用户状态 GetRoomUsersProperty (https://www.volcengine.com/docs/6348/1585134)
            限制 Token 发布权限 LimitTokenPrivilege (https://www.volcengine.com/docs/6348/1188352)
            （不推荐使用）移出用户 KickUser (https://www.volcengine.com/docs/6348/357742)
            （不推荐使用）解散房间 DismissRoom (https://www.volcengine.com/docs/6348/357815)       实时消息通信
            发送房间外点对点消息 SendUnicast (https://www.volcengine.com/docs/6348/1164061)
            发送房间内点对点消息 SendRoomUnicast (https://www.volcengine.com/docs/6348/1164062)
            发送房间内广播消息 SendBroadcast (https://www.volcengine.com/docs/6348/1164063)
            批量发送房间内点对点消息 BatchSendRoomUnicast (https://www.volcengine.com/docs/6348/1164064)        实时通话字幕和翻译
            开启实时字幕 StartSubtitle (https://www.volcengine.com/docs/6348/1402681)
            更新实时字幕 UpdateSubtitle (https://www.volcengine.com/docs/6348/1402683)
            停止实时字幕 StopSubtitle (https://www.volcengine.com/docs/6348/1402682)
        附录 (https://www.volcengine.com/docs/6348/1167931)
    常见问题
        基础功能咨询 (https://www.volcengine.com/docs/6348/1628958)
        集成相关
            参数赋值规范 (https://www.volcengine.com/docs/6348/70114)
            Android 集成常见问题 (https://www.volcengine.com/docs/6348/1155036)
            iOS 集成常见问题 (https://www.volcengine.com/docs/6348/1253477)
       视频云服务专用条款 (https://www.volcengine.com/docs/6348/69035)
        【实时音视频】服务等级协议 (https://www.volcengine.com/docs/6348/70241)
        开源软件合规声明 (https://www.volcengine.com/docs/6348/106886)
        veRTC（SDK）协议
            veRTC（SDK）隐私政策 (https://www.volcengine.com/docs/6348/97456)
            veRTC（SDK）开发者使用合规规范 (https://www.volcengine.com/docs/6348/109879)
        veRTC（应用）协议
            veRTC（应用）免责声明 (https://www.volcengine.com/docs/6348/68916)
            veRTC（应用）隐私政策 (https://www.volcengine.com/docs/6348/68918)
            veRTC（应用）用户协议 (https://www.volcengine.com/docs/6348/128955)
            veRTC（应用）第三方信息共享清单 (https://www.volcengine.com/docs/6348/133654)
            veRTC（应用）个人信息收集清单 (https://www.volcengine.com/docs/6348/133827)
            veRTC（应用）申请操作系统权限列表 (https://www.volcengine.com/docs/6348/155009)
        查看历史版本文档 (https://www.volcengine.com/docs/6348/70068)
        常见问题 (https://www.volcengine.com/docs/6348/914736)