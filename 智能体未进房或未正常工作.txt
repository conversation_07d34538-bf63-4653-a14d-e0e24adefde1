Title: 智能体未进房或未正常工作？--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1557772

Markdown Content:
智能体未进房或未正常工作？--实时音视频-火山引擎

===============

HTTP Code 返回 200

问题描述
----

调用 StartVoiceChat 返回 `200`，但智能体出现以下任一异常情况：

*   智能体未加入房间。
*   智能体加入房间后无响应，例如没有欢迎语。
*   智能体加入房间后有欢迎语，但后续语音沟通无反应。
*   智能体加入房间后，听不到智能体的声音。

可能原因
----

*   若你首次接入遇到该问题：

 导致上述现象的可能原因如下： 
    *   音频设备问题：比如本地麦克风或扬声器配置不当或故障。
    *   服务配置问题：ASR、TTS、LLM 等服务未开通或参数配置错误。
    *   接口参数设置问题：StartVoiceChat 接口缺少必填参数、填写错误或参数大小写错误。
    *   权限问题：跨服务授权未开启，RTC 没有权限调用其他 AI 服务。

*   若之前智能体运行正常，你再次调用 StartVoiceChat 接口时遇到该问题：

 请查看 ASR、TTS、LLM 等服务试用或购买额度是否已用完，或已达限额。

问题排查
----

若你首次接入遇到该问题，可以参考以下步骤进行排查：

### 步骤1：检查音频设备

进入[实时对话式 AI Web 体验 Demo](https://demo.volcvideo.com/aigc/login?from=doc)进行 AI 对话：

*   如果无法和智能体进行正常对话，则可能是音频输入设备问题，可按照如下方法排查： 
    *   Web 端：请参看 [Web 排查无声问题](https://www.volcengine.com/docs/6348/1250198)。
    *   Native 端：请参看 [Native 排查无声问题](https://www.volcengine.com/docs/6348/114298)。

*   如果可以和智能体进行正常对话，则可能是智能体接口设置问题，请继续执行后续排查步骤。

### 步骤2：检查跨服务授权

前往[跨服务授权](https://console.volcengine.com/rtc/aigc/iam)查看是否已开通跨服务授权。若显示未开通可按照提示一键开通。

![Image 4: alt](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_dc1fc2a1f7a279327362953670402fa3.png)

### 步骤3：检查智能体配置

1.   请确保账号已开通 ASR、TTS、LLM 服务，正确获取并配置了对应核心参数。

注意

*   若 ASR、TTS 或 LLM 使用的是火山引擎的服务，请检查是否在使用免费版。如在使用免费版请前往控制台确认免费版额度是否用完。如已开通正式版，请确认是否购买资源包。
*   详细参数说明，请参考[StartVoiceChat](https://www.volcengine.com/docs/6348/1558163)。

可使用[无代码跑通 Demo](https://console.volcengine.com/rtc/guide?from=doc&projectName=default) 快速检验以下核心参数是否正确填写。如无代码跑通 Demo 无法正常运行，则证明参数填写错误，请对参数值进行逐一排查。

 如参数均正确，但智能体仍无法正常运行，请前往[使用的在线推理点](https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint?config=%7B%7D)模型限流是否设置为 `0`。

| 服务名称 | 核心参数 |
| --- | --- |
| ASR | 火山引擎流式语音识别 | * AppId：开通火山引擎流式语音识别服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/16?)获取。 * Cluster：开通的流式语音识别服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/16?)开通服务后获取。 |
| 火山引擎流式语音识别大模型 | * AppId：开通火山引擎流式语音识别大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)获取。 * AccessToken：与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)获取。 |
| TTS | 火山引擎语音合成 | * appid：开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)获取。 * cluster：开通的语音合成服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)开通服务后获取。 * voice_type：已开通音色对应的音色种类（Voice_type）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)购买音色后获取。 |
|  |
| 火山引擎语音合成大模型流式输入流式输出 | * appid：开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)获取。 * token：与开通流式语音识别大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)获取。 * voice_type：已开通音色对应的音色种类（Voice_type）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)购买音色后获取。 |
| 火山引擎声音复刻大模型非流式输入流式输出 | * appid：开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 * cluster：开通的声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)开通服务后获取。 * voice_type：声音复刻声音 ID。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 |
| 火山引擎声音复刻大模型流式输入流式输出 | * appid：开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 * cluster：开通的声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)开通服务后获取。 * voice_type：声音复刻声音 ID。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 |
| MiniMax | * Authorization：API 密钥。前往 [Minimax 账户管理-接口密钥](https://platform.minimaxi.com/login)获取。 * Groupid：用户所属组 ID。前往 [Minimax 账号信息-基本信息](https://platform.minimaxi.com/login)获取。 |
| LLM | 火山方舟平台 | 自定义推理点 EndPointID 或智能体应用 ID。前往[火山方舟控制台](https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint?config=%7B%7D)获取。仅支持自定义推理接入点，不支持预置推理接入点。创建应用时仅支持使用自定义推理接入点，不支持预置推理接入点。 |
| Coze 平台 | * BotId：Coze 智能体 ID。可前往你需要调用的智能体开发页面获取。开发页面 URL 中 bot 参数后的数字即智能体ID。例如开发页面 URL 为：`https://www.coze.cn/space/341/bot/73428668`，则 `BotId` 为 `73428668`。 智能体必须已发布为 API。 * APIKey：Coze 访问密钥。你可以生成[个人访问令牌](https://www.coze.cn/open/oauth/pats)以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考[OAuth 应用管理](https://www.coze.cn/open/docs/developer_guides/oauth_apps)。 |
| 第三方大模型 | URL：第三方大模型 URL。 |

1.   检查 StartVoiceChat 接口请求参数是否正确并完整填写。

*   确保所有必填参数是否填写。
*   检查以下参数的规范性： 
    *   RoomId、TaskId、TargetUserId、UserId：是否按照[参数赋值规范填写](https://www.volcengine.com/docs/6348/70114)，不得出现中文字符。
    *   RoomId：是否与房间内客户端 SDK 进房时的使用的 RoomId 保持一致
    *   TargetUserId：是否与房间内客户端 SDK 进房时使用的 UserId 保持一致。

若按照以上步骤排查后智能体依然无法正常工作，请联系[技术支持](https://console.volcengine.com/workorder/create?step=2&SubProductID=P00000081)提供 OpenAPI 接口请求的 RequestId 进行排查。

HTTP Code 返回 ！200

调用 `StartVoiceChat` 返回 `！200` 状态码时，如 `401`，请查看对应报错进行进行处理。

 例如报错信息提示 `InvalidAuthorization`，则证明使用 Token 无效。可前往[控制台](https://console.volcengine.com/rtc/aigc/listRTC)**Token 校验**检查 Token 是否有效。
