# Agent API 编码规范文档

## 总览

本文档为'心桥'项目 `apps/agent-api` 定义了核心编码规范，旨在确保代码一致性、可维护性和质量。规范基于 PEP 8、FastAPI 最佳实践、Agno 框架以及火山引擎 LLM 集成规范。

## 1. Python 编码规范 (PEP 8)

- **行长度**: 不超过 120 字符。
- **缩进**: 使用 4 个空格。
- **导入顺序**:
    1.  标准库 (e.g., `typing`, `enum`, `os`)
    2.  第三方库 (e.g., `fastapi`, `pydantic`, `openai`)
    3.  本地应用 (e.g., `from agents.selector import ...`)
- **命名规范**:
    - `snake_case` 用于变量和函数。
    - `PascalCase` 用于类名。
    - `UPPER_CASE` 用于常量。

## 2. 环境变量配置规范

### 配置文件结构

注：Uvicorn测试请使用8003端口,在conda虚拟环境下运行命令或测试代码，默认是conda activate xinqiao-py312

项目使用根目录的 `.env` 文件(读取不了可以读取1.env，内容和.env一致)存储所有环境变量，结构如下：

```python
# ✅ 正确: 环境变量访问模式
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Supabase 配置
    supabase_url: str = os.environ.get("SUPABASE_URL")
    supabase_anon_key: str = os.environ.get("SUPABASE_ANON_KEY")
    supabase_service_role_key: str = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    database_url: str = os.environ.get("DATABASE_URL")
    
    # 火山引擎 LLM 配置
    volcano_llm_app_key: str = os.environ.get("VOLCANO_LLM_APP_KEY")
    volcano_llm_base_url: str = "https://ark.cn-beijing.volces.com/api/v3"
    volcano_llm_endpoint_id: str = os.environ.get("VOLCANO_LLM_ENDPOINT_ID")
    
    # 火山引擎其他服务
    volcano_access_key_id: str = os.environ.get("VOLCANO_ACCESS_KEY_ID")
    volcano_secret_access_key: str = os.environ.get("VOLCANO_SECRET_ACCESS_KEY")
    volcano_rtc_app_id: str = os.environ.get("VOLCANO_RTC_APP_ID")
    volcano_rtc_app_key: str = os.environ.get("VOLCANO_RTC_APP_KEY")
    
    # 安全配置
    secret_key: str = os.environ.get("SECRET_KEY")
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# 创建全局设置实例
settings = Settings()
```

### 环境变量访问规范

- **必须使用**: `os.environ.get()` 或 `pydantic_settings.BaseSettings`
- **禁止硬编码**: 任何敏感信息（API密钥、数据库连接等）
- **默认值**: 为非敏感配置提供合理的默认值
- **类型注解**: 所有配置项必须有明确的类型注解

## 3. 火山引擎 LLM 集成规范

### 基础 LLM 客户端配置

```python
# ✅ 正确: 火山引擎 LLM 客户端初始化
import os
from openai import OpenAI

def create_volcano_llm_client() -> OpenAI:
    """创建火山引擎 LLM 客户端"""
    return OpenAI(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("VOLCANO_LLM_APP_KEY"),
    )

# 全局客户端实例
volcano_llm_client = create_volcano_llm_client()
```

### 标准推理调用模式

```python
# ✅ 正确: 非流式推理调用
def get_llm_response(messages: list[dict], model_id: str = None) -> str:
    """获取 LLM 响应"""
    model_id = model_id or os.environ.get("VOLCANO_LLM_ENDPOINT_ID")
    
    try:
        completion = volcano_llm_client.chat.completions.create(
            model=model_id,
            messages=messages,
            temperature=0.7,
            max_tokens=2000,
        )
        return completion.choices[0].message.content
    except Exception as e:
        logger.error(f"LLM 推理失败: {e}")
        raise

# ✅ 正确: 流式推理调用
def get_llm_stream_response(messages: list[dict], model_id: str = None):
    """获取 LLM 流式响应"""
    model_id = model_id or os.environ.get("VOLCANO_LLM_ENDPOINT_ID")
    
    try:
        stream = volcano_llm_client.chat.completions.create(
            model=model_id,
            messages=messages,
            stream=True,
            temperature=0.7,
            max_tokens=2000,
        )
        
        for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
    except Exception as e:
        logger.error(f"LLM 流式推理失败: {e}")
        raise
```

## 4. FastAPI 架构规范

### 依赖注入

使用 `Annotated` 和 `Depends` 进行依赖注入，并为常用依赖创建可重用的类型别名。

```python
# ✅ 正确: 可重用依赖定义
from typing import Annotated
from fastapi import Depends
from sqlalchemy.orm import Session
from db.session import get_db

# 数据库依赖
DatabaseDep = Annotated[Session, Depends(get_db)]

# 设置依赖
SettingsDep = Annotated[Settings, Depends(lambda: settings)]

# LLM 客户端依赖
LLMClientDep = Annotated[OpenAI, Depends(create_volcano_llm_client)]

@router.post("/agents")
async def create_agent(
    agent_data: AgentCreateRequest, 
    db: DatabaseDep,
    settings: SettingsDep,
    llm_client: LLMClientDep
):
    # ...
    pass
```

### 路由与错误处理

```python
# ✅ 正确: 统一错误处理
from fastapi import HTTPException, status
from typing import Any
import logging

logger = logging.getLogger(__name__)

@router.post("/agents/{agent_id}/runs")
async def create_agent_run(
    agent_id: str, 
    body: RunRequest,
    db: DatabaseDep,
    llm_client: LLMClientDep
):
    try:
        agent = get_agent(agent_id=agent_id)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found"
            )
        
        # 使用 LLM 进行推理
        response = await process_agent_run(agent, body, llm_client)
        return response
        
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
```

## 5. Pydantic 模型规范

```python
# ✅ 正确: Pydantic 模型定义
from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any

class AgentRunRequest(BaseModel):
    """代理运行请求模型"""
    message: str = Field(..., min_length=1, description="用户消息")
    stream: bool = Field(True, description="是否流式返回")
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    
    @field_validator('message')
    @classmethod
    def validate_message(cls, v: str) -> str:
        if not v.strip():
            raise ValueError('消息不能为空')
        return v.strip()

class AgentRunResponse(BaseModel):
    """代理运行响应模型"""
    message: str = Field(..., description="代理响应")
    run_id: str = Field(..., description="运行ID")
    usage: Optional[Dict[str, Any]] = Field(None, description="使用情况")
    
    model_config = {"from_attributes": True}
```

## 6. 记忆服务 (MemoryService) 使用规范

随着架构升级，记忆管理已从Agno内部解耦至一个独立的可插拔服务。所有与对话记忆相关的操作，**必须**通过 `MemoryService` 抽象层进行。

### 核心原则
1.  **禁止直接实例化**: 严禁在业务代码中直接创建 `ZepMemoryService` 或 `Mem0MemoryService` 实例。
2.  **依赖注入优先**: **必须**通过FastAPI的依赖注入系统和 `get_memory_service` 工厂来获取服务实例。
3.  **面向接口编程**: 所有代码只应依赖于 `MemoryService` 抽象基类，以确保实现的可替换性。

### 依赖注入配置
```python
# ✅ 正确: MemoryService 依赖注入配置
# 在 /api/services/memory_service.py 或类似文件中
import os
from abc import ABC, abstractmethod
# ...导入Zep/Mem0客户端

class MemoryService(ABC):
    @abstractmethod
    async def add_memory(self, session_id: str, user_message: str, ai_message: str):
        pass

    @abstractmethod
    async def search_memory(self, session_id: str, query: str) -> list:
        pass

# ...ZepMemoryService 和 Mem0MemoryService 的实现...

def get_memory_service() -> MemoryService:
    """工厂函数：根据环境变量返回具体的记忆服务实例"""
    provider = os.environ.get("MEMORY_PROVIDER", "zep").lower()
    if provider == "zep":
        return ZepMemoryService() # 假设已配置好客户端
    elif provider == "mem0":
        return Mem0MemoryService() # 假设已配置好客户端
    else:
        raise ValueError(f"Unsupported memory provider: {provider}")

# 在 /api/deps.py 或类似文件中
from typing import Annotated
from fastapi import Depends

MemoryServiceDep = Annotated[MemoryService, Depends(get_memory_service)]
```

### 在`ChatOrchestrationService`中使用
```python
# ✅ 正确: 在编排服务中使用 MemoryService
# file: /api/services/chat_orchestration_service.py

class ChatOrchestrationService:
    def __init__(self, memory_service: MemoryServiceDep, ...):
        self.memory_service = memory_service
        # ...

    async def handle_message(self, user_message: str, session_id: str):
        # 1. 检索记忆
        memories = await self.memory_service.search_memory(session_id, user_message)

        # 2. 构建提示并调用LLM...
        # ...

        # 3. 添加新记忆 (通常在后台任务中)
        background_tasks.add_task(
            self.memory_service.add_memory, 
            session_id, 
            user_message, 
            ai_response
        )
        # ...
```

## 7. Supabase 集成规范

### 数据库连接配置

```python
# ✅ 正确: Supabase 客户端配置
from supabase import create_client, Client
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Supabase 客户端（用于认证和实时功能）
def create_supabase_client() -> Client:
    """创建 Supabase 客户端"""
    url = os.environ.get("SUPABASE_URL")
    key = os.environ.get("SUPABASE_ANON_KEY")
    return create_client(url, key)

# SQLAlchemy 引擎（用于数据库操作）
def create_db_engine():
    """创建数据库引擎"""
    database_url = os.environ.get("DATABASE_URL")
    engine = create_engine(database_url)
    return engine

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=create_db_engine())

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### 认证集成

```python
# ✅ 正确: JWT 认证处理
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt
import os

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证 JWT Token"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            os.environ.get("SUPABASE_JWT_SECRET"),
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

# 认证依赖
AuthDep = Annotated[dict, Depends(verify_token)]
```

## 8. 错误处理与日志规范

### 统一异常处理

```python
# ✅ 正确: 自定义异常类
class AgentAPIError(Exception):
    """Agent API 基础异常"""
    pass

class AgentNotFoundError(AgentAPIError):
    """代理不存在异常"""
    pass

class LLMError(AgentAPIError):
    """LLM 服务异常"""
    pass

class DatabaseError(AgentAPIError):
    """数据库操作异常"""
    pass

# 全局异常处理器
@app.exception_handler(AgentAPIError)
async def handle_agent_api_error(request: Request, exc: AgentAPIError):
    return JSONResponse(
        status_code=400,
        content={"detail": str(exc), "type": exc.__class__.__name__}
    )
```

### 日志配置

```python
# ✅ 正确: 日志配置
import logging
import sys

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('agent-api.log')
        ]
    )

# 在应用启动时调用
setup_logging()
logger = logging.getLogger(__name__)
```

## 9. 安全性规范

### 敏感数据处理

```python
# ✅ 正确: 敏感数据脱敏
def sanitize_response(response: dict) -> dict:
    """脱敏响应数据"""
    sensitive_fields = ['api_key', 'secret_key', 'password', 'token']
    
    for field in sensitive_fields:
        if field in response:
            response[field] = "***"
    
    return response

# 日志脱敏
def log_safe_dict(data: dict, logger: logging.Logger):
    """安全记录字典数据"""
    safe_data = sanitize_response(data.copy())
    logger.info(f"Data: {safe_data}")
```

### 输入验证

```python
# ✅ 正确: 输入验证
from pydantic import validator
import re

class SecureRequest(BaseModel):
    user_input: str
    
    @validator('user_input')
    def validate_input(cls, v):
        # 防止 SQL 注入
        if re.search(r'(drop|delete|truncate|update|insert)\s+', v, re.IGNORECASE):
            raise ValueError("Potentially dangerous input detected")
        
        # 长度限制
        if len(v) > 10000:
            raise ValueError("Input too long")
        
        return v
```

## 10 最佳实践总结

### 必须遵循
1. **完整类型注解**：所有函数和方法必须有类型注解
2. **环境变量管理**：使用 `os.environ.get()` 或 `BaseSettings`
3. **火山引擎 LLM**：统一使用 OpenAI 兼容接口
4. **依赖注入**：使用 FastAPI 的 `Depends` 系统
5. **Pydantic 验证**：所有API数据模型使用 Pydantic
6. **记忆服务使用**: **必须**通过 `MemoryService` 抽象层与记忆系统交互。
7. **错误处理**：使用统一的异常处理机制
8. **日志记录**：所有关键操作必须记录日志

### 推荐实践
1. **代码复用**：创建可重用的依赖和服务
2. **配置外化**：使用环境变量和 `BaseSettings`
3. **异步优先**：优先使用 `async` 函数处理I/O密集型任务
4. **充分测试**：保证核心业务逻辑的测试覆盖率
5. **安全第一**：对所有用户输入进行验证和脱敏
6. **性能监控**：记录关键操作的性能指标
7. Uvicorn启动服务请使用8003端口或8003端口
8.在conda虚拟环境下运行命令或测试代码，默认是conda activate xinqiao-py312

### 禁止实践
1. **硬编码**：配置值（密钥、URL等）必须通过环境变量加载
2. **直接实例化记忆服务**: **禁止**直接创建`ZepMemoryService`或`Mem0MemoryService`。
3. **全局状态**：避免使用全局变量存储状态
4. **同步阻塞**：不要在异步函数中使用阻塞I/O操作
5. **裸异常捕获**：不要使用 `except Exception:` 捕获所有异常
6. **敏感数据泄露**：不要在日志中记录敏感信息
7. **跨域资源泄露**：确保数据库连接等资源正确释放
