 # 故事 1.8-Frontend: 应用设置模块

## 基本信息
- **故事编号**: 1.8-Frontend
- **故事标题**: 应用设置模块
- **Epic**: MVP - 用户个性化与应用管理
- **用户角色**: 前端开发者
- **优先级**: 中（P1 - 用户体验优化）
- **工作量估计**: 2-3 个工作日
- **依赖关系**: 1.1-Frontend（项目基础设置）, 1.1-Frontend-Permissions（权限管理）
- **Status**: Approved

## 故事描述

作为前端开发者，我需要实现完整的应用设置模块，**以便** 用户能够个性化配置应用行为、管理账户信息、调整可访问性选项，并获得帮助支持，确保应用满足不同用户的个性化需求。

## 验收标准

### AC1: 基础设置界面
- [ ] 实现设置主页面，提供分类清晰的设置选项
- [ ] 支持设置项的搜索和快速访问
- [ ] 实现设置变更的实时预览和保存
- [ ] 提供设置重置和恢复默认选项

### AC2: 外观和可访问性设置
- [ ] 实现主题切换（浅色/深色/自动）
- [ ] 支持字体大小调节（适老化需求）
- [ ] 提供高对比度模式切换
- [ ] 实现语言选择和本地化设置

### AC3: 通知和权限设置
- [ ] 集成权限管理界面（基于故事1.1-Frontend-Permissions）
- [ ] 实现通知类型的细粒度控制
- [ ] 支持勿扰模式和时间段设置
- [ ] 提供数据使用和隐私设置

### AC4: 账户和帮助支持
- [ ] 实现账户信息查看和编辑
- [ ] 提供应用使用教程和帮助文档
- [ ] 集成反馈和客服联系功能
- [ ] 实现关于页面和版本信息展示

## Tasks / Subtasks

### 第一阶段：设置基础架构 (1天)
- [ ] **设置路由和导航** (AC1)
  - 创建设置页面路由结构
  - 实现设置项分类和导航
  - 建立设置数据模型和状态管理
  - 实现设置变更的持久化存储

### 第二阶段：外观和无障碍设置 (1天)
- [ ] **主题和字体设置** (AC2)
  - 实现主题切换功能
  - 创建字体大小调节组件
  - 实现高对比度模式
  - 集成语言选择和切换

### 第三阶段：通知和权限集成 (1天)
- [ ] **权限和隐私设置** (AC3)
  - 集成权限管理界面
  - 实现通知类型控制
  - 创建勿扰模式设置
  - 实现数据使用偏好设置

### 第四阶段：账户和支持功能 (半天)
- [ ] **用户支持模块** (AC4)
  - 实现账户信息管理
  - 创建帮助和教程页面
  - 集成反馈和联系功能
  - 实现关于页面和版本展示

## Dev Notes

### 技术实现要求

基于现有技术栈的设置模块架构：

#### 设置状态管理
```typescript
// store/settings.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Theme = 'light' | 'dark' | 'auto';
type FontSize = 'small' | 'medium' | 'large' | 'extra-large';
type Language = 'zh-CN' | 'en-US';

interface SettingsState {
  // 外观设置
  theme: Theme;
  fontSize: FontSize;
  highContrast: boolean;
  language: Language;
  
  // 通知设置
  notificationsEnabled: boolean;
  reminderTypes: {
    medication: boolean;
    appointment: boolean;
    exercise: boolean;
    social: boolean;
  };
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  
  // 隐私设置
  dataCollection: boolean;
  analytics: boolean;
  crashReporting: boolean;
  
  // 用户偏好
  firstTime: boolean;
  onboardingCompleted: boolean;
  
  // Actions
  updateTheme: (theme: Theme) => void;
  updateFontSize: (size: FontSize) => void;
  updateHighContrast: (enabled: boolean) => void;
  updateNotificationsEnabled: (enabled: boolean) => void;
  updateReminderTypes: (settings: Partial<SettingsState['reminderTypes']>) => void;
  updateQuietHours: (settings: Partial<SettingsState['quietHours']>) => void;
  resetToDefaults: () => void;
}

const defaultSettings = {
  theme: 'auto' as Theme,
  fontSize: 'large' as FontSize, // 适老化默认大字体
  highContrast: false,
  language: 'zh-CN' as Language,
  
  notificationsEnabled: true,
  reminderTypes: {
    medication: true,
    appointment: true,
    exercise: true,
    social: false,
  },
  quietHours: {
    enabled: false,
    startTime: '22:00',
    endTime: '08:00',
  },
  
  dataCollection: false, // 隐私优先
  analytics: false,
  crashReporting: true,
  
  firstTime: true,
  onboardingCompleted: false,
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      ...defaultSettings,

      // Actions
      updateTheme: (theme) => set({ theme }),
      updateFontSize: (fontSize) => set({ fontSize }),
      updateHighContrast: (highContrast) => set({ highContrast }),
      updateNotificationsEnabled: (enabled) => set({ notificationsEnabled: enabled }),
      updateReminderTypes: (settings) => 
        set((state) => ({
          reminderTypes: { ...state.reminderTypes, ...settings },
        })),
      updateQuietHours: (settings) => 
        set((state) => ({
          quietHours: { ...state.quietHours, ...settings },
        })),
      resetToDefaults: () => set(defaultSettings),
    }),
    {
      name: 'app-settings',
    }
  )
);
```

#### 设置页面结构
```typescript
// app/(app)/settings.tsx
import React from 'react';
import { ScrollView, View } from 'react-native';
import { router } from 'expo-router';
import { SettingsSection, SettingsItem } from '@/components/settings';
import { useSettingsStore } from '@/store/settings';

// Helper functions to get display names (assuming they exist elsewhere)
const getThemeDisplayName = (theme: string) => ({ light: '浅色', dark: '深色', auto: '自动' }[theme] || '自动');
const getFontSizeDisplayName = (size: string) => ({ small: '标准', medium: '较大', large: '大', 'extra-large': '超大' }[size] || '大');

export default function SettingsScreen() {
  const { theme, fontSize, notificationsEnabled, highContrast } = useSettingsStore(
    (s) => ({
      theme: s.theme,
      fontSize: s.fontSize,
      notificationsEnabled: s.notificationsEnabled,
      highContrast: s.highContrast,
    })
  );

  const updateHighContrast = useSettingsStore((s) => s.updateHighContrast);
  const updateNotificationsEnabled = useSettingsStore((s) => s.updateNotificationsEnabled);

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="p-4 space-y-6">
        
        {/* 外观设置 */}
        <SettingsSection title="外观设置" icon="palette">
          <SettingsItem
            title="主题"
            subtitle={getThemeDisplayName(theme)}
            onPress={() => router.push('/settings/theme')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="字体大小"
            subtitle={getFontSizeDisplayName(fontSize)}
            onPress={() => router.push('/settings/font-size')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="高对比度"
            subtitle="提高文字可读性"
            rightIcon="toggle"
            toggleValue={highContrast}
            onToggle={updateHighContrast}
          />
        </SettingsSection>

        {/* 通知设置 */}
        <SettingsSection title="通知设置" icon="bell">
          <SettingsItem
            title="推送通知"
            subtitle="接收重要提醒"
            rightIcon="toggle"
            toggleValue={notificationsEnabled}
            onToggle={updateNotificationsEnabled}
          />
          <SettingsItem
            title="提醒类型"
            subtitle="选择接收的提醒类型"
            onPress={() => router.push('/settings/notifications')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="勿扰时间"
            subtitle="设置免打扰时段"
            onPress={() => router.push('/settings/quiet-hours')}
            rightIcon="chevron-right"
          />
        </SettingsSection>

        {/* 隐私与权限 */}
        <SettingsSection title="隐私与权限" icon="shield">
          <SettingsItem
            title="应用权限"
            subtitle="管理麦克风、通知等权限"
            onPress={() => router.push('/settings/permissions')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="数据使用"
            subtitle="控制数据收集偏好"
            onPress={() => router.push('/settings/privacy')}
            rightIcon="chevron-right"
          />
        </SettingsSection>

        {/* 账户与支持 */}
        <SettingsSection title="账户与支持" icon="user">
          <SettingsItem
            title="账户信息"
            subtitle="查看和编辑个人信息"
            onPress={() => router.push('/settings/account')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="使用帮助"
            subtitle="获取使用指导和常见问题"
            onPress={() => router.push('/settings/help')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="意见反馈"
            subtitle="告诉我们您的建议"
            onPress={() => router.push('/settings/feedback')}
            rightIcon="chevron-right"
          />
          <SettingsItem
            title="关于应用"
            subtitle="版本信息和开发团队"
            onPress={() => router.push('/settings/about')}
            rightIcon="chevron-right"
          />
        </SettingsSection>

      </View>
    </ScrollView>
  );
}
```

#### 适老化设置组件
```typescript
// components/settings/font-size-selector.tsx
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useSettingsStore } from '@/store/settings';

type FontSizeKey = 'small' | 'medium' | 'large' | 'extra-large';

const FONT_SIZES: { key: FontSizeKey, label: string, preview: string }[] = [
  { key: 'small', label: '标准', preview: '16px' },
  { key: 'medium', label: '较大', preview: '18px' },
  { key: 'large', label: '大字体', preview: '20px' },
  { key: 'extra-large', label: '超大字体', preview: '24px' }
];

export const FontSizeSelector: React.FC = () => {
  const fontSize = useSettingsStore((s) => s.fontSize);
  const updateFontSize = useSettingsStore((s) => s.updateFontSize);

  return (
    <View className="space-y-4">
      <Text className="text-lg font-semibold text-gray-900 mb-4">
        选择字体大小
      </Text>
      
      {FONT_SIZES.map((size) => (
        <Pressable
          key={size.key}
          onPress={() => updateFontSize(size.key)}
          className={`p-4 rounded-lg border-2 ${
            fontSize === size.key 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-200 bg-white'
          }`}
        >
          <View className="flex-row justify-between items-center">
            <View>
              <Text className="text-lg font-medium text-gray-900">
                {size.label}
              </Text>
              <Text className="text-sm text-gray-600">
                适合日常使用
              </Text>
            </View>
            <Text 
              className="text-gray-700"
              style={{ fontSize: parseInt(size.preview) }}
            >
              预览文字Aa
            </Text>
          </View>
        </Pressable>
      ))}
      
      <View className="mt-6 p-4 bg-amber-50 rounded-lg">
        <Text className="text-amber-800 text-sm">
          💡 提示：推荐使用"大字体"或"超大字体"以获得更好的阅读体验
        </Text>
      </View>
    </View>
  );
};
```

#### 主题切换实现
```typescript
// components/settings/theme-selector.tsx
import React from 'react';
import { View, Text, Pressable, useColorScheme } from 'react-native';
import { useSettingsStore } from '@/store/settings';

type ThemeKey = 'light' | 'dark' | 'auto';

const THEMES: { key: ThemeKey, label: string, description: string, icon: string }[] = [
  { 
    key: 'light', 
    label: '浅色模式', 
    description: '明亮清晰的界面',
    icon: '☀️'
  },
  { 
    key: 'dark', 
    label: '深色模式', 
    description: '护眼的深色界面',
    icon: '🌙'
  },
  { 
    key: 'auto', 
    label: '自动切换', 
    description: '跟随系统设置',
    icon: '🔄'
  }
];

export const ThemeSelector: React.FC = () => {
  const theme = useSettingsStore((s) => s.theme);
  const updateTheme = useSettingsStore((s) => s.updateTheme);
  const systemTheme = useColorScheme();

  return (
    <View className="space-y-4">
      <Text className="text-lg font-semibold text-gray-900 mb-4">
        选择主题
      </Text>
      
      {THEMES.map((themeOption) => (
        <Pressable
          key={themeOption.key}
          onPress={() => updateTheme(themeOption.key)}
          className={`p-4 rounded-lg border-2 ${
            theme === themeOption.key 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-200 bg-white'
          }`}
        >
          <View className="flex-row items-center space-x-4">
            <Text className="text-2xl">{themeOption.icon}</Text>
            <View className="flex-1">
              <Text className="text-lg font-medium text-gray-900">
                {themeOption.label}
              </Text>
              <Text className="text-sm text-gray-600">
                {themeOption.description}
              </Text>
              {themeOption.key === 'auto' && (
                <Text className="text-xs text-blue-600 mt-1">
                  当前系统: {systemTheme === 'dark' ? '深色' : '浅色'}
                </Text>
              )}
            </View>
          </View>
        </Pressable>
      ))}
    </View>
  );
};
```

### 适老化设计要求

#### 设置项可访问性
- **大按钮设计**：设置项高度 ≥ 60pt
- **清晰层级**：使用卡片和分组明确区分设置类别
- **状态反馈**：开关状态使用颜色+图标双重指示
- **操作确认**：重要设置变更提供确认对话框

#### 帮助和支持功能
```typescript
// components/settings/help-section.tsx
export const HelpSection: React.FC = () => {
  const helpTopics = [
    {
      title: '如何开始对话',
      description: '学习如何与AI助手进行语音或文字对话',
      icon: '💬',
      action: () => router.push('/help/conversation')
    },
    {
      title: '设置提醒',
      description: '如何创建和管理日常提醒',
      icon: '⏰',
      action: () => router.push('/help/reminders')
    },
    {
      title: '隐私设置',
      description: '了解如何保护您的隐私',
      icon: '🔒',
      action: () => router.push('/help/privacy')
    },
    {
      title: '常见问题',
      description: '查看最常见的问题和解答',
      icon: '❓',
      action: () => router.push('/help/faq')
    }
  ];

  return (
    <View className="space-y-4">
      {helpTopics.map((topic, index) => (
        <Pressable
          key={index}
          onPress={topic.action}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200"
        >
          <View className="flex-row items-center space-x-4">
            <Text className="text-2xl">{topic.icon}</Text>
            <View className="flex-1">
              <Text className="text-lg font-medium text-gray-900">
                {topic.title}
              </Text>
              <Text className="text-sm text-gray-600 mt-1">
                {topic.description}
              </Text>
            </View>
            <Text className="text-gray-400">›</Text>
          </View>
        </Pressable>
      ))}
    </View>
  );
};
```

### API集成点
- **获取设置**: `GET /api/v1/settings`
- **更新设置**: `PUT /api/v1/settings`
- **获取账户信息**: `GET /api/v1/users/me`
- **更新账户信息**: `PUT /api/v1/users/me`

## Testing

### 功能测试
- [ ] **设置持久化**: 验证设置变更能正确保存和恢复
- [ ] **主题切换**: 测试所有主题模式的正确应用
- [ ] **字体大小**: 验证字体变更在整个应用中生效
- [ ] **权限集成**: 测试权限设置页面的功能完整性

### 用户体验测试
- [ ] **老年用户可用性**: 验证设置界面对老年用户友好
- [ ] **无障碍功能**: 测试屏幕阅读器和辅助功能
- [ ] **帮助系统**: 验证帮助内容的清晰度和实用性
- [ ] **设置恢复**: 测试重置功能的可靠性

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 前端项目基础设置已完成（故事1.1-Frontend）
- [ ] 权限管理模块已实现（故事1.1-Frontend-Permissions）
- [ ] UI组件库已建立
- [ ] 帮助文档内容已准备

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过功能测试
- [ ] 设置模块在iOS和Android上正常工作
- [ ] 用户体验测试结果满足要求
- [ ] 无障碍功能测试通过

### 交付物 (Deliverables)
- [ ] **完整的设置模块**：包含所有设置页面和功能
- [ ] **设置组件库**：可复用的设置界面组件
- [ ] **帮助系统**：用户指导和支持功能
- [ ] **设置管理文档**：开发者使用指南

## 风险与缓解措施

### 主要风险
1. **设置复杂性**：过多的设置选项可能让老年用户感到困惑
2. **状态同步问题**：设置变更可能不能及时在全应用生效
3. **帮助内容过期**：帮助文档可能与实际功能不符
4. **重置风险**：用户可能意外重置所有设置

### 缓解措施
1. **简化设计**：按重要性分组，默认值适合大多数用户
2. **实时同步**：确保设置变更立即在应用中反映
3. **内容维护**：建立帮助内容的更新和维护流程
4. **操作确认**：重要操作提供二次确认

## 后续故事依赖关系

### 🔗 此故事完善以下功能：
- **所有现有功能**：为现有功能提供个性化配置选项
- **用户体验**：通过个性化设置提升整体用户满意度

### 📋 与其他模块的集成点：
- **权限管理**：集成权限设置界面
- **主题系统**：全局主题和字体控制
- **帮助支持**：用户教育和问题解决

## 相关文档引用
- [产品需求 - 易用性](../../prd/requirements.md#NFR1)
- [移动应用技术栈](../../architecture/mobile-app-tech-stack.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md)
- [UX设计指南](../../prd/ux-design.md)