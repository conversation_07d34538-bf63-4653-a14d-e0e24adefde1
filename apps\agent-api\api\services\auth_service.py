# api/services/auth_service.py
"""
认证服务实现 - 对应故事1.2-B的AC1
实现无感身份认证、JWT Token管理、设备指纹识别
"""
import hashlib
import json
import os
import time
import uuid
import base64
import hmac
import random
import struct
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass
from collections import OrderedDict

from supabase import create_client, Client
from jose import JWTError, jwt
from passlib.context import CryptContext

from api.settings import logger, settings
from api.utils.access_token import AccessToken, PrivPublishStream, PrivSubscribeStream

# 环境变量配置
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
SUPABASE_JWT_SECRET = os.getenv("SUPABASE_JWT_SECRET")

# JWT配置 - 使用settings而不是硬编码默认值
from api.settings import get_settings
_settings = get_settings()
JWT_SECRET_KEY = _settings.JWT_SECRET_KEY
JWT_ALGORITHM = _settings.JWT_ALGORITHM
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
JWT_REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))

DEVICE_FINGERPRINT_SECRET = os.getenv("DEVICE_FINGERPRINT_SECRET", "device-fingerprint-secret-key")
ANONYMOUS_USER_PREFIX = os.getenv("ANONYMOUS_USER_PREFIX", "anon_")

@dataclass
class DeviceInfo:
    """设备信息数据类"""
    device_id: str
    platform: str
    app_version: str

@dataclass
class AuthResult:
    """认证结果数据类"""
    user: Dict[str, Any]
    access_token: str
    refresh_token: str
    expires_in: int

class AuthService:
    """认证服务类"""

    def __init__(self):
        """初始化认证服务"""
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def generate_device_fingerprint(self, device_info: DeviceInfo) -> str:
        """
        生成设备指纹 - 实现架构师建议的加固算法
        使用device_id+platform+app_version+服务器端时间戳+随机盐的组合哈希
        """
        try:
            # 服务器端时间戳（按小时取整，确保短期内一致性）
            server_timestamp = int(time.time() // 3600) * 3600

            # 随机盐（基于设备信息生成，确保同设备一致性）
            salt_source = f"{device_info.device_id}_{device_info.platform}_{DEVICE_FINGERPRINT_SECRET}"
            random_salt = hashlib.md5(salt_source.encode()).hexdigest()[:8]

            # 组合指纹字符串
            fingerprint_data = f"{device_info.device_id}_{device_info.platform}_{device_info.app_version}_{server_timestamp}_{random_salt}"

            # 生成SHA256哈希
            device_fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()

            logger.info(f"Generated device fingerprint for device: {device_info.device_id}")
            return device_fingerprint

        except Exception as e:
            logger.error(f"Error generating device fingerprint: {e}")
            raise

    async def create_anonymous_user(self, device_info: DeviceInfo) -> AuthResult:
        """
        创建匿名用户 - AC1: 无感身份认证服务
        """
        try:
            logger.info(f"开始创建匿名用户 - DeviceID: {device_info.device_id}, Platform: {device_info.platform}")

            # 1. 生成设备指纹
            device_fingerprint = self.generate_device_fingerprint(device_info)
            logger.debug(f"生成设备指纹: {device_fingerprint[:16]}...")

            # 2. 检查是否已存在相同设备指纹的用户
            existing_user = await self._find_user_by_fingerprint(device_fingerprint)
            if existing_user:
                logger.info(f"找到现有用户 - UserID: {existing_user['id']}, DeviceFingerprint: {device_fingerprint[:8]}...")
                return await self._generate_auth_tokens(existing_user)

            # 3. 使用Supabase Admin API创建匿名用户（开发环境）
            # 生成唯一的匿名邮箱
            anonymous_email = f"{ANONYMOUS_USER_PREFIX}{uuid.uuid4().hex}@anonymous.dev"
            logger.debug(f"生成匿名邮箱: {anonymous_email}")

            auth_response = self.supabase.auth.admin.create_user({
                "email": anonymous_email,
                "email_confirm": True,  # 自动确认邮箱
                "user_metadata": {
                    "device_fingerprint": device_fingerprint,
                    "device_id": device_info.device_id,
                    "platform": device_info.platform,
                    "app_version": device_info.app_version,
                    "is_anonymous": True
                }
            })

            if not auth_response.user:
                raise Exception("Failed to create anonymous user in Supabase Auth")

            supabase_user_id = auth_response.user.id
            logger.info(f"Supabase Auth用户创建成功 - UserID: {supabase_user_id}")

            # 4. 在我方数据库中创建用户记录
            user_data = {
                "id": supabase_user_id,
                "device_fingerprint": device_fingerprint,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            logger.debug(f"准备插入用户记录: {user_data}")

            # 插入用户记录
            user_insert_result = self.supabase.table("users").insert(user_data).execute()
            if not user_insert_result.data:
                logger.error(f"插入用户记录失败 - UserID: {supabase_user_id}")
                raise Exception("Failed to create user record in database")

            created_user = user_insert_result.data[0]
            logger.info(f"用户记录创建成功 - UserID: {created_user['id']}")

            # 5. 创建初始用户画像
            await self._create_initial_user_profile(supabase_user_id)

            # 6. 生成JWT Token
            auth_result = await self._generate_auth_tokens(created_user)
            logger.info(f"JWT Token生成成功 - UserID: {supabase_user_id}")

            logger.info(f"匿名用户创建完成 - UserID: {supabase_user_id}")
            return auth_result

        except Exception as e:
            logger.error(f"创建匿名用户失败: {e}", exc_info=True)
            raise

    async def _find_user_by_fingerprint(self, device_fingerprint: str) -> Optional[Dict[str, Any]]:
        """根据设备指纹查找用户"""
        try:
            result = self.supabase.table("users").select("*").eq("device_fingerprint", device_fingerprint).execute()
            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Error finding user by fingerprint: {e}")
            return None

    async def _create_initial_user_profile(self, user_id: str) -> None:
        """创建初始用户画像"""
        try:
            profile_data = {
                "user_id": user_id,
                "nickname": None,
                "age_range": None,
                "core_needs": [],
                "preferences": {},
                "onboarding_completed": False,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            result = self.supabase.table("user_profiles").insert(profile_data).execute()
            if not result.data:
                raise Exception("Failed to create initial user profile")

            logger.info(f"Created initial user profile for user: {user_id}")

        except Exception as e:
            logger.error(f"Error creating initial user profile: {e}")
            raise

    async def _generate_auth_tokens(self, user: Dict[str, Any]) -> AuthResult:
        """生成JWT认证令牌"""
        try:
            user_id = user["id"]
            logger.debug(f"开始生成JWT Token - UserID: {user_id}")

            # 访问令牌载荷
            access_token_data = {
                "sub": user_id,
                "user_id": user_id,
                "type": "access",
                "exp": datetime.now(timezone.utc) + timedelta(minutes=JWT_ACCESS_TOKEN_EXPIRE_MINUTES),
                "iat": datetime.now(timezone.utc),
            }

            logger.debug(f"访问令牌载荷: {access_token_data}")

            # 刷新令牌载荷
            refresh_token_data = {
                "sub": user_id,
                "user_id": user_id,
                "type": "refresh",
                "exp": datetime.now(timezone.utc) + timedelta(days=JWT_REFRESH_TOKEN_EXPIRE_DAYS),
                "iat": datetime.now(timezone.utc),
            }

            # 生成令牌
            access_token = jwt.encode(access_token_data, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
            refresh_token = jwt.encode(refresh_token_data, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

            logger.debug(f"JWT Token生成成功 - AccessToken长度: {len(access_token)}, UserID在token中: {user_id}")

            return AuthResult(
                user=user,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
            )

        except Exception as e:
            logger.error(f"生成认证令牌失败 - UserID: {user.get('id', 'unknown')}, Error: {e}", exc_info=True)
            raise

    async def refresh_token(self, refresh_token: str) -> AuthResult:
        """刷新JWT令牌 - AC1: JWT Token管理"""
        try:
            # 验证刷新令牌
            payload = jwt.decode(refresh_token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

            if payload.get("type") != "refresh":
                raise JWTError("Invalid token type")

            user_id = payload.get("user_id")
            if not user_id:
                raise JWTError("Invalid token payload")

            # 获取用户信息
            user_result = self.supabase.table("users").select("*").eq("id", user_id).execute()
            if not user_result.data:
                raise Exception("User not found")

            user = user_result.data[0]

            # 生成新的令牌对
            auth_result = await self._generate_auth_tokens(user)

            logger.info(f"Successfully refreshed tokens for user: {user_id}")
            return auth_result

        except JWTError as e:
            logger.error(f"JWT error during token refresh: {e}")
            raise Exception("Invalid refresh token")
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            raise

    async def verify_access_token(self, access_token: str) -> Optional[Dict[str, Any]]:
        """验证访问令牌 - AC1: JWT Token验证"""
        try:
            payload = jwt.decode(access_token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

            if payload.get("type") != "access":
                raise JWTError("Invalid token type")

            user_id = payload.get("user_id")
            if not user_id:
                raise JWTError("Invalid token payload")

            # 获取用户信息
            user_result = self.supabase.table("users").select("*").eq("id", user_id).execute()
            if not user_result.data:
                return None

            return user_result.data[0]

        except JWTError as e:
            logger.error(f"JWT error during token verification: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying access token: {e}")
            return None

    async def get_current_user(self, access_token: str) -> Optional[Dict[str, Any]]:
        """获取当前认证用户信息"""
        return await self.verify_access_token(access_token)

    def generate_rtc_token(self, room_id: str, user_id: str, expire_seconds: int = 3600) -> str:
        """
        生成客户端加入RTC房间所需的Token - [已重构]

        根据火山引擎官方文档，客户端Token必须由应用服务端生成，
        使用AppId和AppKey进行签名，这与服务端API的鉴权是独立的。
        现在此方法调用重构后的AccessToken工具类。
        """
        if not settings.VOLCANO_RTC_APP_ID or not settings.VOLCANO_RTC_APP_KEY:
            raise ValueError("VOLCANO_RTC_APP_ID 或 VOLCANO_RTC_APP_KEY 未配置")

        # 使用重构后的AccessToken类
        token = AccessToken(
            app_id=settings.VOLCANO_RTC_APP_ID,
            app_key=settings.VOLCANO_RTC_APP_KEY,
            room_id=room_id,
            user_id=user_id
        )

        # 添加权限
        expire_time = int(time.time()) + expire_seconds
        token.add_privilege(PrivPublishStream, expire_time)
        token.add_privilege(PrivSubscribeStream, expire_time)
        token.expire_time(expire_time)

        # 序列化Token
        final_token = token.serialize()

        logger.info(f"为用户 {user_id} 在房间 {room_id} 生成了RTC Token，过期时间: {expire_seconds}秒")
        return final_token

# 全局认证服务实例
auth_service = AuthService()
