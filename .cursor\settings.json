{"rules": ["这是心桥 AI 亲情伴侣项目的 Monorepo 根目录", "项目包含前端(React Native + Expo)和后端(Python + FastAPI + Agno)两个子项目", "前端位于 apps/mobile-app/，后端位于 apps/agent-api/", "共享类型定义位于 shared/contracts/", "遵循 monorepo 最佳实践，各子项目保持独立但协调工作", "AI 提示时请根据当前工作目录选择合适的技术栈和规则", "项目目标：为55-75岁中国老年用户提供AI陪伴服务"], "composer": {"exclude": ["node_modules", "__pycache__", ".venv", "venv", "dist", "build", ".expo", "coverage", ".pytest_cache", ".mypy_cache", ".ruff_cache", "android/app/build", "ios/build", ".git", "plan_b", ".gemini", "other_docs"]}}