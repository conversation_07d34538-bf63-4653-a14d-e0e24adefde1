from typing import Optional, Dict, Any, List
from db.supabase_init import get_supabase_client
from api.settings import logger
from postgrest.exceptions import APIError
from supabase._async.client import AsyncClient
import asyncio # For potential future use with locks if caching is added

class UserProfileService:
    def __init__(self):
        # Placeholder for any initialization if needed in the future
        pass

    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieves the profile for a given user_id.
        """
        client: AsyncClient = await get_supabase_client()
        log_prefix = "UserProfileService:"
        logger.debug(f"{log_prefix} Getting profile for userId: {user_id}")
        try:
            response = await client.table("user_profiles").select("*").eq("user_id", user_id).maybe_single().execute()
            if response.data:
                logger.debug(f"{log_prefix} Successfully retrieved profile for {user_id}.")
                return response.data
            else:
                logger.warning(f"{log_prefix} No profile found for userId: {user_id}")
                return {"error": f"No profile found for user_id {user_id}"}
        except APIError as e:
            logger.error(f"{log_prefix} APIError getting profile for {user_id}: {getattr(e, 'message', e)}", exc_info=True)
            return {"error": f"Database error while fetching profile for {user_id}"}
        except Exception as e:
            logger.error(f"{log_prefix} Unexpected error getting profile for {user_id}: {e}", exc_info=True)
            return {"error": f"An unexpected error occurred."}

    async def save_onboarding_profile_data(
        self,
        user_id: str,
        core_needs: List[str],
        interests: List[str],
        communication_style_preference: str,
        allow_chat_analysis: bool,
    ) -> Optional[Dict[str, Any]]:
        """
        Saves or updates the user's onboarding profile data in the 'user_profiles' table.
        Checks if user_id exists, then updates or inserts accordingly.
        """
        client: AsyncClient = await get_supabase_client()
        logger.info("UserProfileService: ENTERING save_onboarding_profile_data - V3 (select-then-update/insert logic)") # Version check log
        log_prefix = "UserProfileService:"
        logger.debug(f"{log_prefix} Saving onboarding profile data for userId: {user_id}")

        profile_payload = {
            # "user_id": user_id, # user_id is used for eq check, not needed in payload for insert if it's a primary/unique key auto-managed or part of URL
            "core_needs": core_needs,
            "interests": interests,
            "communication_style_preference": communication_style_preference,
            "allow_chat_analysis": allow_chat_analysis,
            # updated_at will be handled by the database trigger or default value if configured
        }
        # For insert, user_id must be in the payload if it's not auto-generated and is a required column.
        # For update, it's used in eq.
        # Let's ensure user_id is in the payload for insert, but not for update (as it's in .eq()).
        # However, Supabase client typically expects all columns for insert, and for update,
        # only the columns to be changed. Given user_id is the identifier, it's crucial.

        # To be safe and align with how upsert would have ideally worked by including user_id in the payload:
        insert_payload = profile_payload.copy()
        insert_payload["user_id"] = user_id

        update_payload = profile_payload # For update, user_id is in .eq()

        try:
            # Check if the profile already exists
            check_response = await client.table("user_profiles") \
                .select("user_id") \
                .eq("user_id", user_id) \
                .maybe_single() \
                .execute()

            operation_successful = False
            final_data = None

            if check_response.data:
                # Profile exists, update it
                logger.debug(f"{log_prefix} Profile for {user_id} exists. Updating.")
                response = await client.table("user_profiles") \
                    .update(update_payload) \
                    .eq("user_id", user_id) \
                    .execute()
                if response.data:
                    final_data = response.data[0] if response.data and isinstance(response.data, list) and len(response.data) > 0 else response.data
                    operation_successful = True
                    logger.debug(f"{log_prefix} Successfully updated profile data for {user_id}. Data: {final_data}")
                else:
                    error_info = getattr(response, 'error', 'Update operation returned no data and no explicit error object')
                    logger.error(f"{log_prefix} Failed to update profile data for {user_id}. Error: {error_info}")

            else:
                # Profile does not exist, insert it
                logger.debug(f"{log_prefix} Profile for {user_id} does not exist. Inserting.")
                response = await client.table("user_profiles") \
                    .insert(insert_payload) \
                    .execute()
                if response.data:
                    final_data = response.data[0] if response.data and isinstance(response.data, list) and len(response.data) > 0 else response.data
                    operation_successful = True
                    logger.debug(f"{log_prefix} Successfully inserted profile data for {user_id}. Data: {final_data}")
                else:
                    # This case might happen if insert fails silently or RLS prevents returning data
                    error_info = getattr(response, 'error', 'Insert operation returned no data and no explicit error object')
                    logger.error(f"{log_prefix} Failed to insert profile data for {user_id}. Error: {error_info}")

            return final_data if operation_successful else None

        except APIError as e:
            logger.error(f"{log_prefix} APIError saving profile data for {user_id}: {getattr(e, 'message', e)}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"{log_prefix} Unexpected error saving profile data for {user_id}: {e}", exc_info=True)
            return None

# Instantiate the service for direct import if needed by other modules (like in auth_routes)
user_profile_service = UserProfileService()
