# 火山引擎RTC智能体故障排除指南

## 概述

本指南旨在帮助开发者解决火山引擎RTC智能体集成过程中遇到的常见问题，特别是"invalid UserID in AgentConfig"错误。

## 常见错误分析

### 1. "invalid UserID in AgentConfig" 错误

**错误表现：**
```
VOLCANO_API_InvalidParameter: 火山引擎API参数错误: check request failed, error: invalid UserID in AgentConfig:
```

**可能原因：**
1. **跨服务授权未开通** - 最常见原因
2. **服务未开通或额度用完**
3. **AppId配置错误**
4. **访问密钥配置错误**
5. **账号权限不足**

## 故障排除步骤

### 步骤1：检查跨服务授权

这是最重要的检查项！

1. 访问 [火山引擎跨服务授权页面](https://console.volcengine.com/rtc/aigc/iam)
2. 检查是否已开通跨服务授权
3. 如果显示未开通，点击"一键开通"

**为什么重要：**
- RTC服务需要权限调用ASR、TTS、LLM等AI服务
- 未开通跨服务授权会导致参数验证失败
- 这是"invalid UserID"错误的最常见原因

### 步骤2：验证服务开通状态

检查以下服务是否已开通：

1. **语音识别(ASR)服务**
   - 访问 [豆包语音控制台](https://console.volcengine.com/speech/service/9999?)
   - 确认已开通语音识别服务
   - 检查免费版额度是否用完

2. **语音合成(TTS)服务**
   - 确认已开通语音合成服务
   - 检查免费版额度是否用完

3. **大模型(LLM)服务**
   - 访问 [火山方舟控制台](https://console.volcengine.com/ark)
   - 确认已开通相应的大模型服务

### 步骤3：检查配置参数

验证以下配置参数：

1. **AppId配置**
   ```bash
   # 检查环境变量
   echo $VOLCANO_RTC_APP_ID
   ```

2. **访问密钥配置**
   ```bash
   # 检查环境变量
   echo $VOLCANO_ACCESS_KEY_ID
   echo $VOLCANO_SECRET_ACCESS_KEY
   ```

3. **参数格式验证**
   - UserId: 字母、数字、@、.、_、-组成，最大128字符
   - TargetUserId: 同上格式要求
   - UserId与TargetUserId不能重复

### 步骤4：使用火山引擎官方Demo验证

**强烈建议**：使用火山引擎官方提供的无代码Demo来验证配置：

1. 访问 [火山引擎RTC无代码Demo](https://console.volcengine.com/rtc/guide?from=doc&projectName=default)
2. 使用你的配置参数运行Demo
3. 如果Demo无法正常运行，说明参数配置有误

这是火山引擎官方推荐的验证方法，比自己写代码测试更可靠。

### 步骤5：使用项目诊断工具

项目提供了内置的诊断工具：

```bash
# 运行配置检查脚本
cd apps/agent-api
python scripts/check_volcano_setup.py
```

或者使用API：

```python
# 使用诊断工具验证配置
from api.utils.volcano_diagnostics import VolcanoDiagnostics

# 验证配置
config = {...}  # 你的配置
result = VolcanoDiagnostics.validate_rtc_config(config)
print(result)

# 测试连接
connection_result = await VolcanoDiagnostics.test_volcano_connection()
print(connection_result)
```

### 步骤5：检查网络和防火墙

确保服务器可以访问火山引擎API：

```bash
# 测试网络连接
curl -I https://rtc.volcengineapi.com
```

## 配置示例

### 正确的配置示例

```json
{
  "AppId": "你的AppId",
  "RoomId": "test-room-123",
  "TaskId": "test-task-123",
  "Config": {
    "ASRConfig": {
      "Provider": "volcano",
      "ProviderParams": {
        "Mode": "bigmodel",
        "AppId": "你的ASR_AppId",
        "AccessToken": "你的ASR_AccessToken",
        "ApiResourceId": "volc.bigasr.sauc.duration",
        "StreamMode": 0
      }
    },
    "TTSConfig": {
      "Provider": "volcano_bidirection",
      "ProviderParams": {
        "app": {
          "appid": "你的TTS_AppId",
          "token": "你的TTS_Token"
        },
        "audio": {
          "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
          "speech_rate": 0,
          "pitch_rate": 0
        },
        "ResourceId": "volc.service_type.10029"
      }
    },
    "LLMConfig": {
      "Mode": "ArkV3",
      "EndPointId": "你的EndPointId",
      "MaxTokens": 1024,
      "Temperature": 0.7,
      "SystemMessages": ["你是一个AI助手。"]
    },
    "AgentConfig": {
      "UserId": "ai-assistant-001",
      "TargetUserId": ["user-001"]
    }
  }
}
```

## 常见问题FAQ

### Q1: 为什么UserId格式正确但仍然报错？
A1: 这通常不是UserId格式问题，而是权限或服务开通问题。请优先检查跨服务授权。

### Q2: 如何确认跨服务授权是否生效？
A2: 开通后等待几分钟，然后重新测试API调用。

### Q3: 免费版额度用完了怎么办？
A3: 需要购买正式版资源包或升级到付费版本。

### Q4: 如何获取正确的AppId和Token？
A4:
- RTC AppId: [RTC控制台](https://console.volcengine.com/rtc)
- ASR/TTS AppId: [豆包语音控制台](https://console.volcengine.com/speech)
- LLM EndPointId: [火山方舟控制台](https://console.volcengine.com/ark)

## 联系支持

如果按照本指南操作后仍然遇到问题，请：

1. 收集错误日志和配置信息
2. 确认已完成所有检查步骤
3. 联系火山引擎技术支持

## 相关链接

- [火山引擎RTC文档](https://www.volcengine.com/docs/6348/)
- [跨服务授权页面](https://console.volcengine.com/rtc/aigc/iam)
- [豆包语音控制台](https://console.volcengine.com/speech)
- [火山方舟控制台](https://console.volcengine.com/ark)
