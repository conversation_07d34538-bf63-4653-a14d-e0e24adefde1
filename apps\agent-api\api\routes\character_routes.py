# api/routes/character_routes.py
"""
角色管理路由 - 对应故事1.2-B的AC3
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Dict, Any, Optional
from pydantic import BaseModel

from api.services.character_service import character_service, Character
from api.dependencies.auth import get_current_user
from api.settings import logger

router = APIRouter(
    prefix="/characters",
    tags=["Character Management"],
)

# Pydantic模型
class CharacterResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    voice_id: Optional[str] = None
    personality: Optional[Dict[str, Any]] = None
    is_default: bool = False
    created_at: Optional[str] = None
    # 为了兼容测试，添加role字段（映射自description）
    role: Optional[str] = None

class CharacterListResponse(BaseModel):
    data: list[CharacterResponse]
    pagination: Dict[str, Any]

class BindResponse(BaseModel):
    success: bool
    message: Optional[str] = None

@router.get(
    "",
    response_model=CharacterListResponse,
    summary="Get Characters List",
    description="Retrieve list of available AI characters",
)
async def get_characters_list(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取AI角色列表 - AC3: AI角色管理API"""
    logger.info("Getting characters list")

    try:
        result = await character_service.get_characters_list(page=page, limit=limit)

        # 转换为响应模型
        character_responses = []
        for character in result.data:
            char_response = CharacterResponse(
                id=character.id,
                name=character.name,
                description=character.description,
                voice_id=character.voice_id,
                personality=character.personality,
                is_default=character.is_default,
                created_at=character.created_at,
                role=character.description  # 兼容测试期望的role字段
            )
            character_responses.append(char_response)

        return CharacterListResponse(
            data=character_responses,
            pagination=result.pagination
        )

    except Exception as e:
        logger.exception(f"Error getting characters list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get characters list"
        )

@router.get(
    "/{character_id}",
    response_model=CharacterResponse,
    summary="Get Character Details",
    description="Retrieve detailed information about a specific AI character",
)
async def get_character_details(
    character_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取AI角色详情 - AC3: AI角色管理API"""
    logger.info(f"Getting character details for: {character_id}")

    try:
        character = await character_service.get_character_by_id(character_id)

        if not character:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Character not found"
            )

        return CharacterResponse(
            id=character.id,
            name=character.name,
            description=character.description,
            voice_id=character.voice_id,
            personality=character.personality,
            is_default=character.is_default,
            created_at=character.created_at,
            role=character.description  # 兼容测试期望的role字段
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting character details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get character details"
        )

# 注意：用户角色绑定路由在user_routes.py中实现，路径为 /user/characters/{character_id}/bind
