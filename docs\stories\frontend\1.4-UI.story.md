# 故事 1.4-UI: 对话界面设计

## 基本信息
- **故事编号**: 1.4-UI
- **故事标题**: 对话界面设计
- **Epic**: MVP - 建立情感连接与核心信任
- **用户角色**: UX设计师 + 前端开发者
- **优先级**: 最高（P0）
- **工作量估计**: 6-9 个工作日
- **依赖关系**: 故事 1.1-UI (基础设计系统与组件库)
- **Status**: Approved

## 故事描述

作为UX设计师和前端开发者，我需要设计并实现"语音优先，文本辅助"的主对话界面和双模交互体验，**以便** 为老年用户提供最自然、最直观的AI对话交互方式。

## 验收标准

### AC1: 主对话界面布局设计
- [ ] 完成主对话页面的整体布局结构设计
- [ ] 设计顶部AI角色信息展示区域
- [ ] 实现中部对话历史区域的FlashList布局
- [ ] 设计底部双模输入控制区域布局

### AC2: 双模交互界面实现
- [ ] 设计并实现语音模式的"按住说话"按钮界面
- [ ] 创建文本模式的输入框和发送按钮布局
- [ ] 实现语音/文本模式切换的流畅动画效果
- [ ] 设计模式切换指示和用户引导界面

### AC3: 对话消息界面设计
- [ ] 设计用户和AI消息气泡的差异化样式
- [ ] 实现消息状态指示器（发送中、已读、播放中）
- [ ] 创建消息时间戳和类型标识的视觉设计
- [ ] 设计长消息的展示和折叠交互

### AC4: 交互反馈和状态界面
- [ ] 设计录音状态的视觉反馈（呼吸光效、水波纹）
- [ ] 实现AI思考和回复过程的加载动画
- [ ] 设计AI实时状态的视觉反馈（如聆listen中、思考中、回答中）
- [ ] 设计实时字幕的展示样式（与最终消息气泡有区分）
- [ ] 创建网络异常和错误状态的提示界面
- [ ] 设计语音播放和文本显示的同步效果

## Tasks / Subtasks

### 第一阶段：主对话界面布局 (2天)
- [ ] **对话页面整体布局** (AC1)
  - 设计`app/(chat)/index.tsx`的完整界面框架
  - 创建三段式布局（头部、对话区、输入区）
  - 实现安全区域适配和响应式布局
  - 设计页面级的加载和错误状态

- [ ] **AI角色信息展示区** (AC1)
  - 设计顶部AI角色的头像和状态显示
  - 创建角色名称和在线状态的视觉设计
  - 实现角色心情和活动状态的动态显示
  - 设计角色信息的点击交互和详情页

- [ ] **对话历史区域布局** (AC1)
  - 设计中部FlashList容器的布局结构
  - 创建对话历史的滚动和定位机制
  - 实现对话分组和时间分割的视觉设计
  - 设计历史消息的加载和分页界面

### 第二阶段：双模输入界面设计 (2-3天)
- [ ] **语音模式界面设计** (AC2)
  - 设计大型"按住说话"按钮的视觉样式
  - 创建录音状态的多层次视觉反馈
  - 实现角落"键盘"切换图标的设计和位置
  - 设计语音输入的提示和引导文案

- [ ] **文本模式界面设计** (AC2)
  - 设计文本输入框和发送按钮的布局
  - 创建键盘弹出时的界面自适应方案
  - 实现角落"语音"切换图标的设计
  - 设计输入状态和字数统计的显示

- [ ] **模式切换动画设计** (AC2)
  - 设计语音到文本模式的过渡动画
  - 实现键盘滑出和输入框淡入效果
  - 创建文本到语音模式的返回动画
  - 优化动画时长和缓动曲线

### 第三阶段：消息气泡和状态设计 (1-2天)
- [ ] **消息气泡设计** (AC3)
  - 设计用户消息气泡（右侧、用户色彩）
  - 创建AI消息气泡（左侧、AI色彩）
  - 实现不同消息类型的视觉区分
  - 设计消息气泡的圆角、阴影和间距

- [ ] **消息状态和元数据** (AC3)
  - 设计消息状态指示器的图标和颜色
  - 创建时间戳的格式和显示位置
  - 实现消息重发和删除的操作界面
  - 设计长消息的展开和收起交互

- [ ] **特殊消息类型设计** (AC3)
  - 设计语音消息的播放控件和波形
  - 创建系统提示消息的特殊样式
  - 实现提醒确认消息的卡片式设计
  - 设计错误消息和重试界面

### 第四阶段：交互反馈和动效实现 (1-2天)
- [ ] **录音状态动效** (AC4)
  - 实现"按住说话"按钮的呼吸光效
  - 创建录音过程中的水波纹动画
  - 设计录音取消和确认的视觉反馈
  - 优化录音动效的性能和流畅度

- [ ] **AI回复过程动效** (AC4)
  - 设计AI思考状态的加载动画
  - 实现文本流式显示的打字机效果
  - 创建语音播放的可视化反馈
  - 设计回复完成的提示动效

- [ ] **AI实时状态指示器设计** (AC4)
  - 设计"聆听中"状态的视觉提示（如：微妙的耳朵图标动效）
  - 设计"思考中"状态的视觉提示（如：呼吸灯或流光效果）
  - 设计"回答中"状态的视觉提示（如：声波纹动画）
  - 确保状态提示既清晰又不干扰用户

- [ ] **交互状态管理** (AC4)
  - 设计网络异常的重连界面
  - 创建语音识别失败的错误提示
  - 实现会话恢复的状态同步界面
  - 设计用户离开和返回的状态处理

## Dev Notes

CRITICAL: This is a **UI design and implementation story** focused on **main chat interface**. 
**PREREQUISITE**: Story 1.1-UI (基础设计系统与组件库) must be completed first.

**Design Focus Areas:**
- 主对话界面的整体布局和信息架构
- 双模交互（语音/文本）的无缝切换体验
- 消息展示的层次结构和视觉设计
- 实时交互的反馈和状态管理

### 技术栈说明
- **框架**: React Native + Expo
- **状态管理**: Zustand（全局状态）+ Context API（组件间通信）
- **动画库**: React Native Reanimated 3
- **列表组件**: @shopify/flash-list (v1.6.x)
- **导航**: Expo Router (file-based routing)
- **实时通信**: WebSocket 或 Server-Sent Events
- **语音处理**: Expo AV + 原生语音识别API
- **UI组件**: 基于1.1-UI故事的设计系统组件库

### 关键数据结构
```typescript
// 消息数据结构
interface ChatMessage {
  id: string;
  type: 'text' | 'voice' | 'system';
  content: string;
  isPartial?: boolean; // 新增：标记是否为实时字幕
  voiceUri?: string;
  timestamp: Date;
  sender: 'user' | 'ai';
  status: 'sending' | 'sent' | 'delivered' | 'failed';
  isPlaying?: boolean; // 语音消息播放状态
  duration?: number; // 语音消息时长(秒)
}

// 对话状态管理
interface ChatState {
  messages: ChatMessage[];
  inputMode: 'voice' | 'text';
  isRecording: boolean;
  aiStatus: 'idle' | 'listening' | 'thinking' | 'answering'; // 新增：AI实时状态
  isConnected: boolean;
  currentPlayingMessageId?: string;
  recordingDuration: number;
  recordingAmplitude: number;
}

// 录音状态
interface RecordingState {
  isRecording: boolean;
  duration: number;
  amplitude: number;
  uri?: string;
  isPaused: boolean;
}

// 双模输入模式
type InputMode = 'voice' | 'text';
type MessageStatus = 'sending' | 'sent' | 'delivered' | 'failed';
```

### Core Components to Implement:
基于1.1-UI提供的基础组件进行专门定制：

```typescript
// 主对话界面页面
app/(chat)/
├── index.tsx            // 主对话页面
├── _layout.tsx          // 对话布局 + ChatProvider
└── settings.tsx         // 对话设置页面

// 对话专用组件
src/components/features/chat/
├── ChatHeader.tsx           // 对话页面头部
├── ChatInputController.tsx  // 双模输入控制器
├── ChatBubble.tsx          // 消息气泡组件
├── MessageList.tsx         // 消息列表容器（FlashList）
├── VoiceButton.tsx         // 语音输入按钮
├── TextInput.tsx           // 文本输入组件
├── RecordingIndicator.tsx  // 录音状态指示器
├── MessageStatusIndicator.tsx // 消息状态指示器
├── TypingIndicator.tsx     // AI打字指示器
└── VoicePlayer.tsx         // 语音播放组件

// 状态管理和工具
src/stores/
├── chatStore.ts            // Zustand chat store
└── recordingStore.ts       // 录音状态管理

src/lib/chat/
├── chatApi.ts              // 聊天API封装
├── voiceProcessor.ts       // 语音处理工具
├── messageUtils.ts         // 消息处理工具
└── websocketClient.ts      // WebSocket客户端
```

### API集成点
```typescript
// 消息发送API
POST /api/v1/chat/messages
Content-Type: application/json
{
  "content": "用户消息内容",
  "type": "text" | "voice",
  "voiceUri"?: "语音文件URI",
  "characterId": "角色ID"
}

// 语音转文字API  
POST /api/v1/speech/transcribe
Content-Type: multipart/form-data
FormData: { audio: File, language?: string }

// 实时消息订阅 (更正)
通过RTC数据通道接收二进制消息:
- **AI状态流**: `magic number: "conv"`, 包含 `Stage` 字段 (`listening`, `thinking`, `answering` 等)
- **实时字幕流**: `magic number: "subv"`, 包含逐字的ASR结果 (`text`, `definite` 等)
- 具体协议需参考 `veRTC.md` 文档中的 `onRoomBinaryMessageReceived` 回调和二进制消息格式。

// 语音合成API
POST /api/v1/speech/synthesize  
Content-Type: application/json
{ 
  "text": "要合成的文本", 
  "voice": "female-gentle",
  "characterId": "角色ID"
}

// 聊天历史API
GET /api/v1/chat/messages?limit=50&before=messageId
POST /api/v1/chat/messages/:id/retry  // 重发失败消息
```

### 状态管理架构
```typescript
// Zustand Chat Store
interface ChatStore {
  // 状态
  messages: ChatMessage[];
  inputMode: InputMode;
  isRecording: boolean;
  isConnected: boolean;
  isAITyping: boolean; // 可被 aiStatus 替代或增强
  aiStatus: 'idle' | 'listening' | 'thinking' | 'answering'; // 新增
  
  // 动作
  addMessage: (message: ChatMessage) => void;
  updateLastMessage: (contentDelta: string, isFinal: boolean) => void; // 新增：支持实时更新字幕
  updateMessageStatus: (id: string, status: MessageStatus) => void;
  switchInputMode: (mode: InputMode) => void;
  setAIStatus: (status: ChatState['aiStatus']) => void; // 新增
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<ChatMessage>;
  sendMessage: (content: string, type: 'text' | 'voice') => Promise<void>;
  retryMessage: (messageId: string) => Promise<void>;
  loadHistoryMessages: (before?: string) => Promise<void>;
  playVoiceMessage: (messageId: string) => Promise<void>;
  stopVoiceMessage: () => void;
}
```

### Key Design Requirements:
从 `@docs/prd/ux-design.md` 和 `@docs/architecture/06-frontend-architecture.md` 中的要求：

1. **双模交互设计**:
   - 语音优先：大型"按住说话"按钮为视觉中心
   - 文本辅助：提供清晰但不突出的文本输入入口
   - 无缝切换：200-300ms的流畅过渡动画
   - 用完即走：文本发送后自动回到语音模式

2. **适老化设计标准**:
   - 超大触摸区域：按钮≥44x44pt
   - 高对比度色彩：确保文字清晰可读
   - 简化界面：减少视觉噪音和分散注意力的元素
   - 直观反馈：即时、温暖的视觉和触觉反馈

3. **性能要求**:
   - FlashList虚拟化：支持长对话历史的流畅滚动
   - 动画优化：使用react-native-reanimated避免JS线程阻塞
   - 内存管理：消息组件的React.memo优化
   - 响应速度：交互响应<100ms，动画>45fps

### 依赖的基础组件（来自1.1-UI）
- `Button` - 语音按钮和发送按钮
- `Input` - 文本输入框
- `Text` - 各级标题和消息文本
- `Icon` - 界面图标（麦克风、键盘、发送等）
- `SafeAreaView` - 安全区域布局
- `ProgressIndicator` - 录音进度和AI思考状态
- `Modal` - 错误提示和确认对话框
- `Card` - 消息气泡基础样式

### 所需环境变量
```typescript
// 在 .env 或 app.config.ts 中配置
EXPO_PUBLIC_API_URL=                    // API基础URL
EXPO_PUBLIC_WEBSOCKET_URL=              // WebSocket连接URL  
EXPO_PUBLIC_VOICE_API_KEY=              // 语音服务API密钥
EXPO_PUBLIC_MAX_VOICE_DURATION=60000    // 最大录音时长(ms)
EXPO_PUBLIC_MESSAGE_RETRY_ATTEMPTS=3    // 消息发送重试次数
EXPO_PUBLIC_ENABLE_VOICE_FEEDBACK=true  // 是否启用语音反馈
EXPO_PUBLIC_CHAT_THROTTLE_MS=100        // 聊天输入节流时间
EXPO_PUBLIC_WEBSOCKET_RECONNECT_DELAY=5000 // WebSocket重连延迟
```

### 异常处理和边界情况
- **网络异常**: 自动重连机制，离线消息队列，友好的错误提示
- **语音识别失败**: 降级到文本输入，提供重试选项
- **API超时**: 消息状态标记，支持手动重发
- **录音权限**: 优雅的权限申请流程，替代方案提示
- **设备兼容性**: 低端设备的性能优化，功能降级策略
- **会话恢复**: 应用切换后的状态恢复，未发送消息保存
- **内存管理**: FlashList虚拟化，消息分页加载
- **音频冲突**: 处理系统来电、其他应用音频占用

### Technical Architecture:
```typescript
// 双模输入控制器的核心实现
const ChatInputController = () => {
  const { inputMode, switchInputMode } = useChatStore();
  
  // 语音模式渲染
  if (inputMode === 'voice') {
    return (
      <VoiceInputArea 
        onSwitchToText={() => switchInputMode('text')}
        onRecordingStart={() => /* 开始录音逻辑 */}
        onRecordingEnd={() => /* 结束录音逻辑 */}
      />
    );
  }
  
  // 文本模式渲染
  return (
    <TextInputArea 
      onSwitchToVoice={() => switchInputMode('voice')}
      onSendMessage={(text) => {
        // 发送文本消息
        sendMessage(text, 'text');
        // 发送后回到语音模式
        switchInputMode('voice');
      }}
    />
  );
};

// FlashList优化配置
const MessageList = () => {
  const { messages } = useChatStore();
  
  const getItemType = useCallback((item: ChatMessage) => {
    return `${item.sender}_${item.type}`; // 'user_text', 'ai_voice' 等
  }, []);
  
  return (
    <FlashList
      data={messages}
      renderItem={({ item }) => <ChatBubble message={item} />}
      estimatedItemSize={80}
      getItemType={getItemType}
      keyExtractor={(item) => item.id}
      inverted // 聊天界面从底部开始
    />
  );
};
```

## Testing

Dev Note: Story Requires the following tests:

- [ ] **Component Unit Tests**: 所有对话组件的单元测试，覆盖率≥95%
  - ChatInputController 双模切换逻辑测试
  - ChatBubble 不同消息类型渲染测试
  - VoiceButton 录音状态管理测试
  - MessageList FlashList集成测试
- [ ] **双模交互测试**: 语音/文本模式切换的完整场景测试
  - 语音→文本→语音的连续切换流程
  - 录音中断和恢复处理
  - 模式切换动画的完整性验证
- [ ] **消息状态测试**: 发送中、已送达、失败等状态的UI反馈测试
  - 消息发送状态指示器测试
  - 重发失败消息的交互测试
  - 网络异常时的状态处理测试
- [ ] **FlashList性能测试**: 长对话历史的滚动性能和内存使用测试
  - 1000+消息的滚动流畅度测试
  - 内存占用不超过50MB的验证
  - getItemType优化效果验证
- [ ] **动画性能测试**: 60fps的模式切换和消息动画测试
  - 录音按钮呼吸光效性能测试
  - 消息气泡出现动画性能测试
  - 低端设备动画降级测试
- [ ] **API集成测试**: 消息发送、语音转文字、实时消息的集成测试
  - WebSocket连接和断线重连测试
  - 语音文件上传和处理测试
  - 消息重试机制测试
- [ ] **无障碍测试**: VoiceOver/TalkBack的对话界面导航测试
  - 语音输入按钮的无障碍标签测试
  - 消息列表的屏幕阅读器支持测试
  - 键盘导航支持测试
- [ ] **错误场景测试**: 网络断开、API失败、语音识别失败的处理测试
  - 离线消息队列功能测试
  - 录音权限被拒绝的处理测试
  - API超时和重试逻辑测试

### 性能基准测试
- **消息列表滚动**: ≥60fps (在低端设备iPhone 8)
- **模式切换动画**: ≤200ms完成
- **消息发送响应**: ≤100ms UI反馈
- **内存使用**: 1000条消息≤50MB
- **录音响应时间**: 按下录音按钮≤50ms开始录音
- **语音转文字**: ≤3秒完成转换并显示结果
- **WebSocket重连**: ≤5秒完成自动重连

### 手动测试场景
- **双模交互流程**: 连续语音输入→文本输入→语音输入的流畅切换
- **录音完整流程**: 长按录音→录音过程中的视觉反馈→取消/完成操作
- **网络异常处理**: 不同网络条件下的消息发送和重试机制
- **设备适配测试**: 横竖屏切换时的界面布局和状态保持
- **多设备验证**: iPhone SE、iPhone 14 Pro Max、iPad等不同尺寸设备
- **权限处理测试**: 首次安装应用时的麦克风权限申请流程
- **音频冲突测试**: 录音过程中来电或其他应用占用音频的处理
- **长时间使用测试**: 连续对话1小时的性能和稳定性验证

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 故事1.1-UI（基础设计系统与组件库）已完成并交付
- [ ] 以下基础组件已可用：Button、Input、Text、Icon、SafeAreaView、ProgressIndicator、Modal、Card
- [ ] 对话交互的UX规范和设计原则已明确审批
- [ ] 双模交互（语音/文本）的切换逻辑和视觉设计已确定
- [ ] 消息气泡的视觉规范和适老化标准已明确
- [ ] 开发环境已配置：React Native Reanimated 3、@shopify/flash-list、Zustand
- [ ] 语音处理权限和API配置已准备就绪
- [ ] WebSocket连接和实时通信方案已确定

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过验证
- [ ] 主对话界面设计完成并获得产品和设计团队审批
- [ ] 双模交互体验达到UX设计要求，用户测试反馈良好
- [ ] 对话界面的性能指标全部达标（见性能基准测试）
- [ ] 所有API集成点已验证可用
- [ ] 无障碍功能测试通过，符合WCAG 2.1 AA标准
- [ ] 错误处理和边界情况已完整覆盖
- [ ] 代码审查完成，技术债务已解决

### 交付物 (Deliverables)
- [ ] **设计文件**: 主对话界面的完整Figma设计稿（包含所有状态和交互）
- [ ] **页面实现**: 对话相关页面的完整代码和路由配置
- [ ] **组件库扩展**: 8个对话专用UI组件（见Core Components列表）
- [ ] **状态管理**: Zustand store和相关工具函数
- [ ] **API集成**: 完整的聊天API封装和WebSocket客户端
- [ ] **动画效果**: 双模切换、录音状态、消息动画的完整实现
- [ ] **性能优化**: FlashList配置和动画的性能优化方案
- [ ] **测试套件**: 单元测试、集成测试、性能测试的完整覆盖
- [ ] **文档**: 组件使用说明、API接口文档、故障排除指南

## 风险与缓解措施

### 主要风险
1. **双模切换复杂性**: 语音/文本模式切换的用户理解困难
2. **性能瓶颈**: 长对话历史的渲染和动画性能
3. **动画流畅度**: 复杂动效在低端设备上的表现
4. **状态同步**: 录音和消息状态的准确同步

### 缓解措施
1. **用户引导设计**: 首次使用的明确指导和视觉提示
2. **性能基准测试**: FlashList优化和内存管理
3. **渐进式动效**: 根据设备性能调整动画复杂度
4. **状态机设计**: 严格的状态管理和错误恢复机制

## 后续依赖关系

### 🔗 此故事完成后解锁的故事：
- **故事1.4**: 实时语音会话流程集成（功能实现）

### 📋 为故事1.4提供的资源：
- 完整的主对话界面实现
- 双模输入控制器组件
- 消息展示和状态管理组件
- 高性能的对话历史渲染方案

## 相关文档引用
- [UX设计规范 - 双模交互设计原则](../../prd/ux-design.md#双模交互设计) - 语音优先的设计理念和切换逻辑
- [UX设计规范 - 适老化对话界面](../../prd/ux-design.md#适老化对话界面) - 消息气泡大小、字体、对比度要求  
- [UX设计规范 - 实时交互反馈](../../prd/ux-design.md#实时交互反馈) - 录音状态、AI思考、消息状态的视觉反馈规范
- [前端架构设计 - 对话组件架构](../../architecture/06-frontend-architecture.md#对话组件架构) - 组件层次和数据流设计
- [前端架构设计 - 实时通信方案](../../architecture/06-frontend-architecture.md#实时通信) - WebSocket集成和错误处理
- [前端架构设计 - 状态管理模式](../../architecture/06-frontend-architecture.md#状态管理) - Zustand store设计模式
- [基础设计系统 - Button组件](./1.1-UI.story.md#button组件) - 语音按钮和发送按钮样式
- [基础设计系统 - Input组件](./1.1-UI.story.md#input组件) - 文本输入框样式和交互
- [基础设计系统 - Card组件](./1.1-UI.story.md#card组件) - 消息气泡基础样式
- [基础设计系统 - 动画系统](./1.1-UI.story.md#动画系统) - 模式切换和消息动画规范
- [基础设计系统 - 颜色系统](./1.1-UI.story.md#颜色系统) - 用户/AI消息的差异化色彩 