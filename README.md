# 心桥 AI 亲情伴侣

> 为中老年用户提供有温度、有记忆的AI陪伴体验

## 📱 项目简介

心桥是一款专为55-75岁中国老年用户设计的AI陪伴应用，通过先进的实时语音技术和智能记忆系统，为面临孤独的老年用户提供可信赖的虚拟亲情伴侣。

## 🏗️ 项目结构 (Monorepo)

```
xinqiao/
├── apps/
│   ├── mobile-app/          # React Native 前端应用
│   └── agent-api/           # Python 后端 API (Agno Framework)
├── shared/
│   └── contracts/           # 前后端共享 Schema
├── docs/                    # 项目文档
├── .github/                 # CI/CD 工作流
└── package.json            # 根级别包管理
```

## 🚀 快速开始

### 环境要求

- **Node.js** >= 20.0.0
- **PNPM** >= 9.0.0
- **Python** >= 3.11
- **uv** (Python 包管理器)

### 安装依赖

```bash
# 安装所有依赖
pnpm install

# 或分别安装
pnpm mobile:install    # 安装前端依赖
cd apps/agent-api && uv pip sync requirements.txt  # 安装后端依赖
```

### 开发启动

```bash
# 同时启动前后端
pnpm dev

# 或分别启动
pnpm mobile:dev        # 启动前端开发服务器
pnpm api:dev          # 启动后端开发服务器
```

## 📦 技术栈

### 前端 (apps/mobile-app)
- **React Native + Expo** - 跨平台移动应用开发
- **TypeScript** - 静态类型检查
- **Nativewind** - Tailwind CSS for React Native
- **Zustand** - 状态管理
- **React Query** - 数据请求与缓存
- **火山引擎 RTC** - 实时音视频通信

### 后端 (apps/agent-api)
- **Python + FastAPI** - 高性能 Web 框架
- **Agno Framework** - AI Agent 开发框架
- **PostgreSQL + Supabase** - 数据存储
- **火山引擎语音服务** - ASR/TTS 服务

## 🔧 开发脚本

### 根级别脚本
```bash
pnpm dev              # 启动所有服务
pnpm build            # 构建所有项目
pnpm lint             # 代码检查
pnpm test             # 运行所有测试
```

### 前端专用脚本
```bash
pnpm mobile:dev       # 启动前端开发
pnpm mobile:build     # 构建前端
pnpm mobile:lint      # 前端代码检查
pnpm mobile:test      # 前端测试
```

### 后端专用脚本
```bash
pnpm api:dev          # 启动后端开发
pnpm api:test         # 后端测试
pnpm api:lint         # 后端代码检查
pnpm api:format       # 代码格式化
```

## 🧪 CI/CD

项目配置了完整的 CI/CD 流水线：

- **路径触发**: 只在相关代码变更时运行对应的检查
- **前端检查**: ESLint, TypeScript, Jest 测试
- **后端检查**: Ruff, MyPy, Pytest 测试
- **统一 CI**: 智能检测变更并运行相应流程

## 📚 文档

- [项目简报](docs/project-beaf.md) - 项目概述和商业价值
- [产品需求文档](docs/prd.md) - 详细功能需求
- [架构设计](docs/architecture.md) - 技术架构说明
- [后端设计](docs/end.md) - 后端详细设计
- [前端设计](docs/front.md) - 前端架构说明
- [UI/UX 规范](docs/uxui.md) - 设计规范和交互指南

## 🔧 开发环境配置

### Cursor IDE 配置

项目针对 Cursor IDE 进行了优化：

- 根目录和子项目都有独立的 `.cursor` 配置
- 多根工作区支持，避免提示混乱
- 针对不同技术栈的专门提示规则

### 推荐的工作区设置

在 Cursor 中打开项目时，建议使用多根工作区：

1. 添加根目录 `xinqiao`
2. 添加前端目录 `apps/mobile-app`  
3. 添加后端目录 `apps/agent-api`

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 开源协议

本项目采用 MIT 协议 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 核心团队

- **产品经理**: John - 产品策略和需求管理
- **架构师**: Winston - 技术架构和开发指导  
- **UX 设计师**: Sally - 用户体验和界面设计

## 📞 联系我们

如有疑问或建议，请通过以下方式联系：

- 项目 Issue: [GitHub Issues](https://github.com/your-org/xinqiao/issues)
- 邮箱: <EMAIL>

---

❤️ 用技术传递温暖，用AI连接真情
