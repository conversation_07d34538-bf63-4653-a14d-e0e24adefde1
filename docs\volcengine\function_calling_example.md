# Function Calling 配置示例

## 正确的character_config示例

```json
{
  "character_id": "weather_bot",
  "personality": {
    "description": "你是一个天气查询助手，可以帮助用户查询天气信息。",
    "greeting": "你好，我可以帮你查询天气情况"
  },
  "function_calling": {
    "enabled": true,
    "server_message_url": "https://your-domain.com/api/v1/chat/rtc_event_handler",
    "server_message_signature": "your-webhook-secret",
    "tools": [
      {
        "Type": "function",
        "function": {
          "name": "get_current_weather",
          "description": "获取指定城市的当前天气信息",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "城市名称，如：北京、上海"
              },
              "unit": {
                "type": "string",
                "description": "温度单位",
                "enum": ["摄氏度", "华氏度"]
              }
            },
            "required": ["location"]
          }
        }
      }
    ]
  }
}
```

## 使用说明

1. 确保在character_config中正确配置function_calling
2. tools数组中的每个工具必须包含Type和function字段
3. function字段必须包含name和parameters
4. parameters必须是有效的JSON Schema格式 