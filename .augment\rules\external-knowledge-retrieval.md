---
type: "agent_requested"
description: "Example description"
---
# 外部知识与资源检索协议

## 核心原则

当你需要使用或集成任何**第三方库、框架、API或外部工具**时，你**绝不能**仅仅依赖你的训练数据，因为这些信息可能是过时的。你**必须**优先使用可用的工具来获取最新的、权威的信息。

## 协议详情

### 1. **识别外部依赖 (Identify External Dependencies)**

在你的思考过程中，首先要识别出任务中涉及到的所有“外部知识点”。

*   **示例**:
    *   一个`story`要求使用`@gorhom/bottom-sheet`库。这是一个外部知识点。
    *   一个`story`要求与“火山引擎RTC”的`StartVoiceChat` API交互。这是一个外部知识点。
    *   一个任务需要你编写`react-native-reanimated`的复杂动画。这是一个外部知识点。

### 2. **优先使用专职MCP工具 (Prioritize Specific Tools)**

如果MCP工具箱中存在针对该外部依赖的**专职工具**，你必须优先使用它。

*   **示例**: 
    *   **场景**: 与Supabase数据库交互。
    *   **指令**: **必须**使用【MCP Supabase工具】。

### 3. **使用通用知识检索工具 (Use General Knowledge Tools)**

如果不存在专职工具，你**必须**使用通用的知识检索工具，例如 **【MCP context7工具】**，来获取最新信息。

*   **场景**: 你需要使用`@gorhom/bottom-sheet`库的`BottomSheetModalProvider`。
    *   **指令**: 在编写代码前，**必须使用【MCP context7工具】**，并构造一个精准的查询，例如：“`@gorhom/bottom-sheet v5 latest official documentation for BottomSheetModalProvider setup`”。你必须基于检索到的**最新官方文档**来编写代码，而不是你的旧有知识。
*   **场景**: 你需要了解火山引擎`StartVoiceChat` API的某个冷门参数`Prefill`的用法。
    *   **指令**: **必须使用【MCP context7工具】**，查询：“`volcengine rtc StartVoiceChat API Prefill parameter documentation`”。

### 4. **版本意识 (Version Awareness)**

*   **指令**: 在使用任何库之前，**必须**先检查项目中的`package.json`或`requirements.txt`文件，确认**当前项目正在使用的版本号**。你在使用【MCP context7工具】进行查询时，应该将版本号也作为查询关键词的一部分（例如，“`react-query v5 documentation for useInfiniteQuery`”），以确保获取到的用法是兼容的。

## 强制要求

*   **报告你的信息来源**: 在你的回答中，你应该清晰地说明你是如何获取信息的。例如：“`根据我使用context7工具查询到的@gorhom/bottom-sheet最新文档，BottomSheetModalProvider应该像这样配置...`”
*   **处理信息缺失**: 如果通过工具也无法找到明确的答案，你必须向用户报告：“`我已使用context7工具查询，但未能找到关于[具体问题]的明确官方文档。我将基于通用知识尝试一个方案，但这可能不是最新的最佳实践，建议您进行验证。`”