"""
对话编排服务 - 核心对话处理逻辑
采用渐进式实现策略：
1. 基础记忆检索 + LLM调用
2. 工具调用集成
3. 性能优化

故事1.7-B: 集成危机检测服务
故事1.6-B: 集成Function Calling工具调用
"""
import logging
import asyncio
import time
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime, timezone
import json

from api.services.memory_service import IMemoryService, get_memory_service
from api.services.llm_proxy_service import LLMProxyService, get_llm_proxy_service
from api.services.tool_executor_service import ToolExecutorService, get_tool_executor_service
from api.services.prompt_builder_service import PromptBuilderService, get_prompt_builder_service
from api.services.crisis_detection_service import CrisisDetectionService, get_crisis_detection_service
from api.services.volcano_client_service import VolcanoClientService, get_volcano_client_service
from api.settings import settings
from api.models.schema_models import ToolCall, ToolResult

logger = logging.getLogger(__name__)

# 故事1.6-B: 架构师建议的工具调用保护机制配置
# Function Calling循环保护常量已移至实例属性
# MAX_TOOL_CALLS = 5  # 最大工具调用次数
# MAX_TOOL_CALL_TIME = 10.0  # 最大工具调用总时间（秒）


class ChatOrchestrationService:
    """对话编排服务 - 统一处理对话流程"""

    def __init__(
        self,
        memory_service: IMemoryService,
        llm_proxy_service: LLMProxyService,
        tool_executor_service: ToolExecutorService,
        prompt_builder_service: PromptBuilderService,
        crisis_detection_service: Optional[CrisisDetectionService] = None,
        volcano_client: Optional[VolcanoClientService] = None,
        max_tool_calls: int = 5,
        max_tool_time_seconds: int = 10
    ):
        self.memory_service = memory_service
        self.llm_proxy_service = llm_proxy_service
        self.tool_executor_service = tool_executor_service
        self.prompt_builder_service = prompt_builder_service
        # 故事1.7-B: 添加危机检测服务
        self.crisis_detection_service = crisis_detection_service or get_crisis_detection_service()
        # 故事1.12-B: 添加火山客户端服务用于危机干预音频禁用
        self.volcano_client = volcano_client or get_volcano_client_service()

        # 故事1.6-B: Function Calling循环保护机制配置
        self.max_tool_calls = max_tool_calls
        self.max_tool_time_seconds = max_tool_time_seconds

    async def handle_message(
        self,
        user_message: str,
        context: Dict[str, Any]
    ) -> str:
        """
        处理用户消息的核心方法 - 集成Function Calling

        Args:
            user_message: 用户消息文本
            context: 上下文信息，包含sessionId, userId, characterId等

        Returns:
            AI生成的回复文本
        """
        start_time = time.time()
        request_id = context.get('requestId', 'unknown')

        try:
            # 提取上下文信息
            user_id = context.get('userId')
            session_id = context.get('sessionId')
            character_id = context.get('characterId', 'default')

            logger.info(f"[{request_id}] 开始处理消息 - 用户: {user_id}, 会话: {session_id}")

            # 【故事1.7-B】第零阶段：危机检测（架构师要求：在最开始进行）
            # 使用run_in_executor避免阻塞异步事件循环（架构师建议）
            loop = asyncio.get_event_loop()
            is_crisis = await loop.run_in_executor(
                None, self.crisis_detection_service.detect, user_message
            )

            if is_crisis:
                logger.error(
                    f"[{request_id}] 检测到危机信号 - CRISIS - 用户: {user_id}, "
                    f"会话: {session_id}, 消息: '{user_message[:50]}...'"
                )

                # [新增] 异步触发用户音频流禁用，防止进一步危机言论
                room_id = context.get("roomId")
                if room_id:
                    asyncio.create_task(
                        self.volcano_client.ban_user_stream(
                            room_id=room_id,
                            user_id=user_id,
                            ban_audio=True,
                            ban_video=False,
                            duration_seconds=600  # 10分钟
                        )
                    )
                else:
                    logger.warning(f"[{request_id}] 无法获取roomId，跳过音频流封禁")

                # 立即返回脚本化危机回复，中断后续流程
                crisis_response = self._get_scripted_crisis_response()

                total_duration = time.time() - start_time
                logger.error(
                    f"[{request_id}] 危机干预回复已生成 - CRISIS - 耗时: {total_duration:.3f}s"
                )

                return crisis_response

            # 第一阶段：记忆检索（带容错机制）
            memory_start_time = time.time()
            memory_context = await self._retrieve_memory_with_fallback(
                user_id=user_id,
                user_message=user_message,
                request_id=request_id
            )
            memory_duration = time.time() - memory_start_time

            # 第二阶段：构建提示
            prompt_start_time = time.time()
            messages = await self._build_prompt(
                user_message=user_message,
                memory_context=memory_context,
                character_id=character_id,
                request_id=request_id
            )
            prompt_duration = time.time() - prompt_start_time

            # 【故事1.6-B】第三阶段：Function Calling集成的LLM调用循环
            llm_start_time = time.time()
            ai_response = await self._handle_function_calling_loop(
                messages=messages,
                context=context,
                request_id=request_id
            )
            llm_duration = time.time() - llm_start_time

            # 记录详细性能指标
            total_duration = time.time() - start_time
            logger.info(
                f"[{request_id}] 处理完成 - 总耗时: {total_duration:.3f}s "
                f"(记忆: {memory_duration:.3f}s, 提示: {prompt_duration:.3f}s, LLM: {llm_duration:.3f}s), "
                f"回复长度: {len(ai_response)}"
            )

            return ai_response

        except Exception as e:
            total_duration = time.time() - start_time
            logger.error(f"[{request_id}] 处理消息时发生错误: {str(e)}, 耗时: {total_duration:.3f}s", exc_info=True)
            # 返回友好的错误回复而不是抛出异常
            return "抱歉，我现在遇到了一些技术问题，请稍后再试。"

    async def handle_message_stream(
        self,
        user_message: str,
        context: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """
        流式处理用户消息的核心方法 - 故事1.14-B SSE流式修复

        实现架构师建议：
        1. SSE流内异常处理 - 异常转换为error事件而不是破坏连接
        2. 资源管理 - 实现30秒超时机制
        3. 危机检测流式响应格式一致性
        4. 真正的流式LLM调用（yield from直接传递数据块）

        Args:
            user_message: 用户消息文本
            context: 上下文信息，包含sessionId, userId, characterId等

        Yields:
            AI生成的回复文本片段
        """
        start_time = time.time()
        request_id = context.get('requestId', f'stream_{int(time.time() * 1000)}')

        try:
            # 在流式响应中包装整个处理流程，确保异常不会破坏连接
            logger.info(f"[{request_id}] 开始真正的流式处理消息")

            # 【故事1.7-B】首先进行危机检测
            # 使用run_in_executor避免阻塞异步事件循环（架构师建议）
            loop = asyncio.get_event_loop()
            is_crisis = await loop.run_in_executor(
                None, self.crisis_detection_service.detect, user_message
            )

            if is_crisis:
                logger.error(
                    f"[{request_id}] 流式处理中检测到危机信号 - CRISIS - "
                    f"消息: '{user_message[:50]}...'"
                )

                # [新增] 异步触发用户音频流禁用，防止进一步危机言论
                user_id = context.get('userId')
                room_id = context.get("roomId")
                if room_id and user_id:
                    asyncio.create_task(
                        self.volcano_client.ban_user_stream(
                            room_id=room_id,
                            user_id=user_id,
                            ban_audio=True,
                            ban_video=False,
                            duration_seconds=600  # 10分钟
                        )
                    )
                else:
                    logger.warning(f"[{request_id}] 无法获取roomId或userId，跳过音频流封禁")

                # 生成脚本化危机回复的流式版本
                async for chunk in self._get_scripted_crisis_response_stream():
                    yield chunk

                total_duration = time.time() - start_time
                logger.error(
                    f"[{request_id}] 危机干预流式回复已完成 - CRISIS - 耗时: {total_duration:.3f}s"
                )
                return

            # 设置30秒超时，符合架构师建议
            async with asyncio.timeout(30):
                # 【故事1.14-B】真正的流式处理 - 直接调用LLM流式方法

                # 1. 获取记忆上下文（非阻塞）
                user_id = context.get('userId')
                session_id = context.get('sessionId')
                character_id = context.get('characterId', 'default')

                # 快速检索记忆（设置较短超时）
                memory_context = {}
                if user_id and session_id:
                    try:
                        async with asyncio.timeout(3):  # 3秒记忆检索超时
                            memory_result = await self.memory_service.get_memory_context(
                                user_id=user_id,
                                query=user_message or "对话记忆"
                            )
                            memory_context = memory_result or {}
                    except asyncio.TimeoutError:
                        logger.warning(f"[{request_id}] 记忆检索超时，使用空上下文")
                        memory_context = {}
                    except Exception as e:
                        logger.warning(f"[{request_id}] 记忆检索失败: {e}，使用空上下文")
                        memory_context = {}

                # 2. 使用PromptBuilderService构建完整的messages（包含角色system prompt）
                try:
                    messages = await self.prompt_builder_service.build_messages(
                        user_message=user_message,
                        memory_context=memory_context,
                        character_id=character_id
                    )
                    logger.info(f"[{request_id}] 流式处理使用完整角色system prompt构建消息")
                except Exception as e:
                    logger.error(f"[{request_id}] 构建完整消息失败，使用简化版本: {e}")
                    # 降级为简化版本
                    messages = []
                    if memory_context.get("context"):
                        messages.append({"role": "system", "content": f"上下文记忆: {memory_context['context']}"})
                    messages.append({"role": "user", "content": user_message})

                request_data = {
                    "model": f"ep-{self.llm_proxy_service.endpoint_id}",
                    "messages": messages,
                    "stream": True,
                    "max_tokens": 2000,
                    "temperature": 0.7
                }

                # 3. 直接使用LLM流式方法（yield from传递数据块）
                first_chunk_time = None
                async for chunk in self.llm_proxy_service._call_volcano_llm_api_stream(request_data):
                    if first_chunk_time is None:
                        first_chunk_time = time.time() - start_time
                        logger.info(f"[{request_id}] 首字延迟: {first_chunk_time:.3f}s")

                    yield chunk

                total_duration = time.time() - start_time
                logger.info(f"[{request_id}] 真正流式处理完成 - 总耗时: {total_duration:.3f}s")

        except asyncio.TimeoutError:
            # 超时处理 - 发送错误信息而不是抛出异常
            logger.error(f"[{request_id}] 流式处理超时")
            yield "[ERROR: 连接超时，请重试]"

        except Exception as e:
            # 捕获所有异常，转换为错误消息而不是破坏连接
            total_duration = time.time() - start_time
            logger.error(f"[{request_id}] 流式处理时发生错误: {str(e)}, 耗时: {total_duration:.3f}s", exc_info=True)
            yield f"[ERROR: {str(e)}]"

    # 【故事1.6-B + 1.14-B】新增：Function Calling工具调用异步处理
    async def _handle_function_calling_loop(
        self,
        messages: List[Dict[str, str]],
        context: Dict[str, Any],
        request_id: str
    ) -> str:
        """
        处理Function Calling工具调用异步流程 - 故事1.14-B修复

        实现架构师建议的异步回调机制：
        1. LLM返回tool_calls后立即调用update_voice_chat
        2. 不再同步循环调用LLM
        3. 等待Webhook回调处理工具结果
        4. 实现状态映射和超时保护

        Args:
            messages: 对话消息历史
            context: 上下文信息（必须包含roomId和taskId）
            request_id: 请求ID

        Returns:
            空字符串或特定信号，表示交互已转交RTC处理
        """
        tool_call_start_time = time.time()

        # 验证必需的上下文参数
        room_id = context.get('roomId')
        task_id = context.get('taskId')
        session_id = context.get('sessionId')

        if not room_id or not task_id:
            logger.error(f"[{request_id}] Function Calling缺少必需参数: roomId={room_id}, taskId={task_id}")
            # 降级为普通LLM调用
            return await self._call_llm_with_fallback(messages, request_id)

        try:
            # 获取工具定义
            tool_definitions = self.tool_executor_service.get_tool_definitions()
            logger.info(f"[{request_id}] 开始Function Calling异步流程，可用工具: {len(tool_definitions)}")

            # 调用LLM，包含工具定义（只调用一次）
            llm_response = await self._call_llm_with_tools(
                messages=messages,
                tools=tool_definitions,
                request_id=request_id
            )

            # 检查是否有工具调用请求
            tool_calls = llm_response.get('tool_calls')
            if not isinstance(tool_calls, list) or not tool_calls:
                # 没有工具调用，返回LLM的直接回复
                logger.info(f"[{request_id}] 无工具调用需求，返回LLM直接回复")
                return llm_response.get('content', '抱歉，我没有生成回复。')

            logger.info(f"[{request_id}] 检测到工具调用: {[tc.get('function', {}).get('name') for tc in tool_calls if isinstance(tc, dict)]}")

            # 执行工具调用
            try:
                # 转换为ToolCall对象
                tool_call_objects = []
                for tc in tool_calls:
                    if not isinstance(tc, dict):
                        logger.warning(f"[{request_id}] 跳过非字典类型的tool_call元素: {type(tc)}")
                        continue

                    function_info = tc.get('function', {})
                    if not isinstance(function_info, dict):
                        logger.warning(f"[{request_id}] 跳过无效的function_info: {type(function_info)}")
                        continue

                    # 处理arguments字段
                    arguments = function_info.get('arguments', {})
                    if isinstance(arguments, str):
                        try:
                            arguments = json.loads(arguments)
                        except json.JSONDecodeError as e:
                            logger.error(f"[{request_id}] 工具调用参数JSON解析失败: {e}, 原始内容: {arguments}")
                            arguments = {}
                    elif arguments is None:
                        arguments = {}

                    tool_call_objects.append(ToolCall(
                        id=tc.get('id', f'call_{int(time.time())}'),
                        name=function_info.get('name', ''),
                        arguments=arguments
                    ))

                # 执行工具
                tool_results = await self.tool_executor_service.execute_tool_calls(
                    tool_calls=tool_call_objects,
                    context=context
                )

                # 【故事1.14-B关键修复】调用update_voice_chat发送工具结果
                # 不再将结果加入messages列表并继续循环调用LLM
                for result in tool_results:
                    # 构造message参数，格式为JSON字符串
                    message_data = {
                        "ToolCallID": result.tool_call_id,
                        "Content": result.content
                    }
                    message_json = json.dumps(message_data, ensure_ascii=False)

                    logger.info(f"[{request_id}] 调用update_voice_chat发送工具结果: {result.tool_call_id}")

                    # 调用火山引擎API
                    update_result = await self.volcano_client.update_voice_chat(
                        room_id=room_id,
                        task_id=task_id,
                        command="function",
                        message=message_json
                    )

                    if update_result.get('success'):
                        logger.info(f"[{request_id}] 工具结果已发送到火山引擎: {result.tool_call_id}")
                    else:
                        logger.error(f"[{request_id}] 发送工具结果失败: {update_result.get('error', '未知错误')}")

                # 【关键变更】返回空字符串，表示交互已转交RTC处理
                # 后续的AI回复将通过Webhook回调生成
                elapsed_time = time.time() - tool_call_start_time
                logger.info(f"[{request_id}] Function Calling异步流程完成，耗时: {elapsed_time:.3f}s，等待Webhook回调")

                return ""  # 空回复，表示等待异步处理

            except Exception as tool_error:
                # 工具调用降级策略：返回错误信息给用户
                logger.error(f"[{request_id}] 工具执行失败: {str(tool_error)}")
                return f"抱歉，工具执行失败: {str(tool_error)}"

        except Exception as e:
            logger.error(f"[{request_id}] Function Calling异步流程发生错误: {str(e)}", exc_info=True)
            # 最终降级策略：回退到基础LLM调用
            return await self._call_llm_with_fallback(messages, request_id)

    async def _call_llm_with_tools(
        self,
        messages: List[Dict[str, str]],
        tools: List[Dict[str, Any]],
        request_id: str
    ) -> Dict[str, Any]:
        """
        调用LLM，包含工具定义

        Args:
            messages: 对话消息历史
            tools: 工具定义列表
            request_id: 请求ID

        Returns:
            LLM响应，可能包含tool_calls
        """
        try:
            # 调用LLM代理服务，传入工具定义
            response = await self.llm_proxy_service.call_llm(
                messages=messages,
                tools=tools
            )
            # 如果响应是字符串，包装成标准格式
            if isinstance(response, str):
                return {
                    "content": response,
                    "tool_calls": []
                }
            return response
        except Exception as e:
            logger.error(f"[{request_id}] LLM工具调用失败: {str(e)}")
            # 降级到普通LLM调用
            return {
                "content": await self._call_llm_with_fallback(messages, request_id),
                "tool_calls": []
            }

    async def _retrieve_memory_with_fallback(
        self,
        user_id: Optional[str],
        user_message: str,
        request_id: str
    ) -> Dict[str, Any]:
        """
        记忆检索（带容错机制）

        Args:
            user_id: 用户ID
            user_message: 用户消息
            request_id: 请求ID

        Returns:
            记忆上下文，失败时返回空上下文
        """
        try:
            if not user_id:
                logger.warning(f"[{request_id}] 缺少用户ID，跳过记忆检索")
                return {"memories": [], "context": "无记忆上下文"}

            # 调用记忆服务
            memories = await self.memory_service.search_memory(
                user_id=user_id,
                query=user_message,
                limit=5
            )

            # 构建与原格式兼容的返回值
            memory_response = {
                "memories": memories,
                "context": "\n".join([msg["content"] for msg in memories])
            }

            logger.info(f"[{request_id}] 记忆检索成功，找到 {len(memory_response.get('memories', []))} 条记忆")
            return memory_response

        except Exception as e:
            # 记忆服务容错：降级为空上下文继续对话
            logger.warning(f"[{request_id}] 记忆检索失败，降级为空上下文: {str(e)}")
            return {"memories": [], "context": "记忆服务暂时不可用"}

    async def _build_prompt(
        self,
        user_message: str,
        memory_context: Dict[str, Any],
        character_id: str,
        request_id: str
    ) -> List[Dict[str, str]]:
        """
        构建LLM提示
        """
        try:
            messages = await self.prompt_builder_service.build_messages(
                user_message=user_message,
                memory_context=memory_context,
                character_id=character_id
            )

            logger.info(f"[{request_id}] 提示构建成功，消息数量: {len(messages)}")
            return messages

        except Exception as e:
            logger.error(f"[{request_id}] 提示构建失败: {str(e)}")
            # 构建最基础的提示
            return [
                {"role": "system", "content": "你是一个有帮助的AI助手。"},
                {"role": "user", "content": user_message}
            ]

    async def _call_llm_with_fallback(
        self,
        messages: List[Dict[str, str]],
        request_id: str
    ) -> str:
        """
        调用LLM（带容错机制）
        """
        try:
            response = await self.llm_proxy_service.call_llm(messages)
            logger.info(f"[{request_id}] LLM调用成功，响应长度: {len(response)}")
            return response

        except Exception as e:
            logger.error(f"[{request_id}] LLM调用失败: {str(e)}")
            return "抱歉，我现在无法正常回复，请稍后再试。"

    def _get_scripted_crisis_response(self) -> str:
        """
        【故事1.7-B】获取脚本化的危机干预回复

        返回包含心理援助热线的标准回复，确保危机时刻的安全响应。
        """
        return (
            "我感受到了你的痛苦和困扰。你并不孤单，现在就有人可以帮助你。\n\n"
            "如果你正在经历心理危机或有自伤的想法，请立即拨打：\n"
            "• **国家心理危机干预热线：400-161-9995**\n"
            "• 或者联系当地的紧急服务\n\n"
            "你的生命很宝贵，请不要放弃。专业的心理咨询师随时准备为你提供帮助和支持。"
        )

    async def _get_scripted_crisis_response_stream(self) -> AsyncGenerator[str, None]:
        """
        【故事1.7-B】获取脚本化危机回复的流式版本

        确保危机干预在流式和非流式接口中的响应格式一致性。
        """
        crisis_text = self._get_scripted_crisis_response()

        # 分块发送，保持流式响应的用户体验
        chunk_size = 30  # 每块30个字符
        for i in range(0, len(crisis_text), chunk_size):
            chunk = crisis_text[i:i + chunk_size]
            yield chunk
            await asyncio.sleep(0.1)  # 稍慢的速度，增强关怀感


async def get_chat_orchestration_service() -> ChatOrchestrationService:
    """获取对话编排服务实例"""
    memory_service = await get_memory_service()
    llm_proxy_service = await get_llm_proxy_service()
    tool_executor_service = await get_tool_executor_service()
    prompt_builder_service = await get_prompt_builder_service()
    crisis_detection_service = get_crisis_detection_service()  # 不需要await

    return ChatOrchestrationService(
        memory_service=memory_service,
        llm_proxy_service=llm_proxy_service,
        tool_executor_service=tool_executor_service,
        prompt_builder_service=prompt_builder_service,
        crisis_detection_service=crisis_detection_service
    )
