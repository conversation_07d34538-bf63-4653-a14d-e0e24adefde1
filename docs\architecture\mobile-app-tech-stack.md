# 心桥移动应用技术栈文档

## 项目概述

心桥移动应用是一个面向老年人的智能助手应用，采用现代化的 React Native 技术栈构建，注重无障碍设计和用户体验。

## 核心技术栈

### 1. React Native & Expo
- **React Native**: `0.76.6`
- **Expo SDK**: `~52.0.26`
- **Expo Router**: `~4.0.17`
- **Expo CLI**: 最新版本

**选择理由**：
- 跨平台开发，一套代码同时支持 iOS 和 Android
- Expo 提供丰富的原生功能和便捷的开发工具
- Expo Router 提供类型安全的文件路由系统

**相关依赖**：
```json
{
  "expo": "~52.0.26",
  "expo-router": "~4.0.17",
  "expo-dev-client": "~5.0.9",
  "expo-constants": "~17.0.4",
  "expo-font": "~13.0.3",
  "expo-image": "~2.0.4",
  "expo-linking": "~7.0.4",
  "expo-localization": "~16.0.1",
  "expo-splash-screen": "~0.29.21",
  "expo-status-bar": "~2.0.1",
  "expo-system-ui": "~4.0.7"
}
```

### 2. TypeScript
- **TypeScript**: `^5.3.3`
- **配置**: 严格模式，支持绝对路径导入

**选择理由**：
- 类型安全，减少运行时错误
- 更好的代码提示和重构支持
- 团队协作中的代码质量保证

### 3. UI 框架 - Nativewind
- **Nativewind**: `4.1.21`
- **Tailwind CSS**: `3.4.4`
- **Tailwind Variants**: `^0.2.1`

**选择理由**：
- 将 Tailwind CSS 带到 React Native
- 快速样式开发，保持设计一致性
- 响应式设计支持

**配置示例**：
```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // 适老化设计色彩
        primary: { /* 高对比度主色 */ },
        secondary: { /* 辅助色 */ }
      },
      fontSize: {
        // 大字体支持
        'xl-plus': '1.375rem',
        '2xl-plus': '1.75rem'
      }
    }
  }
}
```

### 4. 状态管理 - Zustand
- **Zustand**: `4.5.5`

**选择理由**：
- 轻量级，API 简洁
- TypeScript 友好
- 无需额外的 Provider 包装

**使用模式**：
```typescript
interface AuthState {
  token: TokenType | null;
  status: 'idle' | 'signOut' | 'signIn';
  signIn: (data: TokenType) => void;
  signOut: () => void;
}

export const useAuth = create<AuthState>((set) => ({
  // 状态实现
}));
```

### 5. 数据获取 - React Query
- **React Query**: `@tanstack/react-query 5.52.1`
- **React Query Kit**: `^3.3.0`

**选择理由**：
- 强大的服务器状态管理
- 自动缓存、后台更新、错误处理
- React Query Kit 提供类型安全的 API 抽象

**使用模式**：
```typescript
// 使用 React Query Kit
export const usePosts = createQuery({
  queryKey: ['posts'],
  queryFn: () => api.get('/posts'),
});
```

## 导航与路由

### Expo Router
- **版本**: `~4.0.17`
- **特性**: 文件系统路由、类型安全、深度链接支持

**路由结构**：
```
src/app/
├── _layout.tsx          # 根布局
├── (app)/               # 应用主要页面组
│   ├── _layout.tsx      # Tab 导航布局
│   ├── index.tsx        # 首页
│   └── settings.tsx     # 设置页
├── login.tsx            # 登录页
└── onboarding.tsx       # 引导页
```

## UI 组件库

### 核心 UI 组件
- **React Native Safe Area Context**: `4.12.0`
- **React Native Screens**: `4.4.0`
- **React Native Gesture Handler**: `~2.20.2`
- **React Native Reanimated**: `~3.16.1`

### 专用组件
- **Bottom Sheet**: `@gorhom/bottom-sheet 5.0.5`
- **Flash List**: `@shopify/flash-list 1.7.1`
- **SVG 支持**: `react-native-svg 15.8.0`
- **Notifications**: `expo-notifications` (使用 `npx expo install expo-notifications` 安装)
- **Audio/Video**: `expo-av` (用于麦克风和音频功能)

## 表单处理

### React Hook Form
- **React Hook Form**: `7.53.0`
- **Hookform Resolvers**: `3.9.0`
- **Zod 验证**: `3.23.8`

**使用模式**：
```typescript
const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8)
});

const { control, handleSubmit } = useForm<FormType>({
  resolver: zodResolver(schema)
});
```

## 网络请求

### HTTP 客户端
- **Axios**: `1.7.5`

**配置示例**：
```typescript
const api = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL,
  timeout: 10000,
});
```

## 本地存储

### MMKV
- **React Native MMKV**: `3.1.0`

**选择理由**：
- 高性能键值存储
- 同步 API，简化使用
- 跨平台一致性

## 国际化

### i18next
- **i18next**: `23.14.0`
- **React i18next**: `15.0.1`
- **Expo Localization**: `16.0.1`

**支持语言**：
- 中文（简体）
- 英文
- 阿拉伯语

## 动画库

### Moti & Reanimated
- **Moti**: `0.29.0`
- **React Native Reanimated**: `~3.16.1`

**选择理由**：
- Moti 提供声明式动画 API
- Reanimated 提供高性能原生动画
- 适合老年用户的平滑过渡效果

## 开发工具

### 代码质量
- **ESLint**: `8.57.0`
- **Prettier**: `3.3.3`
- **TypeScript**: `^5.3.3`
- **Husky**: `9.1.5` (Git hooks)

### 测试框架
- **Jest**: `29.7.0`
- **React Native Testing Library**: `12.7.2`
- **Jest Expo**: `~52.0.3`

### 构建工具
- **Metro**: Expo 默认配置
- **EAS Build**: Expo 云构建服务

## 性能优化

### 列表渲染
- **FlashList**: 高性能列表组件
- **图片优化**: Expo Image 组件

### 内存管理
- **MMKV**: 高效本地存储
- **React Query**: 智能缓存策略

## 适老化设计支持

### 无障碍功能
- React Native 内置无障碍支持
- 高对比度颜色方案
- 大字体支持
- 语音提示集成

### 键盘处理
- **React Native Keyboard Controller**: `1.13.2`
- 自动键盘避让
- 优化的输入体验

## 版本管理

### 包管理器
- **PNPM**: `9.12.3`
- **Node.js**: 推荐 LTS 版本

### 发布流程
- **NP**: `10.0.7` (自动化发布)
- **EAS Build**: 云端构建
- **版本控制**: 语义化版本

## 环境配置

### 开发环境
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm start

# 运行特定平台
pnpm android  # Android
pnpm ios      # iOS
```

### 构建环境
- **开发环境**: development
- **测试环境**: staging  
- **生产环境**: production

## 代码规范

### 文件命名
- 使用 kebab-case 命名文件
- 组件文件使用 PascalCase

### 组件规范
- 函数式组件优先
- 严格的 TypeScript 类型定义
- Props 接口定义在组件顶部

### 目录结构
```
src/
├── api/          # API 相关代码
├── app/          # 页面路由
├── components/   # 共享组件
├── lib/          # 工具库
├── translations/ # 国际化文件
└── types/        # 类型定义
```

## 推荐的最佳实践

1. **组件设计**: 保持组件单一职责，不超过 80 行代码
2. **状态管理**: 优先使用 React 内置状态，复杂状态使用 Zustand
3. **性能优化**: 使用 React.memo、useMemo、useCallback 适当优化
4. **错误边界**: 使用 react-error-boundary 处理错误
5. **类型安全**: 严格的 TypeScript 配置，避免 any 类型
6. **测试覆盖**: 为核心功能和组件编写单元测试

## 更新策略

- **依赖更新**: 每月检查并更新非主要版本
- **Expo SDK**: 跟随 Expo 发布周期更新
- **安全更新**: 及时应用安全补丁
- **兼容性测试**: 更新后进行全面测试

这份技术栈文档将随着项目发展持续更新，确保技术选择符合项目需求和最佳实践。

## 错误处理与监控
- **Error Boundary**: `react-error-boundary 4.0.11`
- **错误上报**: `@sentry/react-native 5.15.0`
- **网络状态**: `@react-native-community/netinfo 11.2.1` 