{"rules": ["这是后端 Python API 项目目录", "使用 Python, FastAPI, Agno Framework 技术栈", "只对 Python 相关文件提供建议，遵循 Pydantic 和 FastAPI 规范", "重点关注 Agno Agent 的正确使用方式", "使用 Memory 组件和 PostgresMemoryDb 进行记忆管理", "enable_agentic_memory=True 和 enable_user_memories=True 是关键配置", "避免使用已弃用的 AgnoMemoryService 等组件"], "composer": {"exclude": ["__pycache__", ".venv", "venv", ".pytest_cache", ".mypy_cache", ".ruff_cache", "dist", "build"]}}