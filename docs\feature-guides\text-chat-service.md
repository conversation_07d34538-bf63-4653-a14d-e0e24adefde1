# 文本聊天服务 API 说明

## 功能概述

文本聊天服务提供基于Server-Sent Events (SSE)的实时文本对话功能，作为核心语音交互的重要补充和降级方案。该服务支持：

- **实时文本对话**: 通过SSE流式返回AI回复，实现打字机效果
- **智能记忆集成**: 自动加载用户历史对话上下文和长期记忆
- **多设备支持**: 每用户支持最多3个并发连接
- **高可用设计**: 完善的错误处理和资源管理机制
- **生产级性能**: P95延迟 < 1.2秒，支持100+并发会话

**技术特点**:
- 不依赖火山RTC，提供稳定的文本交互通道
- JWT认证保护，确保用户数据安全
- SSE流内错误处理，保证连接稳定性
- 30秒超时机制，防止资源泄漏

## 核心API端点

### POST /api/v1/chat/text_message
发送文本消息并接收AI的流式回复。

**认证方式**: JWT Bearer Token (必须)

**请求格式**:
```http
POST /api/v1/chat/text_message HTTP/1.1
Host: localhost:8003
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "message": "你好，今天天气怎么样？",
  "sessionId": "session-uuid-123", 
  "characterId": "default_character"
}
```

**响应格式**: `Content-Type: text/event-stream`
```http
HTTP/1.1 200 OK
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

event: text_chunk
data: {"delta": "今天"}

event: text_chunk
data: {"delta": "天气"}

event: text_chunk
data: {"delta": "很不错"}

event: stream_end
data: {"messageId": null, "status": "done", "finalContentLength": 9}
```

**错误响应格式**:
```http
event: error
data: {"type": "PROCESSING_ERROR", "message": "具体错误信息"}
```

### GET /api/v1/chat/connections/status
查询当前用户的连接状态（调试用）。

**认证方式**: JWT Bearer Token (必须)

**响应格式**:
```json
{
  "user_id": "user-123",
  "active_connections": 1,
  "max_connections": 3,
  "total_active_users": 25
}
```

## 数据契约

### TextMessageRequest (请求体)
| 字段 | 类型 | 必填 | 说明 | 限制 |
|------|------|------|------|------|
| message | string | ✅ | 用户消息文本 | 1-8000字符 |
| sessionId | string | ✅ | 会话ID | 最小1字符 |
| characterId | string | ❌ | AI角色ID | 默认"default_character" |

### SSE事件类型

#### text_chunk (文本片段)
```json
{
  "event": "text_chunk",
  "data": {
    "delta": "文本片段内容"
  }
}
```

#### stream_end (流结束)
```json
{
  "event": "stream_end", 
  "data": {
    "messageId": null,
    "status": "done",
    "finalContentLength": 123
  }
}
```

#### error (错误事件)
```json
{
  "event": "error",
  "data": {
    "type": "PROCESSING_ERROR|CONNECTION_TIMEOUT|SSE_GENERATOR_ERROR",
    "message": "错误描述信息"
  }
}
```

## 调用示例与注意事项

### 基础调用示例 (JavaScript)

```javascript
// 1. 基本SSE连接
const eventSource = new EventSource('/api/v1/chat/text_message', {
  headers: {
    'Authorization': 'Bearer ' + your_jwt_token
  }
});

// 2. 使用fetch发送POST请求 (推荐)
const response = await fetch('/api/v1/chat/text_message', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + your_jwt_token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: "你好，今天天气怎么样？",
    sessionId: generateSessionId(),
    characterId: "default_character"
  })
});

if (response.ok) {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    const events = parseSSEChunk(chunk);
    
    events.forEach(event => {
      switch (event.event) {
        case 'text_chunk':
          const data = JSON.parse(event.data);
          appendToChat(data.delta);
          break;
        case 'stream_end':
          finalizeChatMessage();
          break;
        case 'error':
          handleError(JSON.parse(event.data));
          break;
      }
    });
  }
}
```

### 关键注意事项

#### 1. 认证管理
- **Token过期**: 实现token自动刷新机制，避免认证失败
- **安全存储**: 使用安全存储方案保护JWT token
- **错误处理**: 妥善处理401认证错误，实现自动重新认证

#### 2. 连接管理
- **并发限制**: 每用户最多3个同时连接，超出限制会收到429错误
- **连接监控**: 定期检查连接状态，及时处理断开和重连
- **资源清理**: 确保在页面卸载时正确关闭SSE连接

#### 3. 错误处理
- **网络错误**: 实现指数退避重试机制，最多重试3次
- **超时处理**: 30秒连接超时，需要重新建立连接
- **错误事件**: SSE流内的error事件不会中断连接，需要根据错误类型决定后续操作

#### 4. 性能优化
- **首次响应**: 系统保证P95延迟 < 1.2秒，但建议设置loading状态
- **内存管理**: 及时清理已完成的聊天内容，避免内存泄漏
- **缓存策略**: 合理缓存会话数据，减少不必要的重复请求

#### 5. 开发调试
- **调试端点**: 使用`/api/v1/chat/connections/status`监控连接状态
- **日志记录**: 服务端会记录详细的请求日志，包含request_id用于问题追踪
- **本地测试**: 确保后端服务在8003端口运行: `uvicorn api.main:app --reload --host 0.0.0.0 --port 8003`

#### 6. 生产环境配置
- **HTTPS要求**: 生产环境必须使用HTTPS协议
- **CORS配置**: 根据实际域名配置合适的CORS策略
- **负载均衡**: 支持水平扩展，但需要考虑SSE连接的粘性会话
- **监控告警**: 建议监控连接数、响应时间、错误率等关键指标

### SSE解析工具函数

```javascript
function parseSSEChunk(chunk) {
  const events = [];
  const lines = chunk.split('\n');
  let currentEvent = {};
  
  for (const line of lines) {
    if (line.startsWith('event:')) {
      currentEvent.event = line.slice(6).trim();
    } else if (line.startsWith('data:')) {
      currentEvent.data = line.slice(5).trim();
    } else if (line === '') {
      if (currentEvent.event && currentEvent.data) {
        events.push({...currentEvent});
        currentEvent = {};
      }
    }
  }
  
  return events;
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025年1月16日  
**服务状态**: ✅ 生产就绪 