"""
认证服务测试 - 对应故事1.2-B: 后端认证服务与用户管理API
基于5个验收标准(AC1-AC5)的完整测试套件
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import json

from api.main import app

client = TestClient(app)

# 测试标记
pytestmark = [pytest.mark.auth, pytest.mark.integration]

class TestAnonymousAuth:
    """AC1: 无感身份认证服务测试"""

    def test_anonymous_login_with_device_info(self):
        """测试基于设备信息的匿名用户创建"""
        device_info = {
            "device_id": "test-device-001",
            "platform": "ios",
            "app_version": "1.0.0"
        }

        response = client.post(
            "/api/v1/auth/anonymous-login",
            json={"device_info": device_info}
        )

        # 预期：这个测试会失败，因为endpoint还没实现
        assert response.status_code == 200
        data = response.json()

        # 验证返回结构
        assert "user" in data
        assert "access_token" in data
        assert "refresh_token" in data

        # 验证用户信息结构
        user = data["user"]
        assert "id" in user
        assert "created_at" in user

        # 验证Token是JWT格式
        access_token = data["access_token"]
        assert isinstance(access_token, str)
        assert len(access_token.split('.')) == 3  # JWT的三部分结构

    def test_device_fingerprint_uniqueness(self):
        """测试设备指纹算法的唯一性保证"""
        device_info_1 = {
            "device_id": "test-device-001",
            "platform": "ios",
            "app_version": "1.0.0"
        }
        device_info_2 = {
            "device_id": "test-device-002",
            "platform": "android",
            "app_version": "1.0.0"
        }

        # 创建两个不同设备的用户
        response1 = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info_1})
        response2 = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info_2})

        assert response1.status_code == 200
        assert response2.status_code == 200

        user1_id = response1.json()["user"]["id"]
        user2_id = response2.json()["user"]["id"]

        # 验证不同设备生成不同用户ID
        assert user1_id != user2_id

    def test_same_device_returns_same_user(self):
        """测试相同设备重复登录返回相同用户"""
        device_info = {
            "device_id": "test-device-consistent",
            "platform": "ios",
            "app_version": "1.0.0"
        }

        # 两次相同设备登录
        response1 = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        response2 = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})

        assert response1.status_code == 200
        assert response2.status_code == 200

        user1_id = response1.json()["user"]["id"]
        user2_id = response2.json()["user"]["id"]

        # 验证相同设备返回相同用户
        assert user1_id == user2_id

    def test_jwt_token_validation(self):
        """测试JWT Token验证机制"""
        # 创建匿名用户
        device_info = {"device_id": "test-device-jwt", "platform": "ios", "app_version": "1.0.0"}
        login_response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})

        assert login_response.status_code == 200
        access_token = login_response.json()["access_token"]

        # 使用Token访问需要认证的端点
        headers = {"Authorization": f"Bearer {access_token}"}
        response = client.get("/api/v1/user/profile", headers=headers)

        assert response.status_code == 200

    def test_token_refresh_mechanism(self):
        """测试JWT Token刷新机制"""
        # 创建匿名用户
        device_info = {"device_id": "test-device-refresh", "platform": "ios", "app_version": "1.0.0"}
        login_response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})

        assert login_response.status_code == 200
        refresh_token = login_response.json()["refresh_token"]

        # 使用refresh token获取新的access token
        refresh_response = client.post(
            "/api/v1/auth/refresh-token",
            json={"refresh_token": refresh_token}
        )

        assert refresh_response.status_code == 200
        data = refresh_response.json()

        assert "access_token" in data
        assert "refresh_token" in data
        assert "expires_in" in data

class TestUserManagementAPI:
    """AC2: 用户管理API测试"""

    @pytest.fixture
    def authenticated_user(self):
        """创建认证用户的测试夹具"""
        device_info = {"device_id": "test-user-mgmt", "platform": "ios", "app_version": "1.0.0"}
        response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        assert response.status_code == 200

        return {
            "user_id": response.json()["user"]["id"],
            "access_token": response.json()["access_token"],
            "headers": {"Authorization": f"Bearer {response.json()['access_token']}"}
        }

    def test_get_user_profile(self, authenticated_user):
        """测试用户画像查询"""
        response = client.get("/api/v1/user/profile", headers=authenticated_user["headers"])

        assert response.status_code == 200
        data = response.json()

        # 验证用户画像基本字段
        assert "id" in data
        assert "nickname" in data
        assert "age_range" in data
        assert "core_needs" in data
        assert "interests" in data
        assert "communication_style_preference" in data

    def test_update_user_profile(self, authenticated_user):
        """测试用户画像更新"""
        update_data = {
            "nickname": "测试用户",
            "age_range": "25-35",
            "preferences": {"language": "zh-CN"},
            "core_needs": ["emotional_support", "daily_chat"]
        }

        response = client.put(
            "/api/v1/user/profile",
            headers=authenticated_user["headers"],
            json=update_data
        )

        assert response.status_code == 200
        data = response.json()

        # 验证更新后的数据
        assert data["nickname"] == "测试用户"
        assert data["age_range"] == "25-35"

    def test_partial_profile_update(self, authenticated_user):
        """测试用户画像部分更新"""
        partial_update = {"nickname": "新昵称"}

        response = client.patch(
            "/api/v1/user/profile",
            headers=authenticated_user["headers"],
            json=partial_update
        )

        assert response.status_code == 200
        data = response.json()
        assert data["nickname"] == "新昵称"

class TestCharacterManagementAPI:
    """AC3: AI角色管理API测试"""

    @pytest.fixture
    def authenticated_user(self):
        """创建认证用户的测试夹具"""
        device_info = {"device_id": "test-char-mgmt", "platform": "ios", "app_version": "1.0.0"}
        response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        assert response.status_code == 200

        return {
            "user_id": response.json()["user"]["id"],
            "headers": {"Authorization": f"Bearer {response.json()['access_token']}"}
        }

    def test_get_characters_list(self, authenticated_user):
        """测试AI角色列表查询"""
        response = client.get("/api/v1/characters", headers=authenticated_user["headers"])

        assert response.status_code == 200
        data = response.json()

        assert "data" in data
        assert "pagination" in data
        assert isinstance(data["data"], list)

    def test_get_character_details(self, authenticated_user):
        """测试AI角色详情查询"""
        # 先获取角色列表
        list_response = client.get("/api/v1/characters", headers=authenticated_user["headers"])
        assert list_response.status_code == 200

        characters = list_response.json()["data"]
        if characters:
            character_id = characters[0]["id"]

            # 获取角色详情
            detail_response = client.get(
                f"/api/v1/characters/{character_id}",
                headers=authenticated_user["headers"]
            )

            assert detail_response.status_code == 200
            data = detail_response.json()

            # 验证角色详情字段
            assert "id" in data
            assert "name" in data
            assert "role" in data
            assert "voice_id" in data

    def test_bind_user_character(self, authenticated_user):
        """测试用户与AI角色绑定"""
        # 先获取一个可用的角色ID
        list_response = client.get("/api/v1/characters", headers=authenticated_user["headers"])
        assert list_response.status_code == 200

        characters = list_response.json()["data"]
        if characters:
            character_id = characters[0]["id"]

            # 绑定角色
            bind_response = client.post(
                f"/api/v1/user/characters/{character_id}/bind",
                headers=authenticated_user["headers"]
            )

            assert bind_response.status_code == 200
            data = bind_response.json()
            assert data["success"] is True

class TestOnboardingAPI:
    """AC4: 引导流程数据API测试"""

    @pytest.fixture
    def authenticated_user(self):
        """创建认证用户的测试夹具"""
        device_info = {"device_id": "test-onboarding", "platform": "ios", "app_version": "1.0.0"}
        response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        assert response.status_code == 200

        return {
            "user_id": response.json()["user"]["id"],
            "headers": {"Authorization": f"Bearer {response.json()['access_token']}"}
        }

    def test_finalize_onboarding(self, authenticated_user):
        """测试引导流程完成确认接口"""
        onboarding_data = {
            "userId": authenticated_user["user_id"],
            "nickname": "小明",
            "core_needs": ["emotional_support", "daily_chat"],
            "interests": ["reading", "music"],
            "communication_style_preference": "warm",
            "allow_chat_analysis": True,
            "character": {
                "name": "艾莉",
                "role": "陪伴者",
                "voice_id": "voice_001"
            }
        }

        response = client.post(
            "/api/v1/auth/finalize_onboarding",
            headers=authenticated_user["headers"],
            json=onboarding_data
        )

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert "message" in data
        assert "user_profile" in data

    def test_onboarding_data_validation(self, authenticated_user):
        """测试引导数据的完整性验证"""
        # 测试不完整的引导数据
        incomplete_data = {
            "userId": authenticated_user["user_id"],
            "nickname": "小明"
            # 缺少必需字段
        }

        response = client.post(
            "/api/v1/auth/finalize_onboarding",
            headers=authenticated_user["headers"],
            json=incomplete_data
        )

        assert response.status_code == 422  # 验证错误

    def test_onboarding_status_check(self, authenticated_user):
        """测试引导完成后状态检查"""
        # 先完成引导流程
        onboarding_data = {
            "userId": authenticated_user["user_id"],
            "nickname": "测试用户",
            "core_needs": ["emotional_support"],
            "interests": ["music"],
            "communication_style_preference": "friendly",
            "allow_chat_analysis": True
        }

        finalize_response = client.post(
            "/api/v1/auth/finalize_onboarding",
            headers=authenticated_user["headers"],
            json=onboarding_data
        )
        assert finalize_response.status_code == 200

        # 检查用户画像中的引导完成状态
        profile_response = client.get("/api/v1/user/profile", headers=authenticated_user["headers"])
        assert profile_response.status_code == 200

        profile_data = profile_response.json()
        assert profile_data.get("onboarding_completed") is True

class TestSecurityAndPerformance:
    """AC5: 安全性与性能测试"""

    def test_unauthorized_access_blocked(self):
        """测试未授权访问被阻止"""
        response = client.get("/api/v1/user/profile")
        assert response.status_code == 401

    def test_invalid_token_rejected(self):
        """测试无效Token被拒绝"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/user/profile", headers=headers)
        assert response.status_code == 401

    def test_expired_token_handling(self):
        """测试过期Token处理"""
        # 这需要模拟过期的Token
        expired_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1c2VyXzEyMyIsImV4cCI6MTYwMDAwMDAwMH0.invalid"
        headers = {"Authorization": f"Bearer {expired_token}"}

        response = client.get("/api/v1/user/profile", headers=headers)
        assert response.status_code == 401

    @pytest.mark.slow
    def test_api_response_time(self):
        """测试API响应时间< 200ms"""
        import time

        # 创建用户并获取Token
        device_info = {"device_id": "test-performance", "platform": "ios", "app_version": "1.0.0"}
        login_response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        assert login_response.status_code == 200

        headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}

        # 测试API响应时间
        start_time = time.time()
        response = client.get("/api/v1/user/profile", headers=headers)
        end_time = time.time()

        assert response.status_code == 200
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        assert response_time < 200, f"API响应时间 {response_time}ms 超过200ms限制"

    @pytest.mark.slow
    def test_concurrent_user_authentication(self):
        """测试并发用户认证支持"""
        import asyncio
        import aiohttp

        async def create_user(session, device_id):
            device_info = {"device_id": device_id, "platform": "ios", "app_version": "1.0.0"}
            async with session.post(
                "http://localhost:8003/api/v1/auth/anonymous-login",
                json={"device_info": device_info}
            ) as response:
                return await response.json()

        async def test_concurrent():
            async with aiohttp.ClientSession() as session:
                # 创建100个并发用户
                tasks = [create_user(session, f"device_{i}") for i in range(100)]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 验证所有请求都成功
                successful_results = [r for r in results if not isinstance(r, Exception)]
                assert len(successful_results) >= 90  # 至少90%成功率

    def test_rate_limiting(self):
        """测试API频率限制"""
        # 创建用户并获取Token
        device_info = {"device_id": "test-rate-limit", "platform": "ios", "app_version": "1.0.0"}
        login_response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        assert login_response.status_code == 200

        headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}

        # 发送大量请求触发频率限制
        rate_limited = False
        for i in range(150):  # 超过每分钟100次的限制
            response = client.get("/api/v1/user/profile", headers=headers)
            if response.status_code == 429:  # Too Many Requests
                rate_limited = True
                break

        assert rate_limited, "API频率限制未生效"

    def test_row_level_security(self):
        """测试数据的行级别安全策略(RLS)"""
        # 创建两个不同的用户
        device_info_1 = {"device_id": "user1", "platform": "ios", "app_version": "1.0.0"}
        device_info_2 = {"device_id": "user2", "platform": "ios", "app_version": "1.0.0"}

        user1_response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info_1})
        user2_response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info_2})

        assert user1_response.status_code == 200
        assert user2_response.status_code == 200

        user1_headers = {"Authorization": f"Bearer {user1_response.json()['access_token']}"}
        user2_headers = {"Authorization": f"Bearer {user2_response.json()['access_token']}"}

        # 获取两个用户的画像
        profile1 = client.get("/api/v1/user/profile", headers=user1_headers)
        profile2 = client.get("/api/v1/user/profile", headers=user2_headers)

        assert profile1.status_code == 200
        assert profile2.status_code == 200

        # 验证用户只能访问自己的数据
        user1_id = profile1.json()["id"]
        user2_id = profile2.json()["id"]

        assert user1_id != user2_id
        # 更深入的RLS测试需要直接数据库查询验证

class TestMemoryServiceIntegration:
    """记忆服务集成测试 - 基于架构师建议"""

    @pytest.fixture
    def authenticated_user(self):
        """创建认证用户的测试夹具"""
        device_info = {"device_id": "test-memory-integration", "platform": "ios", "app_version": "1.0.0"}
        response = client.post("/api/v1/auth/anonymous-login", json={"device_info": device_info})
        assert response.status_code == 200

        return {
            "user_id": response.json()["user"]["id"],
            "headers": {"Authorization": f"Bearer {response.json()['access_token']}"}
        }

    @patch('api.services.memory_service.get_memory_service')
    def test_memory_space_lazy_initialization(self, mock_get_memory_service, authenticated_user):
        """测试记忆空间的延迟初始化机制"""
        # 模拟记忆服务
        mock_memory_instance = AsyncMock()
        mock_get_memory_service.return_value = mock_memory_instance

        # 在用户认证时，记忆空间不应该被初始化
        mock_memory_instance.initialize_user_space.assert_not_called()

        # 只有在首次对话时才初始化记忆空间
        # 这个测试验证了架构师建议中的延迟初始化模式

    def test_user_id_passed_to_memory_service(self, authenticated_user):
        """测试user_id正确传递给MemoryService用于会话隔离"""
        # 这个测试将验证当创建会话时，user_id被正确传递
        # 具体实现将在实现阶段完成
        pass
