# 🔗 Links:
# Source file: https://github.com/obytes/react-native-template-obytes/blob/master/.github/workflows/eas-build-qa.yml
# Starter releasing process: https://starter.obytes.com/ci-cd/app-releasing-process/

# ✍️ Description:
# This workflow is used to trigger a build on EAS for the QA environment.
# It will run on every GitHub release published on the repo or can be triggered manually from the actions tab.
# This workflow will use ./actions/eas-build action to trigger the build on EAS with staging env.

# 🚨 GITHUB SECRETS REQUIRED:
#         - EXPO_TOKEN: Expo token to authenticate with EAS
#         - You can get it from https://expo.dev/settings/access-tokens

name: EAS QA Build (Android & IOS) (EAS)

on:
  workflow_dispatch:
  release:
    types: [published]

jobs:
  Build:
    name: EAS QA Build (Android & IOS) (EAS)
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/mobile-app
    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi
      - name: 📦 Checkout project repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node + PNPM + install deps
        uses: ./.github/actions/setup-node-pnpm-install

      - name: ⏱️ EAS Build
        uses: ./.github/actions/eas-build
        with:
          APP_ENV: staging
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          VERSION: ${{ github.event.release.tag_name }}
          IOS: false # TODO: set as true when IOS account is ready

