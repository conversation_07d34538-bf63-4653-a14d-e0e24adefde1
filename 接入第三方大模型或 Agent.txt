Title: 接入第三方大模型或 Agent--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1399966

Markdown Content:
接入第三方大模型或 Agent--实时音视频-火山引擎

===============
在实时对话式 AI 场景中，支持集成第三方大模型或 Agent，以满足特定业务需求。为确保集成顺利，你需要提供第三方大模型或 Agent 的服务请求接口，并确保该接口符合火山引擎 RTC 标准规范，否则需要对其进行改造。

接入第三方大模型或 Agent 时，如 Dify Agent，支持通过以下方式传递自定义参数：

*   直接在第三方大模型或 Agent 的请求 URL 中通过查询参数的方式拼接业务自定义参数，用于传递非敏感信息（如 `session_id`）。具体实现，参考[接入方法](https://www.volcengine.com/docs/6348/1399966#%E6%8E%A5%E5%85%A5%E6%96%B9%E6%B3%95)。
*   提供 `custom` 字段，通过 JSON 格式用于传递业务自定义参数。

接入方法

1.   准备第三方大模型或 Agent 的请求接口 URL，并满足以下要求：

    *   必须使用 HTTPS 域名。
    *   必须支持公网访问。
    *   必须符合火山引擎 [接口标准](https://www.volcengine.com/docs/6348/1399966#standard)。你可通过 [接口验证工具](https://www.volcengine.com/docs/6348/1399966#tool) 验证接口是否符合标准。
    *   必须支持 SSE 协议。

2.   调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 接口时，按照规定参数结构配置 `LLMConfig`。关键配置如下：

    *   `Mode`：固定设置为 `"CustomLLM"`。
    *   `URL`：填写你的第三方大模型或 Agent 的完整 HTTPS URL。说明

如果需要在每次请求时传递非敏感的业务信息（如 `session_id`），可以直接将它们作为查询参数拼接到此 `URL` 中，格式为：`https://<第三方模型或Agent的API地址>?<业务自定义参数名>=<参数值>`。 
    *   `custom`：可选，可通过该字段传递业务自定义参数。

**配置示例**：

> 下述示例中的参数需根据实际进行替换和配置。参数详细说明，请参见 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163)。

```JSON
{
    "LLMConfig": {
        "Mode": "CustomLLM", // 固定取值 CustomLLM
        "URL": "https://api.example.com/v1/custom_chat_agent?source=volc_rtc&version=1.2", // 填写你的第三方大模型或 Agent 的完整 HTTPS URL，此处示例中，source=volc_rtc&version=1.2 为自定义参数
        "ModelName": "my_agent_model_v2", // 可选，自定义第三方大模型或 Agent 的名称
        "APIKey": "sk-xxxxxxxxxxxxxxxxx", // 可选，如果需要 Bearer Token 鉴权
        "custom": {
            "session_id": "123456",
            "user_name": "user1"
        } // 可选，业务自定义参数
        // ... 其他 LLM 通用参数如 Temperature, MaxTokens 等可以按需配置 ...
    }
}
```

JSON

接口标准

在对话期间，将按照以下格式通过 POST 请求访问你配置的第三方大模型或 Agent 的接口地址，获取相应结果。接口返回需支持 SSE 协议。

Request
-------

*   **Method**: POST

*   **Headers**:

    *   Content-Type: application/json
    *   Authorization: Bearer <your_APIKey> （当需要 Bearer Token 认证方式鉴权大模型时）

*   **Request body**:

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| messages | Array of [messages](https://www.volcengine.com/docs/6348/1399966#messages) | 是 | 参考 `StartVoiceChat.HistoryLength`，包含最近 HistoryLength 轮对话内容。当前 ASR 内容在数组末位。 |
| stream | Boolean | 否 | 取值：`true`。 |
| temperature | Float | 否 | 透传 `StartVoiceChat.LLMConfig.temperature`。 |
| max_tokens | Int | 否 | 透传 `StartVoiceChat.LLMConfig.MaxTokens`。 |
| model | String | 是 | 透传 `StartVoiceChat.LLMConfig.ModelName`。 |
| top_p | Float | 否 | 透传 `StartVoiceChat.LLMConfig.TopP`。 |
| custom | String | 否 | 自定义 JSON 字符串，透传业务传入的自定义参数。 |

messages

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| role | String | 是 | 可取值及含义如下： * `user` * `assistant` |
| content | String | 否 | 对话内容。 |

Response
--------

注意

*   **协议**：必须支持 SSE（Server-Sent Events）协议。
*   **Headers**：Content-Type 必须为 text/event-stream。
*   **结束符**：必须包含`data: [DONE]`结束符。

### StatusCode==200

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | String | 是 | 请求 UUID。不同请求的 ID 需不同，但同一个流式请求的 ID 需相同。 |
| choices | Array of [choices](https://www.volcengine.com/docs/6348/1399966#choices) | 是 | 流式回复对象。 |
| created | Int | 是 | UnixTime 时间戳，精确到秒。同一个请求的流式所有块拥有相同的时间戳。 |
| usage | Object of [usage](https://www.volcengine.com/docs/6348/1399966#usage) | 否 | 最后一个包可带此参数。 |
| object | String | 是 | 取值：`chat.completion.chunk`。填错将导致接口行为异常。 |
| model | String | 是 | 模型 ID。 |
| stream_options | object of [stream_options](https://www.volcengine.com/docs/6348/1399966#streamoptions) | 是 | 流接口选项。 |

Choices

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| finish_reason | String | 是 | * `null`：流式请求未结束。 最后一个流式片段需填入以下值： * `stop`：正常结束。 * `length`：达到 MaxTokens。 * `content_filter`：内容不合规。 |
| delta | Object of [delta](https://www.volcengine.com/docs/6348/1399966#delta) | 是 | 流式片段对象。 |
| index | Int | 是 | 数组中位置，从 0 开始。 |

delta

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| content | String | 是 | 对话内容。 |
| role | String | 是 | 可取值及含义如下： * `user` * `assistant` |

usage

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| completion_tokens | Int | 是 | generated tokens 长度。 |
| prompt_tokens | Int | 是 | prompt tokens 长度。 |
| total_tokens | Int | 是 | prompt+generated tokens 长度。 |

stream_options

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| include_usage | Boolean | 是 | 固定为 `true`，表示流式传输。 |

成功请求及返回样例如下：

```bash
curl -v --location 'https://rtc-cloud-rendering.byted.org/voicechat/test-sse' \
--header 'Content-Type: application/json' \
--data '{
    "messages":[{
        "role":"user",
        "content":"今天适合干什么？"
    }],
    "stream": true,
    "temperature": 0.1,
    "max_tokens": 100,
    "top_p": 0.9,
    "model": "doubao-32k",
    "stream_options": {"include_usage":true}
}'

> POST /voicechat/test-sse HTTP/1.1
> Host: rtc-cloud-rendering.byted.org
> User-Agent: curl/8.4.0
> Accept: */*
> Content-Type: application/json
> Content-Length: 254
>
< HTTP/1.1 200 OK
< Date: Thu, 15 Aug 2024 09:36:02 GMT
< Content-Type: text/event-stream
< Transfer-Encoding: chunked
< Connection: keep-alive
< Access-Control-Allow-Origin: *
< Cache-Control: no-cache

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"role":"assistant"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":1,"delta":{"content":"天"}},{"finish_reason":null,"index":0,"delta":{"content":"从明"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"起，"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":1,"delta":{"content":"幸福"}},{"finish_reason":null,"index":0,"delta":{"content":"做一个"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"的"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"人"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"。"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"喂马，"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"劈柴，"}},{"finish_reason":null,"index":1,"delta":{"content":"周游"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":null,"index":0,"delta":{"content":"世界"}},{"finish_reason":null,"index":1,"delta":{"content":"。"}}],"model":"doubao-32k-2024-07-25","created":1723714562}

data: {"id":"c99d67a4-5ae9-11ef-bdf5-b8cef6dcdbe2","object":"chat.completion.chunk","choices":[{"finish_reason":"stop","index":0,"delta":{}}],"model":"doubao-32k-2024-07-25","created":1723714562,"usage":{"prompt_tokens":1,"completion_tokens":2,"total_tokens":3}}

data: [DONE]
```

bash

### StatusCode==4xx、5xx

请求终止，报错返回。RTC 不做任何处理，仅报错。

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| Error | [Error](https://www.volcengine.com/docs/6348/1399966#error) | 是 | 错误详情。 |

Error

| 参数 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| Code | String | 是 | 错误码。 |
| Message | String | 是 | 错误原因。 |

### StatusCode!==200、4xx、5xx

此时 RTC 不做任何处理，仅报错，直接报错返回 StatusCode。

使用工具验证第三方大模型或 Agent 的请求接口

你可以使用工具验证第三方大模型或 Agent 的请求接口是否符合标准规范。如果符合要求，工具可以返回大模型或 Agent 预期的输出结果，否则将返回错误信息。

注意

目前验证工具仅支持在 Linux 和 macOS 系统上使用。

下载和使用工具
-------

1.   下载验证工具包并解压。

![Image 5](blob:http://localhost/bb27364609507014bf92fce31cc1dcd0)Archive.zip 8.16MB   ![Image 6](blob:http://localhost/aa79dc148544053b53fa2fed5c37b053)   
2.   通过终端，使用 cd 命令进入工具包所在目录。
3.   执行验证命令。注意

如果你使用 zsh 终端，且 URL 中包含特殊字符（如 `?`、`=`、`&` 等）时，必须对特殊字符进行转义。 
    *   Linux
```Bash
./app-linux <llmAPIUrl> <llmModel> <llmAPIKey> <LLMQuestion>
```
Bash    
    *   macOS
```Bash
./app-mac <llmAPIUrl> <llmModel> <llmAPIKey> <LLMQuestion>
```
Bash    

 参数说明如下： 
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| llmAPIUrl | String | 是 | 第三方大模型或 Agent 的完整请求 URL。 | `https://api.cn/v1/chat/completion` |
| llmModel | String | 否 | 第三方大模型或 Agent 的名称。默认值为空。 | `model-v1-chat` |
| llmAPIKey | String | 否 | APIKey。默认值为空。 | `sk-0aeQbtlX2reL` |
| LLMQuestion | String | 否 | 发送给第三方大模型或 Agent 的测试问题。 | `你好` |

验证示例
----

执行结果类似如下所示，表示验证通过：

*   openAI

![Image 7](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_46d3de0843c5281462b6c7513daf4b50.png)
*   自定义 URL

![Image 8](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_d0e2a36d6199b30ba2abd1ef67b09292.png)

FAQ

Q1：是否支持在本地环境调用和测试验证对话式 AI 第三方大模型或 Agent 接口？
-------------------------------------------

支持。

Q2：使用第三方大模型时，设置的多轮历史对话 `HistoryLength` 未生效，每次请求只传递了最新一条对话内容怎么办？
---------------------------------------------------------------

可以尝试以下排查：

1.   检查你的大模型接口是否支持多轮历史对话。
2.   确认第三方大模型的响应中是否包含正确的请求结束标识`data: [DONE]`。如果第三方服务没有在响应末尾返回此结束符，系统可能无法正确识别一次完整请求的结束，从而无法正确地存储和使用历史对话记录。

