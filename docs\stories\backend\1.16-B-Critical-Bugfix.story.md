# 故事 1.16-B: 关键业务逻辑缺陷修复

**Epic**: 后端稳定性与安全增强 (Backend Stability & Security Enhancement)
**负责人**: @dev
**状态**: Draft
**估点**: 5

---

## 目标 (Goals)

本故事旨在修复两个影响系统核心功能的关键缺陷，确保LLM代理服务和RTC事件处理能够正常工作。

- **问题1**: LLM代理服务使用占位符实现，导致内容生成和摘要功能失效
- **问题2**: 智能体状态事件模型与火山引擎官方文档不匹配，导致状态回调解析失败

## 故事 (Story)

**作为** 系统管理员，
**我希望** LLM代理服务能够正确调用火山方舟API生成真实内容，并且RTC状态事件能被正确解析，
**以便** 保证会话摘要、建议问题生成和智能体状态监控等核心功能正常运行。

## 验收标准 (Acceptance Criteria)

### AC-1: LLM代理服务真实API集成
1. **[代码审查]** `apps/agent-api/api/services/llm_proxy_service.py` 中的 `generate_text` 和 `generate_summary` 方法**不再**使用硬编码占位符逻辑。
2. **[代码审查]** 这两个方法**必须**调用真实的火山方舟LLM API，使用与 `call_llm` 方法相同的底层实现。
3. **[功能测试]** 调用 `generate_text("为这段对话生成摘要: 用户询问天气，AI回复晴天")` 应返回由LLM生成的真实摘要，而非硬编码文本。
4. **[集成测试]** `ContentGenerationService` 和 `SuggestedQuestionsService` 依赖的LLM方法能够正常工作并返回智能生成的内容。

### AC-2: 智能体状态事件模型修正
1. **[代码审查]** `apps/agent-api/api/models/rtc_models.py` 中的 `ConversationStatusPayload` 模型**必须**包含以下字段，严格符合火山引擎官方文档：
   - `TaskId: str` - 智能体任务ID
   - `UserID: str` - 说话人UserId
   - `RoundID: int` - 对话轮次，从0开始计数
   - `EventTime: int` - Unix时间戳(ms)
   - `Stage: StageInfo` - 任务状态详细描述
2. **[代码审查]** 创建新的 `StageInfo` 嵌套模型包含 `Code: int` 和 `Description: str` 字段。
3. **[代码审查]** `apps/agent-api/api/routes/rtc_webhook_routes.py` 中的 `_handle_voice_chat_status_event` 方法**必须**更新以使用新的模型结构。
4. **[功能测试]** 能够正确解析包含新结构的火山引擎状态变化事件，无Pydantic验证错误。

## 任务/子任务 (Tasks/Subtasks)

### Task 1: 修复LLM代理服务占位符实现 (AC: 1)
- [ ] **1.1** 重构 `generate_text` 方法调用真实LLM API
  - [ ] 1.1.1 将输入 `prompt` 包装成 `messages` 格式: `[{"role": "user", "content": prompt}]`
  - [ ] 1.1.2 调用 `self.call_llm(messages=messages, tools=None)` 获取真实响应
  - [ ] 1.1.3 实现完整异常处理，失败时返回空字符串而不是硬编码文本
  - [ ] 1.1.4 保留 `max_tokens` 和 `temperature` 参数功能
- [ ] **1.2** 重构 `generate_summary` 方法调用真实LLM API
  - [ ] 1.2.1 构建专门的摘要提示: `f"请为以下对话生成简洁摘要:\n{conversation_text}"`
  - [ ] 1.2.2 使用相同的真实API调用模式替换硬编码逻辑
  - [ ] 1.2.3 设置合适的参数: `max_tokens=500, temperature=0.3` (摘要需要更确定性)
- [ ] **1.3** 验证依赖服务功能恢复
  - [ ] 1.3.1 测试 `ContentGenerationService` 能生成真实会话摘要
  - [ ] 1.3.2 测试 `SuggestedQuestionsService` 能生成智能建议问题

### Task 2: 修正智能体状态事件模型 (AC: 2)  
- [ ] **2.1** 更新 `rtc_models.py` 中的状态事件模型
  - [ ] 2.1.1 创建新的 `StageInfo` 模型:
    ```python
    class StageInfo(BaseModel):
        Code: int = Field(..., description="任务状态码")
        Description: str = Field(..., description="任务状态描述")
    ```
  - [ ] 2.1.2 重写 `ConversationStatusPayload` 模型使用官方文档结构:
    ```python
    class ConversationStatusPayload(BaseModel):
        TaskId: str = Field(..., description="智能体任务ID")
        UserID: str = Field(..., description="说话人UserId")  
        RoundID: int = Field(..., description="对话轮次，从0开始计数")
        EventTime: int = Field(..., description="Unix时间戳(ms)")
        Stage: StageInfo = Field(..., description="任务状态详细描述")
    ```
- [ ] **2.2** 更新状态事件处理器
  - [ ] 2.2.1 修改 `_handle_voice_chat_status_event` 方法使用新的字段结构
  - [ ] 2.2.2 更新日志记录以使用正确字段: `TaskId`, `UserID`, `Stage.Code`, `Stage.Description`
  - [ ] 2.2.3 移除对旧字段 (`status`, `agent_id`) 的引用
- [ ] **2.3** 验证Webhook事件解析
  - [ ] 2.3.1 创建单元测试验证新模型能正确解析火山引擎状态事件
  - [ ] 2.3.2 确保 `RtcWebhookRequest` 的 `Union` 类型包含新的 `ConversationStatusPayload`

### Task 3: 综合测试与验证
- [ ] **3.1** 单元测试覆盖
  - [ ] 3.1.1 为修复的 `generate_text` 和 `generate_summary` 方法添加单元测试
  - [ ] 3.1.2 为新的状态事件模型添加Pydantic验证测试
- [ ] **3.2** 集成测试验证
  - [ ] 3.2.1 端到端测试会话摘要生成功能
  - [ ] 3.2.2 模拟火山引擎状态事件回调解析
- [ ] **3.3** 回归测试确保无破坏性变更
  - [ ] 3.3.1 验证现有LLM调用功能未受影响
  - [ ] 3.3.2 确认其他RTC事件类型仍能正常解析

## Dev Notes

### 技术背景与上下文

**来源**: 基于 `docs/fix_bug_3.md` 问题分析和火山引擎官方文档验证

#### 问题1: LLM代理服务占位符实现分析
**根本原因**: `llm_proxy_service.py` 第602-666行的 `generate_text` 和 `generate_summary` 方法使用硬编码规则而非真实LLM调用。

**受影响服务**:
- `ContentGenerationService` (会话摘要生成)
- `SuggestedQuestionsService` (建议问题生成)  
- 任何依赖这两个方法的业务逻辑

**修复策略**: 
- 复用现有的 `call_llm` 方法实现模式
- 保持方法签名不变，确保向后兼容
- 使用火山方舟API真实生成内容

[Source: apps/agent-api/api/services/llm_proxy_service.py#602-666]

#### 问题2: 智能体状态事件模型不匹配分析
**根本原因**: `ConversationStatusPayload` 模型定义与火山引擎官方文档 `conversation_status_message` 结构完全不符。

**官方文档结构** (通过MCP doc-query-server验证):
```
TaskId (String): 智能体任务ID
UserID (String): 说话人UserId  
RoundID (Int64): 对话轮次，从0开始计数
EventTime (Int64): Unix时间戳(ms)
Stage: 任务状态详细描述
  ├── Code (Int): 任务状态码
  └── Description (String): 任务状态描述
```

**当前错误实现**: 使用了 `status`, `agent_id`, `timestamp`, `metadata` 等不存在的字段。

[Source: 火山引擎官方文档 - 接收状态变化消息 conversation_status_message]

### 关键技术要求

#### 火山方舟LLM集成规范
- **Base URL**: `https://ark.cn-beijing.volces.com`  
- **认证**: 使用 `VOLCANO_LLM_APP_KEY` 通过OpenAI兼容接口
- **Endpoint ID**: 使用 `VOLCANO_LLM_ENDPOINT_ID` 作为模型参数
- **消息格式**: `[{"role": "user", "content": "..."}]`

[Source: docs/architecture/agent-api-coding-standards.md#火山引擎LLM集成规范]

#### Pydantic模型规范
- 使用 `BaseModel` 继承
- 字段使用 `Field(..., description="...")` 注解
- 严格匹配官方文档字段名称和类型
- 嵌套模型独立定义

[Source: docs/architecture/agent-api-coding-standards.md#Pydantic模型规范]

### 测试

**故事要求的测试**:

- [x] **Jest Unit Tests**: (nextToFile: true), 覆盖率要求: 85%
- [x] **Jest Integration Test**: 位置: `/tests/test_llm_bugfix_integration.py`  
- [ ] **E2E Test**: 不需要

**手动测试步骤**:
1. 启动后端服务: `uvicorn api.main:app --host 0.0.0.0 --port 8003`
2. 测试LLM修复: 
   ```python
   # 在Python控制台中测试
   from api.services.llm_proxy_service import get_llm_proxy_service
   service = await get_llm_proxy_service()
   result = await service.generate_text("为这段对话生成摘要: 用户询问天气，AI回复晴天")
   print(f"LLM真实响应: {result}")  # 应该是智能生成的摘要，不是硬编码
   ```
3. 测试状态模型: 发送模拟的火山引擎状态事件到 `/api/v1/chat/rtc_event_handler`，验证无Pydantic解析错误

## Dev Agent Record

### Agent Model Used: [[将在实现时填写]]

### Debug Log References
[[LLM: (Dev Agent) 在实现过程中如果使用debug日志，在此创建引用表]]

### Completion Notes List
[[LLM: (Dev Agent) 记录任何偏离故事的实现细节，可能影响下一个故事的准备]]

### File List
[[LLM: (Dev Agent) 列出所有新建或修改的文件]]

### Change Log
[[LLM: (Dev Agent) 跟踪实现过程中的版本变更]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |

## QA Results
[[LLM: QA Agent Results]] 