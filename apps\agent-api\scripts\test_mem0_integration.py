#!/usr/bin/env python3
"""
测试脚本：验证 Mem0 SDK 集成

使用方法：
python scripts/test_mem0_integration.py

确保环境变量 MEM0_API_KEY 已设置
"""

import asyncio
import os
import sys
import traceback
from typing import Any, Dict

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.services.memory_service import Mem0MemoryServiceImpl
from api.settings import logger


async def test_mem0_integration():
    """测试 Mem0 SDK 集成功能"""

    print("🧠 开始测试 Mem0 SDK 集成...")

    # 检查 API key
    api_key = os.getenv("MEM0_API_KEY")
    if not api_key:
        print("❌ 错误: 环境变量 MEM0_API_KEY 未设置")
        print("请设置: export MEM0_API_KEY=your-api-key")
        return False

    print(f"✅ API Key 已设置 (前8位: {api_key[:8]}...)")

    # 初始化服务
    try:
        memory_service = Mem0MemoryServiceImpl()
        print("✅ Mem0MemoryServiceImpl 初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        traceback.print_exc()
        return False

    # 测试用户ID和会话ID
    test_user_id = "test_user_123"
    test_session_id = "test_session_456"

    print(f"\n📝 测试用户: {test_user_id}")
    print(f"📝 测试会话: {test_session_id}")

    # 测试 1: 添加记忆
    print("\n1️⃣ 测试添加记忆...")
    try:
        await memory_service.add_memory(
            user_id=test_user_id,
            session_id=test_session_id,
            human_message="你好，我是测试用户。我喜欢吃素食，对坚果过敏。",
            assistant_message="你好！我已经记录了您的饮食偏好：素食者，对坚果过敏。",
            metadata={"test": True, "timestamp": "2025-01-28"}
        )
        print("✅ 添加记忆成功")
    except Exception as e:
        print(f"❌ 添加记忆失败: {e}")
        traceback.print_exc()
        return False

    # 添加一些等待时间，让记忆处理完成
    print("⏳ 等待记忆处理...")
    await asyncio.sleep(2)

    # 测试 2: 搜索记忆
    print("\n2️⃣ 测试搜索记忆...")
    try:
        memories = await memory_service.search_memory(
            user_id=test_user_id,
            query="饮食偏好",
            limit=5
        )
        print(f"✅ 搜索记忆成功，找到 {len(memories)} 条记忆")

        for i, memory in enumerate(memories):
            print(f"   记忆 {i+1}: {memory['content'][:50]}...")

    except Exception as e:
        print(f"❌ 搜索记忆失败: {e}")
        traceback.print_exc()
        return False

    # 测试 3: 获取所有记忆
    print("\n3️⃣ 测试获取用户记忆...")
    try:
        all_memories = await memory_service.get_memories(
            user_id=test_user_id,
            session_id=test_session_id,
            limit=10
        )
        print(f"✅ 获取记忆成功，找到 {len(all_memories)} 条记忆")

        for i, memory in enumerate(all_memories):
            print(f"   记忆 {i+1}: {memory['content'][:50]}...")

    except Exception as e:
        print(f"❌ 获取记忆失败: {e}")
        traceback.print_exc()
        return False

    # 测试 4: 获取记忆上下文
    print("\n4️⃣ 测试获取记忆上下文...")
    try:
        context = await memory_service.get_memory_context(
            user_id=test_user_id,
            query="我的饮食习惯"
        )
        print(f"✅ 获取上下文成功")
        print(f"   上下文长度: {len(context['context'])} 字符")
        print(f"   记忆数量: {len(context['memories'])}")

        if context['context']:
            print(f"   上下文预览: {context['context'][:100]}...")

    except Exception as e:
        print(f"❌ 获取上下文失败: {e}")
        traceback.print_exc()
        return False

    # 测试 5: 更新会话元数据
    print("\n5️⃣ 测试更新会话元数据...")
    try:
        success = await memory_service.update_session_metadata(
            user_id=test_user_id,
            session_id=test_session_id,
            summary="测试会话：讨论了用户的饮食偏好和过敏信息",
            metadata={"session_type": "test", "completed": True}
        )
        print(f"✅ 更新元数据{'成功' if success else '失败'}")

    except Exception as e:
        print(f"❌ 更新元数据失败: {e}")
        traceback.print_exc()
        return False

    print("\n🎉 所有测试完成！Mem0 SDK 集成工作正常！")
    print("\n📋 测试总结:")
    print("   ✅ 记忆添加功能正常")
    print("   ✅ 记忆搜索功能正常")
    print("   ✅ 记忆获取功能正常")
    print("   ✅ 上下文构建功能正常")
    print("   ✅ 元数据更新功能正常")

    return True


def test_mem0_import():
    """测试 mem0 SDK 导入"""
    print("📦 测试 Mem0 SDK 导入...")

    try:
        from mem0 import MemoryClient
        print("✅ MemoryClient 导入成功")

        # 测试初始化（不需要API key）
        client = MemoryClient()
        print("✅ MemoryClient 初始化成功")

        return True

    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请安装 mem0ai: pip install mem0ai")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("🧠 Mem0 SDK 集成测试")
    print("=" * 60)

    # 测试导入
    if not test_mem0_import():
        return

    print()

    # 测试集成
    success = await test_mem0_integration()

    print("\n" + "=" * 60)
    if success:
        print("🎊 测试成功！Mem0 SDK 集成完成！")
    else:
        print("💥 测试失败！请检查错误信息并修复。")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
