#!/usr/bin/env python3
"""
火山引擎LLM API调试脚本
用于诊断401认证失败问题
"""

import asyncio
import json
import httpx
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.settings import settings
from api.services.volcano_client_service import VolcanoClientService

async def test_volcano_llm_detailed():
    """详细测试火山引擎LLM API连接"""
    print("🔍 火山引擎LLM API详细调试")
    print("=" * 60)
    sys.stdout.flush()

    # 1. 检查配置
    print("1. 配置检查:")
    print(f"  ACCESS_KEY_ID: {settings.VOLCANO_ACCESS_KEY_ID[:10]}...")
    print(f"  SECRET_ACCESS_KEY: {settings.VOLCANO_SECRET_ACCESS_KEY[:10]}...")
    print(f"  ENDPOINT_ID: {settings.VOLCANO_LLM_ENDPOINT_ID}")
    print(f"  APP_KEY: {settings.VOLCANO_LLM_APP_KEY[:10]}...")
    print()
    sys.stdout.flush()

    # 2. 测试V4签名生成
    print("2. V4签名测试:")
    sys.stdout.flush()
    volcano_client = VolcanoClientService()

    # 准备测试请求
    test_data = {
        "model": settings.VOLCANO_LLM_ENDPOINT_ID,
        "messages": [{"role": "user", "content": "Hello"}],
        "temperature": 0.7,
        "max_tokens": 100
    }

    body = json.dumps(test_data).encode('utf-8')
    path = "/api/v3/chat/completions"

    try:
        # 测试不同的服务名称
        service_names = ["ml_platform", "ark", "llm", "volcengine"]

        for service_name in service_names:
            print(f"  尝试服务名: {service_name}")
            sys.stdout.flush()

            headers = volcano_client.get_signed_headers(
                service=service_name,
                host="ark.cn-beijing.volces.com",
                region="cn-beijing",
                method="POST",
                path=path,
                query_params={},
                body=body
            )

            print(f"    生成的签名头: {headers.get('Authorization', '')[:50]}...")
            sys.stdout.flush()

            # 测试HTTP请求
            async with httpx.AsyncClient(timeout=30.0) as client:
                try:
                    response = await client.post(
                        f"https://ark.cn-beijing.volces.com{path}",
                        json=test_data,
                        headers=headers
                    )

                    if response.status_code == 200:
                        print(f"    ✅ 成功! 服务名 '{service_name}' 正确")
                        result = response.json()
                        print(f"    响应: {result}")
                        sys.stdout.flush()
                        return service_name
                    else:
                        print(f"    ❌ 失败: {response.status_code} - {response.text[:100]}...")
                        sys.stdout.flush()

                except Exception as e:
                    print(f"    ❌ 请求异常: {e}")
                    sys.stdout.flush()
            print()
            sys.stdout.flush()

    except Exception as e:
        print(f"  ❌ V4签名生成失败: {e}")
        sys.stdout.flush()
        return None

    print("3. 尝试其他认证方式:")
    sys.stdout.flush()

    # 3. 测试直接使用APP_KEY作为Bearer Token
    print("  尝试使用APP_KEY作为Bearer Token:")
    sys.stdout.flush()
    headers_simple = {
        "Authorization": f"Bearer {settings.VOLCANO_LLM_APP_KEY}",
        "Content-Type": "application/json"
    }

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                f"https://ark.cn-beijing.volces.com{path}",
                json=test_data,
                headers=headers_simple
            )

            if response.status_code == 200:
                print(f"    ✅ Bearer Token认证成功!")
                result = response.json()
                print(f"    响应: {result}")
                sys.stdout.flush()
                return "bearer_token"
            else:
                print(f"    ❌ Bearer Token认证失败: {response.status_code}")
                print(f"    错误详情: {response.text}")
                sys.stdout.flush()

        except Exception as e:
            print(f"    ❌ Bearer Token请求异常: {e}")
            sys.stdout.flush()

    print("\n4. 网络连接测试:")
    sys.stdout.flush()
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("https://ark.cn-beijing.volces.com")
            print(f"  网络连接: ✅ 可达 (状态码: {response.status_code})")
            sys.stdout.flush()
    except Exception as e:
        print(f"  网络连接: ❌ 不可达 ({e})")
        sys.stdout.flush()

    return None

async def main():
    """主函数"""
    try:
        result = await test_volcano_llm_detailed()

        print("\n" + "=" * 60)
        sys.stdout.flush()
        if result:
            print(f"🎉 找到工作的认证方式: {result}")
            if result == "bearer_token":
                print("建议: 修改LLMProxyService使用Bearer Token认证而不是V4签名")
            else:
                print(f"建议: 修改LLMProxyService使用服务名 '{result}' 进行V4签名")
        else:
            print("❌ 所有认证方式都失败了")
            print("可能的问题:")
            print("1. 火山引擎配置密钥错误")
            print("2. EndpointID不存在或无权限")
            print("3. 网络连接问题")
            print("4. 火山引擎API URL或路径错误")
        sys.stdout.flush()

    except Exception as e:
        print(f"❌ 调试脚本执行失败: {e}")
        sys.stdout.flush()

if __name__ == "__main__":
    asyncio.run(main())
