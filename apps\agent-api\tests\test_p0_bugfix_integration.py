"""
P0级Bug修复集成测试
测试故事1.10-B中的关键缺陷修复
"""
import pytest
import asyncio
import os
import tempfile
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import HTTPException

from api.main import app
from api.settings import Settings
from api.services.llm_proxy_service import LLMProxyService
from api.services.memory_service import IMemoryService, Mem0MemoryServiceImpl
from api.dependencies.auth import get_current_user
from db.session import get_db


class TestP0DatabaseURLFix:
    """测试场景1.3: 数据库URL构建修复验证"""

    def test_database_url_uses_postgres_db_env_var(self):
        """测试数据库配置从环境变量POSTGRES_DB读取"""
        # 设置环境变量
        os.environ["POSTGRES_DB"] = "test_db"

        # 创建新的Settings实例
        settings = Settings()

        # 验证应该使用环境变量的值而非表名 - 注意是DATABASE_URL不是database_url
        assert "test_db" in settings.DATABASE_URL
        assert "agno_memories" not in settings.DATABASE_URL  # 不应该用表名作为数据库名

        # 清理
        if "POSTGRES_DB" in os.environ:
            del os.environ["POSTGRES_DB"]

    def test_database_url_fallback_to_default(self):
        """测试未设置POSTGRES_DB时使用默认数据库名"""
        # 确保环境变量不存在
        if "POSTGRES_DB" in os.environ:
            del os.environ["POSTGRES_DB"]

        settings = Settings()

        # 应该使用默认的postgres数据库名
        assert "postgres" in settings.DATABASE_URL
        assert "agno_memories" not in settings.DATABASE_URL

    def test_application_can_start_with_fixed_db_config(self):
        """测试应用能够正常启动"""
        # 现在应该可以正常启动，不再预期失败
        client = TestClient(app)
        response = client.get("/api/v1/health")
        # 应该能正常获得健康检查响应
        assert response.status_code == 200


class TestP0JWTAuthenticationFix:
    """测试场景1.2: JWT认证机制修复验证"""

    def test_jwt_authentication_dependency_exists(self):
        """测试JWT认证依赖是否正确导入和使用"""
        from api.routes.chat_sse_routes import router

        # 检查路由函数的依赖
        chat_endpoint = None
        for route in router.routes:
            if hasattr(route, 'endpoint') and route.endpoint.__name__ == 'chat_message_sse_endpoint':
                chat_endpoint = route
                break

        assert chat_endpoint is not None, "应该找到chat_message_sse_endpoint路由"

        # 检查路由函数的签名，确保有current_user参数
        import inspect
        sig = inspect.signature(chat_endpoint.endpoint)
        assert 'current_user' in sig.parameters, "JWT认证依赖应该存在于SSE路由中"

    def test_unauthorized_access_rejected(self):
        """测试无JWT Token访问被拒绝"""
        client = TestClient(app)

        # 尝试访问受保护的端点而不提供JWT Token
        response = client.post("/api/v1/chat/text_message", json={
            "message": "test message",
            "sessionId": "test_session",
            "characterId": "test_character"
        })

        # 应该返回401，认证已修复
        assert response.status_code == 401, "应该拒绝无认证的请求"

    def test_user_id_from_jwt_not_request_body(self):
        """测试用户ID从JWT获取而非请求体"""
        from api.models.chat_models import ChatRequest

        # 检查ChatRequest模型是否移除了userId字段
        import inspect
        sig = inspect.signature(ChatRequest.__init__)
        assert 'userId' not in sig.parameters, "ChatRequest不应该包含userId字段"


class TestP0LLMServiceFix:
    """测试场景1.1: LLM服务真实API调用验证"""

    @pytest.mark.asyncio
    async def test_llm_service_calls_real_api(self):
        """测试LLM服务调用真实API而非硬编码回复"""
        llm_service = LLMProxyService()

        # 修正方法名为实际存在的方法
        with patch.object(llm_service, '_call_volcano_llm_with_retry', return_value={"choices": [{"message": {"content": "真实API回复"}}]}):
            result = await llm_service.call_llm([{"role": "user", "content": "测试消息"}])

            # 验证不是硬编码回复
            hardcoded_responses = [
                "我理解您的需求",
                "这是一个简单的回复",
                "感谢您的提问"
            ]

            assert result not in hardcoded_responses, "不应该返回硬编码回复"
            assert "真实API回复" in result, "应该包含真实API回复内容"

    @pytest.mark.asyncio
    async def test_llm_service_has_emergency_fallback(self):
        """测试LLM服务有emergency fallback机制"""
        llm_service = LLMProxyService()

        # 模拟API调用失败
        with patch.object(llm_service, '_call_volcano_llm_with_retry', side_effect=Exception("API不可用")):
            result = await llm_service.call_llm([{"role": "user", "content": "测试消息"}])

            # 应该有fallback回复，不应该抛出异常
            assert result is not None, "API失败时应该有fallback回复"
            assert "抱歉" in result or "稍后" in result, "fallback回复应该包含道歉或延迟信息"

    def test_llm_service_no_hardcoded_methods(self):
        """测试LLM服务没有硬编码方法"""
        llm_service = LLMProxyService()

        # 验证_generate_simple_response等硬编码方法已移除
        assert not hasattr(llm_service, '_generate_simple_response'), "不应该存在硬编码回复方法"
        assert hasattr(llm_service, '_generate_emergency_fallback'), "应该有emergency fallback方法"


class TestP0MemorySystemUserIDFix:
    """测试场景1.4: 记忆系统用户ID修复验证"""

    @pytest.mark.asyncio
    async def test_memory_service_uses_user_id_not_session_id(self):
        """测试记忆系统使用user_id而非session_id"""
        # 创建Mem0服务实例（无需client参数）
        memory_service = Mem0MemoryServiceImpl()

        test_user_id = "user_123"
        test_session_id = "session_456"
        test_human_message = "测试人类消息"
        test_assistant_message = "测试助手回复"

        # Mock _add_memory_sync方法来验证参数
        with patch.object(memory_service, '_add_memory_sync') as mock_add_sync:
            await memory_service.add_memory(test_user_id, test_session_id, test_human_message, test_assistant_message)

            # 验证调用时使用了正确的参数顺序
            mock_add_sync.assert_called_once_with(
                test_user_id, test_session_id, test_human_message, test_assistant_message, None
            )

    def test_memory_service_interface_accepts_both_ids(self):
        """测试记忆服务接口同时接受user_id和session_id"""
        from api.services.memory_service import IMemoryService
        import inspect

        # 检查add_memory方法签名
        add_memory_sig = inspect.signature(IMemoryService.add_memory)
        params = list(add_memory_sig.parameters.keys())

        # 接口应该包含user_id和session_id参数
        assert "user_id" in params, "接口应该包含user_id参数"
        assert "session_id" in params, "接口应该包含session_id参数"


class TestP0IntegrationScenarios:
    """集成测试场景"""

    def test_complete_chat_flow_after_fixes(self):
        """测试修复后的完整对话流程"""
        client = TestClient(app)

        # 模拟有效的JWT Token
        mock_user = {"sub": "test_user_123"}

        with patch('api.dependencies.auth.get_current_user', return_value=mock_user):
            response = client.post("/api/v1/chat/text_message",
                                 headers={"Authorization": "Bearer valid_token"},
                                 json={
                                     "message": "你好，今天天气怎么样？",
                                     "sessionId": "test_session_456",
                                     "characterId": "compassionate_listener"
                                 })

            # P0修复后应该能正常响应
            assert response.status_code == 200, "完整对话流程应该成功"
