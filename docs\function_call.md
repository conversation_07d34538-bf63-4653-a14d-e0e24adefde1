
### Function Calling 功能概述

**Function Calling**（函数调用）功能允许您在实时对话式 AI 场景中，让大模型（LLM）调用您定义的外部工具（如函数、API），以完成它自身无法独立完成的任务。这极大地扩展了 AI 智能体的能力，使其能够处理实时数据查询、文件操作或执行特定业务逻辑。

**典型应用场景**：
*   **天气查询**：用户提问“上海今天天气怎么样？”，AI 智能体调用天气 API 获取实时数据并回答。
*   **股票行情查询**：用户询问某支股票的最新价格。
*   **数学计算**：执行复杂的数学运算。
*   **业务系统交互**：根据用户指令，在内部系统中下单或查询订单状态。

**注意**：该功能目前仅在 **火山方舟平台** 使用 **doubao 非 1.5 代系模型**时，按照非流式方式返回结果。使用 doubao 1.5 代系模型或 DeepSeek 模型时，会以流式方式返回结果。

---

### 功能实现时序图

整个 Function Calling 的交互流程如下：

1.  **开启 Function Calling 功能**：在启动智能体任务时，配置好需要被调用的工具（函数）。
2.  **接收工具调用指令**：当用户的对话触发了已定义的工具时，RTC 服务会向您的业务后端或客户端发送工具调用指令。
3.  **执行工具并返回结果**：您的业务后端或客户端在接收到指令后，执行相应的本地工具（函数/API），并将执行结果返回给 RTC 服务端。此步骤支持多轮调用。
4.  **获取最终音频回复**：RTC 服务端将工具的执行结果整合到对话上下文中，并驱动大模型生成最终的自然语言回复，再通过 TTS 播放给用户。

---

### 具体实现方法

Function Calling 功能支持在**服务端**和**客户端**两种模式下实现。您可以根据业务架构选择最合适的方式。

#### **1. 服务端实现**

当您的业务逻辑主要在服务端处理时，推荐使用此方式，以降低请求延迟和保证安全性。

*   **步骤 1：开启 Function Calling 功能**
    在调用 `StartVoiceChat` 接口时，你需要配置 `LLMConfig` 和 `FunctionCallingConfig` 两个关键部分。
    *   `LLMConfig.Tools`：定义大模型可以调用的工具列表。
    *   `FunctionCallingConfig`：配置一个接收 RTC 回调的 URL，用于接收函数调用指令。

    **请求示例 (`StartVoiceChat`)**:
    ```json
    {
      "AppId": "661e****543cf",
      "RoomId": "Room1",
      "TaskId": "task1",
      "Config": {
        "LLMConfig": {
          "Mode": "ArkV3",
          "EndPointId": "epid****212",
          "Tools": [
            {
              "Type": "function",
              "function": {
                "name": "get_current_weather",
                "description": "获取给定地点的天气",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "location": { "type": "string", "description": "地理位置，比如北京市" }
                  },
                  "required": ["location"]
                }
              }
            }
          ]
        },
        "FunctionCallingConfig": {
          "ServerMessageUrl": "https://example-domain.com/vertc/fc", // 您的回调接收地址
          "ServerMessageSignature": "b46a****8ad6a" // 用于鉴权的签名
        },
        // ... 其他 ASR, TTS, AgentConfig 配置
      }
    }
    ```

*   **步骤 2：接收工具调用指令消息**
    当用户对话触发函数调用时，您在 `ServerMessageUrl` 配置的地址会收到一个 HTTP POST 请求，其 Body 为 JSON 格式，包含调用指令。

    **回调消息结构**:
    | 字段名 | 类型 | 描述 |
    | :--- | :--- | :--- |
    | `message` | Array of Object | 调用指令消息详情数组。 |
    | `message[].id` | String | 本次 Function Calling 任务的标识 ID。 |
    | `message[].function.name`| String | 被调用的函数名称。 |
    | `message[].function.arguments`| String | 函数调用所需的参数，为 JSON 字符串。|
    | `signature` | String | 您在 `StartVoiceChat` 中设置的签名，用于鉴权。 |

*   **步骤 3：将工具调用结果传回 RTC 服务端**
    在执行完本地函数后，您需要调用 `UpdateVoiceChat` 接口将结果回传。

    **请求示例 (`UpdateVoiceChat`)**:
    ```json
    POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-06-01
    {
      "AppId": "661e****543cf",
      "RoomId": "Room1",
      "TaskId": "task1",
      "Command": "function",
      "Message": "{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
    }
    ```
    其中 `Message` 字段是一个 JSON 字符串，包含 `ToolCallID` (来自步骤2的 `id`) 和 `Content` (函数执行结果)。

*   **步骤 4：获取最终答复**
    RTC 服务端处理完您的返回结果后，智能体会生成最终的语音回复。

#### **2. 客户端实现**

当您希望在客户端直接处理函数调用逻辑时，可采用此方式。

*   **步骤 1：开启 Function Calling 功能**
    与服务端实现类似，调用 `StartVoiceChat` 接口，但在 `LLMConfig` 中仅需配置 `Tools` 字段，**无需**配置 `FunctionCallingConfig`。

*   **步骤 2：接收工具调用指令消息**
    客户端通过监听 RTC SDK 的 `onRoomBinaryMessageReceived` 回调来接收指令。
    *   **消息格式**：收到的 `message` 是一个二进制 `Buffer`，需要按以下格式解析：
        1.  **Magic Number (4 bytes)**: 固定为 `"tool"`。
        2.  **Length (4 bytes)**: `Tool_Calls` 信息的长度（大端序）。
        3.  **Tool_Calls (variable bytes)**: 包含工具调用指令的 JSON 字符串。
    *   **解析示例 (C++)**:
        ```cpp
        void onRoomBinaryMessageReceived(const char* uid, int size, const uint8_t* message) {
          std::string tool_calls;
          bool ret = Unpack(message, size, tool_calls); // 自定义拆包函数
          if(ret) {
            ParseData(tool_calls); // 自定义JSON解析函数
          }
        }
        ```

*   **步骤 3：将工具调用结果传回 RTC 服务端**
    客户端调用 `sendUserBinaryMessage` 方法将结果发送给智能体。
    *   **消息格式**：发送的 `buffer` 同样需要遵循特定二进制格式：
        1.  **Magic Number (4 bytes)**: 固定为 `"func"`。
        2.  **Length (4 bytes)**: `Function_Response` 信息的长度（大端序）。
        3.  **Function_Response (variable bytes)**: 包含工具调用结果的 JSON 字符串，内容为 `{"ToolCallID": "...", "Content": "..."}`。
    *   **构造消息示例 (TypeScript)**:
        ```typescript
        this.engine.sendUserBinaryMessage(
          'Your AI Bot Name',
          stringToTLV( // 自定义封装函数
            JSON.stringify({
              ToolCallID: tool_calls?.[0]?.id,
              Content: map[name.toLocaleLowerCase().replaceAll('_', '')],
            }),
            'func'
          )
        );
        ```

*   **步骤 4：获取最终答复**
    智能体收到客户端返回的结果后，会生成并播放最终的语音回复。

### **引用文档**

以上内容整理自以下文档，您可以参考原文获取更详细的参数说明和代码示例：

1.  **[Function Calling（非流式返回结果）--实时音视频-火山引擎](https://www.volcengine.com/docs/6348/1359441)**：这是关于 Function Calling 功能最核心的文档，详细描述了服务端和客户端的实现流程、参数和时序图。
2.  **[大模型配置--实时音视频-火山引擎](https://www.volcengine.com/docs/6348/1581714)**：该文档在介绍火山方舟平台配置时，提及了 `LLMConfig.Tools` 字段，是开启 Function Calling 的前提。
3.  **[启动智能体 StartVoiceChat--实时音视频-火山引擎](https://www.volcengine.com/docs/6348/1558163)**：此接口是发起所有对话式 AI 任务的入口，Function Calling 的配置在此接口中完成。
4.  **[更新智能体 UpdateVoiceChat--实时音视频-火山引擎](https://www.volcengine.com/docs/6348/1404671)**：服务端实现中，通过此接口的 `Command: "function"` 来回传工具执行结果。
5.  **[客户端 API 参考/React Native 3.58/API 详情](https://www.volcengine.com/docs/6348/1390575)** 和 **[客户端 API 参考/iOS 3.58/回调](https://www.volcengine.com/docs/6348/70087)**：这些文档详细说明了客户端实现所需的 `onRoomBinaryMessageReceived` 和 `sendUserBinaryMessage` 等接口。