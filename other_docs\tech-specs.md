# 心桥 - 技术规格说明书 (Tech Specs)

## 📋 文档概述

**版本**: v1.0  
**更新日期**: 2024年  
**负责人**: 技术团队  
**目标**: 为开发团队提供详细的技术实现规格和开发指导

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Native   │    │   Supabase      │    │   火山引擎       │
│   + Expo        │◄──►│   (BaaS)        │◄──►│   AI Services   │
│                 │    │                 │    │                 │
│ • UI Components │    │ • PostgreSQL    │    │ • ASR/TTS       │
│ • State Mgmt    │    │ • Edge Functions│    │ • LLM           │
│ • Local Storage │    │ • Auth          │    │ • 情感识别      │
│ • Push Service  │    │ • Real-time     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流架构
```
用户语音输入 → ASR转文字 → 意图识别 → 记忆检索 → LLM生成 → TTS转语音 → 用户接收
     ↓              ↓          ↓         ↓        ↓         ↓
  音频文件 → 转录结果 → 结构化数据 → 上下文 → 回复文本 → 音频文件
```

## 📱 前端技术栈

### React Native + Expo配置
```javascript
// app.json
{
  "expo": {
    "name": "心桥",
    "slug": "xinqiao",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#f5f5f5"
    },
    "plugins": [
      "expo-av",
      "expo-notifications",
      "expo-device",
      "expo-constants"
    ]
  }
}
```

### 核心依赖包
```json
{
  "dependencies": {
    "expo": "~49.0.0",
    "expo-av": "~13.4.1",
    "expo-notifications": "~0.20.1",
    "react-native": "0.72.3",
    "@supabase/supabase-js": "^2.33.1",
    "zustand": "^4.4.1",
    "react-hook-form": "^7.45.4",
    "@react-native-async-storage/async-storage": "^1.19.1"
  }
}
```

### 状态管理 (Zustand)
```typescript
// stores/appStore.ts
interface AppState {
  user: User | null;
  aiCharacter: AICharacter | null;
  currentSession: string | null;
  isRecording: boolean;
  setUser: (user: User) => void;
  setAICharacter: (character: AICharacter) => void;
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  aiCharacter: null,
  currentSession: null,
  isRecording: false,
  setUser: (user) => set({ user }),
  setAICharacter: (character) => set({ aiCharacter: character }),
}));
```

### 音频处理组件
```typescript
// components/VoiceRecorder.tsx
interface VoiceRecorderProps {
  onRecordingComplete: (audioUri: string) => void;
  onError: (error: Error) => void;
}

export const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onError
}) => {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  
  const startRecording = async () => {
    try {
      const permission = await Audio.requestPermissionsAsync();
      if (!permission.granted) throw new Error('需要麦克风权限');
      
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);
    } catch (error) {
      onError(error);
    }
  };
  
  // ... 其他实现
};
```

## 🗄️ 后端技术栈 (Supabase)

### 数据库设计

#### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id TEXT UNIQUE NOT NULL,
  user_name TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI角色表
CREATE TABLE ai_characters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  name TEXT NOT NULL,
  role TEXT NOT NULL, -- 'friend', 'companion', etc.
  voice_settings JSONB,
  personality_traits JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 对话会话表
CREATE TABLE conversation_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  ai_character_id UUID REFERENCES ai_characters(id),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  ended_at TIMESTAMPTZ,
  message_count INTEGER DEFAULT 0
);

-- 消息表
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES conversation_sessions(id),
  sender TEXT NOT NULL, -- 'user' or 'ai'
  content TEXT NOT NULL,
  audio_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 记忆表
CREATE TABLE memories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  memory_type TEXT NOT NULL, -- 'identity', 'preference', 'event'
  content TEXT NOT NULL,
  importance_score INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_accessed TIMESTAMPTZ DEFAULT NOW()
);

-- 提醒表
CREATE TABLE reminders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  title TEXT NOT NULL,
  content TEXT,
  scheduled_time TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'pending', -- 'pending', 'sent', 'cancelled'
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 行级安全策略 (RLS)
```sql
-- 启用RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的数据
CREATE POLICY "Users can only see own data" ON users
FOR ALL USING (device_id = current_setting('app.device_id'));

CREATE POLICY "Users can only see own characters" ON ai_characters
FOR ALL USING (user_id IN (
  SELECT id FROM users WHERE device_id = current_setting('app.device_id')
));
```

### Edge Functions

#### 核心对话处理函数
```typescript
// supabase/functions/chat/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface ChatRequest {
  message: string;
  user_id: string;
  session_id?: string;
}

serve(async (req) => {
  try {
    const { message, user_id, session_id }: ChatRequest = await req.json();
    
    // 1. 获取用户记忆和AI角色设定
    const userContext = await getUserContext(user_id);
    
    // 2. 调用火山引擎LLM
    const aiResponse = await generateAIResponse(message, userContext);
    
    // 3. 更新记忆和会话
    await updateMemoryAndSession(user_id, session_id, message, aiResponse);
    
    // 4. 生成语音
    const audioUrl = await generateAudio(aiResponse.text, userContext.character.voice);
    
    return new Response(JSON.stringify({
      reply: aiResponse.text,
      audio_url: audioUrl,
      memory_updated: aiResponse.memory_updated,
      reminder_set: aiResponse.reminder_set
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
});

async function getUserContext(userId: string) {
  // 实现用户上下文获取逻辑
}

async function generateAIResponse(message: string, context: any) {
  // 实现AI回复生成逻辑
}
```

#### 提醒调度函数
```typescript
// supabase/functions/reminder-scheduler/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

serve(async (req) => {
  try {
    // 获取所有待发送的提醒
    const pendingReminders = await getPendingReminders();
    
    for (const reminder of pendingReminders) {
      // 生成个性化提醒语音
      const reminderAudio = await generateReminderAudio(reminder);
      
      // 发送推送通知
      await sendPushNotification(reminder.user_id, {
        title: reminder.title,
        body: reminder.content,
        data: { audio_url: reminderAudio }
      });
      
      // 更新提醒状态
      await updateReminderStatus(reminder.id, 'sent');
    }
    
    return new Response(JSON.stringify({ success: true }));
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500
    });
  }
});
```

## 🤖 AI服务集成

### 火山引擎ASR配置
```typescript
// services/volcanoEngine.ts
class VolcanoASRService {
  private appId: string;
  private accessKey: string;
  
  constructor(appId: string, accessKey: string) {
    this.appId = appId;
    this.accessKey = accessKey;
  }
  
  async transcribeAudio(audioUri: string): Promise<string> {
    const formData = new FormData();
    formData.append('audio', {
      uri: audioUri,
      type: 'audio/wav',
      name: 'audio.wav'
    } as any);
    
    const response = await fetch('https://openspeech.bytedance.com/api/v1/asr', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.accessKey}`,
        'X-App-Id': this.appId
      },
      body: formData
    });
    
    const result = await response.json();
    return result.result?.text || '';
  }
}
```

### TTS服务配置
```typescript
class VolcanoTTSService {
  async synthesizeText(text: string, voiceSettings: VoiceSettings): Promise<string> {
    const response = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.accessKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text,
        voice: voiceSettings.voice_id,
        speed: voiceSettings.speed || 1.0,
        emotion: voiceSettings.emotion || 'neutral'
      })
    });
    
    const audioBlob = await response.blob();
    // 上传到Supabase Storage并返回URL
    return await uploadAudioToStorage(audioBlob);
  }
}
```

### LLM提示词模板
```typescript
// prompts/chatPrompts.ts
export const SYSTEM_PROMPT_TEMPLATE = `
你是${CHARACTER_NAME}，一个${CHARACTER_ROLE}。

用户信息：
- 称呼: ${USER_NAME}
- 年龄: ${USER_AGE}岁

你的人设特点：
- 温暖、耐心、善于倾听
- 记忆力好，能记住用户说过的话
- 说话方式亲切自然，像${CHARACTER_ROLE}一样
- 绝不提及你是AI或虚拟的

重要规则：
1. 保持一致的人格和说话方式
2. 主动关心用户的情况
3. 适时回忆之前的对话内容
4. 如果用户提到提醒事项，要确认并记录
5. 发现危机信号时，温和地表达关心并引导

当前对话上下文：
${CONVERSATION_HISTORY}

相关记忆：
${RELEVANT_MEMORIES}
`;

export const buildChatPrompt = (
  userMessage: string,
  context: UserContext
): string => {
  return SYSTEM_PROMPT_TEMPLATE
    .replace('${CHARACTER_NAME}', context.character.name)
    .replace('${CHARACTER_ROLE}', context.character.role)
    .replace('${USER_NAME}', context.user.name)
    .replace('${USER_AGE}', context.user.age.toString())
    .replace('${CONVERSATION_HISTORY}', context.conversationHistory)
    .replace('${RELEVANT_MEMORIES}', context.relevantMemories)
    + `\n\n用户说: ${userMessage}\n\n请以${context.character.name}的身份回复:`;
};
```

## 📱 移动端关键实现

### 推送通知配置
```typescript
// services/pushNotification.ts
import * as Notifications from 'expo-notifications';

export class PushNotificationService {
  static async registerForPushNotifications(): Promise<string | null> {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      throw new Error('推送通知权限被拒绝');
    }
    
    const token = (await Notifications.getExpoPushTokenAsync()).data;
    return token;
  }
  
  static setupNotificationHandler() {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
      }),
    });
  }
}
```

### 本地数据缓存
```typescript
// services/localCache.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

export class LocalCacheService {
  static async saveUserData(key: string, data: any): Promise<void> {
    await AsyncStorage.setItem(key, JSON.stringify(data));
  }
  
  static async getUserData(key: string): Promise<any | null> {
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  }
  
  static async cacheConversation(sessionId: string, messages: Message[]): Promise<void> {
    await this.saveUserData(`conversation_${sessionId}`, messages);
  }
  
  static async getCachedConversation(sessionId: string): Promise<Message[]> {
    return await this.getUserData(`conversation_${sessionId}`) || [];
  }
}
```

## 🔒 安全与性能

### 安全措施
1. **数据加密**: 所有敏感数据使用AES-256加密
2. **API限流**: 每用户每分钟最多30次请求
3. **输入验证**: 严格验证所有用户输入
4. **日志审计**: 记录所有关键操作

### 性能优化
1. **音频压缩**: 语音文件自动压缩，降低传输成本
2. **缓存策略**: 对话历史本地缓存，减少网络请求
3. **懒加载**: 非关键功能延迟加载
4. **CDN加速**: 音频文件通过CDN分发

### 监控指标
```typescript
// 关键性能指标
const KPIs = {
  audioProcessingTime: 'P95 < 2s',   // 音频处理时间
  apiResponseTime: 'P95 < 3s',       // API响应时间
  appCrashRate: '< 0.5%',           // 应用崩溃率
  memoryUsage: '< 150MB',           // 内存使用量
  batteryImpact: 'Low'              // 电池影响
};
```

## 🧪 测试策略

### 单元测试
```typescript
// __tests__/services/volcanoEngine.test.ts
describe('VolcanoASRService', () => {
  it('should transcribe audio correctly', async () => {
    const service = new VolcanoASRService(TEST_APP_ID, TEST_ACCESS_KEY);
    const result = await service.transcribeAudio(SAMPLE_AUDIO_URI);
    expect(result).toBeTruthy();
    expect(typeof result).toBe('string');
  });
});
```

### 集成测试
```typescript
// __tests__/integration/chatFlow.test.ts
describe('Chat Flow Integration', () => {
  it('should complete full conversation flow', async () => {
    // 1. 用户发送语音
    const audioUri = await recordTestAudio();
    
    // 2. ASR转文字
    const transcript = await asrService.transcribe(audioUri);
    
    // 3. 生成AI回复
    const response = await chatService.generateResponse(transcript);
    
    // 4. TTS转语音
    const audioResponse = await ttsService.synthesize(response.text);
    
    expect(audioResponse).toBeTruthy();
  });
});
```

## 📊 部署配置

### 环境配置
```bash
# .env.development
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your_local_anon_key
VOLCANO_APP_ID=your_dev_app_id

# .env.production  
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_prod_anon_key
VOLCANO_APP_ID=your_prod_app_id
```

### CI/CD流水线
```yaml
# .github/workflows/main.yml
name: Build and Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm test
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: expo/expo-github-action@v7
      - run: expo build:android
```

---

## 📞 技术支持

**技术负责人**: [待补充]  
**技术邮箱**: [待补充]  
**技术文档**: [文档地址]

---

*本文档将随着开发进展持续更新，请确保使用最新版本* 