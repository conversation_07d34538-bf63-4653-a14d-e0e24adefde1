"""
共享线程池管理器 - P1性能调优

优化并发性能，避免多个服务创建独立线程池造成资源浪费
实现全局线程池复用和智能资源管理
"""

import asyncio
import concurrent.futures
import threading
import os
from typing import Optional
from api.settings import logger

class SharedThreadPoolManager:
    """全局共享线程池管理器"""

    _instance: Optional['SharedThreadPoolManager'] = None
    _lock = threading.Lock()

    def __new__(cls) -> 'SharedThreadPoolManager':
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化线程池管理器"""
        if hasattr(self, '_initialized'):
            return

        # P1性能调优：根据CPU核心数智能设置线程池大小
        cpu_count = os.cpu_count() or 4

        # 为不同类型的任务创建专用线程池
        self._io_pool_size = min(max(cpu_count * 4, 20), 50)  # IO密集型任务：CPU数*4，最大50
        self._compute_pool_size = min(max(cpu_count, 4), 16)  # CPU密集型任务：CPU数，最大16

        self._io_executor: Optional[concurrent.futures.ThreadPoolExecutor] = None
        self._compute_executor: Optional[concurrent.futures.ThreadPoolExecutor] = None
        self._executor_lock = asyncio.Lock()

        self._initialized = True

        logger.info(f"共享线程池管理器初始化 - IO池: {self._io_pool_size}线程, 计算池: {self._compute_pool_size}线程")

    async def get_io_executor(self) -> concurrent.futures.ThreadPoolExecutor:
        """获取IO密集型任务的线程池"""
        if self._io_executor is None:
            async with self._executor_lock:
                if self._io_executor is None:
                    self._io_executor = concurrent.futures.ThreadPoolExecutor(
                        max_workers=self._io_pool_size,
                        thread_name_prefix="shared_io_pool"
                    )
                    logger.info(f"创建共享IO线程池: {self._io_pool_size}线程")

        return self._io_executor

    async def get_compute_executor(self) -> concurrent.futures.ThreadPoolExecutor:
        """获取CPU密集型任务的线程池"""
        if self._compute_executor is None:
            async with self._executor_lock:
                if self._compute_executor is None:
                    self._compute_executor = concurrent.futures.ThreadPoolExecutor(
                        max_workers=self._compute_pool_size,
                        thread_name_prefix="shared_compute_pool"
                    )
                    logger.info(f"创建共享计算线程池: {self._compute_pool_size}线程")

        return self._compute_executor

    async def run_io_task(self, func, *args, **kwargs):
        """
        在IO线程池中执行任务

        适用于：
        - HTTP请求
        - 数据库连接
        - 文件操作
        - 外部API调用
        """
        executor = await self.get_io_executor()
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(executor, func, *args, **kwargs)

    async def run_compute_task(self, func, *args, **kwargs):
        """
        在计算线程池中执行任务

        适用于：
        - CPU密集型计算
        - 数据处理
        - 算法运算
        - 图像/音频处理
        """
        executor = await self.get_compute_executor()
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(executor, func, *args, **kwargs)

    def get_pool_stats(self) -> dict:
        """获取线程池统计信息"""
        stats = {
            "io_pool_size": self._io_pool_size,
            "compute_pool_size": self._compute_pool_size,
            "io_pool_created": self._io_executor is not None,
            "compute_pool_created": self._compute_executor is not None
        }

        # 添加运行时统计（如果线程池已创建）
        if self._io_executor:
            stats["io_pool_threads"] = self._io_executor._threads
        if self._compute_executor:
            stats["compute_pool_threads"] = self._compute_executor._threads

        return stats

    async def shutdown(self):
        """优雅关闭所有线程池"""
        if self._io_executor:
            self._io_executor.shutdown(wait=True)
            logger.info("IO线程池已关闭")

        if self._compute_executor:
            self._compute_executor.shutdown(wait=True)
            logger.info("计算线程池已关闭")

# 全局实例
_shared_pool_manager = SharedThreadPoolManager()

async def get_shared_io_executor() -> concurrent.futures.ThreadPoolExecutor:
    """获取共享IO线程池"""
    return await _shared_pool_manager.get_io_executor()

async def get_shared_compute_executor() -> concurrent.futures.ThreadPoolExecutor:
    """获取共享计算线程池"""
    return await _shared_pool_manager.get_compute_executor()

async def run_in_shared_io_pool(func, *args, **kwargs):
    """在共享IO线程池中执行任务"""
    return await _shared_pool_manager.run_io_task(func, *args, **kwargs)

async def run_in_shared_compute_pool(func, *args, **kwargs):
    """在共享计算线程池中执行任务"""
    return await _shared_pool_manager.run_compute_task(func, *args, **kwargs)

def get_thread_pool_stats() -> dict:
    """获取线程池统计信息"""
    return _shared_pool_manager.get_pool_stats()
