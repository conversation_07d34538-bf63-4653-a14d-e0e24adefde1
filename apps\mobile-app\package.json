{"name": "myapp", "version": "0.0.1", "private": true, "main": "expo-router/entry", "scripts": {"start": "cross-env EXPO_NO_DOTENV=1 expo start", "prebuild": "cross-env EXPO_NO_DOTENV=1 pnpm expo prebuild", "android": "cross-env EXPO_NO_DOTENV=1 expo run:android", "ios": "cross-env EXPO_NO_DOTENV=1 expo run:ios", "web": "cross-env EXPO_NO_DOTENV=1 expo start --web", "xcode": "xed -b ios", "doctor": "npx expo-doctor@latest", "preinstall": "npx only-allow pnpm", "start:staging": "cross-env APP_ENV=staging pnpm run start", "prebuild:staging": "cross-env APP_ENV=staging pnpm run prebuild", "prebuild:development": "cross-env APP_ENV=development pnpm run prebuild", "android:staging": "cross-env APP_ENV=staging pnpm run android", "ios:staging": "cross-env APP_ENV=staging pnpm run ios", "start:production": "cross-env APP_ENV=production pnpm run start", "prebuild:production": "cross-env APP_ENV=production pnpm run prebuild", "android:production": "cross-env APP_ENV=production pnpm run android", "ios:production": "cross-env APP_ENV=production pnpm run ios", "build:development:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:staging:ios": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform ios", "build:staging:android": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform android ", "build:production:ios": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "prepare": "husky", "app-release": "cross-env SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft", "version": "pnpm run prebuild && git add .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc  --noemit", "lint:translations": "eslint ./src/translations/ --fix --ext .json  ", "test": "jest", "check-all": "pnpm run lint && pnpm run type-check && pnpm run lint:translations && pnpm run test", "test:ci": "pnpm run test --coverage", "test:watch": "pnpm run test --watch", "install-maestro": "curl -Ls 'https://get.maestro.mobile.dev' | bash", "e2e-test": "maestro test .maestro/ -e APP_ID=com.obytes.development"}, "dependencies": {"@expo/metro-runtime": "^4.0.1", "@gorhom/bottom-sheet": "^5.0.5", "@hookform/resolvers": "^3.9.0", "@shopify/flash-list": "1.7.1", "@tanstack/react-query": "^5.52.1", "app-icon-badge": "^0.1.2", "axios": "^1.7.5", "expo": "~52.0.26", "expo-constants": "~17.0.4", "expo-dev-client": "~5.0.9", "expo-font": "~13.0.3", "expo-image": "~2.0.4", "expo-linking": "~7.0.4", "expo-localization": "~16.0.1", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.21", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.7", "i18next": "^23.14.0", "lodash.memoize": "^4.1.2", "moti": "^0.29.0", "nativewind": "^4.1.21", "react": "18.3.1", "react-dom": "18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.1", "react-native": "0.76.6", "react-native-edge-to-edge": "^1.1.2", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-controller": "^1.13.2", "react-native-mmkv": "~3.1.0", "react-native-reanimated": "~3.16.1", "react-native-restart": "0.0.27", "react-native-safe-area-context": "4.12.0", "react-native-screens": "^4.4.0", "react-native-svg": "~15.8.0", "react-native-web": "~0.19.13", "react-query-kit": "^3.3.0", "tailwind-variants": "^0.2.1", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.26.0", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@dev-plugins/react-query": "^0.0.7", "@expo/config": "~10.0.3", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react-native": "^12.7.2", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.12", "@types/lodash.memoize": "^4.1.9", "@types/react": "~18.3.12", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-expo": "^7.1.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-compiler": "19.0.0-beta-a7bf2bd-20241110", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.15.2", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-unicorn": "^46.0.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^9.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "~52.0.3", "jest-junit": "^16.0.0", "lint-staged": "^15.2.9", "np": "^10.0.7", "prettier": "^3.3.3", "tailwindcss": "3.4.4", "ts-jest": "^29.1.2", "typescript": "^5.3.3"}, "repository": {"type": "git", "url": "git+https://github.com/user/repo-name.git"}, "packageManager": "pnpm@9.12.3", "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-restart"]}}, "install": {"exclude": ["eslint-config-expo"]}}, "osMetadata": {"initVersion": "7.0.5"}}