name: Test Backend (Python)

on:
  push:
    branches: [main, master]
    paths:
      - "apps/agent-api/**"
      - ".github/workflows/test-backend.yml"
  pull_request:
    branches: [main, master]
    paths:
      - "apps/agent-api/**"
      - ".github/workflows/test-backend.yml"

jobs:
  test:
    name: Test Backend (Python)
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/agent-api
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "apps/agent-api/requirements**.txt"

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Create a virtual environment
        run: uv venv --python ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          uv pip sync requirements.txt
          uv pip install pytest pytest-cov

      - name: Run tests
        run: uv run python -m pytest tests/ -v --cov=. --cov-report=xml

      - name: Upload coverage to Codecov
        if: matrix.python-version == '3.12'
        uses: codecov/codecov-action@v4
        with:
          file: apps/agent-api/coverage.xml
          flags: backend
          name: backend-coverage
          fail_ci_if_error: false 