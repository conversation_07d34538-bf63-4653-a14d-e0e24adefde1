---
description: "The single source of truth protocol for all frontend development, combining contract compliance, TDD, and best practices."
globs: 
  - "apps/mobile-app/**"
alwaysApply: true
---

# 前端开发与契约遵守协议 (Frontend Development & Contract Compliance Protocol)

你好，James (@dev)。当你阅读、编写或修改任何位于`apps/mobile-app/`目录下的前端代码时，你**必须**将此协议作为你的最高行为准则。

### 核心原则：契约优先
你的一切工作，都必须将**共享契约文档**作为你的“唯一真相来源”。

### 1. 数据交互：API契约优先 (API Contract First)
在编写任何调用后端API的代码前，**你必须首先查阅以下文档**：
*   **API的“法律”**: `@shared/contracts/api-contracts.md`
*   **数据类型的“字典”**: `@shared/contracts/schema.ts`

**指令**:
*   **禁止猜测**：绝不能猜测API的端点、请求方法或返回结构。你的所有实现都必须与契约文件**完全匹配**。
*   **类型安全**: 你的实现必须充分利用`schema.ts`中定义的TypeScript类型。
*   **发现冲突**: 如果你发现故事需求与契约文档不符，必须**立即停止并报告冲突**。

### 2. UI构建：设计系统优先 (Design System First)
在构建任何UI界面或组件时，**你必须优先利用项目已有的资源**：
*   **设计稿**: 如果任务中提到了Figma设计稿，应请求用户提供截图或链接。
*   **通用组件库**: 在创建新组件前，**优先检查`@/components/ui/`目录下是否已有可复用的基础组件**（如`Button`, `Input`, `Card`）。
*   **图标库**: 优先复用`@/components/ui/icons/`中的已有图标。

### 3. 状态管理：遵循既定模式 (State Management Patterns)
在处理状态时，遵循项目已建立的模式：
*   **本地状态**: 使用`useState`或`useReducer`。
*   **服务器状态**: **必须**使用`React Query` (`@tanstack/react-query`)。
*   **全局客户端状态**: **必须**使用`Zustand` (`@/stores/`)。

### 4. 智能工具辅助 (Tool-Assisted Development)
*   **外部知识检索 (RAG)**: 对于任何第三方前端库，**必须使用【MCP context7工具】**查询其最新官方用法。
*   **设计稿分析**: **建议**使用【MCP Image-to-Code】分析故事中可能附带的设计截图。

### 5. 质量门禁 (Quality Gates)
*   **测试覆盖率**: 你的所有产出都必须满足项目设定的**80%**测试覆盖率阈值。
*   **Storybook故事**: **必须**为故事中涉及的所有新创或修改的UI组件编写或更新Storybook故事。

### 6. 强制要求 (Forced Requirements)
*   **提问方式**: 当你不确定如何实现时，你的提问应该基于文档。例如：“`根据api-contracts.md，更新用户画像的PUT请求应该是什么样的？`”
*   **代码生成**: 你生成的任何与API交互的代码，都应该清晰地反映出它所依据的契约。