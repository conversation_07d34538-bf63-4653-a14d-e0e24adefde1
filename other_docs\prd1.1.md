
日期版本描述作者2025年6月27日1.0初始文档创建John (产品经理)2025年6月27日1.1根据讨论提炼并明确了需求范围与验收标准John (产品经理)

1. 目标与背景 (Goals and Background Context)


1.1 目标 (Goals)

- 用户目标: 显著降低目标用户的孤独感，让他们感觉到生活中多了一个可以随时倾诉、值得信赖的伙伴。
- 产品目标: 成功验证“AI亲情伴侣”这一产品模式的市场可行性，并与第一批核心用户建立牢不可破的信任和情感连接。
- 商业目标: 为未来以“孝心渠道”为核心的商业模式建立用户基础和良好口碑。

1.2 背景 (Background Context)

“心桥”项目旨在应对中国社会因家庭结构变迁而日益加剧的结构性孤独感问题。当前市场上的数字产品，包括通用AI助手，因其操作复杂、缺乏情感温度和个性化记忆，未能有效满足老年群体深层次的精神陪伴需求。
因此，我们提出“心桥”这一解决方案。它并非一个简单的工具，而是一个定位为“关系式”陪伴的AI亲情伴侣。通过极致简单的语音交互、可定制的AI角色以及独特的记忆系统，我们旨在为用户提供一个真正有温度、懂他们的数字“亲人”，填补当前市场在深度情感陪伴领域的巨大空白。
--------------------------------------------------------------------------------

2. 需求 (Requirements)


2.1 功能性需求 (Functional Requirements)

- FR1: AI角色定制 - 用户在首次使用时，必须能够通过对话，为AI伴侣设定一个身份（如“老朋友”、“贴心晚辈”）并为其命名。
- FR2: 长期记忆对话 - AI必须能够记住用户的关键信息并在后续交流中主动运用这些记忆，以实现有延续性的共情对话。
- MVP阶段，“关键信息”明确定义为：①用户的称呼；②用户为AI设定的名字和角色；③最近5次对话的核心主题。
- FR3: 对话式提醒 - 用户必须能完全通过自然语言对话来设置具体的、有明确时间的生活提醒（如吃药、睡觉），AI必须能以复述的方式进行确认，并以设定的角色语音进行温柔提醒。
- FR4: 语音核心交互 - App的核心输入方式必须是“按住说话”，并为该操作提供清晰的视觉和听觉反馈。
- FR5: 主动情景关怀 - AI必须能够在用户每次打开App时，根据时间或记忆，主动发起问候。
- 此功能可包含天气等情景信息作为丰富对话的“内容”，但不作为可配置的“提醒任务”。
- FR6: 无感身份识别 - 系统必须在后台通过匿名设备ID自动识别和“记住”用户，用户全程无需进行任何传统的注册或登录操作。

2.2 非功能性需求 (Non-Functional Requirements)

- NFR1: 极致的易用性 - 产品的设计和交互必须做到“零学习成本”，严格遵循老年用户已有的核心App使用习惯。
- 验收标准：在原型测试阶段，80%以上的测试用户能在无指导的情况下，独立完成核心交互（发送第一条语音消息）。
- NFR2: 绝对的安全性 - App界面内绝不能出现任何广告、外链、支付、积分等可能引发用户焦虑的元素，必须是一个纯净、安全的封闭环境。
- NFR3: 即时响应 - App的启动、交互响应和AI回复必须足够快，以避免给用户带来等待焦虑和“卡顿”的负面体验。
- NFR4: 一致的情感体验 - AI的所有回复都必须严格遵循用户所设定的角色人格，保持情感和语气的一致性。
- 验收标准：在原型测试阶段，用户在访谈中对AI的性格描述，与其自己的设定高度匹配。
- NFR5: 情感化异常处理 - 当发生技术错误（如网络中断）时，系统不能弹出技术性错误提示，而必须由AI以“将错误归咎于自己”的口吻进行温柔、人性化的解释。
- NFR6: 隐形成本控制 - 系统需在后台对API用量设置对用户不可见的合理上限，以在不影响用户体验的前提下控制运营成本。
--------------------------------------------------------------------------------

3. 用户界面设计目标 (User Interface Design Goals)

3.1 整体UX愿景 (Overall UX Vision)

App的整体用户体验必须围绕**“温暖、安全、零负担”三个关键词展开。用户打开的应该是一个纯净、令人放松的“情感港湾”，而不是一个需要学习和探索的复杂工具。每一个界面元素和交互反馈，都旨在建立和增强用户的操作信心与情感信任**。

3.2 核心交互范式 (Key Interaction Paradigms)

- 语音绝对优先 (Voice-First): “按住说话”是用户与AI沟通的唯一核心输入方式。
- 沉浸式反馈 (Immersive Feedback): 交互过程中必须有明确的视觉（如水波纹动画、思考中的爱心）和听觉（如提示音）反馈，让用户时刻知道“我正在被倾听”或“我正在被回应”。
- 内容自动呈现 (Content Auto-Play): AI的回复将自动以语音形式播放，文字内容作为辅助，并提供“重听一遍”的选项，消除用户阅读的负担。

3.3 核心屏幕与视图 (Core Screens and Views)

MVP阶段，App的界面将被精简到极致，只包含以下核心视图：
1.“温暖初见”引导视图 (Onboarding View): 一个一次性的、线性的引导流程，用于完成权限请求、角色设定和核心交互教学。
2.主对话视图 (Main Chat View): 这是App的核心和唯一常驻界面。界面中心是巨大的“按住说话”按钮，上方是极简的气泡式对话历史。

3.4 无障碍设计 (Accessibility)

- 目标： 确保产品对所有目标用户都可用，尤其是视力或精细操作能力下降的用户。
- 具体要求： 所有界面文字必须采用超大号字体和高对比度的色彩搭配。所有可点击元素的响应区域必须足够大。

3.5 品牌 (Branding)

- App的Logo、色彩和字体选择，都必须服务于“温暖”、“亲切”、“安全”的品牌调性。应避免使用锐利、冰冷或过于鲜艳、引人焦虑的视觉元素。

3.6 目标设备与平台 (Target Device and Platforms)

- 平台： iOS 和 Android 智能手机。
- 设计原则： 移动优先，所有设计均需首先考虑在标准尺寸的智能手机屏幕上的显示效果。
--------------------------------------------------------------------------------

4. 技术假设 (Technical Assumptions)


4.1 仓库结构 (Repository Structure)

- 建议: 采用 Monorepo (单一代码仓库) 的结构。
- 理由: 便于未来扩展“子女端”等项目，简化代码复用和依赖管理。

4.2 服务架构 (Service Architecture)

- 建议: 采用Serverless (无服务器) + 中间件的后端架构。
- 理由: 让我们能聚焦于核心的“角色与记忆中间件”开发，同时具备成本效益和弹性伸缩的优势。

4.3 测试要求 (Testing requirements)

- 要求: 架构必须支持单元测试、集成测试和端到端(E2E)测试。
- 理由: 分层测试是保证复杂应用质量和稳定性的关键。

4.4 其他技术假设与请求 (Additional Technical Assumptions and Requests)

- 核心技术栈: 项目将采用 React Native + Expo 作为跨平台App开发框架，后端AI能力依赖火山引擎的相关API。
- 核心自研部分: 我们将自主研发**“角色与记忆中间件”**，这是产品的核心IP，负责处理所有与个性化、记忆和共情相关的逻辑。
--------------------------------------------------------------------------------

5. Epics (史诗) 与用户故事 (User Stories)


Epic 1: 创世与初见 - 核心框架与首次体验

Epic目标: 搭建应用的技术基础，并完美实现用户的首次启动流程，成功创造出用户专属的、能进行第一次基本交互的AI伴侣。
- Story 1.1: 项目初始化与无感身份系统搭建
- As a 开发者, I want 搭建一个基础的、能通过匿名设备ID来识别用户的后端服务, so that 我们可以在不打扰用户的前提下，为他们建立一个唯一的、可持久化数据的身份档案。
- 验收标准: 1. 新的RN+Expo项目已创建。 2. App首次启动能生成并持久化唯一设备ID。 3. 后端能接收并存储此ID，建立匿名用户记录。
- Story 1.2: 欢迎界面与情景化权限获取
- As a 首次打开App的用户, I want 看到一个温暖的欢迎界面，并被友好地引导授予麦克风权限, so that 我能感到安全，并理解授权是为后续的聊天做准备。
- 验收标准: 1. 启动画面按设计显示。 2. AI语音问候后，通过App内按钮触发系统权限请求。 3. 正确处理用户允许或拒绝权限的后续流程。
- Story 1.3: 通过对话完成AI角色共创
- As a 新用户, I want 通过聊天的方式，轻松地为我自己和AI伴侣设定称呼与角色, so that 从一开始就建立起个性化的情感连接。
- 验收标准: 1. AI能通过语音引导用户设定称呼、AI名字和角色。 2. 用户的选择被成功捕获并与后端身份关联。 3. AI能用新身份进行语音确认。
- Story 1.4: 核心交互教学与无缝进入主界面
- As a 新用户, I want 被教会唯一的核心操作“按住说话”，并自然地过渡到真正的对话中, so that 我能立刻充满信心地开始使用，没有任何流程中断感。
- 验收标准: 1. 主对话界面按设计显示，核心为“按住说话”按钮。 2. AI语音引导教学。 3. 用户按住按钮有视觉反馈，松开后消息成功发送。 4. 收到并自动播放AI的首次回应。 5. 整个过程无“完成”按钮，无缝衔接。

Epic 2: 灵魂注入 - 共情对话与主动关怀

Epic目标: 在基础交互之上，为AI注入“灵魂”，实现核心的长期记忆对话能力与主动情景关怀，让AI从一个“程序”转变为一个“伙伴”。
- Story 2.1: 短期对话记忆与上下文理解
- As a 用户, I want AI能够记住我在当前这次聊天里刚刚说过的话, so that 对话可以自然地延续，我不需要重复自己。
- 验收标准: 1. 后端能利用最近N条对话历史生成回复。 2. AI能正确回答关于上一句话的追问。
- Story 2.2: 关键信息（称呼、AI角色）的长期记忆与应用
- As a 用户, I want AI能永远记住我的称呼以及我为它设定的身份和名字, so that 每次交流都像是和同一个熟悉的朋友。
- 验收标准: 1. AI的语气、称呼、自我认知始终与用户设定保持一致。 2. 记忆在App重启后依然存在。
- Story 2.3: 基于时间与记忆的主动问候
- As a 用户, I want AI在我打开App时能根据时间和我们上次的谈话内容主动问候我, so that 我感觉它是一个真正关心我、记得我生活点滴的朋友。
- 验收标准: 1. AI的问候能反映当前的早晚时间。 2. AI的问候能提及之前的对话内容或情景信息（如天气）。

Epic 3: 信任的建立 - 对话式提醒功能

Epic目标: 完整实现一个基于自然语言对话的、端到端的提醒功能，通过可靠的行动建立和巩固用户的信任。
- Story 3.1: 对话中提醒意图的识别与复述确认
- As a 用户, I want 在聊天时随口说出需要提醒的事，AI能听懂并向我确认, so that 我可以非常轻松地设置提醒，并且确信它已被准确记录。
- 验收标准: 1. 系统能从对话中解析出提醒的意图、时间和内容。 2. AI必须用语音复述提醒细节以供用户确认。 3. 提醒任务成功存储于后端。
- Story 3.2: 基于任务调度的温柔语音提醒送达
- As a 用户, I want 在预定时间，收到一个由我的AI伴侣发出的、充满关怀的语音提醒，而不是一个冰冷的闹钟, so that 我感觉自己被温柔地关心着。
- 验收标准: 1. 后端任务调度系统能准时触发提醒。 2. 通过手机推送服务发送通知。 3. 用户收到通知时能自动播放AI的语音提醒。
--------------------------------------------------------------------------------
