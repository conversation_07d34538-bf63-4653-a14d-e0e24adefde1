 # 故事 1.7-UI: 危机干预界面设计

## 基本信息
- **故事编号**: 1.7-UI
- **故事标题**: 危机干预界面设计
- **Epic**: MVP - 安全保障与风险防范
- **用户角色**: UI设计师, 前端开发者
- **优先级**: 高（P0 - 安全合规要求）
- **工作量估计**: 2-3 个工作日
- **依赖关系**: 1.1-Frontend（项目基础设置）
- **Status**: Approved

## 故事描述

作为UI设计师，我需要设计危机干预相关的界面组件和交互流程，**以便** 当系统检测到用户可能处于危机状态时，能够及时、恰当、有效地为用户提供专业帮助资源和紧急联系方式。

## 验收标准

### AC1: 危机检测预警界面
- [ ] 设计温和、非侵入性的危机关怀提示界面
- [ ] 提供"我很好"和"我需要帮助"两个主要操作选项
- [ ] 界面配色使用温暖、安全的色调（避免红色等警示色）
- [ ] 文案语调温和关怀，避免医疗术语

### AC2: 安全脚本展示界面
- [ ] 设计简洁清晰的安全脚本展示卡片
- [ ] 支持分步骤展示呼吸练习、接地技巧等自助方法
- [ ] 提供语音朗读功能的控制界面
- [ ] 设计进度指示器显示当前步骤

### AC3: 紧急联系界面
- [ ] 设计一键拨打紧急热线的大按钮（心理危机干预热线：400-161-9995）
- [ ] 提供专业心理咨询机构联系方式列表
- [ ] 设计紧急联系人快速呼叫功能
- [ ] 添加地理位置显示就近心理健康服务机构

### AC4: 资源推荐界面
- [ ] 设计心理健康资源推荐卡片（文章、视频、冥想练习）
- [ ] 提供情绪日记记录入口
- [ ] 设计后续关怀提醒设置界面
- [ ] 添加专业咨询预约引导界面

## Tasks / Subtasks

### 阶段一：危机检测界面设计 (1天)
- [ ] **危机关怀提示设计** (AC1)
  - 设计温和的弹出式关怀界面
  - 创建情感支持的插图和图标
  - 设计用户情绪状态确认组件
  - 制作多种危机级别的界面变体

### 阶段二：安全脚本界面设计 (1天)
- [ ] **自助方法展示设计** (AC2)
  - 设计呼吸练习引导界面
  - 创建接地技巧步骤展示组件
  - 设计音频播放控制器
  - 制作练习进度和计时器界面

### 阶段三：紧急资源界面设计 (1天)
- [ ] **紧急联系界面设计** (AC3)
  - 设计醒目的紧急热线呼叫按钮
  - 创建专业机构信息卡片
  - 设计地图集成显示组件
  - 制作联系历史记录界面

- [ ] **资源推荐界面设计** (AC4)
  - 设计心理健康资源浏览界面
  - 创建情绪记录入口设计
  - 设计关怀提醒设置组件
  - 制作咨询预约流程界面

## Dev Notes

### 设计关键要求

#### 适老化考虑（基于NFR1）
- 字体大小：紧急信息使用 ≥ 20pt 字体
- 按钮尺寸：紧急呼叫按钮 ≥ 60x60pt
- 色彩对比度：所有文本对比度 > 7:1
- 操作简化：单步操作，减少认知负担

#### 情感设计原则
- **温暖色调**：使用暖色系（橙色、绿色）替代红色警示
- **柔和视觉**：圆角设计，避免尖锐线条
- **安全感营造**：使用包容性插图和图标
- **隐私保护**：提供匿名使用选项

#### 无障碍设计要求
- 支持VoiceOver/TalkBack屏幕阅读器
- 提供高对比度模式
- 支持字体缩放（最大200%）
- 键盘导航完全支持

#### 技术选型建议 (Technology Stack Recommendations)
- **地理位置与地图显示**: 推荐使用 `react-native-maps` 库，它提供了强大的地图展示和交互功能，能很好地支持"就近心理健康服务机构"的显示需求。
- **语音朗读 (TTS)**: 推荐使用 `expo-speech` 库。作为Expo生态的一部分，它集成简单，能快速实现安全脚本的语音朗读功能，降低开发复杂度。

#### 数据模型定义 (Data Models)
- **心理健康资源卡片 (Resource Card)**
  - `id`: string (唯一标识)
  - `type`: 'article' | 'video' | 'meditation' (资源类型)
  - `title`: string (标题)
  - `coverImage`: url (封面图)
  - `duration?`: string (若是视频/音频，显示时长)
  - `source`: string (来源)
- **专业机构信息 (Service Provider)**
  - `id`: string (唯一标识)
  - `name`: string (机构名称)
  - `address`: string (地址)
  - `phone`: string (联系电话)
  - `distance?`: string (距离，通过地理位置计算得出)

#### 边缘场景处理 (Edge Case Handling)
- **地理位置授权失败**: 若用户拒绝授权，"就近机构"功能应优雅降级。建议将对应按钮置灰，并提供一个信息图标解释为何需要此权限，引导用户在需要时开启。
- **电话呼叫失败**: 在设备无SIM卡或无法拨打电话的情况下，应通过一个Toast或非侵入式弹窗向用户提示"无法拨打电话"，而不是无响应。
- **资源加载失败**: 当心理健康资源或机构列表加载失败时，应在相应区域显示一个友好的空状态或错误提示界面（例如，"网络似乎出了点问题，请稍后重试"），避免出现空白。

### 关键界面规范

#### 1. 危机关怀弹窗
```
┌─────────────────────────────┐
│        🤗 关怀提示          │
│                            │
│  我们注意到您可能正在经历    │
│  一些困难。您的感受很重要，   │
│  我们在这里支持您。          │
│                            │
│  ┌──────────┐ ┌──────────┐ │
│  │ 我很好   │ │ 我需要帮助│ │
│  └──────────┘ └──────────┘ │
│                            │
│     [稍后再问] [不再提醒]    │
└─────────────────────────────┘
```

#### 2. 紧急热线界面
```
┌─────────────────────────────┐
│      🆘 紧急求助热线         │
│                            │
│  ┌─────────────────────────┐ │
│  │   📞 400-161-9995      │ │
│  │   24小时心理危机干预    │ │
│  │        [一键拨打]       │ │
│  └─────────────────────────┘ │
│                            │
│       其他专业资源          │
│  ┌─────────────────────────┐ │
│  │ 🏥 就近心理健康中心    │ │
│  │ 📝 在线心理咨询        │ │
│  │ 📱 紧急联系人          │ │
│  └─────────────────────────┘ │
└─────────────────────────────┘
```

#### 3. 安全脚本展示
```
┌─────────────────────────────┐
│       🌸 呼吸练习           │
│                            │
│     步骤 1/5: 深呼吸        │
│  ▓▓▓▓▓░░░░░ 50%            │
│                            │
│  请跟随指示进行深呼吸：     │
│                            │
│      ⭕ 吸气 4 秒           │
│         ⏱️ 3...2...1       │
│                            │
│  ┌───────┐ ┌───────┐       │
│  │   ⏸️  │ │  ⏭️   │       │
│  │ 暂停  │ │ 下一步│       │
│  └───────┘ └───────┘       │
└─────────────────────────────┘
```

## Testing

### 用户体验测试要求
- [ ] **情感响应测试**: 测试界面是否能产生安全感和信任感
- [ ] **危机场景模拟**: 模拟不同危机级别的用户反应
- [ ] **无障碍功能测试**: 验证屏幕阅读器和辅助功能
- [ ] **老年用户测试**: 验证适老化设计的有效性

### 功能测试检查点
- [ ] 危机检测界面的触发条件和展示逻辑
- [ ] 紧急热线拨打功能的可用性
- [ ] 安全脚本音频播放的稳定性
- [ ] 地理定位和就近资源推荐的准确性

## 出入条件

### 进入条件 (Entry Criteria)
- [ ] 前端项目基础设置已完成（故事1.1-Frontend）
- [ ] 心理健康专家已提供安全脚本内容
- [ ] 紧急联系资源已收集整理完成
- [ ] 法律合规要求已明确

### 退出条件 (Exit Criteria)
- [ ] 所有验收标准已通过设计评审
- [ ] 原型已通过心理健康专家审核
- [ ] 无障碍设计已通过专业测试
- [ ] 用户测试反馈为正面

### 交付物 (Deliverables)
- [ ] **完整的UI设计稿**：包含所有危机干预界面
- [ ] **交互原型**：展示完整的危机干预流程
- [ ] **设计规范文档**：颜色、字体、组件使用指南
- [ ] **无障碍设计说明**：辅助功能实现要求

## 风险与缓解措施

### 主要风险
1. **过度医疗化设计**：界面可能显得过于临床化，让用户感到距离感
2. **文化敏感性问题**：不同文化背景用户对危机干预的接受度不同
3. **隐私担忧**：用户可能担心危机状态被记录或分享
4. **法律责任风险**：界面设计可能涉及医疗建议的法律边界

### 缓解措施
1. **人性化设计**：使用温暖的插图和友好的文案语调
2. **文化适配**：提供多种文化背景下的界面变体
3. **隐私透明**：明确说明数据处理和隐私保护政策
4. **免责声明**：清晰标明应用仅提供支持资源，不替代专业医疗建议

## 后续故事依赖关系

### 🔗 此故事为以下故事提供设计基础：
- **故事1.7-Frontend**: 危机干预功能实现
- **故事1.1-Frontend-ErrorHandling**: 全局错误处理（用户友好的异常提示）

### 📋 相关的其他功能模块：
- **提醒功能**：危机关怀的后续提醒设置
- **设置功能**：危机干预功能的开启/关闭选项

## 相关文档引用
- [产品需求 - 基础危机干预](../../prd/requirements.md#FR7)
- [UX设计指南](../../prd/ux-design.md)
- [移动应用编码标准](../../architecture/mobile-app-coding-standards.md)
- [用户故事 - 安全保障](../../prd/user-stories.md)