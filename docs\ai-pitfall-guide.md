# AI开发避坑指南 (AI Pitfall Guide)

*本项目中AI开发常见错误与正确实践的知识库。所有开发者（包括AI）在开始任务前都必须阅读此文档。*

## 后端开发 (Backend)

### 错误：在循环中进行数据库查询 (N+1 Problem)
- **问题描述**: 在一个`for`循环中多次调用数据库查询方法，例如`for item in items: await db.get_item_details(item.id)`。
- **正确做法**: **必须**先收集所有需要查询的ID，然后使用`WHERE id IN (...)`进行**一次性批量查询**，以避免数据库性能问题。

### 错误：与特定AI框架的实现强耦合
- **问题描述**: 业务逻辑直接依赖并调用某个特定AI框架（如旧版Agno）的内部记忆模块（如`agent.memory.add_memory()`）。这导致在更换记忆系统（如迁移到Zep/Mem0）时，需要大规模重构所有相关的业务代码。
- **正确做法**: **必须**通过一个抽象的服务层（如项目中的`MemoryService`）来封装与外部AI服务的交互。业务代码只应与这个抽象层打交道，而不关心底层的具体实现是Zep、Mem0还是其他服务。这确保了在更换底层AI服务时，我们只需要修改`MemoryService`的具体实现，而业务逻辑保持不变，实现了高度解耦和灵活性。

## 前端开发 (Frontend)

### 错误：忘记清理useEffect中的副作用
- **问题描述**: 在`useEffect`中添加了事件监听(`addEventListener`)或定时器(`setInterval`)，但在返回的清理函数中忘记移除它们。
- **正确做法**: 任何在`useEffect`中注册的订阅、监听或定时器，**都必须**在返回的清理函数中被正确地清理（如`removeEventListener`, `clearInterval`），以防止内存泄漏和意外行为。