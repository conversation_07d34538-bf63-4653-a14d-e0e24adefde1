"""
环境变量配置测试 - 对应AC1场景3: 环境变量配置fallback机制
"""
import pytest
import os
import tempfile
from unittest.mock import patch, mock_open
from pydantic import ValidationError

class TestEnvironmentConfiguration:
    """场景3: 环境变量配置fallback机制"""

    def test_settings_fallback_mechanism(self):
        """测试当.env和1.env文件都不存在时，Settings.load_with_fallback应返回默认配置，不抛异常"""
        from api.settings import Settings
        # patch open使其总是FileNotFoundError，模拟两个文件都不存在
        with patch("builtins.open", side_effect=FileNotFoundError):
            # 调用load_with_fallback应不会抛异常，返回Settings实例
            settings = Settings.load_with_fallback()
            # 检查部分默认值（如PROJECT_NAME）
            assert settings.PROJECT_NAME == "Agent API"
            # 检查DATABASE_URL被正确构建
            assert settings.DATABASE_URL is not None

    def test_required_environment_variables(self):
        """测试必需的环境变量是否正确加载"""
        from api.settings import settings

        # 这些环境变量应该存在（从.env或1.env文件加载）
        assert settings.SUPABASE_URL is not None, "SUPABASE_URL应该被设置"
        assert settings.SUPABASE_SERVICE_ROLE_KEY is not None, "SUPABASE_SERVICE_ROLE_KEY应该被设置"
        assert settings.VOLCANO_LLM_APP_KEY is not None, "VOLCANO_LLM_APP_KEY应该被设置"

    def test_settings_use_pydantic_v2_syntax(self):
        """测试Settings类是否使用了正确的Pydantic V2语法"""
        from api.settings import Settings
        # 检查是否有model_config属性
        assert hasattr(Settings, 'model_config'), "Settings类应该有model_config属性"
        # 检查model_config是dict，并包含关键字段
        model_config = Settings.model_config
        assert isinstance(model_config, dict), "model_config应该是dict类型"
        assert 'env_file' in model_config, "model_config应包含env_file字段"
        assert model_config['env_file'] == '.env', "env_file应为.env"

    def test_database_url_construction(self):
        """测试数据库URL是否正确构建"""
        from api.settings import settings

        # 根据Dev Notes，DATABASE_URL应该是PostgreSQL格式
        assert settings.DATABASE_URL is not None, "DATABASE_URL应该被构建"
        assert settings.DATABASE_URL.startswith("postgresql://"), "DATABASE_URL应该是PostgreSQL格式"
