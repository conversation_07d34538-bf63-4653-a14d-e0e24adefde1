import asyncio
import os
from openai import AsyncOpenAI
from dotenv import load_dotenv

# --- 1. 配置 ---
# 加载 1.env 文件中的环境变量
load_dotenv(dotenv_path="../1.env")

# 火山引擎 (方舟) LLM 配置
ARK_API_KEY = os.environ.get("VOLCENGINE_API_KEY")
VOLCANO_LLM_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
VOLCANO_LLM_ENDPOINT_ID = os.environ.get("VOLCANO_LLM_ENDPOINT_ID")

# Zep Cloud 配置
ZEP_API_KEY = os.environ.get("ZEP_API_KEY")

# 用于演示的唯一用户标识符
USER_ID = "demo_user_volcano_zep_chat_001"
SESSION_ID = f"session_for_{USER_ID}"

# 为聊天机器人设计的系统提示
SYSTEM_PROMPT = """你是一个拥有长期记忆的 AI 助手。
请利用 Zep 提供的对话历史摘要和最近的几条消息，来更连贯地、有上下文地回应用户。"""


def map_zep_role_to_openai(role_type: str) -> str:
    """将 Zep 的角色类型映射到 OpenAI 兼容的角色字符串。"""
    if role_type.lower() in ["human", "user"]:
        return "user"
    elif role_type.lower() in ["ai", "assistant"]:
        return "assistant"
    elif role_type.lower() == "system":
        return "system"
    return "user"  # 默认为 user


async def test_volcano_llm_connection(llm_client, endpoint_id):
    """测试火山引擎 LLM 连接"""
    try:
        print(f">>> 测试火山引擎 LLM 连接 (Endpoint: {endpoint_id})...")
        test_response = await llm_client.chat.completions.create(
            model=endpoint_id,
            messages=[{"role": "user", "content": "你好，请回复'测试成功'"}],
            max_tokens=50,
        )
        print(f">>> LLM 测试成功: {test_response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f">>> LLM 连接测试失败: {e}")
        return False


async def chat_with_ai():
    """
    主聊天循环函数，集成了 Zep 记忆和火山引擎 LLM。
    """
    # --- 检查 API Keys 是否已配置 ---
    print(">>> 正在验证配置...")
    print(f">>> ZEP_API_KEY: {'✅ 已设置' if ZEP_API_KEY else '❌ 未设置'}")
    print(f">>> VOLCENGINE_API_KEY: {'✅ 已设置' if ARK_API_KEY else '❌ 未设置'}")
    print(f">>> VOLCANO_LLM_ENDPOINT_ID: {VOLCANO_LLM_ENDPOINT_ID if VOLCANO_LLM_ENDPOINT_ID else '❌ 未设置'}")

    if not ARK_API_KEY or not VOLCANO_LLM_ENDPOINT_ID:
        print("\n❌ 错误：请在 1.env 文件中设置火山引擎相关的环境变量。")
        print("需要设置：VOLCENGINE_API_KEY, VOLCANO_LLM_ENDPOINT_ID")
        print("请检查你的火山引擎控制台，确认正确的 Endpoint ID。")
        return

    if not ZEP_API_KEY:
        print("\n⚠️ 警告：ZEP_API_KEY 未设置，将跳过 Zep 记忆功能。")
        zep_enabled = False
    elif not ZEP_API_KEY.startswith("z_"):
        print("\n❌ 错误：ZEP_API_KEY 格式不正确。Zep Cloud API 密钥必须以 'z_' 开头。")
        print("请确认你使用的是 Zep Cloud API 密钥，而不是 Zep Open Source 密钥。")
        return
    else:
        zep_enabled = True

    # --- 初始化 LLM 客户端 ---
    print("\n--- 正在初始化火山引擎 LLM 客户端 ---")
    llm_client = AsyncOpenAI(
        base_url=VOLCANO_LLM_BASE_URL,
        api_key=ARK_API_KEY,
    )

    # --- 测试 LLM 连接 ---
    if not await test_volcano_llm_connection(llm_client, VOLCANO_LLM_ENDPOINT_ID):
        print("\n❌ 火山引擎 LLM 连接失败，请检查：")
        print("1. VOLCENGINE_API_KEY 是否正确")
        print("2. VOLCANO_LLM_ENDPOINT_ID 是否有效 (当前值:", VOLCANO_LLM_ENDPOINT_ID, ")")
        print("3. 网络连接是否正常")
        print("请登录火山引擎控制台 -> 火山方舟 -> 模型推理，确认正确的 Endpoint ID")
        return

    # --- 初始化 Zep 客户端（如果启用） ---
    zep_client = None
    if zep_enabled:
        try:
            # 使用最新的 zep-cloud SDK
            from zep_cloud.client import AsyncZep
            from zep_cloud import Message

            print("--- 正在初始化 Zep 客户端 ---")
            zep_client = AsyncZep(api_key=ZEP_API_KEY)

            # 尝试添加用户
            print(f"--- 正在为用户 {USER_ID} 设置记忆空间 ---")
            try:
                # 使用新的API格式添加用户
                await zep_client.user.add(
                    user_id=USER_ID,
                    email="<EMAIL>",
                    first_name="Alex",
                    last_name="Hu",
                    metadata={"client": "volcano_llm_chat"}
                )
                print("用户创建/更新成功。")
            except Exception as e:
                print(f"添加用户时出现信息 (可能已存在，可忽略): {e}")

            # 创建或获取会话
            try:
                await zep_client.memory.add_session(session_id=SESSION_ID, user_id=USER_ID)
                print("会话创建/更新成功。")
            except Exception as e:
                print(f"添加会话时出现信息 (可能已存在，可忽略): {e}")

        except ImportError:
            print(">>> 错误：未找到 zep-cloud SDK。请安装：pip install zep-cloud")
            zep_enabled = False
        except Exception as e:
            print(f">>> Zep 初始化失败: {e}")
            print(">>> 将跳过 Zep 记忆功能，仅使用 LLM")
            zep_enabled = False

    print("\n--- 聊天开始 ---")
    if zep_enabled:
        print(f"你好！我是你的 AI 助手（已启用记忆功能）。所有对话都将为用户 '{USER_ID}' 保存。")
    else:
        print("你好！我是你的 AI 助手（记忆功能已禁用）。")
    print("输入 'quit' 或 'exit' 来结束对话。")

    while True:
        try:
            user_input = await asyncio.to_thread(input, f"\n你 ({USER_ID}): ")

            if user_input.lower() in ["quit", "exit"]:
                print("--- 聊天结束，下次再见！ ---")
                break

            # --- 构建 LLM 消息 ---
            llm_messages = [{"role": "system", "content": SYSTEM_PROMPT}]

            # --- 如果启用了 Zep，从 Zep 检索记忆 ---
            if zep_enabled and zep_client:
                try:
                    print(">>> 正在从 Zep 检索记忆...")
                    memory = await zep_client.memory.get(session_id=SESSION_ID)

                    # 检查是否有上下文信息
                    if memory and hasattr(memory, 'context') and memory.context:
                        print(f">>> Zep 上下文摘要: {memory.context}")
                        llm_messages.append({
                            "role": "assistant",
                            "content": f"这是我们之前对话的摘要，请将此作为你的记忆:\n{memory.context}",
                        })

                    # 检查是否有最近的消息
                    if memory and hasattr(memory, 'messages') and memory.messages:
                        print(">>> Zep 最近消息:")
                        for msg in memory.messages[-5:]:  # 只取最近5条消息
                            # 获取角色类型
                            role_type = getattr(msg, 'role_type', 'user')
                            role = map_zep_role_to_openai(role_type)
                            print(f">>> - {role}: {msg.content}")
                            llm_messages.append({"role": role, "content": msg.content})

                except Exception as e:
                    print(f">>> 检索 Zep 记忆失败: {e}")

            # 添加用户当前输入
            llm_messages.append({"role": "user", "content": user_input})

            # --- 调用火山引擎 LLM ---
            print(">>> 正在调用火山引擎 LLM 生成回复...")
            try:
                response = await llm_client.chat.completions.create(
                    model=VOLCANO_LLM_ENDPOINT_ID,
                    messages=llm_messages,
                    max_tokens=1024,
                )
                ai_response_content = response.choices[0].message.content
                print(f"\nAI: {ai_response_content}")
            except Exception as llm_error:
                print(f">>> 调用 LLM 时发生错误: {llm_error}")
                print(f">>> 使用的 Endpoint ID: {VOLCANO_LLM_ENDPOINT_ID}")
                print(">>> 请检查 Endpoint ID 是否正确或已过期")
                continue

            # --- 将新对话存入 Zep 记忆（如果启用） ---
            if zep_enabled and zep_client:
                try:
                    print(">>> 正在将新对话存入 Zep 记忆库...")
                    # 创建新消息，使用正确的API格式
                    new_messages = [
                        Message(role_type="user", content=user_input),
                        Message(role_type="assistant", content=ai_response_content),
                    ]
                    await zep_client.memory.add(session_id=SESSION_ID, messages=new_messages)
                    print(">>> 记忆存储成功。")
                except Exception as e:
                    print(f">>> 存储记忆失败: {e}")

        except Exception as e:
            print(f"\n在聊天过程中发生错误: {e}")
            print("请检查你的 API Keys、网络连接和模型 Endpoint ID 是否正确。")


if __name__ == "__main__":
    try:
        asyncio.run(chat_with_ai())
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
